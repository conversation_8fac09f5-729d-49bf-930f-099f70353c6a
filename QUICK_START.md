# Quick Start Guide - Enhanced Build System

This guide will help you get started with the new environment-specific build system for your Ionic Angular app.

## 🚀 Quick Test

First, test that everything is set up correctly:

```bash
npm run test:build-system
```

This will validate your configuration and show any issues that need to be fixed.

## 🏗️ Basic Usage

### Build for different environments

```bash
# Production build (optimized)
npm run build:production

# Development build (with debugging)
npm run build:development

# Tenant-specific builds
npm run build:alhumaid
npm run build:alhamdan
npm run build:algarawi

# Demo environment
npm run build:demo
```

### Using Ionic CLI

The enhanced build system integrates with Ionic CLI:

```bash
ionic build --configuration=production
ionic build --configuration=alhumaid
```

### Advanced options

```bash
# Build with verbose logging
npm run build:env -- --env=production --verbose

# Build without copying tenant files
npm run build:env -- --env=alhumaid --no-copy-files

# Test file copying without building
npm run build:env -- --env=alhumaid --skip-build
```

## 📁 What Gets Built

When you run a build, the system:

1. **Validates** your environment configuration
2. **Runs pre-build hooks** (generates build info, sets up assets)
3. **Builds your Angular app** with the specified configuration
4. **Copies tenant-specific files** (well-known files, assets, etc.)
5. **Runs post-build hooks** (generates sitemap, robots.txt, etc.)

## 🏢 Tenant-Specific Builds

For tenant builds (alhumaid, alhamdan, algarawi), the system automatically:

- Uses the correct environment configuration
- Copies tenant-specific `.well-known` files
- Uses tenant-specific `index.html`
- Copies tenant assets to the build output
- Applies tenant-specific optimizations

## 📂 File Structure

Your tenant files should be organized like this:

```
src/tenants/
├── alhumaid/
│   ├── .well-known/
│   │   ├── apple-app-site-association
│   │   └── apple-developer-merchantid-domain-association
│   ├── assets/          # Tenant-specific assets
│   ├── index.html       # Tenant-specific index
│   └── favicon.ico      # Tenant-specific favicon
├── alhamdan/
│   └── ...
└── algarawi/
    └── ...
```

## 🔧 Customization

### Adding a new environment

1. Create environment file: `src/environments/environment.newenv.ts`
2. Add Angular configuration in `angular.json`
3. Add to `build.config.js` environments array
4. Add npm script in `package.json`

### Adding a new tenant

1. Create tenant directory: `src/tenants/newtenant/`
2. Add tenant files (index.html, .well-known/, etc.)
3. Add to `build.config.js` tenantMapping
4. Add npm script: `"build:newtenant": "npm run build:env -- --env=newtenant"`

## 🚨 Troubleshooting

### Common Issues

**Environment not found:**
```bash
npm run test:build-system
```
This will show which environments are missing configurations.

**Tenant files not copying:**
```bash
npm run build:env -- --env=alhumaid --verbose
```
Use verbose mode to see exactly what's happening.

**Build fails:**
Check that your environment file exists:
- `src/environments/environment.{env}.ts`
- Angular configuration in `angular.json`

### Debug Mode

Always use `--verbose` when troubleshooting:

```bash
npm run build:env -- --env=production --verbose
```

## 📋 Migration Checklist

If you're migrating from the old system:

- [ ] Test the build system: `npm run test:build-system`
- [ ] Try a simple build: `npm run build:development`
- [ ] Test tenant build: `npm run build:alhumaid`
- [ ] Verify files are copied correctly
- [ ] Update your deployment scripts to use new commands
- [ ] Update CI/CD pipelines if needed

## 🎯 Next Steps

1. **Test your builds** with the new system
2. **Update deployment scripts** to use the new commands
3. **Customize** `build.config.js` for your specific needs
4. **Add environment-specific assets** in `src/assets/environments/`
5. **Customize build hooks** in `scripts/pre-build.js` and `scripts/post-build.js`

## 📚 More Information

- See `BUILD_SYSTEM.md` for detailed documentation
- Check `build.config.js` for all configuration options
- Look at the scripts in `scripts/` directory for customization examples

## 🆘 Getting Help

If you encounter issues:

1. Run `npm run test:build-system` to diagnose problems
2. Use `--verbose` flag to see detailed output
3. Check the generated files in `dist/app/browser/`
4. Verify your tenant directory structure matches the expected format

The new build system is backward compatible, so your existing scripts will continue to work while you transition to the enhanced features.
