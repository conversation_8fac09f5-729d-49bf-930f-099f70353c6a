const fs = require('fs');
const path = require('path');

// Get tenant from command line arguments
const args = process.argv.slice(2);

// Require tenant argument
if (args.length === 0) {
  console.error('Error: No tenant specified. Please provide a tenant name as an argument.');
  console.error('Usage: node copy-well-known-files.js <tenant-name>');
  process.exit(1);
}

const tenant = args[0];
console.log(`Using tenant: ${tenant}`);
const files = [
  'apple-app-site-association',
  'apple-developer-merchantid-domain-association'
];

// Paths
const sourceDir = path.join(__dirname, 'src', 'tenants', tenant, '.well-known');
const targetDir = path.join(__dirname, 'dist', 'app', 'browser', '.well-known');

// Create .well-known directory if it doesn't exist
if (!fs.existsSync(targetDir)) {
  fs.mkdirSync(targetDir, { recursive: true });
}

// Copy each file
files.forEach(file => {
  const sourcePath = path.join(sourceDir, file);
  const targetPath = path.join(targetDir, file);

  if (fs.existsSync(sourcePath)) {
    fs.copyFileSync(sourcePath, targetPath);
    console.log(`Copied ${file} from tenant ${tenant}`);
  } else {
    console.error(`Source file not found: ${sourcePath}`);
  }
});

console.log('Well-known files copy completed');
