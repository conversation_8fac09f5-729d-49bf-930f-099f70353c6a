{"name": "family-social-app", "version": "0.0.1", "author": "<PERSON><PERSON>", "homepage": "https://hsb.sa", "scripts": {"ng": "ng", "start": "ng serve", "start:dev": "ng serve --configuration development", "start:local": "ng serve --configuration local --port 4100 --host 0.0.0.0 --disable-host-check --proxy-config proxy.conf.json --ssl --ssl-key D:\\altwijry.test.key --ssl-cert D:\\altwijry.test.crt", "build": "ng build", "build:dev": "ng build --configuration development", "build:cf": "ng build --configuration=${ENVIRONMENT}", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint", "dev:ssr": "ng run app:serve-ssr", "serve:ssr": "node dist/app/server/main.js", "build:ssr": "ng build && ng run app:server", "prerender": "ng run app:prerender", "deploy-alhumaid": "ng build --configuration=alhumaid && node copy-well-known-files.js alhumaid && firebase deploy --only hosting:alhumaid-20f35"}, "private": true, "dependencies": {"@alyle/ui": "17.0.0", "@angular/animations": "^18.1.0", "@angular/cdk": "^18.1.0", "@angular/common": "^18.1.0", "@angular/core": "^18.1.0", "@angular/forms": "^18.1.0", "@angular/platform-browser": "^18.1.0", "@angular/platform-browser-dynamic": "^18.1.0", "@angular/platform-server": "^18.1.0", "@angular/router": "^18.1.0", "@angular/ssr": "^18.1.0", "@capacitor-community/app-icon": "^6.0.0", "@capacitor/android": "^7.0.0", "@capacitor/app": "^7.0.0", "@capacitor/app-launcher": "^7.0.0", "@capacitor/browser": "^7.0.0", "@capacitor/clipboard": "^7.0.0", "@capacitor/core": "^7.0.0", "@capacitor/device": "^7.0.0", "@capacitor/filesystem": "^7.0.0", "@capacitor/haptics": "^7.0.0", "@capacitor/ios": "^7.0.0", "@capacitor/keyboard": "^7.0.0", "@capacitor/preferences": "^7.0.0", "@capacitor/share": "^7.0.0", "@capacitor/splash-screen": "^7.0.0", "@capacitor/status-bar": "^7.0.0", "@dustfoundation/ngx-google-analytics": "^15.0.0", "@emran-alhaddad/saudi-riyal-font": "^1.0.1", "@ionic/angular": "^8.2.5", "@ionic/angular-server": "7", "@ionic/storage": "^4.0.0", "@ionic/storage-angular": "^4.0.0", "@lottiefiles/lottie-player": "^2.0.2", "@splidejs/splide": "^4.1.4", "angularx-qrcode": "18.0.2", "capacitor-pass-to-wallet": "^4.0.0", "d3": "^7.8.5", "d3-org-chart": "^2.7.0", "d3-selection": "^3.0.0", "d3-shape": "^3.2.0", "d3-transition": "^3.0.1", "d3-zoom": "^3.0.0", "dagre": "^0.8.5", "dagre-compound": "^0.0.13", "date-fns": "^2.29.3", "esbuild": "^0.19.5", "express": "^4.15.2", "fabric": "^6.7.0", "firebase": "^11.5.0", "html2canvas": "^1.4.1", "ionicons": "^6.0.3", "laravel-echo": "^2.0.2", "lottie-web": "^5.12.2", "ngx-lottie": "12.0.0", "ngx-scanner-qrcode": "1.7.3", "ngx-splide": "^7.0.1", "partysocket": "^1.0.1", "pusher-js": "^8.4.0-rc2", "rxjs": "7.5.0", "semver": "^7.6.3", "swiper": "^11.2.5", "tslib": "^2.3.0", "zone.js": "~0.14.7"}, "devDependencies": {"@angular-builders/custom-webpack": "^18.0.0", "@angular-devkit/build-angular": "^18.1.0", "@angular-eslint/builder": "^16.2.0", "@angular-eslint/eslint-plugin": "^16.2.0", "@angular-eslint/eslint-plugin-template": "^14.0.0", "@angular-eslint/template-parser": "^14.0.0", "@angular/cli": "^18.1.0", "@angular/compiler": "^18.1.0", "@angular/compiler-cli": "^18.1.0", "@angular/language-service": "^18.1.0", "@capacitor/assets": "^3.0.1", "@capacitor/cli": "^7.0.0", "@ionic/angular-toolkit": "9", "@ionic/cli": "^7.1.1", "@types/cordova": "^11.0.3", "@types/d3": "^7.4.0", "@types/d3-flextree": "^2.1.1", "@types/d3-org-chart": "^2.6.3", "@types/dagre": "^0.7.48", "@types/express": "^4.17.0", "@types/jasmine": "~4.0.0", "@types/jsdom": "^21.1.7", "@types/node": "^20.8.2", "@types/qrcode": "^1.5.5", "@typescript-eslint/eslint-plugin": "5.3.0", "@typescript-eslint/parser": "5.3.0", "browser-sync": "^3.0.0", "eslint": "^7.6.0", "eslint-plugin-import": "2.22.1", "eslint-plugin-jsdoc": "30.7.6", "eslint-plugin-prefer-arrow": "1.2.2", "jasmine-core": "~4.3.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "ts-node": "~8.3.0", "typescript": "~5.4.2"}, "description": "Family Social App", "packageManager": "yarn@1.22.19+sha512.ff4579ab459bb25aa7c0ff75b62acebe576f6084b36aa842971cf250a5d8c6cd3bc9420b22ce63c7f93a0857bc6ef29291db39c3e7a23aab5adfd5a4dd6c5d71", "volta": {"node": "22.13.1"}}