# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a multi-tenant Angular/Ionic family social application built with TypeScript. The app serves different family organizations (tenants) including Altwijry, Alhumaid, and Algarawi, each with custom branding and configurations.

## Development Commands

### Core Development
- `yarn` - Install dependencies (uses Yarn package manager)
- `yarn start` - Start development server (uses 'development' configuration)
- `yarn start:dev` - Alias for development server
- `yarn start:local` - Local development with SSL and proxy configuration
- `yarn build` - Build for production
- `yarn build:dev` - Build for development
- `yarn build:cf` - Build with environment-specific configuration (using $ENVIRONMENT variable)
- `yarn test` - Run unit tests with <PERSON>rma/Jasmine
- `yarn lint` - Run ESLint linting

### Mobile Development
- Use Capacitor for iOS/Android builds
- Capacitor config is in `capacitor.config.ts`
- Native projects are in `ios/` and `android/` directories

## Architecture & Multi-Tenancy

### Environment Configuration
The app uses Angular's environment replacement system for multi-tenant support:
- `environment.ts` - Default/development (uses Alhumaid configuration)
- `environment.prod.ts` - Production (Altwijry)
- `environment.alhumaid.ts` - Alhumaid tenant
- `environment.algarawi.ts` - Algarawi tenant
- `environment.dev.ts` - Development environment

### Tenant-Specific Assets
- Each tenant has custom HTML templates in `src/tenants/`
- App icons and branding assets are tenant-specific
- Firebase hosting configurations support multiple tenants


### Icons Usage
- **Primary Icon Library**: hugeicons-pro (already installed)
- **Installation**: `npm install @hugeicons/angular @hugeicons-pro/core-{variant}-{style}`
- **Icon Browser**: Browse all available icons at https://hugeicons.com/api/icons
- **Getting Icon Names Right** (NEVER GUESS):
  1. Always use the icon browser to find exact names: https://hugeicons.com/api/icons
  2. **Conversion Rule**: API `"name"` → Angular Import Name
    - Convert kebab-case to PascalCase + "Icon" suffix
    - Example: `"call-02"` → `Call02Icon`
    - Example: `"user-group"` → `UserGroupIcon`
    - Example: `"school-01"` → `School01Icon`
  3. **Validation**: API object `{"name": "call-02"}` = Angular `Call02Icon` ✅
  4. **Always verify** import exists before using
- **Usage**: Import specific icons and use `<hugeicons-icon>` component
- **Example**:
```typescript
import { Notification01Icon } from '@hugeicons-pro/core-stroke-rounded'
// In component: notification01Icon = Notification01Icon;
// In template: <hugeicons-icon [icon]="notification01Icon" [size]="24" color="currentColor" [strokeWidth]="1.5">
```
- **Avoid**: ion-icons except for Ionic-specific components

### Key Services Architecture
- **AppDataService**: Handles app initialization and core data loading
- **FeaturesService**: Manages feature flags and capabilities
- **StorageService**: Local storage abstraction using Ionic Storage
- **AuthService & ProfileService**: User authentication and profile management
- **ThemeService**: Dynamic theming with light/dark mode support
- **ApiService**: HTTP client wrapper with interceptor support

### Routing & Guards
- Uses Angular router with custom guards for authentication
- `CheckIsLoggedIn` guard verifies JWT tokens and expiration
- `HasRoles` guard checks user permissions
- Lazy loading pattern for most routes
- Dynamic route loading after app initialization

## Key Features

### Family Tree Visualization
- D3.js-based family tree/org chart (`d3-org-chart.ts`)
- Interactive family graph with user profiles
- Located in `/pages/family-graph/`

### Multi-Module Structure
- **Activities**: Event management and registration
- **Store**: E-commerce functionality with payment integration
- **Members**: Membership management and subscriptions
- **Quran Competitions**: Competition management
- **Workout Programs**: Fitness tracking with Strava integration
- **Consultation**: Consultation booking system
- **Documents & Literature**: Content management

### Payment Integration
- MyFatoorah payment gateway integration
- Apple Pay support
- Multiple checkout flows for different modules

### Mobile Features
- QR code scanning for authentication and features
- App icon customization (alternate icons)
- Push notifications support
- Camera integration for profile photos
- Apple Wallet pass integration

## Development Guidelines

### Code Style
- Uses ESLint with Angular-specific rules
- TypeScript strict mode enabled
- SCSS for styling with Ionic design system
- Arabic language support (RTL) with locale-specific formatting

### State Management
- RxJS for reactive programming patterns
- Services use BehaviorSubject for state management
- Local storage for persistence via StorageService
- No external state management library (NgRx, Akita, etc.)

### Testing
- Karma + Jasmine for unit tests
- Test files follow `*.spec.ts` naming convention
- Limited test coverage currently exists

### Build Considerations
- Bundle size limits configured in `angular.json` (2MB warning, 5MB error)
- Code splitting implemented for lazy-loaded routes
- Asset optimization for multiple image formats (webp, png, svg)
- SSR support for better SEO and performance

## Important Notes

- The app requires initialization of StorageService, AppDataService, and FeaturesService before routing
- Authentication tokens are stored locally with expiration checking
- Multi-language support primarily for Arabic (RTL)
- Capacitor is used for native mobile features
- Firebase is used for hosting and deployment
