#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * Enhanced build script that handles environment-specific configurations
 * and integrates custom file operations into the build process.
 *
 * Usage:
 *   node scripts/build-with-env.js --env=production
 *   node scripts/build-with-env.js --env=development --tenant=alhumaid
 *   node scripts/build-with-env.js --env=demo --copy-files
 */

class EnvironmentBuilder {
  constructor() {
    this.args = this.parseArguments();
    this.projectRoot = path.resolve(__dirname, '..');

    // Load configuration
    try {
      this.config = require(path.join(this.projectRoot, 'build.config.js'));
    } catch (error) {
      console.warn('⚠️  build.config.js not found, using defaults');
      this.config = this.getDefaultConfig();
    }

    this.supportedEnvironments = this.config.environments;
  }

  getDefaultConfig() {
    return {
      environments: ['production', 'development', 'demo', 'alhumaid', 'alhamdan', 'algarawi', 'local', 'staging', 'ci'],
      tenantMapping: { 'alhumaid': 'alhumaid', 'alhamdan': 'alhamdan', 'algarawi': 'algarawi', 'demo': 'demo' },
      hooks: { preBuild: { enabled: true }, postBuild: { enabled: true } },
      paths: { dist: 'dist/app/browser', tenants: 'src/tenants' }
    };
  }

  parseArguments() {
    const args = process.argv.slice(2);
    const parsed = {
      env: 'production',
      tenant: null,
      copyFiles: true,
      verbose: false,
      skipBuild: false
    };

    args.forEach(arg => {
      if (arg.startsWith('--env=')) {
        parsed.env = arg.split('=')[1];
      } else if (arg.startsWith('--tenant=')) {
        parsed.tenant = arg.split('=')[1];
      } else if (arg === '--no-copy-files') {
        parsed.copyFiles = false;
      } else if (arg === '--verbose') {
        parsed.verbose = true;
      } else if (arg === '--skip-build') {
        parsed.skipBuild = true;
      } else if (arg === '--help') {
        this.showHelp();
        process.exit(0);
      }
    });

    return parsed;
  }

  showHelp() {
    console.log(`
Environment-specific Build Script for Ionic Angular App

Usage:
  node scripts/build-with-env.js [options]

Options:
  --env=<environment>     Environment to build for (default: production)
                         Supported: ${this.supportedEnvironments.join(', ')}
  --tenant=<tenant>       Specific tenant to build for (overrides env detection)
  --no-copy-files        Skip copying tenant-specific files
  --verbose              Enable verbose logging
  --skip-build           Skip Angular build, only run file operations
  --help                 Show this help message

Examples:
  npm run build:env -- --env=production
  npm run build:env -- --env=alhumaid --verbose
  npm run build:env -- --env=development --no-copy-files
  ionic build --configuration=production
    `);
  }

  validateEnvironment() {
    if (!this.supportedEnvironments.includes(this.args.env)) {
      console.error(`❌ Error: Unsupported environment '${this.args.env}'`);
      console.error(`Supported environments: ${this.supportedEnvironments.join(', ')}`);
      process.exit(1);
    }
  }

  log(message, force = false) {
    if (this.args.verbose || force) {
      console.log(`🔧 ${message}`);
    }
  }

  async runPreBuildHooks() {
    this.log('Running pre-build hooks...', true);

    const preBuildScript = path.join(this.projectRoot, 'scripts', 'pre-build.js');
    if (fs.existsSync(preBuildScript)) {
      try {
        execSync(`node "${preBuildScript}" --env=${this.args.env}`, {
          stdio: this.args.verbose ? 'inherit' : 'pipe',
          cwd: this.projectRoot
        });
        this.log('Pre-build hooks completed');
      } catch (error) {
        console.error('❌ Pre-build hooks failed:', error.message);
        process.exit(1);
      }
    }
  }

  async runAngularBuild() {
    if (this.args.skipBuild) {
      this.log('Skipping Angular build as requested', true);
      return;
    }

    this.log(`Building Angular app with configuration: ${this.args.env}`, true);

    try {
      const buildCommand = `ng build --configuration=${this.args.env}`;
      this.log(`Executing: ${buildCommand}`);

      execSync(buildCommand, {
        stdio: this.args.verbose ? 'inherit' : 'pipe',
        cwd: this.projectRoot
      });

      this.log('Angular build completed successfully', true);
    } catch (error) {
      console.error('❌ Angular build failed:', error.message);
      process.exit(1);
    }
  }

  async runPostBuildHooks() {
    this.log('Running post-build hooks...', true);

    // Copy tenant-specific files
    if (this.args.copyFiles) {
      await this.copyTenantFiles();
    }

    // Run custom post-build script if it exists
    const postBuildScript = path.join(this.projectRoot, 'scripts', 'post-build.js');
    if (fs.existsSync(postBuildScript)) {
      try {
        execSync(`node "${postBuildScript}" --env=${this.args.env}`, {
          stdio: this.args.verbose ? 'inherit' : 'pipe',
          cwd: this.projectRoot
        });
        this.log('Post-build hooks completed');
      } catch (error) {
        console.error('❌ Post-build hooks failed:', error.message);
        process.exit(1);
      }
    }
  }

  async copyTenantFiles() {
    const tenant = this.args.tenant || this.detectTenantFromEnvironment();

    if (!tenant) {
      this.log('No tenant detected, skipping file copy');
      return;
    }

    this.log(`Copying tenant-specific files for: ${tenant}`, true);

    const copyScript = path.join(this.projectRoot, 'scripts', 'copy-tenant-files.js');
    if (fs.existsSync(copyScript)) {
      try {
        execSync(`node "${copyScript}" ${tenant}`, {
          stdio: this.args.verbose ? 'inherit' : 'pipe',
          cwd: this.projectRoot
        });
        this.log('Tenant files copied successfully');
      } catch (error) {
        console.error('❌ Failed to copy tenant files:', error.message);
        process.exit(1);
      }
    } else {
      // Fallback to original copy script
      const originalCopyScript = path.join(this.projectRoot, 'copy-well-known-files.js');
      if (fs.existsSync(originalCopyScript)) {
        try {
          execSync(`node "${originalCopyScript}" ${tenant}`, {
            stdio: this.args.verbose ? 'inherit' : 'pipe',
            cwd: this.projectRoot
          });
          this.log('Well-known files copied successfully (fallback)');
        } catch (error) {
          console.error('❌ Failed to copy well-known files:', error.message);
          process.exit(1);
        }
      }
    }
  }

  detectTenantFromEnvironment() {
    // Use configuration mapping
    return this.config.tenantMapping[this.args.env] || null;
  }

  async build() {
    console.log(`🚀 Starting environment-specific build for: ${this.args.env}`);

    this.validateEnvironment();

    try {
      await this.runPreBuildHooks();
      await this.runAngularBuild();
      await this.runPostBuildHooks();

      console.log(`✅ Build completed successfully for environment: ${this.args.env}`);
    } catch (error) {
      console.error('❌ Build failed:', error.message);
      process.exit(1);
    }
  }
}

// Run the builder
if (require.main === module) {
  const builder = new EnvironmentBuilder();
  builder.build().catch(error => {
    console.error('❌ Unexpected error:', error);
    process.exit(1);
  });
}

module.exports = EnvironmentBuilder;
