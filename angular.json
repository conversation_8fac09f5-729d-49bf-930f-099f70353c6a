{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"app": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"allowedCommonJsDependencies": ["raf", "core-js", "html2canvas", "rgbcolor", "semver", "qrcode"], "outputPath": "dist/app/browser", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "node_modules/ngx-scanner-qrcode/wasm/", "output": "assets/wasm/"}, {"glob": "**/*", "input": "src/assets/.well-known", "output": ".well-known"}, {"glob": "**/*", "input": "src/assets", "output": "assets"}, {"glob": "**/*.svg", "input": "node_modules/ionicons/dist/ionicons/svg", "output": "./svg"}], "styles": ["src/global.scss", "src/global.less", "src/theme/variables.scss", "node_modules/@splidejs/splide/dist/css/splide.min.css", "node_modules/@splidejs/splide/dist/css/themes/splide-default.min.css"], "stylePreprocessorOptions": {"includePaths": ["node_modules/"]}, "scripts": [{"input": "./node_modules/@lottiefiles/lottie-player/dist/lottie-player.js", "inject": false, "bundleName": "lottie-player"}, "node_modules/@splidejs/splide/dist/js/splide.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "200kb", "maximumError": "400kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "index": {"input": "src/tenants/altwijry.html", "output": "index.html"}, "outputHashing": "all"}, "demo": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "200kb", "maximumError": "400kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.demo.ts"}], "index": {"input": "src/tenants/demo/index.html", "output": "index.html"}, "outputHashing": "all"}, "alhumaid": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "200kb", "maximumError": "400kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.alhumaid.ts"}], "index": {"input": "src/tenants/alhumaid/index.html", "output": "index.html"}, "outputHashing": "all"}, "alhamdan": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "200kb", "maximumError": "400kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.alhamdan.ts"}], "index": {"input": "src/tenants/alhamdan/index.html", "output": "index.html"}, "outputHashing": "all"}, "algarawi": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "200kb", "maximumError": "400kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.algarawi.ts"}], "index": {"input": "src/tenants/algarawi/index.html", "output": "index.html"}, "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}]}, "local": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.local.ts"}]}, "ci": {"progress": false}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "app:build:production"}, "development": {"buildTarget": "app:build:development"}, "alhumaid": {"buildTarget": "app:build:<PERSON><PERSON><PERSON>"}, "alhamdan": {"buildTarget": "app:build:alhamdan"}, "demo": {"buildTarget": "app:build:demo"}, "local": {"buildTarget": "app:build:local"}, "ci": {"progress": false}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "app:build"}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}, "server": {"builder": "@angular-devkit/build-angular:server", "options": {"outputPath": "dist/app/server", "main": "server.ts", "tsConfig": "tsconfig.server.json", "inlineStyleLanguage": "scss"}, "configurations": {"production": {"outputHashing": "media", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}, "development": {"optimization": false, "sourceMap": true, "extractLicenses": false, "vendorChunk": true, "buildOptimizer": false, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}]}, "local": {"optimization": false, "sourceMap": true, "extractLicenses": false, "vendorChunk": true, "buildOptimizer": false, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.local.ts"}]}, "ci": {}}, "defaultConfiguration": "production"}, "serve-ssr": {"builder": "@angular-devkit/build-angular:ssr-dev-server", "configurations": {"development": {"browserTarget": "app:build:development", "serverTarget": "app:server:development"}, "local": {"browserTarget": "app:build:local", "serverTarget": "app:server:local"}, "production": {"browserTarget": "app:build:production", "serverTarget": "app:server:production"}}, "defaultConfiguration": "development"}, "prerender": {"builder": "@angular-devkit/build-angular:prerender", "options": {"routes": ["/"]}, "configurations": {"production": {"browserTarget": "app:build:production", "serverTarget": "app:server:production"}, "development": {"browserTarget": "app:build:development", "serverTarget": "app:server:development"}, "local": {"browserTarget": "app:build:local", "serverTarget": "app:server:local"}}, "defaultConfiguration": "production"}}}}, "cli": {"packageManager": "yarn", "schematicCollections": ["@ionic/angular-toolkit"], "analytics": false}, "schematics": {"@ionic/angular-toolkit:component": {"styleext": "scss"}, "@ionic/angular-toolkit:page": {"styleext": "scss"}}}