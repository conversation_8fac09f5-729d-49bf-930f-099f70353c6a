export async function onRequest(context: any) {
  const url = new URL(context.request.url);
  const adminPath = url.pathname;
  const hostname = url.hostname;

  // Determine target URL based on hostname
  let targetUrl: string;

  if (hostname === 'demo.hsb.sa') {
    targetUrl = `https://demo-admin.hsb.sa${adminPath}${url.search}`;
  } else if (hostname === 'altuwaijri.sa') {
    targetUrl = `https://portal.altwijry.com${adminPath}${url.search}`;
  } else {
    return new Response('Not Found', { status: 404 });
  }

  // Create new headers without Host header
  const headers = new Headers(context.request.headers);
  headers.delete('host');

  // Add X-Forwarded headers for proper proxy behavior
  headers.set('X-Forwarded-Host', hostname);
  headers.set('X-Forwarded-Proto', url.protocol.replace(':', ''));

  // Create a new request with the modified headers
  const request = new Request(targetUrl, {
    method: context.request.method,
    headers: headers,
    body: context.request.method !== 'GET' && context.request.method !== 'HEAD'
      ? context.request.body
      : undefined,
    redirect: 'manual'
  });

  // Fetch from the target URL
  const response = await fetch(request);

  // Create new response headers
  const responseHeaders = new Headers(response.headers);

  // Remove headers that might cause issues
  responseHeaders.delete('content-encoding');
  responseHeaders.delete('content-length');

  // Handle redirects - rewrite them to stay on the original domain
  if (response.status >= 300 && response.status < 400) {
    const location = responseHeaders.get('location');
    if (location) {
      const locationUrl = new URL(location, targetUrl);
      // Rewrite the location to use the original domain
      if (hostname === 'demo.hsb.sa' && locationUrl.hostname === 'demo-admin.hsb.sa') {
        locationUrl.hostname = hostname;
        locationUrl.protocol = url.protocol;
        responseHeaders.set('location', locationUrl.toString());
      } else if (hostname === 'altuwaijri.sa' && locationUrl.hostname === 'portal.altwijry.com') {
        locationUrl.hostname = hostname;
        locationUrl.protocol = url.protocol;
        responseHeaders.set('location', locationUrl.toString());
      }
    }
  }

  // Add CORS headers if needed
  responseHeaders.set('Access-Control-Allow-Origin', '*');

  // Return the response with modified headers
  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: responseHeaders
  });
}
