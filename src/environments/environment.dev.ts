// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  baseUrl: 'https://api.altwijry.test',
  storageUrl: 'https://api.altwijry.test',
  appURL: 'https://altuwaijri.sa',
  myfatoorah: {
    applepay_js_url: 'https://demo.myfatoorah.com/applepay/v2/applepay.js',
    session_js_url: 'https://demo.myfatoorah.com/cardview/v2/session.js',
    countryCode: 'SAU',
    currencyCode: 'SAR',
  },
  STRAVA_CLIENT_ID: 116238,
  STRAVA_CALLBACK_DOMAIN: 'altuwaijri.sa',
  partKitHost: 's.altwijry.com',
  wsAppId: 'percqzbbshkl6jqnn6yy',
  wsHost: 's.altwijry.com',
  wsPort: 80,
  wssPort: 443,
  wsForceTLS: true,
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
