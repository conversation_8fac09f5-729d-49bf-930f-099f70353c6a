{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-checkbox_entry_js.js", "mappings": ";;;;;;;;;;;;;;AAAA;AACA;AACA;AAC2D;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,0BAA0B,GAAIC,EAAE,IAAK;EACzC,MAAMC,SAAS,GAAGD,EAAE;EACpB,IAAIE,aAAa;EACjB,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAID,aAAa,KAAKE,SAAS,EAAE;MAC/B;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACM,MAAMC,YAAY,GAAGJ,SAAS,CAACK,KAAK,KAAKF,SAAS,IAAIG,YAAY,CAACN,SAAS,CAAC;MAC7E,MAAMO,qBAAqB,GAAGP,SAAS,CAACQ,YAAY,CAAC,YAAY,CAAC;MAChE;MACCR,SAAS,CAACQ,YAAY,CAAC,iBAAiB,CAAC,IAAIR,SAAS,CAACS,UAAU,KAAK,IAAK;MAC9E,MAAMC,eAAe,GAAGb,uDAAa,CAACG,SAAS,CAAC;MAChD;AACN;AACA;AACA;MACMC,aAAa,GACXD,SAAS,CAACW,MAAM,KAAK,IAAI,IAAK,CAACP,YAAY,IAAI,CAACG,qBAAqB,IAAIG,eAAe,KAAK,IAAK;IACtG;IACA,OAAOT,aAAa;EACtB,CAAC;EACD,OAAO;IAAEC;EAAiB,CAAC;AAC7B,CAAC;AACD,MAAMI,YAAY,GAAIN,SAAS,IAAK;EAClC,MAAMY,IAAI,GAAGZ,SAAS,CAACS,UAAU;EACjC,IAAIG,IAAI,KAAK,IAAI,EAAE;IACjB,OAAO,KAAK;EACd;EACA;AACF;AACA;AACA;AACA;EACE,IAAIC,2BAA2B,CAACC,QAAQ,CAACd,SAAS,CAACe,OAAO,CAAC,IAAIf,SAAS,CAACgB,aAAa,CAAC,gBAAgB,CAAC,KAAK,IAAI,EAAE;IACjH,OAAO,IAAI;EACb;EACA;AACF;AACA;AACA;AACA;EACE,IAAIC,6BAA6B,CAACH,QAAQ,CAACd,SAAS,CAACe,OAAO,CAAC,IAAIf,SAAS,CAACkB,WAAW,KAAK,EAAE,EAAE;IAC7F,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC;AACD,MAAML,2BAA2B,GAAG,CAAC,WAAW,CAAC;AACjD,MAAMI,6BAA6B,GAAG,CAAC,YAAY,EAAE,cAAc,EAAE,WAAW,CAAC;;;;;;;;;;;;;;;;;;;;;ACjEjF;AACA;AACA;AAC6G;AAC7B;AAC8B;AACnD;AACqB;AACnB;AAE7D,MAAMsB,cAAc,GAAG,6mNAA6mN;AAEpoN,MAAMC,aAAa,GAAG,giOAAgiO;AAEtjO,MAAMC,QAAQ,GAAG,MAAM;EACrBC,WAAWA,CAACC,OAAO,EAAE;IACnBtB,qDAAgB,CAAC,IAAI,EAAEsB,OAAO,CAAC;IAC/B,IAAI,CAACC,SAAS,GAAGrB,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACsB,QAAQ,GAAGtB,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACuB,OAAO,GAAGvB,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAACwB,QAAQ,GAAGxB,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACyB,OAAO,GAAI,UAASC,WAAW,EAAG,EAAC;IACxC,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B;IACA;IACA,IAAI,CAACC,2BAA2B,GAAG,KAAK;IACxC;AACJ;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,UAAU,GAAIC,KAAK,IAAK;MAC3B,MAAMC,SAAS,GAAI,IAAI,CAACC,OAAO,GAAGF,KAAM;MACxC,IAAI,CAACT,SAAS,CAACY,IAAI,CAAC;QAClBD,OAAO,EAAED,SAAS;QAClBG,KAAK,EAAE,IAAI,CAACA;MACd,CAAC,CAAC;IACJ,CAAC;IACD,IAAI,CAACC,aAAa,GAAIC,EAAE,IAAK;MAC3BA,EAAE,CAACC,cAAc,CAAC,CAAC;MACnB,IAAI,CAACC,QAAQ,CAAC,CAAC;MACf,IAAI,CAACT,UAAU,CAAC,CAAC,IAAI,CAACG,OAAO,CAAC;MAC9B,IAAI,CAACO,aAAa,GAAG,KAAK;IAC5B,CAAC;IACD,IAAI,CAACC,OAAO,GAAG,MAAM;MACnB,IAAI,CAAClB,QAAQ,CAACW,IAAI,CAAC,CAAC;IACtB,CAAC;IACD,IAAI,CAACQ,MAAM,GAAG,MAAM;MAClB,IAAI,CAAClB,OAAO,CAACU,IAAI,CAAC,CAAC;IACrB,CAAC;IACD,IAAI,CAACS,OAAO,GAAIN,EAAE,IAAK;MACrB,IAAI,CAACD,aAAa,CAACC,EAAE,CAAC;IACxB,CAAC;IACD,IAAI,CAACO,KAAK,GAAG/D,SAAS;IACtB,IAAI,CAACgE,IAAI,GAAG,IAAI,CAACnB,OAAO;IACxB,IAAI,CAACO,OAAO,GAAG,KAAK;IACpB,IAAI,CAACO,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACM,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACX,KAAK,GAAG,IAAI;IACjB,IAAI,CAACY,cAAc,GAAG,OAAO;IAC7B,IAAI,CAACC,OAAO,GAAG,eAAe;IAC9B,IAAI,CAACC,SAAS,GAAG,QAAQ;IACzB,IAAI,CAAC5D,MAAM,GAAGR,SAAS;EACzB;EACAqE,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAACC,oBAAoB,GAAG3E,+DAA0B,CAAC,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC;EACnE;;EACA2E,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAACC,SAAS,CAAC,CAAC;IAChB;IACA,IAAI,CAAC,IAAI,CAACF,oBAAoB,CAACvE,gBAAgB,CAAC,CAAC,EAAE;MACjD,IAAI,CAACgD,mBAAmB,GAAG0B,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEhD,uDAAqB,CAAC,IAAI,CAAC9B,EAAE,CAAC,CAAC;IAC9E;EACF;EACA+E,YAAYA,CAAA,EAAG;IACb,IAAI,CAACH,SAAS,CAAC,CAAC;EAClB;EACAA,SAASA,CAAA,EAAG;IACV,MAAMI,KAAK,GAAG;MACZ,sBAAsB,EAAE,IAAI,CAACX;IAC/B,CAAC;IACD;IACA,IAAI,IAAI,CAACK,oBAAoB,CAACvE,gBAAgB,CAAC,CAAC,EAAE;MAChD6E,KAAK,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAACxB,OAAO;IAC1C;IACA,IAAI,CAACR,QAAQ,CAACS,IAAI,CAACuB,KAAK,CAAC;EAC3B;EACAlB,QAAQA,CAAA,EAAG;IACT,IAAI,IAAI,CAACmB,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACC,KAAK,CAAC,CAAC;IACtB;EACF;EACA;EACAC,MAAMA,CAAA,EAAG;IACP,MAAM;MAAET;IAAqB,CAAC,GAAG,IAAI;IACrC,OAAOA,oBAAoB,CAACvE,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAACiF,oBAAoB,CAAC,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;EACtG;EACAA,cAAcA,CAAA,EAAG;IACf,MAAM;MAAElB,KAAK;MAAEX,OAAO;MAAEa,QAAQ;MAAErE,EAAE;MAAEsF,UAAU;MAAEvB,aAAa;MAAEZ,mBAAmB;MAAEF,OAAO;MAAEsB,OAAO;MAAED,cAAc;MAAEF,IAAI;MAAEV,KAAK;MAAEc;IAAW,CAAC,GAAG,IAAI;IACxJ,MAAMe,IAAI,GAAGhD,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAMiD,IAAI,GAAGF,UAAU,CAACC,IAAI,EAAExB,aAAa,CAAC;IAC5ChC,uDAAiB,CAAC,IAAI,EAAE/B,EAAE,EAAEoE,IAAI,EAAEZ,OAAO,GAAGE,KAAK,GAAG,EAAE,EAAEW,QAAQ,CAAC;IACjE,OAAQxE,qDAAC,CAAC6B,iDAAI,EAAE;MAAE+D,KAAK,EAAErD,qDAAkB,CAAC+B,KAAK,EAAE;QAC/C,CAACoB,IAAI,GAAG,IAAI;QACZ,SAAS,EAAElD,qDAAW,CAAC,UAAU,EAAErC,EAAE,CAAC;QACtC,kBAAkB,EAAEwD,OAAO;QAC3B,mBAAmB,EAAEa,QAAQ;QAC7B,wBAAwB,EAAEN,aAAa;QACvC2B,WAAW,EAAE,IAAI;QACjB,CAAE,oBAAmBnB,OAAQ,EAAC,GAAG,IAAI;QACrC,CAAE,sBAAqBC,SAAU,EAAC,GAAG,IAAI;QACzC,CAAE,4BAA2BF,cAAe,EAAC,GAAG;MAClD,CAAC,CAAC;MAAEJ,OAAO,EAAE,IAAI,CAACA;IAAQ,CAAC,EAAErE,qDAAC,CAAC,OAAO,EAAE;MAAE4F,KAAK,EAAE;IAAmB,CAAC,EAAE5F,qDAAC,CAAC,OAAO,EAAEgF,MAAM,CAACC,MAAM,CAAC;MAAEa,IAAI,EAAE,UAAU;MAAEnC,OAAO,EAAEA,OAAO,GAAG,IAAI,GAAGpD,SAAS;MAAEiE,QAAQ,EAAEA,QAAQ;MAAEuB,EAAE,EAAE3C,OAAO;MAAE4C,QAAQ,EAAE,IAAI,CAAClC,aAAa;MAAEK,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACA,OAAO,CAAC,CAAC;MAAEC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACA,MAAM,CAAC,CAAC;MAAE6B,GAAG,EAAGb,OAAO,IAAM,IAAI,CAACA,OAAO,GAAGA;IAAS,CAAC,EAAE9B,mBAAmB,CAAC,CAAC,EAAEtD,qDAAC,CAAC,KAAK,EAAE;MAAE4F,KAAK,EAAE;QACzW,oBAAoB,EAAE,IAAI;QAC1B,2BAA2B,EAAEzF,EAAE,CAACmB,WAAW,KAAK;MAClD;IAAE,CAAC,EAAEtB,qDAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,EAAEA,qDAAC,CAAC,KAAK,EAAE;MAAE4F,KAAK,EAAE;IAAiB,CAAC,EAAE5F,qDAAC,CAAC,KAAK,EAAE;MAAE4F,KAAK,EAAE,eAAe;MAAEM,OAAO,EAAE,WAAW;MAAEC,IAAI,EAAE;IAAY,CAAC,EAAER,IAAI,CAAC,CAAC,CAAC,CAAC;EACvJ;EACA;EACAJ,oBAAoBA,CAAA,EAAG;IACrB,IAAI,CAAC,IAAI,CAAChC,2BAA2B,EAAE;MACrCjB,qDAAe,CAAE;AACvB;AACA;AACA;AACA;AACA,gNAAgN,EAAE,IAAI,CAACnC,EAAE,CAAC;MACpN,IAAI,IAAI,CAACY,MAAM,EAAE;QACfuB,qDAAe,CAAE;AACzB,wHAAwH,EAAE,IAAI,CAACnC,EAAE,CAAC;MAC5H;MACA,IAAI,CAACoD,2BAA2B,GAAG,IAAI;IACzC;IACA,MAAM;MAAEe,KAAK;MAAEX,OAAO;MAAEa,QAAQ;MAAErE,EAAE;MAAEsF,UAAU;MAAEvB,aAAa;MAAEd,OAAO;MAAEmB,IAAI;MAAEV;IAAM,CAAC,GAAG,IAAI;IAC9F,MAAM6B,IAAI,GAAGhD,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM;MAAEjC,KAAK;MAAE2F,OAAO;MAAEC;IAAU,CAAC,GAAGjE,uDAAY,CAACjC,EAAE,EAAEiD,OAAO,CAAC;IAC/D,MAAMuC,IAAI,GAAGF,UAAU,CAACC,IAAI,EAAExB,aAAa,CAAC;IAC5ChC,uDAAiB,CAAC,IAAI,EAAE/B,EAAE,EAAEoE,IAAI,EAAEZ,OAAO,GAAGE,KAAK,GAAG,EAAE,EAAEW,QAAQ,CAAC;IACjE,OAAQxE,qDAAC,CAAC6B,iDAAI,EAAE;MAAE,iBAAiB,EAAEpB,KAAK,GAAG2F,OAAO,GAAG,IAAI;MAAE,cAAc,EAAG,GAAEzC,OAAQ,EAAC;MAAE,aAAa,EAAEa,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAE8B,IAAI,EAAE,UAAU;MAAEV,KAAK,EAAErD,qDAAkB,CAAC+B,KAAK,EAAE;QACnL,CAACoB,IAAI,GAAG,IAAI;QACZ,SAAS,EAAElD,qDAAW,CAAC,UAAU,EAAErC,EAAE,CAAC;QACtC,kBAAkB,EAAEwD,OAAO;QAC3B,mBAAmB,EAAEa,QAAQ;QAC7B,wBAAwB,EAAEN,aAAa;QACvC,iBAAiB,EAAE,IAAI;QACvB2B,WAAW,EAAE;MACf,CAAC,CAAC;MAAExB,OAAO,EAAE,IAAI,CAACA;IAAQ,CAAC,EAAErE,qDAAC,CAAC,KAAK,EAAE;MAAE4F,KAAK,EAAE,eAAe;MAAEM,OAAO,EAAE,WAAW;MAAEC,IAAI,EAAE;IAAY,CAAC,EAAER,IAAI,CAAC,EAAE3F,qDAAC,CAAC,OAAO,EAAE;MAAEuG,OAAO,EAAEnD;IAAQ,CAAC,EAAEiD,SAAS,CAAC,EAAErG,qDAAC,CAAC,OAAO,EAAE;MAAE8F,IAAI,EAAE,UAAU;MAAE,cAAc,EAAG,GAAEnC,OAAQ,EAAC;MAAEa,QAAQ,EAAEA,QAAQ;MAAEuB,EAAE,EAAE3C,OAAO;MAAE4C,QAAQ,EAAE,IAAI,CAAClC,aAAa;MAAEK,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACA,OAAO,CAAC,CAAC;MAAEC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACA,MAAM,CAAC,CAAC;MAAE6B,GAAG,EAAGb,OAAO,IAAM,IAAI,CAACA,OAAO,GAAGA;IAAS,CAAC,CAAC,CAAC;EACzY;EACAK,UAAUA,CAACC,IAAI,EAAExB,aAAa,EAAE;IAC9B,IAAIyB,IAAI,GAAGzB,aAAa,GAAIlE,qDAAC,CAAC,MAAM,EAAE;MAAE0B,CAAC,EAAE,aAAa;MAAEyE,IAAI,EAAE;IAAO,CAAC,CAAC,GAAKnG,qDAAC,CAAC,MAAM,EAAE;MAAE0B,CAAC,EAAE,2BAA2B;MAAEyE,IAAI,EAAE;IAAO,CAAC,CAAE;IAC1I,IAAIT,IAAI,KAAK,IAAI,EAAE;MACjBC,IAAI,GAAGzB,aAAa,GAAIlE,qDAAC,CAAC,MAAM,EAAE;QAAE0B,CAAC,EAAE,UAAU;QAAEyE,IAAI,EAAE;MAAO,CAAC,CAAC,GAAKnG,qDAAC,CAAC,MAAM,EAAE;QAAE0B,CAAC,EAAE,kCAAkC;QAAEyE,IAAI,EAAE;MAAO,CAAC,CAAE;IAC5I;IACA,OAAOR,IAAI;EACb;EACA,IAAIxF,EAAEA,CAAA,EAAG;IAAE,OAAO4B,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWyE,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC7B,SAAS,EAAE,CAAC,cAAc,CAAC;MAC3B,UAAU,EAAE,CAAC,cAAc;IAC7B,CAAC;EAAE;AACL,CAAC;AACD,IAAInD,WAAW,GAAG,CAAC;AACnBR,QAAQ,CAACsC,KAAK,GAAG;EACfsB,GAAG,EAAE9D,cAAc;EACnB+D,EAAE,EAAE9D;AACN,CAAC;;;;;;;;;;;;;;;;;;;;ACrKD;AACA;AACA;AACA,MAAMJ,WAAW,GAAGA,CAACoE,QAAQ,EAAEzG,EAAE,KAAK;EACpC,OAAOA,EAAE,CAAC0G,OAAO,CAACD,QAAQ,CAAC,KAAK,IAAI;AACtC,CAAC;AACD;AACA;AACA;AACA,MAAMrE,kBAAkB,GAAGA,CAAC+B,KAAK,EAAEwC,WAAW,KAAK;EACjD,OAAO,OAAOxC,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACyC,MAAM,GAAG,CAAC,GAChD/B,MAAM,CAACC,MAAM,CAAC;IAAE,WAAW,EAAE,IAAI;IAAE,CAAE,aAAYX,KAAM,EAAC,GAAG;EAAK,CAAC,EAAEwC,WAAW,CAAC,GAAGA,WAAW;AACnG,CAAC;AACD,MAAME,YAAY,GAAIC,OAAO,IAAK;EAChC,IAAIA,OAAO,KAAK1G,SAAS,EAAE;IACzB,MAAM2G,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACH,OAAO,CAAC,GAAGA,OAAO,GAAGA,OAAO,CAACI,KAAK,CAAC,GAAG,CAAC;IACnE,OAAOH,KAAK,CACTI,MAAM,CAAE/F,CAAC,IAAKA,CAAC,IAAI,IAAI,CAAC,CACxBgG,GAAG,CAAEhG,CAAC,IAAKA,CAAC,CAACiG,IAAI,CAAC,CAAC,CAAC,CACpBF,MAAM,CAAE/F,CAAC,IAAKA,CAAC,KAAK,EAAE,CAAC;EAC5B;EACA,OAAO,EAAE;AACX,CAAC;AACD,MAAMkG,WAAW,GAAIR,OAAO,IAAK;EAC/B,MAAMM,GAAG,GAAG,CAAC,CAAC;EACdP,YAAY,CAACC,OAAO,CAAC,CAACS,OAAO,CAAEnG,CAAC,IAAMgG,GAAG,CAAChG,CAAC,CAAC,GAAG,IAAK,CAAC;EACrD,OAAOgG,GAAG;AACZ,CAAC;AACD,MAAMI,MAAM,GAAG,sBAAsB;AACrC,MAAMC,OAAO;EAAA,IAAAC,IAAA,GAAAC,8KAAA,CAAG,WAAOC,GAAG,EAAEhE,EAAE,EAAEiE,SAAS,EAAEC,SAAS,EAAK;IACvD,IAAIF,GAAG,IAAI,IAAI,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAACJ,MAAM,CAACO,IAAI,CAACH,GAAG,CAAC,EAAE;MACtD,MAAMI,MAAM,GAAGC,QAAQ,CAAChH,aAAa,CAAC,YAAY,CAAC;MACnD,IAAI+G,MAAM,EAAE;QACV,IAAIpE,EAAE,IAAI,IAAI,EAAE;UACdA,EAAE,CAACC,cAAc,CAAC,CAAC;QACrB;QACA,OAAOmE,MAAM,CAACE,IAAI,CAACN,GAAG,EAAEC,SAAS,EAAEC,SAAS,CAAC;MAC/C;IACF;IACA,OAAO,KAAK;EACd,CAAC;EAAA,gBAXKL,OAAOA,CAAAU,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAZ,IAAA,CAAAa,KAAA,OAAAC,SAAA;EAAA;AAAA,GAWZ", "sources": ["./node_modules/@ionic/core/dist/esm/form-controller-ed77647a.js", "./node_modules/@ionic/core/dist/esm/ion-checkbox.entry.js", "./node_modules/@ionic/core/dist/esm/theme-17531cdf.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { h as findItemLabel } from './helpers-3379ba19.js';\n\n/**\n * Creates a controller that tracks whether a form control is using the legacy or modern syntax. This should be removed when the legacy form control syntax is removed.\n *\n * @internal\n * @prop el: The Ionic form component to reference\n */\nconst createLegacyFormController = (el) => {\n  const controlEl = el;\n  let legacyControl;\n  const hasLegacyControl = () => {\n    if (legacyControl === undefined) {\n      /**\n       * Detect if developers are using the legacy form control syntax\n       * so a deprecation warning is logged. This warning can be disabled\n       * by either using the new `label` property or setting `aria-label`\n       * on the control.\n       * Alternatively, components that use a slot for the label\n       * can check to see if the component has slotted text\n       * in the light DOM.\n       */\n      const hasLabelProp = controlEl.label !== undefined || hasLabelSlot(controlEl);\n      const hasAriaLabelAttribute = controlEl.hasAttribute('aria-label') ||\n        // Shadow DOM form controls cannot use aria-labelledby\n        (controlEl.hasAttribute('aria-labelledby') && controlEl.shadowRoot === null);\n      const legacyItemLabel = findItemLabel(controlEl);\n      /**\n       * Developers can manually opt-out of the modern form markup\n       * by setting `legacy=\"true\"` on components.\n       */\n      legacyControl =\n        controlEl.legacy === true || (!hasLabelProp && !hasAriaLabelAttribute && legacyItemLabel !== null);\n    }\n    return legacyControl;\n  };\n  return { hasLegacyControl };\n};\nconst hasLabelSlot = (controlEl) => {\n  const root = controlEl.shadowRoot;\n  if (root === null) {\n    return false;\n  }\n  /**\n   * Components that have a named label slot\n   * also have other slots, so we need to query for\n   * anything that is explicitly passed to slot=\"label\"\n   */\n  if (NAMED_LABEL_SLOT_COMPONENTS.includes(controlEl.tagName) && controlEl.querySelector('[slot=\"label\"]') !== null) {\n    return true;\n  }\n  /**\n   * Components that have an unnamed slot for the label\n   * have no other slots, so we can check the textContent\n   * of the element.\n   */\n  if (UNNAMED_LABEL_SLOT_COMPONENTS.includes(controlEl.tagName) && controlEl.textContent !== '') {\n    return true;\n  }\n  return false;\n};\nconst NAMED_LABEL_SLOT_COMPONENTS = ['ION-RANGE'];\nconst UNNAMED_LABEL_SLOT_COMPONENTS = ['ION-TOGGLE', 'ION-CHECKBOX', 'ION-RADIO'];\n\nexport { createLegacyFormController as c };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement } from './index-2d388930.js';\nimport { c as createLegacyFormController } from './form-controller-ed77647a.js';\nimport { i as inheritAriaAttributes, d as renderHiddenInput, e as getAriaLabel } from './helpers-3379ba19.js';\nimport { p as printIonWarning } from './index-595d62c9.js';\nimport { c as createColorClasses, h as hostContext } from './theme-17531cdf.js';\nimport { b as getIonMode } from './ionic-global-b3fc28dd.js';\n\nconst checkboxIosCss = \":host{--checkbox-background-checked:var(--ion-color-primary, #3880ff);--border-color-checked:var(--ion-color-primary, #3880ff);--checkmark-color:var(--ion-color-primary-contrast, #fff);--checkmark-width:1;--transition:none;display:inline-block;position:relative;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item){width:100%;height:100%}:host([slot=start]:not(.legacy-checkbox)),:host([slot=end]:not(.legacy-checkbox)){width:auto}:host(.legacy-checkbox){width:var(--size);height:var(--size)}:host(.ion-color){--checkbox-background-checked:var(--ion-color-base);--border-color-checked:var(--ion-color-base);--checkmark-color:var(--ion-color-contrast)}:host(.legacy-checkbox) label{top:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;position:absolute;width:100%;height:100%;border:0;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:none;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;opacity:0}@supports (inset-inline-start: 0){:host(.legacy-checkbox) label{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host(.legacy-checkbox) label{left:0}:host-context([dir=rtl]):host(.legacy-checkbox) label,:host-context([dir=rtl]).legacy-checkbox label{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(.legacy-checkbox) label:dir(rtl){left:unset;right:unset;right:0}}}:host(.legacy-checkbox) label::-moz-focus-inner{border:0}.checkbox-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit;cursor:inherit}.label-text-wrapper{pointer-events:none;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item:not(.legacy-checkbox)) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.checkbox-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.checkbox-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.checkbox-icon{border-radius:var(--border-radius);position:relative;-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--checkbox-background);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.legacy-checkbox) .checkbox-icon{display:block;width:100%;height:100%}:host(:not(.legacy-checkbox)) .checkbox-icon{width:var(--size);height:var(--size)}.checkbox-icon path{fill:none;stroke:var(--checkmark-color);stroke-width:var(--checkmark-width);opacity:0}:host(.checkbox-justify-space-between) .checkbox-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.checkbox-justify-start) .checkbox-wrapper{-ms-flex-pack:start;justify-content:start}:host(.checkbox-justify-end) .checkbox-wrapper{-ms-flex-pack:end;justify-content:end}:host(.checkbox-alignment-start) .checkbox-wrapper{-ms-flex-align:start;align-items:start}:host(.checkbox-alignment-center) .checkbox-wrapper{-ms-flex-align:center;align-items:center}:host(.checkbox-label-placement-start) .checkbox-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.checkbox-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-end) .checkbox-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.checkbox-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.checkbox-label-placement-stacked) .checkbox-wrapper{-ms-flex-direction:column;flex-direction:column}:host(.checkbox-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper:dir(rtl){-webkit-transform-origin:right top;transform-origin:right top}}:host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper:dir(rtl){-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host(.checkbox-checked) .checkbox-icon,:host(.checkbox-indeterminate) .checkbox-icon{border-color:var(--border-color-checked);background:var(--checkbox-background-checked)}:host(.checkbox-checked) .checkbox-icon path,:host(.checkbox-indeterminate) .checkbox-icon path{opacity:1}:host(.checkbox-disabled){pointer-events:none}:host{--border-radius:50%;--border-width:1px;--border-style:solid;--border-color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.23);--checkbox-background:var(--ion-item-background, var(--ion-background-color, #fff));--size:26px}:host(.checkbox-disabled){opacity:0.3}:host(.in-item.legacy-checkbox){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:10px;margin-bottom:9px;display:block;position:static}:host(.in-item.legacy-checkbox[slot=start]){-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:8px;margin-bottom:8px}\";\n\nconst checkboxMdCss = \":host{--checkbox-background-checked:var(--ion-color-primary, #3880ff);--border-color-checked:var(--ion-color-primary, #3880ff);--checkmark-color:var(--ion-color-primary-contrast, #fff);--checkmark-width:1;--transition:none;display:inline-block;position:relative;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item){width:100%;height:100%}:host([slot=start]:not(.legacy-checkbox)),:host([slot=end]:not(.legacy-checkbox)){width:auto}:host(.legacy-checkbox){width:var(--size);height:var(--size)}:host(.ion-color){--checkbox-background-checked:var(--ion-color-base);--border-color-checked:var(--ion-color-base);--checkmark-color:var(--ion-color-contrast)}:host(.legacy-checkbox) label{top:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;position:absolute;width:100%;height:100%;border:0;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:none;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;opacity:0}@supports (inset-inline-start: 0){:host(.legacy-checkbox) label{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host(.legacy-checkbox) label{left:0}:host-context([dir=rtl]):host(.legacy-checkbox) label,:host-context([dir=rtl]).legacy-checkbox label{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(.legacy-checkbox) label:dir(rtl){left:unset;right:unset;right:0}}}:host(.legacy-checkbox) label::-moz-focus-inner{border:0}.checkbox-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit;cursor:inherit}.label-text-wrapper{pointer-events:none;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item:not(.legacy-checkbox)) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.checkbox-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.checkbox-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.checkbox-icon{border-radius:var(--border-radius);position:relative;-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--checkbox-background);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.legacy-checkbox) .checkbox-icon{display:block;width:100%;height:100%}:host(:not(.legacy-checkbox)) .checkbox-icon{width:var(--size);height:var(--size)}.checkbox-icon path{fill:none;stroke:var(--checkmark-color);stroke-width:var(--checkmark-width);opacity:0}:host(.checkbox-justify-space-between) .checkbox-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.checkbox-justify-start) .checkbox-wrapper{-ms-flex-pack:start;justify-content:start}:host(.checkbox-justify-end) .checkbox-wrapper{-ms-flex-pack:end;justify-content:end}:host(.checkbox-alignment-start) .checkbox-wrapper{-ms-flex-align:start;align-items:start}:host(.checkbox-alignment-center) .checkbox-wrapper{-ms-flex-align:center;align-items:center}:host(.checkbox-label-placement-start) .checkbox-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.checkbox-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-end) .checkbox-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.checkbox-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.checkbox-label-placement-stacked) .checkbox-wrapper{-ms-flex-direction:column;flex-direction:column}:host(.checkbox-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper:dir(rtl){-webkit-transform-origin:right top;transform-origin:right top}}:host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper:dir(rtl){-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host(.checkbox-checked) .checkbox-icon,:host(.checkbox-indeterminate) .checkbox-icon{border-color:var(--border-color-checked);background:var(--checkbox-background-checked)}:host(.checkbox-checked) .checkbox-icon path,:host(.checkbox-indeterminate) .checkbox-icon path{opacity:1}:host(.checkbox-disabled){pointer-events:none}:host{--border-radius:calc(var(--size) * .125);--border-width:2px;--border-style:solid;--border-color:rgb(var(--ion-text-color-rgb, 0, 0, 0), 0.6);--checkmark-width:3;--checkbox-background:var(--ion-item-background, var(--ion-background-color, #fff));--transition:background 180ms cubic-bezier(0.4, 0, 0.2, 1);--size:18px}.checkbox-icon path{stroke-dasharray:30;stroke-dashoffset:30}:host(.checkbox-checked) .checkbox-icon path,:host(.checkbox-indeterminate) .checkbox-icon path{stroke-dashoffset:0;-webkit-transition:stroke-dashoffset 90ms linear 90ms;transition:stroke-dashoffset 90ms linear 90ms}:host(.legacy-checkbox.checkbox-disabled),:host(.checkbox-disabled) .label-text-wrapper{opacity:0.38}:host(.checkbox-disabled) .native-wrapper{opacity:0.63}:host(.in-item.legacy-checkbox){margin-left:0;margin-right:0;margin-top:18px;margin-bottom:18px;display:block;position:static}:host(.in-item.legacy-checkbox[slot=start]){-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:36px;margin-inline-end:36px;margin-top:18px;margin-bottom:18px}\";\n\nconst Checkbox = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.ionStyle = createEvent(this, \"ionStyle\", 7);\n    this.inputId = `ion-cb-${checkboxIds++}`;\n    this.inheritedAttributes = {};\n    // TODO(FW-3100): remove this\n    // This flag ensures we log the deprecation warning at most once.\n    this.hasLoggedDeprecationWarning = false;\n    /**\n     * Sets the checked property and emits\n     * the ionChange event. Use this to update the\n     * checked state in response to user-generated\n     * actions such as a click.\n     */\n    this.setChecked = (state) => {\n      const isChecked = (this.checked = state);\n      this.ionChange.emit({\n        checked: isChecked,\n        value: this.value,\n      });\n    };\n    this.toggleChecked = (ev) => {\n      ev.preventDefault();\n      this.setFocus();\n      this.setChecked(!this.checked);\n      this.indeterminate = false;\n    };\n    this.onFocus = () => {\n      this.ionFocus.emit();\n    };\n    this.onBlur = () => {\n      this.ionBlur.emit();\n    };\n    this.onClick = (ev) => {\n      this.toggleChecked(ev);\n    };\n    this.color = undefined;\n    this.name = this.inputId;\n    this.checked = false;\n    this.indeterminate = false;\n    this.disabled = false;\n    this.value = 'on';\n    this.labelPlacement = 'start';\n    this.justify = 'space-between';\n    this.alignment = 'center';\n    this.legacy = undefined;\n  }\n  connectedCallback() {\n    this.legacyFormController = createLegacyFormController(this.el); // TODO(FW-3100): remove this\n  }\n  componentWillLoad() {\n    this.emitStyle();\n    // TODO(FW-3100): remove check\n    if (!this.legacyFormController.hasLegacyControl()) {\n      this.inheritedAttributes = Object.assign({}, inheritAriaAttributes(this.el));\n    }\n  }\n  styleChanged() {\n    this.emitStyle();\n  }\n  emitStyle() {\n    const style = {\n      'interactive-disabled': this.disabled,\n    };\n    // TODO(FW-3100): remove this\n    if (this.legacyFormController.hasLegacyControl()) {\n      style['checkbox-checked'] = this.checked;\n    }\n    this.ionStyle.emit(style);\n  }\n  setFocus() {\n    if (this.focusEl) {\n      this.focusEl.focus();\n    }\n  }\n  // TODO(FW-3100): run contents of renderCheckbox directly instead\n  render() {\n    const { legacyFormController } = this;\n    return legacyFormController.hasLegacyControl() ? this.renderLegacyCheckbox() : this.renderCheckbox();\n  }\n  renderCheckbox() {\n    const { color, checked, disabled, el, getSVGPath, indeterminate, inheritedAttributes, inputId, justify, labelPlacement, name, value, alignment, } = this;\n    const mode = getIonMode(this);\n    const path = getSVGPath(mode, indeterminate);\n    renderHiddenInput(true, el, name, checked ? value : '', disabled);\n    return (h(Host, { class: createColorClasses(color, {\n        [mode]: true,\n        'in-item': hostContext('ion-item', el),\n        'checkbox-checked': checked,\n        'checkbox-disabled': disabled,\n        'checkbox-indeterminate': indeterminate,\n        interactive: true,\n        [`checkbox-justify-${justify}`]: true,\n        [`checkbox-alignment-${alignment}`]: true,\n        [`checkbox-label-placement-${labelPlacement}`]: true,\n      }), onClick: this.onClick }, h(\"label\", { class: \"checkbox-wrapper\" }, h(\"input\", Object.assign({ type: \"checkbox\", checked: checked ? true : undefined, disabled: disabled, id: inputId, onChange: this.toggleChecked, onFocus: () => this.onFocus(), onBlur: () => this.onBlur(), ref: (focusEl) => (this.focusEl = focusEl) }, inheritedAttributes)), h(\"div\", { class: {\n        'label-text-wrapper': true,\n        'label-text-wrapper-hidden': el.textContent === '',\n      } }, h(\"slot\", null)), h(\"div\", { class: \"native-wrapper\" }, h(\"svg\", { class: \"checkbox-icon\", viewBox: \"0 0 24 24\", part: \"container\" }, path)))));\n  }\n  // TODO(FW-3100): remove this\n  renderLegacyCheckbox() {\n    if (!this.hasLoggedDeprecationWarning) {\n      printIonWarning(`ion-checkbox now requires providing a label with either the default slot or the \"aria-label\" attribute. To migrate, remove any usage of \"ion-label\" and pass the label text to either the component or the \"aria-label\" attribute.\n\nExample: <ion-checkbox>Label</ion-checkbox>\nExample with aria-label: <ion-checkbox aria-label=\"Label\"></ion-checkbox>\n\nDevelopers can use the \"legacy\" property to continue using the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.`, this.el);\n      if (this.legacy) {\n        printIonWarning(`ion-checkbox is being used with the \"legacy\" property enabled which will forcibly enable the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.\nDevelopers can dismiss this warning by removing their usage of the \"legacy\" property and using the new checkbox syntax.`, this.el);\n      }\n      this.hasLoggedDeprecationWarning = true;\n    }\n    const { color, checked, disabled, el, getSVGPath, indeterminate, inputId, name, value } = this;\n    const mode = getIonMode(this);\n    const { label, labelId, labelText } = getAriaLabel(el, inputId);\n    const path = getSVGPath(mode, indeterminate);\n    renderHiddenInput(true, el, name, checked ? value : '', disabled);\n    return (h(Host, { \"aria-labelledby\": label ? labelId : null, \"aria-checked\": `${checked}`, \"aria-hidden\": disabled ? 'true' : null, role: \"checkbox\", class: createColorClasses(color, {\n        [mode]: true,\n        'in-item': hostContext('ion-item', el),\n        'checkbox-checked': checked,\n        'checkbox-disabled': disabled,\n        'checkbox-indeterminate': indeterminate,\n        'legacy-checkbox': true,\n        interactive: true,\n      }), onClick: this.onClick }, h(\"svg\", { class: \"checkbox-icon\", viewBox: \"0 0 24 24\", part: \"container\" }, path), h(\"label\", { htmlFor: inputId }, labelText), h(\"input\", { type: \"checkbox\", \"aria-checked\": `${checked}`, disabled: disabled, id: inputId, onChange: this.toggleChecked, onFocus: () => this.onFocus(), onBlur: () => this.onBlur(), ref: (focusEl) => (this.focusEl = focusEl) })));\n  }\n  getSVGPath(mode, indeterminate) {\n    let path = indeterminate ? (h(\"path\", { d: \"M6 12L18 12\", part: \"mark\" })) : (h(\"path\", { d: \"M5.9,12.5l3.8,3.8l8.8-8.8\", part: \"mark\" }));\n    if (mode === 'md') {\n      path = indeterminate ? (h(\"path\", { d: \"M2 12H22\", part: \"mark\" })) : (h(\"path\", { d: \"M1.73,12.91 8.1,19.28 22.79,4.59\", part: \"mark\" }));\n    }\n    return path;\n  }\n  get el() { return getElement(this); }\n  static get watchers() { return {\n    \"checked\": [\"styleChanged\"],\n    \"disabled\": [\"styleChanged\"]\n  }; }\n};\nlet checkboxIds = 0;\nCheckbox.style = {\n  ios: checkboxIosCss,\n  md: checkboxMdCss\n};\n\nexport { Checkbox as ion_checkbox };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst hostContext = (selector, el) => {\n  return el.closest(selector) !== null;\n};\n/**\n * Create the mode and color classes for the component based on the classes passed in\n */\nconst createColorClasses = (color, cssClassMap) => {\n  return typeof color === 'string' && color.length > 0\n    ? Object.assign({ 'ion-color': true, [`ion-color-${color}`]: true }, cssClassMap) : cssClassMap;\n};\nconst getClassList = (classes) => {\n  if (classes !== undefined) {\n    const array = Array.isArray(classes) ? classes : classes.split(' ');\n    return array\n      .filter((c) => c != null)\n      .map((c) => c.trim())\n      .filter((c) => c !== '');\n  }\n  return [];\n};\nconst getClassMap = (classes) => {\n  const map = {};\n  getClassList(classes).forEach((c) => (map[c] = true));\n  return map;\n};\nconst SCHEME = /^[a-z][a-z0-9+\\-.]*:/;\nconst openURL = async (url, ev, direction, animation) => {\n  if (url != null && url[0] !== '#' && !SCHEME.test(url)) {\n    const router = document.querySelector('ion-router');\n    if (router) {\n      if (ev != null) {\n        ev.preventDefault();\n      }\n      return router.push(url, direction, animation);\n    }\n  }\n  return false;\n};\n\nexport { createColorClasses as c, getClassMap as g, hostContext as h, openURL as o };\n"], "names": ["h", "findItemLabel", "createLegacyFormController", "el", "controlEl", "legacyControl", "hasLegacyControl", "undefined", "hasLabelProp", "label", "hasLabelSlot", "hasAriaLabelAttribute", "hasAttribute", "shadowRoot", "legacyItemLabel", "legacy", "root", "NAMED_LABEL_SLOT_COMPONENTS", "includes", "tagName", "querySelector", "UNNAMED_LABEL_SLOT_COMPONENTS", "textContent", "c", "r", "registerInstance", "d", "createEvent", "H", "Host", "f", "getElement", "i", "inheritAriaAttributes", "renderHiddenInput", "e", "getAriaLabel", "p", "printIonWarning", "createColorClasses", "hostContext", "b", "getIonMode", "checkboxIosCss", "checkboxMdCss", "Checkbox", "constructor", "hostRef", "ionChange", "ionFocus", "ionBlur", "ionStyle", "inputId", "checkboxIds", "inheritedAttributes", "hasLoggedDeprecationWarning", "setChecked", "state", "isChecked", "checked", "emit", "value", "toggleChecked", "ev", "preventDefault", "setFocus", "indeterminate", "onFocus", "onBlur", "onClick", "color", "name", "disabled", "labelPlacement", "justify", "alignment", "connectedCallback", "legacyFormController", "componentWillLoad", "emitStyle", "Object", "assign", "styleChanged", "style", "focusEl", "focus", "render", "renderLegacyCheckbox", "renderCheckbox", "getSV<PERSON>ath", "mode", "path", "class", "interactive", "type", "id", "onChange", "ref", "viewBox", "part", "labelId", "labelText", "role", "htmlFor", "watchers", "ios", "md", "ion_checkbox", "selector", "closest", "cssClassMap", "length", "getClassList", "classes", "array", "Array", "isArray", "split", "filter", "map", "trim", "getClassMap", "for<PERSON>ach", "SCHEME", "openURL", "_ref", "_asyncToGenerator", "url", "direction", "animation", "test", "router", "document", "push", "_x", "_x2", "_x3", "_x4", "apply", "arguments", "g", "o"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0, 1, 2]}