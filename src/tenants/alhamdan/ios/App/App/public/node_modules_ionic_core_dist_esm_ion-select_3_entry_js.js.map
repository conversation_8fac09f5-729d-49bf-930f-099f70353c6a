{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-select_3_entry_js.js", "mappings": ";;;;;;;;;;;;;;AAAA;AACA;AACA;AAC2D;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,0BAA0B,GAAIC,EAAE,IAAK;EACzC,MAAMC,SAAS,GAAGD,EAAE;EACpB,IAAIE,aAAa;EACjB,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAID,aAAa,KAAKE,SAAS,EAAE;MAC/B;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACM,MAAMC,YAAY,GAAGJ,SAAS,CAACK,KAAK,KAAKF,SAAS,IAAIG,YAAY,CAACN,SAAS,CAAC;MAC7E,MAAMO,qBAAqB,GAAGP,SAAS,CAACQ,YAAY,CAAC,YAAY,CAAC;MAChE;MACCR,SAAS,CAACQ,YAAY,CAAC,iBAAiB,CAAC,IAAIR,SAAS,CAACS,UAAU,KAAK,IAAK;MAC9E,MAAMC,eAAe,GAAGb,uDAAa,CAACG,SAAS,CAAC;MAChD;AACN;AACA;AACA;MACMC,aAAa,GACXD,SAAS,CAACW,MAAM,KAAK,IAAI,IAAK,CAACP,YAAY,IAAI,CAACG,qBAAqB,IAAIG,eAAe,KAAK,IAAK;IACtG;IACA,OAAOT,aAAa;EACtB,CAAC;EACD,OAAO;IAAEC;EAAiB,CAAC;AAC7B,CAAC;AACD,MAAMI,YAAY,GAAIN,SAAS,IAAK;EAClC,MAAMY,IAAI,GAAGZ,SAAS,CAACS,UAAU;EACjC,IAAIG,IAAI,KAAK,IAAI,EAAE;IACjB,OAAO,KAAK;EACd;EACA;AACF;AACA;AACA;AACA;EACE,IAAIC,2BAA2B,CAACC,QAAQ,CAACd,SAAS,CAACe,OAAO,CAAC,IAAIf,SAAS,CAACgB,aAAa,CAAC,gBAAgB,CAAC,KAAK,IAAI,EAAE;IACjH,OAAO,IAAI;EACb;EACA;AACF;AACA;AACA;AACA;EACE,IAAIC,6BAA6B,CAACH,QAAQ,CAACd,SAAS,CAACe,OAAO,CAAC,IAAIf,SAAS,CAACkB,WAAW,KAAK,EAAE,EAAE;IAC7F,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC;AACD,MAAML,2BAA2B,GAAG,CAAC,WAAW,CAAC;AACjD,MAAMI,6BAA6B,GAAG,CAAC,YAAY,EAAE,cAAc,EAAE,WAAW,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjEjF;AACA;AACA;AAC+H;AAC/C;AACJ;AACqE;AACtF;AACsE;AAClF;AACmD;AAC/B;AACW;AACjB;AAChC;AACa;AACE;AAE5C,MAAMwC,YAAY,GAAG,uxQAAuxQ;AAE5yQ,MAAMC,WAAW,GAAG,0hkBAA0hkB;AAE9ikB,MAAMC,MAAM,GAAG,MAAM;EACnBC,WAAWA,CAACC,OAAO,EAAE;IACnBxC,qDAAgB,CAAC,IAAI,EAAEwC,OAAO,CAAC;IAC/B,IAAI,CAACC,SAAS,GAAGvC,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACwC,SAAS,GAAGxC,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACyC,UAAU,GAAGzC,qDAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IACpD,IAAI,CAAC0C,QAAQ,GAAG1C,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC2C,OAAO,GAAG3C,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAAC4C,QAAQ,GAAG5C,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC6C,OAAO,GAAI,WAAUC,SAAS,EAAG,EAAC;IACvC,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B;IACA,IAAI,CAACC,2BAA2B,GAAG,KAAK;IACxC,IAAI,CAACC,OAAO,GAAIC,EAAE,IAAK;MACrB,IAAI,CAACC,QAAQ,CAAC,CAAC;MACf,IAAI,CAACC,IAAI,CAACF,EAAE,CAAC;IACf,CAAC;IACD,IAAI,CAACG,OAAO,GAAG,MAAM;MACnB,IAAI,CAACX,QAAQ,CAACY,IAAI,CAAC,CAAC;IACtB,CAAC;IACD,IAAI,CAACC,MAAM,GAAG,MAAM;MAClB,IAAI,CAACZ,OAAO,CAACW,IAAI,CAAC,CAAC;IACrB,CAAC;IACD,IAAI,CAACE,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,UAAU,GAAG,QAAQ;IAC1B,IAAI,CAACC,KAAK,GAAG9E,SAAS;IACtB,IAAI,CAAC+E,WAAW,GAAG/E,SAAS;IAC5B,IAAI,CAACgF,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,IAAI,GAAGjF,SAAS;IACrB,IAAI,CAACkF,SAAS,GAAG,OAAO;IACxB,IAAI,CAACC,gBAAgB,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,OAAO,GAAG,eAAe;IAC9B,IAAI,CAAClF,KAAK,GAAGF,SAAS;IACtB,IAAI,CAACqF,cAAc,GAAG,OAAO;IAC7B,IAAI,CAAC7E,MAAM,GAAGR,SAAS;IACvB,IAAI,CAACsF,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,IAAI,GAAG,IAAI,CAACtB,OAAO;IACxB,IAAI,CAACuB,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,WAAW,GAAGzF,SAAS;IAC5B,IAAI,CAAC0F,YAAY,GAAG1F,SAAS;IAC7B,IAAI,CAAC2F,UAAU,GAAG3F,SAAS;IAC3B,IAAI,CAAC4F,YAAY,GAAG5F,SAAS;IAC7B,IAAI,CAAC6F,KAAK,GAAG7F,SAAS;IACtB,IAAI,CAAC8F,KAAK,GAAG9F,SAAS;EACxB;EACA+F,YAAYA,CAAA,EAAG;IACb,IAAI,CAACC,SAAS,CAAC,CAAC;EAClB;EACAC,QAAQA,CAACH,KAAK,EAAE;IACd,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACnC,SAAS,CAACe,IAAI,CAAC;MAAEoB;IAAM,CAAC,CAAC;EAChC;EACAI,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAAC/B,mBAAmB,GAAGtC,uDAAiB,CAAC,IAAI,CAACjC,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC;EACvE;EACMuG,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,8KAAA;MACxB,MAAM;QAAEzG;MAAG,CAAC,GAAGwG,KAAI;MACnBA,KAAI,CAACE,oBAAoB,GAAG3G,+DAA0B,CAACC,EAAE,CAAC;MAC1DwG,KAAI,CAACG,eAAe,GAAG5E,gEAAqB,CAAC/B,EAAE,EAAE,MAAMwG,KAAI,CAACI,aAAa,EAAE,MAAMJ,KAAI,CAACK,SAAS,CAAC;MAChGL,KAAI,CAACM,oBAAoB,CAAC,CAAC;MAC3BN,KAAI,CAACJ,SAAS,CAAC,CAAC;MAChBI,KAAI,CAACO,SAAS,GAAG1D,6DAAe,CAACmD,KAAI,CAACxG,EAAE,EAAE,mBAAmB,eAAAyG,8KAAA,CAAE,aAAY;QACzED,KAAI,CAACM,oBAAoB,CAAC,CAAC;QAC3B;AACN;AACA;AACA;AACA;AACA;QACMhF,qDAAW,CAAC0E,KAAI,CAAC;MACnB,CAAC,EAAC;IAAC;EACL;EACAQ,oBAAoBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACD,SAAS,EAAE;MAClB,IAAI,CAACA,SAAS,CAACE,UAAU,CAAC,CAAC;MAC3B,IAAI,CAACF,SAAS,GAAG3G,SAAS;IAC5B;IACA,IAAI,IAAI,CAACuG,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACO,OAAO,CAAC,CAAC;MAC9B,IAAI,CAACP,eAAe,GAAGvG,SAAS;IAClC;EACF;EACA;AACF;AACA;AACA;AACA;AACA;EACQwE,IAAIA,CAACuC,KAAK,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAX,8KAAA;MAChB,IAAIW,MAAI,CAAChC,QAAQ,IAAIgC,MAAI,CAACpC,UAAU,EAAE;QACpC,OAAO5E,SAAS;MAClB;MACAgH,MAAI,CAACpC,UAAU,GAAG,IAAI;MACtB,MAAMqC,OAAO,GAAID,MAAI,CAACC,OAAO,SAASD,MAAI,CAACE,aAAa,CAACH,KAAK,CAAE;MAChEE,OAAO,CAACE,YAAY,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QAChCJ,MAAI,CAACC,OAAO,GAAGjH,SAAS;QACxBgH,MAAI,CAACpC,UAAU,GAAG,KAAK;QACvBoC,MAAI,CAACnD,UAAU,CAACa,IAAI,CAAC,CAAC;QACtBsC,MAAI,CAACzC,QAAQ,CAAC,CAAC;MACjB,CAAC,CAAC;MACF,MAAM0C,OAAO,CAACI,OAAO,CAAC,CAAC;MACvB;MACA,IAAIL,MAAI,CAAC9B,SAAS,KAAK,SAAS,EAAE;QAChC,IAAIoC,eAAe,GAAGN,MAAI,CAACO,SAAS,CAACC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAC3B,KAAK,CAAC,CAAC4B,OAAO,CAACV,MAAI,CAAClB,KAAK,CAAC;QAC5EwB,eAAe,GAAGA,eAAe,GAAG,CAAC,CAAC,GAAGA,eAAe,GAAG,CAAC,CAAC,CAAC;QAC9D,MAAMK,YAAY,GAAGV,OAAO,CAACpG,aAAa,CAAE,sCAAqCyG,eAAe,GAAG,CAAE,GAAE,CAAC;QACxG,IAAIK,YAAY,EAAE;UAChB7F,uDAAY,CAAC6F,YAAY,CAAC;UAC1B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACQ,MAAMC,aAAa,GAAGD,YAAY,CAAC9G,aAAa,CAAC,yBAAyB,CAAC;UAC3E,IAAI+G,aAAa,EAAE;YACjBA,aAAa,CAACC,KAAK,CAAC,CAAC;UACvB;QACF;MACF;MACA,OAAOZ,OAAO;IAAC;EACjB;EACAC,aAAaA,CAAC5C,EAAE,EAAE;IAChB,IAAIwD,eAAe,GAAG,IAAI,CAAC5C,SAAS;IACpC,IAAI4C,eAAe,KAAK,cAAc,IAAI,IAAI,CAACxC,QAAQ,EAAE;MACvDyC,OAAO,CAACC,IAAI,CAAE,+BAA8BF,eAAgB,mEAAkE,CAAC;MAC/HA,eAAe,GAAG,OAAO;IAC3B;IACA,IAAIA,eAAe,KAAK,SAAS,IAAI,CAACxD,EAAE,EAAE;MACxCyD,OAAO,CAACC,IAAI,CAAE,iCAAgCF,eAAgB,kEAAiE,CAAC;MAChIA,eAAe,GAAG,OAAO;IAC3B;IACA,IAAIA,eAAe,KAAK,cAAc,EAAE;MACtC,OAAO,IAAI,CAACG,eAAe,CAAC,CAAC;IAC/B;IACA,IAAIH,eAAe,KAAK,SAAS,EAAE;MACjC,OAAO,IAAI,CAACI,WAAW,CAAC5D,EAAE,CAAC;IAC7B;IACA,OAAO,IAAI,CAAC6D,SAAS,CAAC,CAAC;EACzB;EACAzB,oBAAoBA,CAAA,EAAG;IACrB,MAAMO,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,IAAI,CAACA,OAAO,EAAE;MACZ;IACF;IACA,MAAMM,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,MAAMzB,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,QAAQ,IAAI,CAACZ,SAAS;MACpB,KAAK,cAAc;QACjB+B,OAAO,CAACmB,OAAO,GAAG,IAAI,CAACC,wBAAwB,CAACd,SAAS,EAAEzB,KAAK,CAAC;QACjE;MACF,KAAK,SAAS;QACZ,MAAMwC,OAAO,GAAGrB,OAAO,CAACpG,aAAa,CAAC,oBAAoB,CAAC;QAC3D,IAAIyH,OAAO,EAAE;UACXA,OAAO,CAACC,OAAO,GAAG,IAAI,CAACC,oBAAoB,CAACjB,SAAS,EAAEzB,KAAK,CAAC;QAC/D;QACA;MACF,KAAK,OAAO;QACV,MAAM2C,SAAS,GAAG,IAAI,CAACnD,QAAQ,GAAG,UAAU,GAAG,OAAO;QACtD2B,OAAO,CAACyB,MAAM,GAAG,IAAI,CAACC,iBAAiB,CAACpB,SAAS,EAAEkB,SAAS,EAAE3C,KAAK,CAAC;QACpE;IACJ;EACF;EACAuC,wBAAwBA,CAACO,IAAI,EAAEC,WAAW,EAAE;IAC1C,MAAMC,kBAAkB,GAAGF,IAAI,CAACpB,GAAG,CAAEuB,MAAM,IAAK;MAC9C,MAAMjD,KAAK,GAAGkD,cAAc,CAACD,MAAM,CAAC;MACpC;MACA,MAAME,WAAW,GAAGC,KAAK,CAACC,IAAI,CAACJ,MAAM,CAACK,SAAS,CAAC,CAC7CC,MAAM,CAAEC,GAAG,IAAKA,GAAG,KAAK,UAAU,CAAC,CACnCC,IAAI,CAAC,GAAG,CAAC;MACZ,MAAMC,QAAQ,GAAI,GAAEC,YAAa,IAAGR,WAAY,EAAC;MACjD,OAAO;QACLS,IAAI,EAAEC,gBAAgB,CAACd,WAAW,EAAE/C,KAAK,EAAE,IAAI,CAACf,WAAW,CAAC,GAAG,UAAU,GAAG,EAAE;QAC9E6E,IAAI,EAAEb,MAAM,CAAChI,WAAW;QACxB8I,QAAQ,EAAEL,QAAQ;QAClBM,OAAO,EAAEA,CAAA,KAAM;UACb,IAAI,CAAC7D,QAAQ,CAACH,KAAK,CAAC;QACtB;MACF,CAAC;IACH,CAAC,CAAC;IACF;IACAgD,kBAAkB,CAACiB,IAAI,CAAC;MACtBH,IAAI,EAAE,IAAI,CAAC/E,UAAU;MACrB6E,IAAI,EAAE,QAAQ;MACdI,OAAO,EAAEA,CAAA,KAAM;QACb,IAAI,CAAClG,SAAS,CAACc,IAAI,CAAC,CAAC;MACvB;IACF,CAAC,CAAC;IACF,OAAOoE,kBAAkB;EAC3B;EACAH,iBAAiBA,CAACC,IAAI,EAAEH,SAAS,EAAEI,WAAW,EAAE;IAC9C,MAAMmB,WAAW,GAAGpB,IAAI,CAACpB,GAAG,CAAEuB,MAAM,IAAK;MACvC,MAAMjD,KAAK,GAAGkD,cAAc,CAACD,MAAM,CAAC;MACpC;MACA,MAAME,WAAW,GAAGC,KAAK,CAACC,IAAI,CAACJ,MAAM,CAACK,SAAS,CAAC,CAC7CC,MAAM,CAAEC,GAAG,IAAKA,GAAG,KAAK,UAAU,CAAC,CACnCC,IAAI,CAAC,GAAG,CAAC;MACZ,MAAMC,QAAQ,GAAI,GAAEC,YAAa,IAAGR,WAAY,EAAC;MACjD,OAAO;QACLgB,IAAI,EAAExB,SAAS;QACfoB,QAAQ,EAAEL,QAAQ;QAClBtJ,KAAK,EAAE6I,MAAM,CAAChI,WAAW,IAAI,EAAE;QAC/B+E,KAAK;QACLoE,OAAO,EAAEP,gBAAgB,CAACd,WAAW,EAAE/C,KAAK,EAAE,IAAI,CAACf,WAAW,CAAC;QAC/DC,QAAQ,EAAE+D,MAAM,CAAC/D;MACnB,CAAC;IACH,CAAC,CAAC;IACF,OAAOgF,WAAW;EACpB;EACAxB,oBAAoBA,CAACI,IAAI,EAAEC,WAAW,EAAE;IACtC,MAAMsB,cAAc,GAAGvB,IAAI,CAACpB,GAAG,CAAEuB,MAAM,IAAK;MAC1C,MAAMjD,KAAK,GAAGkD,cAAc,CAACD,MAAM,CAAC;MACpC;MACA,MAAME,WAAW,GAAGC,KAAK,CAACC,IAAI,CAACJ,MAAM,CAACK,SAAS,CAAC,CAC7CC,MAAM,CAAEC,GAAG,IAAKA,GAAG,KAAK,UAAU,CAAC,CACnCC,IAAI,CAAC,GAAG,CAAC;MACZ,MAAMC,QAAQ,GAAI,GAAEC,YAAa,IAAGR,WAAY,EAAC;MACjD,OAAO;QACLW,IAAI,EAAEb,MAAM,CAAChI,WAAW,IAAI,EAAE;QAC9B8I,QAAQ,EAAEL,QAAQ;QAClB1D,KAAK;QACLoE,OAAO,EAAEP,gBAAgB,CAACd,WAAW,EAAE/C,KAAK,EAAE,IAAI,CAACf,WAAW,CAAC;QAC/DC,QAAQ,EAAE+D,MAAM,CAAC/D,QAAQ;QACzB8E,OAAO,EAAGM,QAAQ,IAAK;UACrB,IAAI,CAACnE,QAAQ,CAACmE,QAAQ,CAAC;UACvB,IAAI,CAAC,IAAI,CAAC9E,QAAQ,EAAE;YAClB,IAAI,CAAC+E,KAAK,CAAC,CAAC;UACd;QACF;MACF,CAAC;IACH,CAAC,CAAC;IACF,OAAOF,cAAc;EACvB;EACMjC,WAAWA,CAAC5D,EAAE,EAAE;IAAA,IAAAgG,MAAA;IAAA,OAAAjE,8KAAA;MACpB,MAAM;QAAEpB,IAAI;QAAEI;MAAe,CAAC,GAAGiF,MAAI;MACrC,MAAMnF,gBAAgB,GAAGmF,MAAI,CAACnF,gBAAgB;MAC9C,MAAMoF,IAAI,GAAGlH,6DAAU,CAACiH,MAAI,CAAC;MAC7B,MAAME,YAAY,GAAGD,IAAI,KAAK,IAAI,GAAG,KAAK,GAAG,IAAI;MACjD,MAAMjF,QAAQ,GAAGgF,MAAI,CAAChF,QAAQ;MAC9B,MAAMQ,KAAK,GAAGwE,MAAI,CAACxE,KAAK;MACxB,IAAIiB,KAAK,GAAGzC,EAAE;MACd,IAAImG,IAAI,GAAG,MAAM;MACjB,IAAIH,MAAI,CAAChE,oBAAoB,CAACvG,gBAAgB,CAAC,CAAC,EAAE;QAChD,MAAM2K,IAAI,GAAGJ,MAAI,CAAC1K,EAAE,CAAC+K,OAAO,CAAC,UAAU,CAAC;QACxC;QACA;QACA;QACA,IAAID,IAAI,KAAKA,IAAI,CAACtB,SAAS,CAACwB,QAAQ,CAAC,qBAAqB,CAAC,IAAIF,IAAI,CAACtB,SAAS,CAACwB,QAAQ,CAAC,oBAAoB,CAAC,CAAC,EAAE;UAC7G7D,KAAK,GAAG8D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAExG,EAAE,CAAC,EAAE;YAAEyG,MAAM,EAAE;cACnDC,eAAe,EAAEN;YACnB;UAAE,CAAC,CAAC;UACND,IAAI,GAAG,OAAO;QAChB;MACF,CAAC,MACI;QACH,MAAMQ,yBAAyB,GAAG5F,cAAc,KAAK,UAAU,IAAIA,cAAc,KAAK,SAAS;QAC/F;AACN;AACA;AACA;AACA;QACM,IAAI4F,yBAAyB,IAAKV,IAAI,KAAK,IAAI,IAAItF,IAAI,KAAKjF,SAAU,EAAE;UACtEyK,IAAI,GAAG,OAAO;UACd;AACR;AACA;AACA;AACA;QACM,CAAC,MACI;UACH1D,KAAK,GAAG8D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAExG,EAAE,CAAC,EAAE;YAAEyG,MAAM,EAAE;cACnDC,eAAe,EAAEV,MAAI,CAACY;YACxB;UAAE,CAAC,CAAC;QACR;MACF;MACA,MAAMC,WAAW,GAAGN,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAEP,IAAI;QACpDxD,KAAK;QAAEqE,SAAS,EAAE,QAAQ;QAAEX,IAAI;QAChCD;MAAa,CAAC,EAAErF,gBAAgB,CAAC,EAAE;QAAEkG,SAAS,EAAE,oBAAoB;QAAExB,QAAQ,EAAE,CAAC,gBAAgB,EAAE1E,gBAAgB,CAAC0E,QAAQ,CAAC;QAAEyB,cAAc,EAAE;UAC7IC,MAAM,EAAEpG,gBAAgB,CAACoG,MAAM;UAC/BC,SAAS,EAAErG,gBAAgB,CAACqG,SAAS;UACrCC,OAAO,EAAEtG,gBAAgB,CAACsG,OAAO;UACjCnG,QAAQ;UACRQ,KAAK;UACLyC,OAAO,EAAE+B,MAAI,CAAC9B,oBAAoB,CAAC8B,MAAI,CAAC/C,SAAS,EAAEzB,KAAK;QAC1D;MAAE,CAAC,CAAC;MACN,OAAO1D,oDAAiB,CAACsJ,MAAM,CAACP,WAAW,CAAC;IAAC;EAC/C;EACMlD,eAAeA,CAAA,EAAG;IAAA,IAAA0D,MAAA;IAAA,OAAAtF,8KAAA;MACtB,MAAMkE,IAAI,GAAGlH,6DAAU,CAACsI,MAAI,CAAC;MAC7B,MAAMxG,gBAAgB,GAAGwG,MAAI,CAACxG,gBAAgB;MAC9C,MAAMyG,eAAe,GAAGf,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAEP;MAAK,CAAC,EAAEpF,gBAAgB,CAAC,EAAE;QAAEiD,OAAO,EAAEuD,MAAI,CAACtD,wBAAwB,CAACsD,MAAI,CAACpE,SAAS,EAAEoE,MAAI,CAAC7F,KAAK,CAAC;QAAE+D,QAAQ,EAAE,CAAC,qBAAqB,EAAE1E,gBAAgB,CAAC0E,QAAQ;MAAE,CAAC,CAAC;MACtN,OAAOvH,oDAAqB,CAACoJ,MAAM,CAACE,eAAe,CAAC;IAAC;EACvD;EACMzD,SAASA,CAAA,EAAG;IAAA,IAAA0D,MAAA;IAAA,OAAAxF,8KAAA;MAChB;AACJ;AACA;AACA;AACA;AACA;AACA;MACI,IAAInG,KAAK;MACT,IAAI4L,SAAS;MACb,IAAID,MAAI,CAACvF,oBAAoB,CAACvG,gBAAgB,CAAC,CAAC,EAAE;QAChDG,KAAK,GAAG2L,MAAI,CAACE,QAAQ,CAAC,CAAC;QACvBD,SAAS,GAAG5L,KAAK,GAAGA,KAAK,CAACa,WAAW,GAAG,IAAI;MAC9C,CAAC,MACI;QACH+K,SAAS,GAAGD,MAAI,CAACC,SAAS;MAC5B;MACA,MAAM3G,gBAAgB,GAAG0G,MAAI,CAAC1G,gBAAgB;MAC9C,MAAMsD,SAAS,GAAGoD,MAAI,CAACvG,QAAQ,GAAG,UAAU,GAAG,OAAO;MACtD,MAAMiF,IAAI,GAAGlH,6DAAU,CAACwI,MAAI,CAAC;MAC7B,MAAMG,SAAS,GAAGnB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAEP;MAAK,CAAC,EAAEpF,gBAAgB,CAAC,EAAE;QAAEoG,MAAM,EAAEpG,gBAAgB,CAACoG,MAAM,GAAGpG,gBAAgB,CAACoG,MAAM,GAAGO,SAAS;QAAEpD,MAAM,EAAEmD,MAAI,CAAClD,iBAAiB,CAACkD,MAAI,CAACtE,SAAS,EAAEkB,SAAS,EAAEoD,MAAI,CAAC/F,KAAK,CAAC;QAAEsC,OAAO,EAAE,CAChO;UACEwB,IAAI,EAAEiC,MAAI,CAAChH,UAAU;UACrB6E,IAAI,EAAE,QAAQ;UACdI,OAAO,EAAEA,CAAA,KAAM;YACb+B,MAAI,CAACjI,SAAS,CAACc,IAAI,CAAC,CAAC;UACvB;QACF,CAAC,EACD;UACEkF,IAAI,EAAEiC,MAAI,CAACrG,MAAM;UACjBsE,OAAO,EAAGmC,cAAc,IAAK;YAC3BJ,MAAI,CAAC5F,QAAQ,CAACgG,cAAc,CAAC;UAC/B;QACF,CAAC,CACF;QAAEpC,QAAQ,EAAE,CACX,cAAc,EACd1E,gBAAgB,CAAC0E,QAAQ,EACzBgC,MAAI,CAACvG,QAAQ,GAAG,uBAAuB,GAAG,qBAAqB;MAC/D,CAAC,CAAC;MACN,OAAO9C,oDAAe,CAACkJ,MAAM,CAACM,SAAS,CAAC;IAAC;EAC3C;EACA;AACF;AACA;EACE3B,KAAKA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACpD,OAAO,EAAE;MACjB,OAAOiF,OAAO,CAACC,OAAO,CAAC,KAAK,CAAC;IAC/B;IACA,OAAO,IAAI,CAAClF,OAAO,CAACmF,OAAO,CAAC,CAAC;EAC/B;EACA;EACAL,QAAQA,CAAA,EAAG;IACT,OAAOrM,uDAAa,CAAC,IAAI,CAACE,EAAE,CAAC;EAC/B;EACAyM,QAAQA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,OAAO,CAAC,CAAC,KAAK,EAAE;EAC9B;EACA,IAAI/E,SAASA,CAAA,EAAG;IACd,OAAO2B,KAAK,CAACC,IAAI,CAAC,IAAI,CAACvJ,EAAE,CAAC2M,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;EAClE;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAIT,SAASA,CAAA,EAAG;IACd,MAAM;MAAE5L;IAAM,CAAC,GAAG,IAAI;IACtB,IAAIA,KAAK,KAAKF,SAAS,EAAE;MACvB,OAAOE,KAAK;IACd;IACA,MAAM;MAAEuG;IAAU,CAAC,GAAG,IAAI;IAC1B,IAAIA,SAAS,KAAK,IAAI,EAAE;MACtB,OAAOA,SAAS,CAAC1F,WAAW;IAC9B;IACA;EACF;EACAuL,OAAOA,CAAA,EAAG;IACR,MAAM5G,YAAY,GAAG,IAAI,CAACA,YAAY;IACtC,IAAIA,YAAY,IAAI,IAAI,IAAIA,YAAY,KAAK,EAAE,EAAE;MAC/C,OAAOA,YAAY;IACrB;IACA,OAAO8G,YAAY,CAAC,IAAI,CAACjF,SAAS,EAAE,IAAI,CAACzB,KAAK,EAAE,IAAI,CAACf,WAAW,CAAC;EACnE;EACAR,QAAQA,CAAA,EAAG;IACT,IAAI,IAAI,CAACkI,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAAC5E,KAAK,CAAC,CAAC;IACtB;EACF;EACA7B,SAASA,CAAA,EAAG;IACV,MAAM;MAAEhB;IAAS,CAAC,GAAG,IAAI;IACzB,MAAM0H,KAAK,GAAG;MACZ,sBAAsB,EAAE1H;IAC1B,CAAC;IACD,IAAI,IAAI,CAACsB,oBAAoB,CAACvG,gBAAgB,CAAC,CAAC,EAAE;MAChD2M,KAAK,CAAC,aAAa,CAAC,GAAG,IAAI;MAC3BA,KAAK,CAAC,QAAQ,CAAC,GAAG,IAAI;MACtBA,KAAK,CAAC,iBAAiB,CAAC,GAAG1H,QAAQ;MACnC0H,KAAK,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAACjH,WAAW,KAAKzF,SAAS;MACzD0M,KAAK,CAAC,WAAW,CAAC,GAAG,IAAI,CAACL,QAAQ,CAAC,CAAC;MACpCK,KAAK,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC9H,UAAU;IACtC;IACA,IAAI,CAACZ,QAAQ,CAACU,IAAI,CAACgI,KAAK,CAAC;EAC3B;EACAC,WAAWA,CAAA,EAAG;IACZ,MAAM;MAAEzM;IAAM,CAAC,GAAG,IAAI;IACtB,OAAQT,qDAAC,CAAC,KAAK,EAAE;MAAEmN,KAAK,EAAE;QACtB,oBAAoB,EAAE,IAAI;QAC1B,2BAA2B,EAAE,CAAC,IAAI,CAACC;MACrC,CAAC;MAAEC,IAAI,EAAE;IAAQ,CAAC,EAAE5M,KAAK,KAAKF,SAAS,GAAGP,qDAAC,CAAC,MAAM,EAAE;MAAE8F,IAAI,EAAE;IAAQ,CAAC,CAAC,GAAG9F,qDAAC,CAAC,KAAK,EAAE;MAAEmN,KAAK,EAAE;IAAa,CAAC,EAAE1M,KAAK,CAAC,CAAC;EACtH;EACA6M,kBAAkBA,CAAA,EAAG;IACnB,IAAIC,EAAE;IACN,CAACA,EAAE,GAAG,IAAI,CAACzG,eAAe,MAAM,IAAI,IAAIyG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,mBAAmB,CAAC,CAAC;EAC3F;EACA;AACF;AACA;AACA;EACE,IAAIxG,SAASA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC7G,EAAE,CAACiB,aAAa,CAAC,gBAAgB,CAAC;EAChD;EACA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIgM,QAAQA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC3M,KAAK,KAAKF,SAAS,IAAI,IAAI,CAACyG,SAAS,KAAK,IAAI;EAC5D;EACA;AACF;AACA;AACA;EACEyG,oBAAoBA,CAAA,EAAG;IACrB,MAAM3C,IAAI,GAAGlH,6DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM8J,cAAc,GAAG5C,IAAI,KAAK,IAAI,IAAI,IAAI,CAACtF,IAAI,KAAK,SAAS;IAC/D,IAAIkI,cAAc,EAAE;MAClB;AACN;AACA;AACA;AACA;AACA;AACA;MACM,OAAO,CACL1N,qDAAC,CAAC,KAAK,EAAE;QAAEmN,KAAK,EAAE;MAA2B,CAAC,EAAEnN,qDAAC,CAAC,KAAK,EAAE;QAAEmN,KAAK,EAAE;MAAuB,CAAC,CAAC,EAAEnN,qDAAC,CAAC,KAAK,EAAE;QAAEmN,KAAK,EAAE;UAC3G,sBAAsB,EAAE,IAAI;UAC5B,6BAA6B,EAAE,CAAC,IAAI,CAACC;QACvC;MAAE,CAAC,EAAEpN,qDAAC,CAAC,KAAK,EAAE;QAAEmN,KAAK,EAAE,cAAc;QAAE,aAAa,EAAE,MAAM;QAAEQ,GAAG,EAAGxN,EAAE,IAAM,IAAI,CAAC4G,aAAa,GAAG5G;MAAI,CAAC,EAAE,IAAI,CAACM,KAAK,CAAC,CAAC,EAAET,qDAAC,CAAC,KAAK,EAAE;QAAEmN,KAAK,EAAE;MAAqB,CAAC,CAAC,CAAC,EAClK,IAAI,CAACD,WAAW,CAAC,CAAC,CACnB;IACH;IACA;AACJ;AACA;AACA;IACI,OAAO,IAAI,CAACA,WAAW,CAAC,CAAC;EAC3B;EACAU,YAAYA,CAAA,EAAG;IACb,MAAM;MAAErI,QAAQ;MAAEpF,EAAE;MAAEgF,UAAU;MAAEgB,YAAY;MAAEP,cAAc;MAAED,OAAO;MAAEK,WAAW;MAAER,IAAI;MAAEY,KAAK;MAAEN,IAAI;MAAEO;IAAM,CAAC,GAAG,IAAI;IACvH,MAAMyE,IAAI,GAAGlH,6DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM4H,yBAAyB,GAAG5F,cAAc,KAAK,UAAU,IAAIA,cAAc,KAAK,SAAS;IAC/F,MAAMiI,cAAc,GAAG,CAACrC,yBAAyB;IACjD,MAAMsC,GAAG,GAAG5K,mDAAK,CAAC/C,EAAE,CAAC,GAAG,KAAK,GAAG,KAAK;IACrC,MAAM4N,MAAM,GAAG5K,qDAAW,CAAC,UAAU,EAAE,IAAI,CAAChD,EAAE,CAAC;IAC/C,MAAM6N,qBAAqB,GAAGlD,IAAI,KAAK,IAAI,IAAItF,IAAI,KAAK,SAAS,IAAI,CAACuI,MAAM;IAC5EzL,uDAAiB,CAAC,IAAI,EAAEnC,EAAE,EAAE2F,IAAI,EAAEmI,UAAU,CAAC5H,KAAK,CAAC,EAAEd,QAAQ,CAAC;IAC9D,OAAQvF,qDAAC,CAAC6B,iDAAI,EAAE;MAAE+C,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEuI,KAAK,EAAE/J,qDAAkB,CAAC,IAAI,CAACiC,KAAK,EAAE;QAC3E,CAACyF,IAAI,GAAG,IAAI;QACZ,SAAS,EAAEiD,MAAM;QACjB,eAAe,EAAE5K,qDAAW,CAAC,oBAAoB,EAAEhD,EAAE,CAAC;QACtD,iBAAiB,EAAEoF,QAAQ;QAC3B,iBAAiB,EAAEJ,UAAU;QAC7B,mBAAmB,EAAEgB,YAAY,KAAK5F,SAAS;QAC/C,WAAW,EAAE,IAAI,CAACqM,QAAQ,CAAC,CAAC;QAC5B,iBAAiB,EAAE5G,WAAW,KAAKzF,SAAS;QAC5C,eAAe,EAAE,IAAI;QACrB,CAAE,UAASuN,GAAI,EAAC,GAAG,IAAI;QACvB,CAAE,eAActI,IAAK,EAAC,GAAGA,IAAI,KAAKjF,SAAS;QAC3C,CAAE,kBAAiBoF,OAAQ,EAAC,GAAGkI,cAAc;QAC7C,CAAE,gBAAezH,KAAM,EAAC,GAAGA,KAAK,KAAK7F,SAAS;QAC9C,CAAE,0BAAyBqF,cAAe,EAAC,GAAG;MAChD,CAAC;IAAE,CAAC,EAAE5F,qDAAC,CAAC,OAAO,EAAE;MAAEmN,KAAK,EAAE,gBAAgB;MAAEe,EAAE,EAAE;IAAe,CAAC,EAAE,IAAI,CAACT,oBAAoB,CAAC,CAAC,EAAEzN,qDAAC,CAAC,KAAK,EAAE;MAAEmN,KAAK,EAAE,gBAAgB;MAAEQ,GAAG,EAAGxN,EAAE,IAAM,IAAI,CAACsL,eAAe,GAAGtL,EAAG;MAAEkN,IAAI,EAAE;IAAY,CAAC,EAAE,IAAI,CAACc,gBAAgB,CAAC,CAAC,EAAE,CAAC3C,yBAAyB,IAAI,IAAI,CAAC4C,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC,EAAE7C,yBAAyB,IAAI,IAAI,CAAC4C,gBAAgB,CAAC,CAAC,EAAEJ,qBAAqB,IAAIhO,qDAAC,CAAC,KAAK,EAAE;MAAEmN,KAAK,EAAE;IAAmB,CAAC,CAAC,CAAC,CAAC;EACra;EACA;EACAmB,kBAAkBA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAAC3J,2BAA2B,EAAE;MACrCjC,qDAAe,CAAE;AACvB;AACA;AACA;AACA;AACA,gNAAgN,EAAE,IAAI,CAACvC,EAAE,CAAC;MACpN,IAAI,IAAI,CAACY,MAAM,EAAE;QACf2B,qDAAe,CAAE;AACzB,0HAA0H,EAAE,IAAI,CAACvC,EAAE,CAAC;MAC9H;MACA,IAAI,CAACwE,2BAA2B,GAAG,IAAI;IACzC;IACA,MAAM;MAAEY,QAAQ;MAAEpF,EAAE;MAAEqE,OAAO;MAAEW,UAAU;MAAEgB,YAAY;MAAEL,IAAI;MAAEE,WAAW;MAAEK;IAAM,CAAC,GAAG,IAAI;IAC1F,MAAMyE,IAAI,GAAGlH,6DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM;MAAEyI,SAAS;MAAEkC;IAAQ,CAAC,GAAG/L,uDAAY,CAACrC,EAAE,EAAEqE,OAAO,CAAC;IACxDlC,uDAAiB,CAAC,IAAI,EAAEnC,EAAE,EAAE2F,IAAI,EAAEmI,UAAU,CAAC5H,KAAK,CAAC,EAAEd,QAAQ,CAAC;IAC9D,MAAMiJ,YAAY,GAAG,IAAI,CAAC3B,OAAO,CAAC,CAAC;IACnC,IAAI4B,UAAU,GAAGD,YAAY;IAC7B,IAAIC,UAAU,KAAK,EAAE,IAAIzI,WAAW,KAAKzF,SAAS,EAAE;MAClDkO,UAAU,GAAGzI,WAAW;IAC1B;IACA;IACA;IACA;IACA;IACA,MAAM0I,YAAY,GAAGrC,SAAS,KAAK9L,SAAS,GAAIkO,UAAU,KAAK,EAAE,GAAI,GAAEA,UAAW,KAAIpC,SAAU,EAAC,GAAGA,SAAS,GAAIoC,UAAU;IAC3H,OAAQzO,qDAAC,CAAC6B,iDAAI,EAAE;MAAE+C,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEqF,IAAI,EAAE,QAAQ;MAAE,eAAe,EAAE,SAAS;MAAE,eAAe,EAAE1E,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAE,YAAY,EAAEmJ,YAAY;MAAEvB,KAAK,EAAE;QAC/J,CAACrC,IAAI,GAAG,IAAI;QACZ,SAAS,EAAE3H,qDAAW,CAAC,UAAU,EAAEhD,EAAE,CAAC;QACtC,eAAe,EAAEgD,qDAAW,CAAC,oBAAoB,EAAEhD,EAAE,CAAC;QACtD,iBAAiB,EAAEoF,QAAQ;QAC3B,iBAAiB,EAAEJ,UAAU;QAC7B,mBAAmB,EAAEgB,YAAY,KAAK5F,SAAS;QAC/C,eAAe,EAAE;MACnB;IAAE,CAAC,EAAE,IAAI,CAAC4N,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAACC,gBAAgB,CAAC,CAAC,EAAEpO,qDAAC,CAAC,OAAO,EAAE;MAAEkO,EAAE,EAAEK;IAAQ,CAAC,EAAEG,YAAY,CAAC,EAAE,IAAI,CAACL,aAAa,CAAC,CAAC,CAAC;EAC3H;EACA;AACF;AACA;AACA;AACA;EACEF,gBAAgBA,CAAA,EAAG;IACjB,MAAM;MAAEnI;IAAY,CAAC,GAAG,IAAI;IAC5B,MAAMwI,YAAY,GAAG,IAAI,CAAC3B,OAAO,CAAC,CAAC;IACnC,IAAI8B,mBAAmB,GAAG,KAAK;IAC/B,IAAIF,UAAU,GAAGD,YAAY;IAC7B,IAAIC,UAAU,KAAK,EAAE,IAAIzI,WAAW,KAAKzF,SAAS,EAAE;MAClDkO,UAAU,GAAGzI,WAAW;MACxB2I,mBAAmB,GAAG,IAAI;IAC5B;IACA,MAAMC,iBAAiB,GAAG;MACxB,aAAa,EAAE,IAAI;MACnB,oBAAoB,EAAED;IACxB,CAAC;IACD,MAAME,QAAQ,GAAGF,mBAAmB,GAAG,aAAa,GAAG,MAAM;IAC7D,OAAQ3O,qDAAC,CAAC,KAAK,EAAE;MAAE,aAAa,EAAE,MAAM;MAAEmN,KAAK,EAAEyB,iBAAiB;MAAEvB,IAAI,EAAEwB;IAAS,CAAC,EAAEJ,UAAU,CAAC;EACnG;EACA;AACF;AACA;AACA;EACEL,gBAAgBA,CAAA,EAAG;IACjB,MAAMtD,IAAI,GAAGlH,6DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM;MAAEuB,UAAU;MAAEe,UAAU;MAAEC;IAAa,CAAC,GAAG,IAAI;IACrD,IAAI2I,IAAI;IACR,IAAI3J,UAAU,IAAIgB,YAAY,KAAK5F,SAAS,EAAE;MAC5CuO,IAAI,GAAG3I,YAAY;IACrB,CAAC,MACI;MACH,MAAM4I,WAAW,GAAGjE,IAAI,KAAK,KAAK,GAAGrH,kDAAa,GAAGE,kDAAc;MACnEmL,IAAI,GAAG5I,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAG6I,WAAW;IAChF;IACA,OAAO/O,qDAAC,CAAC,UAAU,EAAE;MAAEmN,KAAK,EAAE,aAAa;MAAEE,IAAI,EAAE,MAAM;MAAE,aAAa,EAAE,MAAM;MAAEyB,IAAI,EAAEA;IAAK,CAAC,CAAC;EACjG;EACA,IAAIE,SAASA,CAAA,EAAG;IACd,IAAIzB,EAAE,EAAE0B,EAAE;IACV,MAAM;MAAEjJ,WAAW;MAAE7F,EAAE;MAAEqE,OAAO;MAAEE;IAAoB,CAAC,GAAG,IAAI;IAC9D,MAAM8J,YAAY,GAAG,IAAI,CAAC3B,OAAO,CAAC,CAAC;IACnC,MAAM;MAAER;IAAU,CAAC,GAAG7J,uDAAY,CAACrC,EAAE,EAAEqE,OAAO,CAAC;IAC/C,MAAM0K,YAAY,GAAG,CAACD,EAAE,GAAG,CAAC1B,EAAE,GAAG,IAAI,CAAClB,SAAS,MAAM,IAAI,IAAIkB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG7I,mBAAmB,CAAC,YAAY,CAAC,MAAM,IAAI,IAAIuK,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG5C,SAAS;IAC/J;AACJ;AACA;AACA;AACA;IACI,IAAI8C,aAAa,GAAGX,YAAY;IAChC,IAAIW,aAAa,KAAK,EAAE,IAAInJ,WAAW,KAAKzF,SAAS,EAAE;MACrD4O,aAAa,GAAGnJ,WAAW;IAC7B;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAIkJ,YAAY,KAAK3O,SAAS,EAAE;MAC9B4O,aAAa,GAAGA,aAAa,KAAK,EAAE,GAAGD,YAAY,GAAI,GAAEA,YAAa,KAAIC,aAAc,EAAC;IAC3F;IACA,OAAOA,aAAa;EACtB;EACAd,aAAaA,CAAA,EAAG;IACd,MAAM;MAAE9I,QAAQ;MAAEf,OAAO;MAAEW;IAAW,CAAC,GAAG,IAAI;IAC9C,OAAQnF,qDAAC,CAAC,QAAQ,EAAE;MAAEuF,QAAQ,EAAEA,QAAQ;MAAE2I,EAAE,EAAE1J,OAAO;MAAE,YAAY,EAAE,IAAI,CAACwK,SAAS;MAAE,eAAe,EAAE,SAAS;MAAE,eAAe,EAAG,GAAE7J,UAAW,EAAC;MAAEH,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEE,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEyI,GAAG,EAAGX,OAAO,IAAM,IAAI,CAACA,OAAO,GAAGA;IAAS,CAAC,CAAC;EAC9O;EACAoC,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEvI;IAAqB,CAAC,GAAG,IAAI;IACrC,OAAOA,oBAAoB,CAACvG,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAACgO,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAACV,YAAY,CAAC,CAAC;EAClG;EACA,IAAIzN,EAAEA,CAAA,EAAG;IAAE,OAAO4B,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWsN,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC7B,UAAU,EAAE,CAAC,cAAc,CAAC;MAC5B,YAAY,EAAE,CAAC,cAAc,CAAC;MAC9B,aAAa,EAAE,CAAC,cAAc,CAAC;MAC/B,OAAO,EAAE,CAAC,cAAc;IAC1B,CAAC;EAAE;AACL,CAAC;AACD,MAAMnF,gBAAgB,GAAGA,CAACoF,YAAY,EAAEC,YAAY,EAAEjK,WAAW,KAAK;EACpE,IAAIgK,YAAY,KAAK/O,SAAS,EAAE;IAC9B,OAAO,KAAK;EACd;EACA,IAAIkJ,KAAK,CAAC+F,OAAO,CAACF,YAAY,CAAC,EAAE;IAC/B,OAAOA,YAAY,CAACG,IAAI,CAAEC,GAAG,IAAKC,cAAc,CAACD,GAAG,EAAEH,YAAY,EAAEjK,WAAW,CAAC,CAAC;EACnF,CAAC,MACI;IACH,OAAOqK,cAAc,CAACL,YAAY,EAAEC,YAAY,EAAEjK,WAAW,CAAC;EAChE;AACF,CAAC;AACD,MAAMiE,cAAc,GAAIpJ,EAAE,IAAK;EAC7B,MAAMkG,KAAK,GAAGlG,EAAE,CAACkG,KAAK;EACtB,OAAOA,KAAK,KAAK9F,SAAS,GAAGJ,EAAE,CAACmB,WAAW,IAAI,EAAE,GAAG+E,KAAK;AAC3D,CAAC;AACD,MAAM4H,UAAU,GAAI5H,KAAK,IAAK;EAC5B,IAAIA,KAAK,IAAI,IAAI,EAAE;IACjB,OAAO9F,SAAS;EAClB;EACA,IAAIkJ,KAAK,CAAC+F,OAAO,CAACnJ,KAAK,CAAC,EAAE;IACxB,OAAOA,KAAK,CAACyD,IAAI,CAAC,GAAG,CAAC;EACxB;EACA,OAAOzD,KAAK,CAACuJ,QAAQ,CAAC,CAAC;AACzB,CAAC;AACD,MAAMD,cAAc,GAAGA,CAACL,YAAY,EAAEC,YAAY,EAAEjK,WAAW,KAAK;EAClE,IAAI,OAAOA,WAAW,KAAK,UAAU,EAAE;IACrC,OAAOA,WAAW,CAACgK,YAAY,EAAEC,YAAY,CAAC;EAChD,CAAC,MACI,IAAI,OAAOjK,WAAW,KAAK,QAAQ,EAAE;IACxC,OAAOgK,YAAY,CAAChK,WAAW,CAAC,KAAKiK,YAAY,CAACjK,WAAW,CAAC;EAChE,CAAC,MACI;IACH,OAAOmE,KAAK,CAAC+F,OAAO,CAACD,YAAY,CAAC,GAAGA,YAAY,CAACrO,QAAQ,CAACoO,YAAY,CAAC,GAAGA,YAAY,KAAKC,YAAY;EAC1G;AACF,CAAC;AACD,MAAMxC,YAAY,GAAGA,CAAC8C,IAAI,EAAExJ,KAAK,EAAEf,WAAW,KAAK;EACjD,IAAIe,KAAK,KAAK9F,SAAS,EAAE;IACvB,OAAO,EAAE;EACX;EACA,IAAIkJ,KAAK,CAAC+F,OAAO,CAACnJ,KAAK,CAAC,EAAE;IACxB,OAAOA,KAAK,CACT0B,GAAG,CAAE+H,CAAC,IAAKC,YAAY,CAACF,IAAI,EAAEC,CAAC,EAAExK,WAAW,CAAC,CAAC,CAC9CsE,MAAM,CAAEoG,GAAG,IAAKA,GAAG,KAAK,IAAI,CAAC,CAC7BlG,IAAI,CAAC,IAAI,CAAC;EACf,CAAC,MACI;IACH,OAAOiG,YAAY,CAACF,IAAI,EAAExJ,KAAK,EAAEf,WAAW,CAAC,IAAI,EAAE;EACrD;AACF,CAAC;AACD,MAAMyK,YAAY,GAAGA,CAACF,IAAI,EAAExJ,KAAK,EAAEf,WAAW,KAAK;EACjD,MAAM2K,SAAS,GAAGJ,IAAI,CAACK,IAAI,CAAEF,GAAG,IAAK;IACnC,OAAOL,cAAc,CAACtJ,KAAK,EAAEkD,cAAc,CAACyG,GAAG,CAAC,EAAE1K,WAAW,CAAC;EAChE,CAAC,CAAC;EACF,OAAO2K,SAAS,GAAGA,SAAS,CAAC3O,WAAW,GAAG,IAAI;AACjD,CAAC;AACD,IAAImD,SAAS,GAAG,CAAC;AACjB,MAAMuF,YAAY,GAAG,yBAAyB;AAC9CjG,MAAM,CAACkJ,KAAK,GAAG;EACbkD,GAAG,EAAEtM,YAAY;EACjBuM,EAAE,EAAEtM;AACN,CAAC;AAED,MAAMuM,eAAe,GAAG,qBAAqB;AAE7C,MAAMC,YAAY,GAAG,MAAM;EACzBtM,WAAWA,CAACC,OAAO,EAAE;IACnBxC,qDAAgB,CAAC,IAAI,EAAEwC,OAAO,CAAC;IAC/B,IAAI,CAACO,OAAO,GAAI,cAAa+L,eAAe,EAAG,EAAC;IAChD,IAAI,CAAChL,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACc,KAAK,GAAG9F,SAAS;EACxB;EACA6O,MAAMA,CAAA,EAAG;IACP,OAAOpP,qDAAC,CAAC6B,iDAAI,EAAE;MAAEoI,IAAI,EAAE,QAAQ;MAAEiE,EAAE,EAAE,IAAI,CAAC1J,OAAO;MAAE2I,KAAK,EAAEvJ,6DAAU,CAAC,IAAI;IAAE,CAAC,CAAC;EAC/E;EACA,IAAIzD,EAAEA,CAAA,EAAG;IAAE,OAAO4B,qDAAU,CAAC,IAAI,CAAC;EAAE;AACtC,CAAC;AACD,IAAIwO,eAAe,GAAG,CAAC;AACvBD,YAAY,CAACrD,KAAK,GAAGoD,eAAe;AAEpC,MAAMG,mBAAmB,GAAG,oQAAoQ;AAEhS,MAAMC,kBAAkB,GAAG,y+BAAy+B;AAEpgC,MAAMC,aAAa,GAAG,MAAM;EAC1B1M,WAAWA,CAACC,OAAO,EAAE;IACnBxC,qDAAgB,CAAC,IAAI,EAAEwC,OAAO,CAAC;IAC/B,IAAI,CAAC6H,MAAM,GAAGvL,SAAS;IACvB,IAAI,CAACwL,SAAS,GAAGxL,SAAS;IAC1B,IAAI,CAACyL,OAAO,GAAGzL,SAAS;IACxB,IAAI,CAACsF,QAAQ,GAAGtF,SAAS;IACzB,IAAI,CAACuI,OAAO,GAAG,EAAE;EACnB;EACA6H,mBAAmBA,CAAC9L,EAAE,EAAE;IACtB,MAAM;MAAEiE;IAAQ,CAAC,GAAG,IAAI;IACxB,OAAOA,OAAO,CAACoH,IAAI,CAAElI,CAAC,IAAKA,CAAC,CAAC3B,KAAK,KAAKxB,EAAE,CAAC+L,MAAM,CAACvK,KAAK,CAAC;EACzD;EACA;AACF;AACA;AACA;AACA;EACEwK,iBAAiBA,CAAChM,EAAE,EAAE;IACpB,MAAMyE,MAAM,GAAG,IAAI,CAACqH,mBAAmB,CAAC9L,EAAE,CAAC;IAC3C,MAAMiM,MAAM,GAAG,IAAI,CAACC,SAAS,CAAClM,EAAE,CAAC;IACjC,IAAIyE,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACe,OAAO,EAAE;MAClEpH,wDAAQ,CAACqG,MAAM,CAACe,OAAO,EAAEyG,MAAM,CAAC;IAClC;EACF;EACA;AACF;AACA;AACA;EACEE,oBAAoBA,CAAA,EAAG;IACrB,MAAMnI,OAAO,GAAG,IAAI,CAAC1I,EAAE,CAAC+K,OAAO,CAAC,aAAa,CAAC;IAC9C,IAAIrC,OAAO,EAAE;MACXA,OAAO,CAAC8D,OAAO,CAAC,CAAC;IACnB;EACF;EACAsE,UAAUA,CAACpM,EAAE,EAAE;IACb,MAAM;MAAEgB;IAAS,CAAC,GAAG,IAAI;IACzB,MAAMyD,MAAM,GAAG,IAAI,CAACqH,mBAAmB,CAAC9L,EAAE,CAAC;IAC3C;IACA;IACA,IAAIgB,QAAQ,IAAIyD,MAAM,EAAE;MACtBA,MAAM,CAACmB,OAAO,GAAG5F,EAAE,CAACyG,MAAM,CAACb,OAAO;IACpC;EACF;EACAsG,SAASA,CAAClM,EAAE,EAAE;IACZ,MAAM;MAAEgB,QAAQ;MAAEiD;IAAQ,CAAC,GAAG,IAAI;IAClC,IAAIjD,QAAQ,EAAE;MACZ;MACA;MACA,OAAOiD,OAAO,CAACc,MAAM,CAAE5B,CAAC,IAAKA,CAAC,CAACyC,OAAO,CAAC,CAAC1C,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAC3B,KAAK,CAAC;IAC7D;IACA;IACA;IACA,MAAMiD,MAAM,GAAG,IAAI,CAACqH,mBAAmB,CAAC9L,EAAE,CAAC;IAC3C,OAAOyE,MAAM,GAAGA,MAAM,CAACjD,KAAK,GAAG9F,SAAS;EAC1C;EACA2Q,aAAaA,CAACpI,OAAO,EAAE;IACrB,MAAM;MAAEjD;IAAS,CAAC,GAAG,IAAI;IACzB,QAAQA,QAAQ;MACd,KAAK,IAAI;QACP,OAAO,IAAI,CAACsL,qBAAqB,CAACrI,OAAO,CAAC;MAC5C;QACE,OAAO,IAAI,CAACsI,kBAAkB,CAACtI,OAAO,CAAC;IAC3C;EACF;EACAqI,qBAAqBA,CAACrI,OAAO,EAAE;IAC7B,OAAOA,OAAO,CAACf,GAAG,CAAEuB,MAAM,IAAMtJ,qDAAC,CAAC,UAAU,EAAE;MAAEmN,KAAK,EAAE/B,MAAM,CAACC,MAAM,CAAC;QACjE;QACA,uBAAuB,EAAE/B,MAAM,CAACmB;MAClC,CAAC,EAAEnH,qDAAW,CAACgG,MAAM,CAACc,QAAQ,CAAC;IAAE,CAAC,EAAEpK,qDAAC,CAAC,cAAc,EAAE;MAAEqG,KAAK,EAAEiD,MAAM,CAACjD,KAAK;MAAEd,QAAQ,EAAE+D,MAAM,CAAC/D,QAAQ;MAAEkF,OAAO,EAAEnB,MAAM,CAACmB,OAAO;MAAE9E,OAAO,EAAE,OAAO;MAAEC,cAAc,EAAE,KAAK;MAAEyL,WAAW,EAAGxM,EAAE,IAAK;QAC7L,IAAI,CAACoM,UAAU,CAACpM,EAAE,CAAC;QACnB,IAAI,CAACgM,iBAAiB,CAAChM,EAAE,CAAC;QAC1B;QACA5C,qDAAW,CAAC,IAAI,CAAC;MACnB;IAAE,CAAC,EAAEqH,MAAM,CAACa,IAAI,CAAC,CAAE,CAAC;EACxB;EACAiH,kBAAkBA,CAACtI,OAAO,EAAE;IAC1B,MAAM2B,OAAO,GAAG3B,OAAO,CAACc,MAAM,CAAE5B,CAAC,IAAKA,CAAC,CAACyC,OAAO,CAAC,CAAC1C,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAC3B,KAAK,CAAC,CAAC,CAAC,CAAC;IACvE,OAAQrG,qDAAC,CAAC,iBAAiB,EAAE;MAAEqG,KAAK,EAAEoE,OAAO;MAAE4G,WAAW,EAAGxM,EAAE,IAAK,IAAI,CAACgM,iBAAiB,CAAChM,EAAE;IAAE,CAAC,EAAEiE,OAAO,CAACf,GAAG,CAAEuB,MAAM,IAAMtJ,qDAAC,CAAC,UAAU,EAAE;MAAEmN,KAAK,EAAE/B,MAAM,CAACC,MAAM,CAAC;QAC5J;QACA,oBAAoB,EAAE/B,MAAM,CAACjD,KAAK,KAAKoE;MACzC,CAAC,EAAEnH,qDAAW,CAACgG,MAAM,CAACc,QAAQ,CAAC;IAAE,CAAC,EAAEpK,qDAAC,CAAC,WAAW,EAAE;MAAEqG,KAAK,EAAEiD,MAAM,CAACjD,KAAK;MAAEd,QAAQ,EAAE+D,MAAM,CAAC/D,QAAQ;MAAEX,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACoM,oBAAoB,CAAC,CAAC;MAAEM,OAAO,EAAGzM,EAAE,IAAK;QAChK,IAAIA,EAAE,CAAC0M,GAAG,KAAK,GAAG,EAAE;UAClB;AACV;AACA;AACA;AACA;UACU,IAAI,CAACP,oBAAoB,CAAC,CAAC;QAC7B;MACF;IAAE,CAAC,EAAE1H,MAAM,CAACa,IAAI,CAAC,CAAE,CAAC,CAAC;EACzB;EACAiF,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEtD,MAAM;MAAEE,OAAO;MAAElD,OAAO;MAAEiD;IAAU,CAAC,GAAG,IAAI;IACpD,MAAMyF,qBAAqB,GAAGzF,SAAS,KAAKxL,SAAS,IAAIyL,OAAO,KAAKzL,SAAS;IAC9E,OAAQP,qDAAC,CAAC6B,iDAAI,EAAE;MAAEsL,KAAK,EAAEvJ,6DAAU,CAAC,IAAI;IAAE,CAAC,EAAE5D,qDAAC,CAAC,UAAU,EAAE,IAAI,EAAE8L,MAAM,KAAKvL,SAAS,IAAIP,qDAAC,CAAC,iBAAiB,EAAE,IAAI,EAAE8L,MAAM,CAAC,EAAE0F,qBAAqB,IAAKxR,qDAAC,CAAC,UAAU,EAAE,IAAI,EAAEA,qDAAC,CAAC,WAAW,EAAE;MAAEmN,KAAK,EAAE;IAAgB,CAAC,EAAEpB,SAAS,KAAKxL,SAAS,IAAIP,qDAAC,CAAC,IAAI,EAAE,IAAI,EAAE+L,SAAS,CAAC,EAAEC,OAAO,KAAKzL,SAAS,IAAIP,qDAAC,CAAC,GAAG,EAAE,IAAI,EAAEgM,OAAO,CAAC,CAAC,CAAE,EAAE,IAAI,CAACkF,aAAa,CAACpI,OAAO,CAAC,CAAC,CAAC;EAC7V;EACA,IAAI3I,EAAEA,CAAA,EAAG;IAAE,OAAO4B,qDAAU,CAAC,IAAI,CAAC;EAAE;AACtC,CAAC;AACD2O,aAAa,CAACzD,KAAK,GAAG;EACpBkD,GAAG,EAAEK,mBAAmB;EACxBJ,EAAE,EAAEK;AACN,CAAC;;;;;;;;;;;;;;;;;AC7yBD;AACA;AACA;AAC+C;AACE;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMvO,qBAAqB,GAAGA,CAAC/B,EAAE,EAAE2R,gBAAgB,EAAEC,YAAY,KAAK;EACpE,IAAIC,iBAAiB;EACrB,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IACpC,MAAMlL,aAAa,GAAG+K,gBAAgB,CAAC,CAAC;IACxC;IACA;AACJ;AACA;AACA;IACI/K,aAAa,KAAKxG,SAAS;IACzB;AACN;AACA;AACA;AACA;IACMJ,EAAE,CAACM,KAAK,KAAKF,SAAS,IACtBwR,YAAY,CAAC,CAAC,KAAK,IAAI,EAAE;MACzB,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;EACD,MAAMvE,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIyE,uBAAuB,CAAC,CAAC,EAAE;MAC7B;AACN;AACA;AACA;AACA;AACA;MACMJ,uDAAG,CAAC,MAAM;QACRK,aAAa,CAAC,CAAC;MACjB,CAAC,CAAC;IACJ;EACF,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMA,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMnL,aAAa,GAAG+K,gBAAgB,CAAC,CAAC;IACxC,IAAI/K,aAAa,KAAKxG,SAAS,EAAE;MAC/B;IACF;IACA,IAAI,CAAC0R,uBAAuB,CAAC,CAAC,EAAE;MAC9BlL,aAAa,CAACkG,KAAK,CAACkF,cAAc,CAAC,OAAO,CAAC;MAC3C;IACF;IACA,MAAMC,KAAK,GAAGL,YAAY,CAAC,CAAC,CAACM,WAAW;IACxC;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACID,KAAK,KAAK,CAAC,IACTrL,aAAa,CAACuL,YAAY,KAAK,IAAI,IACnCV,iDAAG,KAAKrR,SAAS,IACjB,2EAA6B,EAAE;MAC/B;AACN;AACA;AACA;AACA;AACA;AACA;MACM,IAAIyR,iBAAiB,KAAKzR,SAAS,EAAE;QACnC;MACF;MACA,MAAMgS,EAAE,GAAIP,iBAAiB,GAAG,IAAIQ,oBAAoB,CAAE3N,EAAE,IAAK;QAC/D;AACR;AACA;AACA;QACQ,IAAIA,EAAE,CAAC,CAAC,CAAC,CAAC4N,iBAAiB,KAAK,CAAC,EAAE;UACjCP,aAAa,CAAC,CAAC;UACfK,EAAE,CAACnL,UAAU,CAAC,CAAC;UACf4K,iBAAiB,GAAGzR,SAAS;QAC/B;MACF,CAAC;MACD;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACM;QAAEmS,SAAS,EAAE,IAAI;QAAE1R,IAAI,EAAEb;MAAG,CAAC,CAAE;MAC/BoS,EAAE,CAACI,OAAO,CAAC5L,aAAa,CAAC;MACzB;IACF;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;IACIA,aAAa,CAACkG,KAAK,CAAC2F,WAAW,CAAC,OAAO,EAAG,GAAER,KAAK,GAAG,IAAK,IAAG,CAAC;EAC/D,CAAC;EACD,MAAM/K,OAAO,GAAGA,CAAA,KAAM;IACpB,IAAI2K,iBAAiB,EAAE;MACrBA,iBAAiB,CAAC5K,UAAU,CAAC,CAAC;MAC9B4K,iBAAiB,GAAGzR,SAAS;IAC/B;EACF,CAAC;EACD,OAAO;IACLiN,mBAAmB;IACnBnG;EACF,CAAC;AACH,CAAC;;;;;;;;;;;;;;;;;;;;ACtJD;AACA;AACA;AACA,MAAMlE,WAAW,GAAGA,CAAC0P,QAAQ,EAAE1S,EAAE,KAAK;EACpC,OAAOA,EAAE,CAAC+K,OAAO,CAAC2H,QAAQ,CAAC,KAAK,IAAI;AACtC,CAAC;AACD;AACA;AACA;AACA,MAAMzP,kBAAkB,GAAGA,CAACiC,KAAK,EAAEyN,WAAW,KAAK;EACjD,OAAO,OAAOzN,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAAC0N,MAAM,GAAG,CAAC,GAChD3H,MAAM,CAACC,MAAM,CAAC;IAAE,WAAW,EAAE,IAAI;IAAE,CAAE,aAAYhG,KAAM,EAAC,GAAG;EAAK,CAAC,EAAEyN,WAAW,CAAC,GAAGA,WAAW;AACnG,CAAC;AACD,MAAME,YAAY,GAAIC,OAAO,IAAK;EAChC,IAAIA,OAAO,KAAK1S,SAAS,EAAE;IACzB,MAAM2S,KAAK,GAAGzJ,KAAK,CAAC+F,OAAO,CAACyD,OAAO,CAAC,GAAGA,OAAO,GAAGA,OAAO,CAACE,KAAK,CAAC,GAAG,CAAC;IACnE,OAAOD,KAAK,CACTtJ,MAAM,CAAErI,CAAC,IAAKA,CAAC,IAAI,IAAI,CAAC,CACxBwG,GAAG,CAAExG,CAAC,IAAKA,CAAC,CAAC6R,IAAI,CAAC,CAAC,CAAC,CACpBxJ,MAAM,CAAErI,CAAC,IAAKA,CAAC,KAAK,EAAE,CAAC;EAC5B;EACA,OAAO,EAAE;AACX,CAAC;AACD,MAAM+B,WAAW,GAAI2P,OAAO,IAAK;EAC/B,MAAMlL,GAAG,GAAG,CAAC,CAAC;EACdiL,YAAY,CAACC,OAAO,CAAC,CAACI,OAAO,CAAE9R,CAAC,IAAMwG,GAAG,CAACxG,CAAC,CAAC,GAAG,IAAK,CAAC;EACrD,OAAOwG,GAAG;AACZ,CAAC;AACD,MAAMuL,MAAM,GAAG,sBAAsB;AACrC,MAAMC,OAAO;EAAA,IAAAC,IAAA,GAAA5M,8KAAA,CAAG,WAAO6M,GAAG,EAAE5O,EAAE,EAAE6O,SAAS,EAAEC,SAAS,EAAK;IACvD,IAAIF,GAAG,IAAI,IAAI,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAACH,MAAM,CAACM,IAAI,CAACH,GAAG,CAAC,EAAE;MACtD,MAAMI,MAAM,GAAGC,QAAQ,CAAC1S,aAAa,CAAC,YAAY,CAAC;MACnD,IAAIyS,MAAM,EAAE;QACV,IAAIhP,EAAE,IAAI,IAAI,EAAE;UACdA,EAAE,CAACkP,cAAc,CAAC,CAAC;QACrB;QACA,OAAOF,MAAM,CAACvJ,IAAI,CAACmJ,GAAG,EAAEC,SAAS,EAAEC,SAAS,CAAC;MAC/C;IACF;IACA,OAAO,KAAK;EACd,CAAC;EAAA,gBAXKJ,OAAOA,CAAAS,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAX,IAAA,CAAAY,KAAA,OAAAC,SAAA;EAAA;AAAA,GAWZ", "sources": ["./node_modules/@ionic/core/dist/esm/form-controller-ed77647a.js", "./node_modules/@ionic/core/dist/esm/ion-select_3.entry.js", "./node_modules/@ionic/core/dist/esm/notch-controller-8c9c0e54.js", "./node_modules/@ionic/core/dist/esm/theme-17531cdf.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { h as findItemLabel } from './helpers-3379ba19.js';\n\n/**\n * Creates a controller that tracks whether a form control is using the legacy or modern syntax. This should be removed when the legacy form control syntax is removed.\n *\n * @internal\n * @prop el: The Ionic form component to reference\n */\nconst createLegacyFormController = (el) => {\n  const controlEl = el;\n  let legacyControl;\n  const hasLegacyControl = () => {\n    if (legacyControl === undefined) {\n      /**\n       * Detect if developers are using the legacy form control syntax\n       * so a deprecation warning is logged. This warning can be disabled\n       * by either using the new `label` property or setting `aria-label`\n       * on the control.\n       * Alternatively, components that use a slot for the label\n       * can check to see if the component has slotted text\n       * in the light DOM.\n       */\n      const hasLabelProp = controlEl.label !== undefined || hasLabelSlot(controlEl);\n      const hasAriaLabelAttribute = controlEl.hasAttribute('aria-label') ||\n        // Shadow DOM form controls cannot use aria-labelledby\n        (controlEl.hasAttribute('aria-labelledby') && controlEl.shadowRoot === null);\n      const legacyItemLabel = findItemLabel(controlEl);\n      /**\n       * Developers can manually opt-out of the modern form markup\n       * by setting `legacy=\"true\"` on components.\n       */\n      legacyControl =\n        controlEl.legacy === true || (!hasLabelProp && !hasAriaLabelAttribute && legacyItemLabel !== null);\n    }\n    return legacyControl;\n  };\n  return { hasLegacyControl };\n};\nconst hasLabelSlot = (controlEl) => {\n  const root = controlEl.shadowRoot;\n  if (root === null) {\n    return false;\n  }\n  /**\n   * Components that have a named label slot\n   * also have other slots, so we need to query for\n   * anything that is explicitly passed to slot=\"label\"\n   */\n  if (NAMED_LABEL_SLOT_COMPONENTS.includes(controlEl.tagName) && controlEl.querySelector('[slot=\"label\"]') !== null) {\n    return true;\n  }\n  /**\n   * Components that have an unnamed slot for the label\n   * have no other slots, so we can check the textContent\n   * of the element.\n   */\n  if (UNNAMED_LABEL_SLOT_COMPONENTS.includes(controlEl.tagName) && controlEl.textContent !== '') {\n    return true;\n  }\n  return false;\n};\nconst NAMED_LABEL_SLOT_COMPONENTS = ['ION-RANGE'];\nconst UNNAMED_LABEL_SLOT_COMPONENTS = ['ION-TOGGLE', 'ION-CHECKBOX', 'ION-RADIO'];\n\nexport { createLegacyFormController as c };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement, i as forceUpdate } from './index-2d388930.js';\nimport { c as createLegacyFormController } from './form-controller-ed77647a.js';\nimport { c as createNotchController } from './notch-controller-8c9c0e54.js';\nimport { k as inheritAttributes, f as focusElement, h as findItemLabel, d as renderHiddenInput, e as getAriaLabel } from './helpers-3379ba19.js';\nimport { p as printIonWarning } from './index-595d62c9.js';\nimport { c as popoverController, b as actionSheetController, a as alertController, s as safeCall } from './overlays-8fc6656c.js';\nimport { i as isRTL } from './dir-912e3e13.js';\nimport { h as hostContext, c as createColorClasses, g as getClassMap } from './theme-17531cdf.js';\nimport { w as watchForOptions } from './watch-options-355a920a.js';\nimport { w as chevronExpand, q as caretDownSharp } from './index-ecfc2c9f.js';\nimport { b as getIonMode } from './ionic-global-b3fc28dd.js';\nimport './index-7a14ecec.js';\nimport './framework-delegate-aa433dea.js';\nimport './hardware-back-button-39299f84.js';\n\nconst selectIosCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--placeholder-color:currentColor;--placeholder-opacity:0.6;--background:transparent;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #3880ff);--highlight-color-valid:var(--ion-color-success, #2dd36f);--highlight-color-invalid:var(--ion-color-danger, #eb445a);--highlight-color:var(--highlight-color-focused);display:block;position:relative;font-family:var(--ion-font-family, inherit);white-space:nowrap;cursor:pointer;z-index:2}:host(:not(.legacy-select)){width:100%}:host(.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.legacy-select){-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.in-item.legacy-select){position:static;max-width:45%}:host(.select-disabled){pointer-events:none}:host(.ion-focused) button{border:2px solid #5e9ed6}:host([slot=start]:not(.legacy-select)),:host([slot=end]:not(.legacy-select)){width:auto}.select-placeholder{color:var(--placeholder-color);opacity:var(--placeholder-opacity)}:host(.legacy-select) label{top:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;position:absolute;width:100%;height:100%;border:0;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:none;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;opacity:0}@supports (inset-inline-start: 0){:host(.legacy-select) label{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host(.legacy-select) label{left:0}:host-context([dir=rtl]):host(.legacy-select) label,:host-context([dir=rtl]).legacy-select label{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(.legacy-select) label:dir(rtl){left:unset;right:unset;right:0}}}:host(.legacy-select) label::-moz-focus-inner{border:0}button{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.select-icon{-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0;position:relative}:host(.in-item-color) .select-icon{color:inherit}:host(.select-label-placement-stacked) .select-icon,:host(.select-label-placement-floating) .select-icon{position:absolute;height:100%}:host(.select-ltr.select-label-placement-stacked) .select-icon,:host(.select-ltr.select-label-placement-floating) .select-icon{right:var(--padding-end, 0)}:host(.select-rtl.select-label-placement-stacked) .select-icon,:host(.select-rtl.select-label-placement-floating) .select-icon{left:var(--padding-start, 0)}.select-text{-ms-flex:1;flex:1;min-width:16px;font-size:inherit;text-overflow:ellipsis;white-space:inherit;overflow:hidden}.select-wrapper{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal;cursor:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.select-wrapper .select-placeholder{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}:host(.ion-touched.ion-invalid){--highlight-color:var(--highlight-color-invalid)}:host(.ion-valid){--highlight-color:var(--highlight-color-valid)}.label-text-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text,::slotted([slot=label]){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden,.select-outline-notch-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);overflow:hidden}:host(.select-justify-space-between) .select-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.select-justify-start) .select-wrapper{-ms-flex-pack:start;justify-content:start}:host(.select-justify-end) .select-wrapper{-ms-flex-pack:end;justify-content:end}:host(.select-label-placement-start) .select-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.select-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-end) .select-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.select-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.select-label-placement-stacked) .select-wrapper,:host(.select-label-placement-floating) .select-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-label-placement-floating) .label-text-wrapper{max-width:100%}:host(.select-ltr.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0;-ms-flex-positive:1;flex-grow:1;width:100%}:host(.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}:host(.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:0}:host(.select-expanded.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.ion-focused.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-value.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:1}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-expanded.select-label-placement-floating) .label-text-wrapper,:host(.ion-focused.select-label-placement-floating) .label-text-wrapper,:host(.has-value.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}:host(.legacy-select){--padding-top:10px;--padding-end:8px;--padding-bottom:10px;--padding-start:16px}:host(:not(.legacy-select)){min-height:44px}:host(.select-label-placement-floating),:host(.select-label-placement-stacked){min-height:56px}.select-icon{width:18px;height:18px;color:var(--ion-color-step-650, #595959)}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{width:calc(100% - 18px - 4px)}:host(.select-disabled){opacity:0.3}\";\n\nconst selectMdCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--placeholder-color:currentColor;--placeholder-opacity:0.6;--background:transparent;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #3880ff);--highlight-color-valid:var(--ion-color-success, #2dd36f);--highlight-color-invalid:var(--ion-color-danger, #eb445a);--highlight-color:var(--highlight-color-focused);display:block;position:relative;font-family:var(--ion-font-family, inherit);white-space:nowrap;cursor:pointer;z-index:2}:host(:not(.legacy-select)){width:100%}:host(.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.legacy-select){-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.in-item.legacy-select){position:static;max-width:45%}:host(.select-disabled){pointer-events:none}:host(.ion-focused) button{border:2px solid #5e9ed6}:host([slot=start]:not(.legacy-select)),:host([slot=end]:not(.legacy-select)){width:auto}.select-placeholder{color:var(--placeholder-color);opacity:var(--placeholder-opacity)}:host(.legacy-select) label{top:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;position:absolute;width:100%;height:100%;border:0;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:none;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;opacity:0}@supports (inset-inline-start: 0){:host(.legacy-select) label{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host(.legacy-select) label{left:0}:host-context([dir=rtl]):host(.legacy-select) label,:host-context([dir=rtl]).legacy-select label{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(.legacy-select) label:dir(rtl){left:unset;right:unset;right:0}}}:host(.legacy-select) label::-moz-focus-inner{border:0}button{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.select-icon{-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0;position:relative}:host(.in-item-color) .select-icon{color:inherit}:host(.select-label-placement-stacked) .select-icon,:host(.select-label-placement-floating) .select-icon{position:absolute;height:100%}:host(.select-ltr.select-label-placement-stacked) .select-icon,:host(.select-ltr.select-label-placement-floating) .select-icon{right:var(--padding-end, 0)}:host(.select-rtl.select-label-placement-stacked) .select-icon,:host(.select-rtl.select-label-placement-floating) .select-icon{left:var(--padding-start, 0)}.select-text{-ms-flex:1;flex:1;min-width:16px;font-size:inherit;text-overflow:ellipsis;white-space:inherit;overflow:hidden}.select-wrapper{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal;cursor:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.select-wrapper .select-placeholder{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}:host(.ion-touched.ion-invalid){--highlight-color:var(--highlight-color-invalid)}:host(.ion-valid){--highlight-color:var(--highlight-color-valid)}.label-text-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text,::slotted([slot=label]){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden,.select-outline-notch-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);overflow:hidden}:host(.select-justify-space-between) .select-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.select-justify-start) .select-wrapper{-ms-flex-pack:start;justify-content:start}:host(.select-justify-end) .select-wrapper{-ms-flex-pack:end;justify-content:end}:host(.select-label-placement-start) .select-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.select-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-end) .select-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.select-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.select-label-placement-stacked) .select-wrapper,:host(.select-label-placement-floating) .select-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-label-placement-floating) .label-text-wrapper{max-width:100%}:host(.select-ltr.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0;-ms-flex-positive:1;flex-grow:1;width:100%}:host(.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}:host(.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:0}:host(.select-expanded.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.ion-focused.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-value.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:1}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-expanded.select-label-placement-floating) .label-text-wrapper,:host(.ion-focused.select-label-placement-floating) .label-text-wrapper,:host(.has-value.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}:host(.select-fill-solid){--background:var(--ion-color-step-50, #f2f2f2);--border-color:var(--ion-color-step-500, gray);--border-radius:4px;--padding-start:16px;--padding-end:16px}:host(.select-fill-solid) .select-wrapper{border-bottom:var(--border-width) var(--border-style) var(--border-color)}:host(.has-focus.select-fill-solid.ion-valid),:host(.select-fill-solid.ion-touched.ion-invalid){--border-color:var(--highlight-color)}:host(.select-fill-solid) .select-bottom{border-top:none}@media (any-hover: hover){:host(.select-fill-solid:hover){--background:var(--ion-color-step-100, #e6e6e6);--border-color:var(--ion-color-step-750, #404040)}}:host(.select-fill-solid.select-expanded),:host(.select-fill-solid.ion-focused){--background:var(--ion-color-step-150, #d9d9d9);--border-color:var(--ion-color-step-750, #404040)}:host(.select-fill-solid) .select-wrapper{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0px;border-bottom-left-radius:0px}:host-context([dir=rtl]):host(.select-fill-solid) .select-wrapper,:host-context([dir=rtl]).select-fill-solid .select-wrapper{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0px;border-bottom-left-radius:0px}@supports selector(:dir(rtl)){:host(.select-fill-solid) .select-wrapper:dir(rtl){border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0px;border-bottom-left-radius:0px}}:host(.select-fill-solid.select-label-placement-stacked) .label-text-wrapper,:host(.select-expanded.select-fill-solid.select-label-placement-floating) .label-text-wrapper,:host(.ion-focused.select-fill-solid.select-label-placement-floating) .label-text-wrapper,:host(.has-value.select-fill-solid.select-label-placement-floating) .label-text-wrapper{max-width:calc(100% / 0.75)}:host(.select-fill-outline){--border-color:var(--ion-color-step-300, #b3b3b3);--border-radius:4px;--padding-start:16px;--padding-end:16px}:host(.select-fill-outline.select-shape-round){--border-radius:28px;--padding-start:32px;--padding-end:32px}:host(.has-focus.select-fill-outline.ion-valid),:host(.select-fill-outline.ion-touched.ion-invalid){--border-color:var(--highlight-color)}@media (any-hover: hover){:host(.select-fill-outline:hover){--border-color:var(--ion-color-step-750, #404040)}}:host(.select-fill-outline.select-expanded),:host(.select-fill-outline.ion-focused){--border-width:2px;--border-color:var(--highlight-color)}:host(.select-fill-outline) .select-bottom{border-top:none}:host(.select-fill-outline) .select-wrapper{border-bottom:none}:host(.select-ltr.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-fill-outline.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-fill-outline.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-fill-outline.select-label-placement-floating) .label-text-wrapper{position:absolute;max-width:calc(100% - var(--padding-start) - var(--padding-end))}:host(.select-fill-outline) .label-text-wrapper{position:relative;z-index:1}:host(.select-expanded.select-fill-outline.select-label-placement-floating) .label-text-wrapper,:host(.ion-focused.select-fill-outline.select-label-placement-floating) .label-text-wrapper,:host(.has-value.select-fill-outline.select-label-placement-floating) .label-text-wrapper,:host(.select-fill-outline.select-label-placement-stacked) .label-text-wrapper{-webkit-transform:translateY(-32%) scale(0.75);transform:translateY(-32%) scale(0.75);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;max-width:calc((100% - var(--padding-start) - var(--padding-end) - 8px) / 0.75)}:host(.select-fill-outline.select-label-placement-stacked) select,:host(.select-fill-outline.select-label-placement-floating) select{margin-left:0;margin-right:0;margin-top:6px;margin-bottom:6px}:host(.select-fill-outline) .select-outline-container{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;width:100%;height:100%}:host(.select-fill-outline) .select-outline-start,:host(.select-fill-outline) .select-outline-end{pointer-events:none}:host(.select-fill-outline) .select-outline-start,:host(.select-fill-outline) .select-outline-notch,:host(.select-fill-outline) .select-outline-end{border-top:var(--border-width) var(--border-style) var(--border-color);border-bottom:var(--border-width) var(--border-style) var(--border-color);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.select-fill-outline) .select-outline-notch{max-width:calc(100% - var(--padding-start) - var(--padding-end))}:host(.select-fill-outline) .notch-spacer{-webkit-padding-end:8px;padding-inline-end:8px;font-size:calc(1em * 0.75);opacity:0;pointer-events:none}:host(.select-fill-outline) .select-outline-start{-webkit-border-start:var(--border-width) var(--border-style) var(--border-color);border-inline-start:var(--border-width) var(--border-style) var(--border-color)}:host(.select-ltr.select-fill-outline) .select-outline-start{border-radius:var(--border-radius) 0px 0px var(--border-radius)}:host(.select-rtl.select-fill-outline) .select-outline-start{border-radius:0px var(--border-radius) var(--border-radius) 0px}:host(.select-fill-outline) .select-outline-start{width:calc(var(--padding-start) - 4px)}:host(.select-fill-outline) .select-outline-end{-webkit-border-end:var(--border-width) var(--border-style) var(--border-color);border-inline-end:var(--border-width) var(--border-style) var(--border-color)}:host(.select-ltr.select-fill-outline) .select-outline-end{border-radius:0px var(--border-radius) var(--border-radius) 0px}:host(.select-rtl.select-fill-outline) .select-outline-end{border-radius:var(--border-radius) 0px 0px var(--border-radius)}:host(.select-fill-outline) .select-outline-end{-ms-flex-positive:1;flex-grow:1}:host(.select-expanded.select-fill-outline.select-label-placement-floating) .select-outline-notch,:host(.ion-focused.select-fill-outline.select-label-placement-floating) .select-outline-notch,:host(.has-value.select-fill-outline.select-label-placement-floating) .select-outline-notch,:host(.select-fill-outline.select-label-placement-stacked) .select-outline-notch{border-top:none}:host{--border-width:1px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))))}:host(.legacy-select){--padding-top:10px;--padding-end:0;--padding-bottom:10px;--padding-start:16px}:host(:not(.legacy-select)){min-height:56px}.select-icon{width:13px;-webkit-transition:-webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:-webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:transform 0.15s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);color:var(--ion-color-step-500, gray)}:host(.select-label-placement-floating.select-expanded) .label-text-wrapper,:host(.select-label-placement-floating.ion-focused) .label-text-wrapper,:host(.select-label-placement-stacked.select-expanded) .label-text-wrapper,:host(.select-label-placement-stacked.ion-focused) .label-text-wrapper{color:var(--highlight-color)}:host(.has-focus.select-label-placement-floating.ion-valid) .label-text-wrapper,:host(.select-label-placement-floating.ion-touched.ion-invalid) .label-text-wrapper,:host(.has-focus.select-label-placement-stacked.ion-valid) .label-text-wrapper,:host(.select-label-placement-stacked.ion-touched.ion-invalid) .label-text-wrapper{color:var(--highlight-color)}.select-highlight{bottom:-1px;position:absolute;width:100%;height:2px;-webkit-transform:scale(0);transform:scale(0);-webkit-transition:-webkit-transform 200ms;transition:-webkit-transform 200ms;transition:transform 200ms;transition:transform 200ms, -webkit-transform 200ms;background:var(--highlight-color)}@supports (inset-inline-start: 0){.select-highlight{inset-inline-start:0}}@supports not (inset-inline-start: 0){.select-highlight{left:0}:host-context([dir=rtl]) .select-highlight{left:unset;right:unset;right:0}[dir=rtl] .select-highlight{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.select-highlight:dir(rtl){left:unset;right:unset;right:0}}}:host(.select-expanded) .select-highlight,:host(.ion-focused) .select-highlight{-webkit-transform:scale(1);transform:scale(1)}:host(.in-item) .select-highlight{bottom:0}@supports (inset-inline-start: 0){:host(.in-item) .select-highlight{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host(.in-item) .select-highlight{left:0}:host-context([dir=rtl]):host(.in-item) .select-highlight,:host-context([dir=rtl]).in-item .select-highlight{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(.in-item) .select-highlight:dir(rtl){left:unset;right:unset;right:0}}}:host(.select-expanded:not(.legacy-select):not(.has-expanded-icon)) .select-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host(.select-expanded) .select-wrapper .select-icon,:host(.has-focus.ion-valid) .select-wrapper .select-icon,:host(.ion-touched.ion-invalid) .select-wrapper .select-icon,:host(.ion-focused) .select-wrapper .select-icon{color:var(--highlight-color)}:host-context(.item-label-stacked) .select-icon,:host-context(.item-label-floating:not(.item-fill-outline)) .select-icon,:host-context(.item-label-floating.item-fill-outline){-webkit-transform:translate3d(0,  -9px,  0);transform:translate3d(0,  -9px,  0)}:host-context(.item-has-focus):host(:not(.has-expanded-icon)) .select-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host-context(.item-has-focus.item-label-stacked):host(:not(.has-expanded-icon)) .select-icon,:host-context(.item-has-focus.item-label-floating:not(.item-fill-outline)):host(:not(.has-expanded-icon)) .select-icon{-webkit-transform:translate3d(0,  -9px,  0) rotate(180deg);transform:translate3d(0,  -9px,  0) rotate(180deg)}:host(.select-shape-round){--border-radius:16px}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{width:calc(100% - 13px - 4px)}:host(.select-disabled){opacity:0.38}\";\n\nconst Select = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionCancel = createEvent(this, \"ionCancel\", 7);\n    this.ionDismiss = createEvent(this, \"ionDismiss\", 7);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.ionStyle = createEvent(this, \"ionStyle\", 7);\n    this.inputId = `ion-sel-${selectIds++}`;\n    this.inheritedAttributes = {};\n    // This flag ensures we log the deprecation warning at most once.\n    this.hasLoggedDeprecationWarning = false;\n    this.onClick = (ev) => {\n      this.setFocus();\n      this.open(ev);\n    };\n    this.onFocus = () => {\n      this.ionFocus.emit();\n    };\n    this.onBlur = () => {\n      this.ionBlur.emit();\n    };\n    this.isExpanded = false;\n    this.cancelText = 'Cancel';\n    this.color = undefined;\n    this.compareWith = undefined;\n    this.disabled = false;\n    this.fill = undefined;\n    this.interface = 'alert';\n    this.interfaceOptions = {};\n    this.justify = 'space-between';\n    this.label = undefined;\n    this.labelPlacement = 'start';\n    this.legacy = undefined;\n    this.multiple = false;\n    this.name = this.inputId;\n    this.okText = 'OK';\n    this.placeholder = undefined;\n    this.selectedText = undefined;\n    this.toggleIcon = undefined;\n    this.expandedIcon = undefined;\n    this.shape = undefined;\n    this.value = undefined;\n  }\n  styleChanged() {\n    this.emitStyle();\n  }\n  setValue(value) {\n    this.value = value;\n    this.ionChange.emit({ value });\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAttributes(this.el, ['aria-label']);\n  }\n  async connectedCallback() {\n    const { el } = this;\n    this.legacyFormController = createLegacyFormController(el);\n    this.notchController = createNotchController(el, () => this.notchSpacerEl, () => this.labelSlot);\n    this.updateOverlayOptions();\n    this.emitStyle();\n    this.mutationO = watchForOptions(this.el, 'ion-select-option', async () => {\n      this.updateOverlayOptions();\n      /**\n       * We need to re-render the component\n       * because one of the new ion-select-option\n       * elements may match the value. In this case,\n       * the rendered selected text should be updated.\n       */\n      forceUpdate(this);\n    });\n  }\n  disconnectedCallback() {\n    if (this.mutationO) {\n      this.mutationO.disconnect();\n      this.mutationO = undefined;\n    }\n    if (this.notchController) {\n      this.notchController.destroy();\n      this.notchController = undefined;\n    }\n  }\n  /**\n   * Open the select overlay. The overlay is either an alert, action sheet, or popover,\n   * depending on the `interface` property on the `ion-select`.\n   *\n   * @param event The user interface event that called the open.\n   */\n  async open(event) {\n    if (this.disabled || this.isExpanded) {\n      return undefined;\n    }\n    this.isExpanded = true;\n    const overlay = (this.overlay = await this.createOverlay(event));\n    overlay.onDidDismiss().then(() => {\n      this.overlay = undefined;\n      this.isExpanded = false;\n      this.ionDismiss.emit();\n      this.setFocus();\n    });\n    await overlay.present();\n    // focus selected option for popovers\n    if (this.interface === 'popover') {\n      let indexOfSelected = this.childOpts.map((o) => o.value).indexOf(this.value);\n      indexOfSelected = indexOfSelected > -1 ? indexOfSelected : 0; // default to first option if nothing selected\n      const selectedItem = overlay.querySelector(`.select-interface-option:nth-child(${indexOfSelected + 1})`);\n      if (selectedItem) {\n        focusElement(selectedItem);\n        /**\n         * Browsers such as Firefox do not\n         * correctly delegate focus when manually\n         * focusing an element with delegatesFocus.\n         * We work around this by manually focusing\n         * the interactive element.\n         * ion-radio and ion-checkbox are the only\n         * elements that ion-select-popover uses, so\n         * we only need to worry about those two components\n         * when focusing.\n         */\n        const interactiveEl = selectedItem.querySelector('ion-radio, ion-checkbox');\n        if (interactiveEl) {\n          interactiveEl.focus();\n        }\n      }\n    }\n    return overlay;\n  }\n  createOverlay(ev) {\n    let selectInterface = this.interface;\n    if (selectInterface === 'action-sheet' && this.multiple) {\n      console.warn(`Select interface cannot be \"${selectInterface}\" with a multi-value select. Using the \"alert\" interface instead.`);\n      selectInterface = 'alert';\n    }\n    if (selectInterface === 'popover' && !ev) {\n      console.warn(`Select interface cannot be a \"${selectInterface}\" without passing an event. Using the \"alert\" interface instead.`);\n      selectInterface = 'alert';\n    }\n    if (selectInterface === 'action-sheet') {\n      return this.openActionSheet();\n    }\n    if (selectInterface === 'popover') {\n      return this.openPopover(ev);\n    }\n    return this.openAlert();\n  }\n  updateOverlayOptions() {\n    const overlay = this.overlay;\n    if (!overlay) {\n      return;\n    }\n    const childOpts = this.childOpts;\n    const value = this.value;\n    switch (this.interface) {\n      case 'action-sheet':\n        overlay.buttons = this.createActionSheetButtons(childOpts, value);\n        break;\n      case 'popover':\n        const popover = overlay.querySelector('ion-select-popover');\n        if (popover) {\n          popover.options = this.createPopoverOptions(childOpts, value);\n        }\n        break;\n      case 'alert':\n        const inputType = this.multiple ? 'checkbox' : 'radio';\n        overlay.inputs = this.createAlertInputs(childOpts, inputType, value);\n        break;\n    }\n  }\n  createActionSheetButtons(data, selectValue) {\n    const actionSheetButtons = data.map((option) => {\n      const value = getOptionValue(option);\n      // Remove hydrated before copying over classes\n      const copyClasses = Array.from(option.classList)\n        .filter((cls) => cls !== 'hydrated')\n        .join(' ');\n      const optClass = `${OPTION_CLASS} ${copyClasses}`;\n      return {\n        role: isOptionSelected(selectValue, value, this.compareWith) ? 'selected' : '',\n        text: option.textContent,\n        cssClass: optClass,\n        handler: () => {\n          this.setValue(value);\n        },\n      };\n    });\n    // Add \"cancel\" button\n    actionSheetButtons.push({\n      text: this.cancelText,\n      role: 'cancel',\n      handler: () => {\n        this.ionCancel.emit();\n      },\n    });\n    return actionSheetButtons;\n  }\n  createAlertInputs(data, inputType, selectValue) {\n    const alertInputs = data.map((option) => {\n      const value = getOptionValue(option);\n      // Remove hydrated before copying over classes\n      const copyClasses = Array.from(option.classList)\n        .filter((cls) => cls !== 'hydrated')\n        .join(' ');\n      const optClass = `${OPTION_CLASS} ${copyClasses}`;\n      return {\n        type: inputType,\n        cssClass: optClass,\n        label: option.textContent || '',\n        value,\n        checked: isOptionSelected(selectValue, value, this.compareWith),\n        disabled: option.disabled,\n      };\n    });\n    return alertInputs;\n  }\n  createPopoverOptions(data, selectValue) {\n    const popoverOptions = data.map((option) => {\n      const value = getOptionValue(option);\n      // Remove hydrated before copying over classes\n      const copyClasses = Array.from(option.classList)\n        .filter((cls) => cls !== 'hydrated')\n        .join(' ');\n      const optClass = `${OPTION_CLASS} ${copyClasses}`;\n      return {\n        text: option.textContent || '',\n        cssClass: optClass,\n        value,\n        checked: isOptionSelected(selectValue, value, this.compareWith),\n        disabled: option.disabled,\n        handler: (selected) => {\n          this.setValue(selected);\n          if (!this.multiple) {\n            this.close();\n          }\n        },\n      };\n    });\n    return popoverOptions;\n  }\n  async openPopover(ev) {\n    const { fill, labelPlacement } = this;\n    const interfaceOptions = this.interfaceOptions;\n    const mode = getIonMode(this);\n    const showBackdrop = mode === 'md' ? false : true;\n    const multiple = this.multiple;\n    const value = this.value;\n    let event = ev;\n    let size = 'auto';\n    if (this.legacyFormController.hasLegacyControl()) {\n      const item = this.el.closest('ion-item');\n      // If the select is inside of an item containing a floating\n      // or stacked label then the popover should take up the\n      // full width of the item when it presents\n      if (item && (item.classList.contains('item-label-floating') || item.classList.contains('item-label-stacked'))) {\n        event = Object.assign(Object.assign({}, ev), { detail: {\n            ionShadowTarget: item,\n          } });\n        size = 'cover';\n      }\n    }\n    else {\n      const hasFloatingOrStackedLabel = labelPlacement === 'floating' || labelPlacement === 'stacked';\n      /**\n       * The popover should take up the full width\n       * when using a fill in MD mode or if the\n       * label is floating/stacked.\n       */\n      if (hasFloatingOrStackedLabel || (mode === 'md' && fill !== undefined)) {\n        size = 'cover';\n        /**\n         * Otherwise the popover\n         * should be positioned relative\n         * to the native element.\n         */\n      }\n      else {\n        event = Object.assign(Object.assign({}, ev), { detail: {\n            ionShadowTarget: this.nativeWrapperEl,\n          } });\n      }\n    }\n    const popoverOpts = Object.assign(Object.assign({ mode,\n      event, alignment: 'center', size,\n      showBackdrop }, interfaceOptions), { component: 'ion-select-popover', cssClass: ['select-popover', interfaceOptions.cssClass], componentProps: {\n        header: interfaceOptions.header,\n        subHeader: interfaceOptions.subHeader,\n        message: interfaceOptions.message,\n        multiple,\n        value,\n        options: this.createPopoverOptions(this.childOpts, value),\n      } });\n    return popoverController.create(popoverOpts);\n  }\n  async openActionSheet() {\n    const mode = getIonMode(this);\n    const interfaceOptions = this.interfaceOptions;\n    const actionSheetOpts = Object.assign(Object.assign({ mode }, interfaceOptions), { buttons: this.createActionSheetButtons(this.childOpts, this.value), cssClass: ['select-action-sheet', interfaceOptions.cssClass] });\n    return actionSheetController.create(actionSheetOpts);\n  }\n  async openAlert() {\n    /**\n     * TODO FW-3194\n     * Remove legacyFormController logic.\n     * Remove label and labelText vars\n     * Pass `this.labelText` instead of `labelText`\n     * when setting the header.\n     */\n    let label;\n    let labelText;\n    if (this.legacyFormController.hasLegacyControl()) {\n      label = this.getLabel();\n      labelText = label ? label.textContent : null;\n    }\n    else {\n      labelText = this.labelText;\n    }\n    const interfaceOptions = this.interfaceOptions;\n    const inputType = this.multiple ? 'checkbox' : 'radio';\n    const mode = getIonMode(this);\n    const alertOpts = Object.assign(Object.assign({ mode }, interfaceOptions), { header: interfaceOptions.header ? interfaceOptions.header : labelText, inputs: this.createAlertInputs(this.childOpts, inputType, this.value), buttons: [\n        {\n          text: this.cancelText,\n          role: 'cancel',\n          handler: () => {\n            this.ionCancel.emit();\n          },\n        },\n        {\n          text: this.okText,\n          handler: (selectedValues) => {\n            this.setValue(selectedValues);\n          },\n        },\n      ], cssClass: [\n        'select-alert',\n        interfaceOptions.cssClass,\n        this.multiple ? 'multiple-select-alert' : 'single-select-alert',\n      ] });\n    return alertController.create(alertOpts);\n  }\n  /**\n   * Close the select interface.\n   */\n  close() {\n    if (!this.overlay) {\n      return Promise.resolve(false);\n    }\n    return this.overlay.dismiss();\n  }\n  // TODO FW-3194 Remove this\n  getLabel() {\n    return findItemLabel(this.el);\n  }\n  hasValue() {\n    return this.getText() !== '';\n  }\n  get childOpts() {\n    return Array.from(this.el.querySelectorAll('ion-select-option'));\n  }\n  /**\n   * Returns any plaintext associated with\n   * the label (either prop or slot).\n   * Note: This will not return any custom\n   * HTML. Use the `hasLabel` getter if you\n   * want to know if any slotted label content\n   * was passed.\n   */\n  get labelText() {\n    const { label } = this;\n    if (label !== undefined) {\n      return label;\n    }\n    const { labelSlot } = this;\n    if (labelSlot !== null) {\n      return labelSlot.textContent;\n    }\n    return;\n  }\n  getText() {\n    const selectedText = this.selectedText;\n    if (selectedText != null && selectedText !== '') {\n      return selectedText;\n    }\n    return generateText(this.childOpts, this.value, this.compareWith);\n  }\n  setFocus() {\n    if (this.focusEl) {\n      this.focusEl.focus();\n    }\n  }\n  emitStyle() {\n    const { disabled } = this;\n    const style = {\n      'interactive-disabled': disabled,\n    };\n    if (this.legacyFormController.hasLegacyControl()) {\n      style['interactive'] = true;\n      style['select'] = true;\n      style['select-disabled'] = disabled;\n      style['has-placeholder'] = this.placeholder !== undefined;\n      style['has-value'] = this.hasValue();\n      style['has-focus'] = this.isExpanded;\n    }\n    this.ionStyle.emit(style);\n  }\n  renderLabel() {\n    const { label } = this;\n    return (h(\"div\", { class: {\n        'label-text-wrapper': true,\n        'label-text-wrapper-hidden': !this.hasLabel,\n      }, part: \"label\" }, label === undefined ? h(\"slot\", { name: \"label\" }) : h(\"div\", { class: \"label-text\" }, label)));\n  }\n  componentDidRender() {\n    var _a;\n    (_a = this.notchController) === null || _a === void 0 ? void 0 : _a.calculateNotchWidth();\n  }\n  /**\n   * Gets any content passed into the `label` slot,\n   * not the <slot> definition.\n   */\n  get labelSlot() {\n    return this.el.querySelector('[slot=\"label\"]');\n  }\n  /**\n   * Returns `true` if label content is provided\n   * either by a prop or a content. If you want\n   * to get the plaintext value of the label use\n   * the `labelText` getter instead.\n   */\n  get hasLabel() {\n    return this.label !== undefined || this.labelSlot !== null;\n  }\n  /**\n   * Renders the border container\n   * when fill=\"outline\".\n   */\n  renderLabelContainer() {\n    const mode = getIonMode(this);\n    const hasOutlineFill = mode === 'md' && this.fill === 'outline';\n    if (hasOutlineFill) {\n      /**\n       * The outline fill has a special outline\n       * that appears around the select and the label.\n       * Certain stacked and floating label placements cause the\n       * label to translate up and create a \"cut out\"\n       * inside of that border by using the notch-spacer element.\n       */\n      return [\n        h(\"div\", { class: \"select-outline-container\" }, h(\"div\", { class: \"select-outline-start\" }), h(\"div\", { class: {\n            'select-outline-notch': true,\n            'select-outline-notch-hidden': !this.hasLabel,\n          } }, h(\"div\", { class: \"notch-spacer\", \"aria-hidden\": \"true\", ref: (el) => (this.notchSpacerEl = el) }, this.label)), h(\"div\", { class: \"select-outline-end\" })),\n        this.renderLabel(),\n      ];\n    }\n    /**\n     * If not using the outline style,\n     * we can render just the label.\n     */\n    return this.renderLabel();\n  }\n  renderSelect() {\n    const { disabled, el, isExpanded, expandedIcon, labelPlacement, justify, placeholder, fill, shape, name, value } = this;\n    const mode = getIonMode(this);\n    const hasFloatingOrStackedLabel = labelPlacement === 'floating' || labelPlacement === 'stacked';\n    const justifyEnabled = !hasFloatingOrStackedLabel;\n    const rtl = isRTL(el) ? 'rtl' : 'ltr';\n    const inItem = hostContext('ion-item', this.el);\n    const shouldRenderHighlight = mode === 'md' && fill !== 'outline' && !inItem;\n    renderHiddenInput(true, el, name, parseValue(value), disabled);\n    return (h(Host, { onClick: this.onClick, class: createColorClasses(this.color, {\n        [mode]: true,\n        'in-item': inItem,\n        'in-item-color': hostContext('ion-item.ion-color', el),\n        'select-disabled': disabled,\n        'select-expanded': isExpanded,\n        'has-expanded-icon': expandedIcon !== undefined,\n        'has-value': this.hasValue(),\n        'has-placeholder': placeholder !== undefined,\n        'ion-focusable': true,\n        [`select-${rtl}`]: true,\n        [`select-fill-${fill}`]: fill !== undefined,\n        [`select-justify-${justify}`]: justifyEnabled,\n        [`select-shape-${shape}`]: shape !== undefined,\n        [`select-label-placement-${labelPlacement}`]: true,\n      }) }, h(\"label\", { class: \"select-wrapper\", id: \"select-label\" }, this.renderLabelContainer(), h(\"div\", { class: \"native-wrapper\", ref: (el) => (this.nativeWrapperEl = el), part: \"container\" }, this.renderSelectText(), !hasFloatingOrStackedLabel && this.renderSelectIcon(), this.renderListbox()), hasFloatingOrStackedLabel && this.renderSelectIcon(), shouldRenderHighlight && h(\"div\", { class: \"select-highlight\" }))));\n  }\n  // TODO FW-3194 - Remove this\n  renderLegacySelect() {\n    if (!this.hasLoggedDeprecationWarning) {\n      printIonWarning(`ion-select now requires providing a label with either the \"label\" property or the \"aria-label\" attribute. To migrate, remove any usage of \"ion-label\" and pass the label text to either the \"label\" property or the \"aria-label\" attribute.\n\nExample: <ion-select label=\"Favorite Color\">...</ion-select>\nExample with aria-label: <ion-select aria-label=\"Favorite Color\">...</ion-select>\n\nDevelopers can use the \"legacy\" property to continue using the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.`, this.el);\n      if (this.legacy) {\n        printIonWarning(`ion-select is being used with the \"legacy\" property enabled which will forcibly enable the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.\n    Developers can dismiss this warning by removing their usage of the \"legacy\" property and using the new select syntax.`, this.el);\n      }\n      this.hasLoggedDeprecationWarning = true;\n    }\n    const { disabled, el, inputId, isExpanded, expandedIcon, name, placeholder, value } = this;\n    const mode = getIonMode(this);\n    const { labelText, labelId } = getAriaLabel(el, inputId);\n    renderHiddenInput(true, el, name, parseValue(value), disabled);\n    const displayValue = this.getText();\n    let selectText = displayValue;\n    if (selectText === '' && placeholder !== undefined) {\n      selectText = placeholder;\n    }\n    // If there is a label then we need to concatenate it with the\n    // current value (or placeholder) and a comma so it separates\n    // nicely when the screen reader announces it, otherwise just\n    // announce the value / placeholder\n    const displayLabel = labelText !== undefined ? (selectText !== '' ? `${selectText}, ${labelText}` : labelText) : selectText;\n    return (h(Host, { onClick: this.onClick, role: \"button\", \"aria-haspopup\": \"listbox\", \"aria-disabled\": disabled ? 'true' : null, \"aria-label\": displayLabel, class: {\n        [mode]: true,\n        'in-item': hostContext('ion-item', el),\n        'in-item-color': hostContext('ion-item.ion-color', el),\n        'select-disabled': disabled,\n        'select-expanded': isExpanded,\n        'has-expanded-icon': expandedIcon !== undefined,\n        'legacy-select': true,\n      } }, this.renderSelectText(), this.renderSelectIcon(), h(\"label\", { id: labelId }, displayLabel), this.renderListbox()));\n  }\n  /**\n   * Renders either the placeholder\n   * or the selected values based on\n   * the state of the select.\n   */\n  renderSelectText() {\n    const { placeholder } = this;\n    const displayValue = this.getText();\n    let addPlaceholderClass = false;\n    let selectText = displayValue;\n    if (selectText === '' && placeholder !== undefined) {\n      selectText = placeholder;\n      addPlaceholderClass = true;\n    }\n    const selectTextClasses = {\n      'select-text': true,\n      'select-placeholder': addPlaceholderClass,\n    };\n    const textPart = addPlaceholderClass ? 'placeholder' : 'text';\n    return (h(\"div\", { \"aria-hidden\": \"true\", class: selectTextClasses, part: textPart }, selectText));\n  }\n  /**\n   * Renders the chevron icon\n   * next to the select text.\n   */\n  renderSelectIcon() {\n    const mode = getIonMode(this);\n    const { isExpanded, toggleIcon, expandedIcon } = this;\n    let icon;\n    if (isExpanded && expandedIcon !== undefined) {\n      icon = expandedIcon;\n    }\n    else {\n      const defaultIcon = mode === 'ios' ? chevronExpand : caretDownSharp;\n      icon = toggleIcon !== null && toggleIcon !== void 0 ? toggleIcon : defaultIcon;\n    }\n    return h(\"ion-icon\", { class: \"select-icon\", part: \"icon\", \"aria-hidden\": \"true\", icon: icon });\n  }\n  get ariaLabel() {\n    var _a, _b;\n    const { placeholder, el, inputId, inheritedAttributes } = this;\n    const displayValue = this.getText();\n    const { labelText } = getAriaLabel(el, inputId);\n    const definedLabel = (_b = (_a = this.labelText) !== null && _a !== void 0 ? _a : inheritedAttributes['aria-label']) !== null && _b !== void 0 ? _b : labelText;\n    /**\n     * If developer has specified a placeholder\n     * and there is nothing selected, the selectText\n     * should have the placeholder value.\n     */\n    let renderedLabel = displayValue;\n    if (renderedLabel === '' && placeholder !== undefined) {\n      renderedLabel = placeholder;\n    }\n    /**\n     * If there is a developer-defined label,\n     * then we need to concatenate the developer label\n     * string with the current current value.\n     * The label for the control should be read\n     * before the values of the control.\n     */\n    if (definedLabel !== undefined) {\n      renderedLabel = renderedLabel === '' ? definedLabel : `${definedLabel}, ${renderedLabel}`;\n    }\n    return renderedLabel;\n  }\n  renderListbox() {\n    const { disabled, inputId, isExpanded } = this;\n    return (h(\"button\", { disabled: disabled, id: inputId, \"aria-label\": this.ariaLabel, \"aria-haspopup\": \"listbox\", \"aria-expanded\": `${isExpanded}`, onFocus: this.onFocus, onBlur: this.onBlur, ref: (focusEl) => (this.focusEl = focusEl) }));\n  }\n  render() {\n    const { legacyFormController } = this;\n    return legacyFormController.hasLegacyControl() ? this.renderLegacySelect() : this.renderSelect();\n  }\n  get el() { return getElement(this); }\n  static get watchers() { return {\n    \"disabled\": [\"styleChanged\"],\n    \"isExpanded\": [\"styleChanged\"],\n    \"placeholder\": [\"styleChanged\"],\n    \"value\": [\"styleChanged\"]\n  }; }\n};\nconst isOptionSelected = (currentValue, compareValue, compareWith) => {\n  if (currentValue === undefined) {\n    return false;\n  }\n  if (Array.isArray(currentValue)) {\n    return currentValue.some((val) => compareOptions(val, compareValue, compareWith));\n  }\n  else {\n    return compareOptions(currentValue, compareValue, compareWith);\n  }\n};\nconst getOptionValue = (el) => {\n  const value = el.value;\n  return value === undefined ? el.textContent || '' : value;\n};\nconst parseValue = (value) => {\n  if (value == null) {\n    return undefined;\n  }\n  if (Array.isArray(value)) {\n    return value.join(',');\n  }\n  return value.toString();\n};\nconst compareOptions = (currentValue, compareValue, compareWith) => {\n  if (typeof compareWith === 'function') {\n    return compareWith(currentValue, compareValue);\n  }\n  else if (typeof compareWith === 'string') {\n    return currentValue[compareWith] === compareValue[compareWith];\n  }\n  else {\n    return Array.isArray(compareValue) ? compareValue.includes(currentValue) : currentValue === compareValue;\n  }\n};\nconst generateText = (opts, value, compareWith) => {\n  if (value === undefined) {\n    return '';\n  }\n  if (Array.isArray(value)) {\n    return value\n      .map((v) => textForValue(opts, v, compareWith))\n      .filter((opt) => opt !== null)\n      .join(', ');\n  }\n  else {\n    return textForValue(opts, value, compareWith) || '';\n  }\n};\nconst textForValue = (opts, value, compareWith) => {\n  const selectOpt = opts.find((opt) => {\n    return compareOptions(value, getOptionValue(opt), compareWith);\n  });\n  return selectOpt ? selectOpt.textContent : null;\n};\nlet selectIds = 0;\nconst OPTION_CLASS = 'select-interface-option';\nSelect.style = {\n  ios: selectIosCss,\n  md: selectMdCss\n};\n\nconst selectOptionCss = \":host{display:none}\";\n\nconst SelectOption = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.inputId = `ion-selopt-${selectOptionIds++}`;\n    this.disabled = false;\n    this.value = undefined;\n  }\n  render() {\n    return h(Host, { role: \"option\", id: this.inputId, class: getIonMode(this) });\n  }\n  get el() { return getElement(this); }\n};\nlet selectOptionIds = 0;\nSelectOption.style = selectOptionCss;\n\nconst selectPopoverIosCss = \".sc-ion-select-popover-ios-h ion-list.sc-ion-select-popover-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list-header.sc-ion-select-popover-ios,ion-label.sc-ion-select-popover-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}\";\n\nconst selectPopoverMdCss = \".sc-ion-select-popover-md-h ion-list.sc-ion-select-popover-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list-header.sc-ion-select-popover-md,ion-label.sc-ion-select-popover-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list.sc-ion-select-popover-md ion-radio.sc-ion-select-popover-md::part(container){opacity:0}ion-item.sc-ion-select-popover-md{--inner-border-width:0}.item-radio-checked.sc-ion-select-popover-md{--background:rgba(var(--ion-color-primary-rgb, 56, 128, 255), 0.08);--background-focused:var(--ion-color-primary, #3880ff);--background-focused-opacity:0.2;--background-hover:var(--ion-color-primary, #3880ff);--background-hover-opacity:0.12}.item-checkbox-checked.sc-ion-select-popover-md{--background-activated:var(--ion-item-color, var(--ion-text-color, #000));--background-focused:var(--ion-item-color, var(--ion-text-color, #000));--background-hover:var(--ion-item-color, var(--ion-text-color, #000));--color:var(--ion-color-primary, #3880ff)}\";\n\nconst SelectPopover = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.header = undefined;\n    this.subHeader = undefined;\n    this.message = undefined;\n    this.multiple = undefined;\n    this.options = [];\n  }\n  findOptionFromEvent(ev) {\n    const { options } = this;\n    return options.find((o) => o.value === ev.target.value);\n  }\n  /**\n   * When an option is selected we need to get the value(s)\n   * of the selected option(s) and return it in the option\n   * handler\n   */\n  callOptionHandler(ev) {\n    const option = this.findOptionFromEvent(ev);\n    const values = this.getValues(ev);\n    if (option === null || option === void 0 ? void 0 : option.handler) {\n      safeCall(option.handler, values);\n    }\n  }\n  /**\n   * Dismisses the host popover that the `ion-select-popover`\n   * is rendered within.\n   */\n  dismissParentPopover() {\n    const popover = this.el.closest('ion-popover');\n    if (popover) {\n      popover.dismiss();\n    }\n  }\n  setChecked(ev) {\n    const { multiple } = this;\n    const option = this.findOptionFromEvent(ev);\n    // this is a popover with checkboxes (multiple value select)\n    // we need to set the checked value for this option\n    if (multiple && option) {\n      option.checked = ev.detail.checked;\n    }\n  }\n  getValues(ev) {\n    const { multiple, options } = this;\n    if (multiple) {\n      // this is a popover with checkboxes (multiple value select)\n      // return an array of all the checked values\n      return options.filter((o) => o.checked).map((o) => o.value);\n    }\n    // this is a popover with radio buttons (single value select)\n    // return the value that was clicked, otherwise undefined\n    const option = this.findOptionFromEvent(ev);\n    return option ? option.value : undefined;\n  }\n  renderOptions(options) {\n    const { multiple } = this;\n    switch (multiple) {\n      case true:\n        return this.renderCheckboxOptions(options);\n      default:\n        return this.renderRadioOptions(options);\n    }\n  }\n  renderCheckboxOptions(options) {\n    return options.map((option) => (h(\"ion-item\", { class: Object.assign({\n        // TODO FW-4784\n        'item-checkbox-checked': option.checked\n      }, getClassMap(option.cssClass)) }, h(\"ion-checkbox\", { value: option.value, disabled: option.disabled, checked: option.checked, justify: \"start\", labelPlacement: \"end\", onIonChange: (ev) => {\n        this.setChecked(ev);\n        this.callOptionHandler(ev);\n        // TODO FW-4784\n        forceUpdate(this);\n      } }, option.text))));\n  }\n  renderRadioOptions(options) {\n    const checked = options.filter((o) => o.checked).map((o) => o.value)[0];\n    return (h(\"ion-radio-group\", { value: checked, onIonChange: (ev) => this.callOptionHandler(ev) }, options.map((option) => (h(\"ion-item\", { class: Object.assign({\n        // TODO FW-4784\n        'item-radio-checked': option.value === checked\n      }, getClassMap(option.cssClass)) }, h(\"ion-radio\", { value: option.value, disabled: option.disabled, onClick: () => this.dismissParentPopover(), onKeyUp: (ev) => {\n        if (ev.key === ' ') {\n          /**\n           * Selecting a radio option with keyboard navigation,\n           * either through the Enter or Space keys, should\n           * dismiss the popover.\n           */\n          this.dismissParentPopover();\n        }\n      } }, option.text))))));\n  }\n  render() {\n    const { header, message, options, subHeader } = this;\n    const hasSubHeaderOrMessage = subHeader !== undefined || message !== undefined;\n    return (h(Host, { class: getIonMode(this) }, h(\"ion-list\", null, header !== undefined && h(\"ion-list-header\", null, header), hasSubHeaderOrMessage && (h(\"ion-item\", null, h(\"ion-label\", { class: \"ion-text-wrap\" }, subHeader !== undefined && h(\"h3\", null, subHeader), message !== undefined && h(\"p\", null, message)))), this.renderOptions(options))));\n  }\n  get el() { return getElement(this); }\n};\nSelectPopover.style = {\n  ios: selectPopoverIosCss,\n  md: selectPopoverMdCss\n};\n\nexport { Select as ion_select, SelectOption as ion_select_option, SelectPopover as ion_select_popover };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index-7a14ecec.js';\nimport { r as raf } from './helpers-3379ba19.js';\n\n/**\n * A utility to calculate the size of an outline notch\n * width relative to the content passed. This is used in\n * components such as `ion-select` with `fill=\"outline\"`\n * where we need to pass slotted HTML content. This is not\n * needed when rendering plaintext content because we can\n * render the plaintext again hidden with `opacity: 0` inside\n * of the notch. As a result we can rely on the intrinsic size\n * of the element to correctly compute the notch width. We\n * cannot do this with slotted content because we cannot project\n * it into 2 places at once.\n *\n * @internal\n * @param el: The host element\n * @param getNotchSpacerEl: A function that returns a reference to the notch spacer element inside of the component template.\n * @param getLabelSlot: A function that returns a reference to the slotted content.\n */\nconst createNotchController = (el, getNotchSpacerEl, getLabelSlot) => {\n  let notchVisibilityIO;\n  const needsExplicitNotchWidth = () => {\n    const notchSpacerEl = getNotchSpacerEl();\n    if (\n    /**\n     * If the notch is not being used\n     * then we do not need to set the notch width.\n     */\n    notchSpacerEl === undefined ||\n      /**\n       * If either the label property is being\n       * used or the label slot is not defined,\n       * then we do not need to estimate the notch width.\n       */\n      el.label !== undefined ||\n      getLabelSlot() === null) {\n      return false;\n    }\n    return true;\n  };\n  const calculateNotchWidth = () => {\n    if (needsExplicitNotchWidth()) {\n      /**\n       * Run this the frame after\n       * the browser has re-painted the host element.\n       * Otherwise, the label element may have a width\n       * of 0 and the IntersectionObserver will be used.\n       */\n      raf(() => {\n        setNotchWidth();\n      });\n    }\n  };\n  /**\n   * When using a label prop we can render\n   * the label value inside of the notch and\n   * let the browser calculate the size of the notch.\n   * However, we cannot render the label slot in multiple\n   * places so we need to manually calculate the notch dimension\n   * based on the size of the slotted content.\n   *\n   * This function should only be used to set the notch width\n   * on slotted label content. The notch width for label prop\n   * content is automatically calculated based on the\n   * intrinsic size of the label text.\n   */\n  const setNotchWidth = () => {\n    const notchSpacerEl = getNotchSpacerEl();\n    if (notchSpacerEl === undefined) {\n      return;\n    }\n    if (!needsExplicitNotchWidth()) {\n      notchSpacerEl.style.removeProperty('width');\n      return;\n    }\n    const width = getLabelSlot().scrollWidth;\n    if (\n    /**\n     * If the computed width of the label is 0\n     * and notchSpacerEl's offsetParent is null\n     * then that means the element is hidden.\n     * As a result, we need to wait for the element\n     * to become visible before setting the notch width.\n     *\n     * We do not check el.offsetParent because\n     * that can be null if the host element has\n     * position: fixed applied to it.\n     * notchSpacerEl does not have position: fixed.\n     */\n    width === 0 &&\n      notchSpacerEl.offsetParent === null &&\n      win !== undefined &&\n      'IntersectionObserver' in win) {\n      /**\n       * If there is an IO already attached\n       * then that will update the notch\n       * once the element becomes visible.\n       * As a result, there is no need to create\n       * another one.\n       */\n      if (notchVisibilityIO !== undefined) {\n        return;\n      }\n      const io = (notchVisibilityIO = new IntersectionObserver((ev) => {\n        /**\n         * If the element is visible then we\n         * can try setting the notch width again.\n         */\n        if (ev[0].intersectionRatio === 1) {\n          setNotchWidth();\n          io.disconnect();\n          notchVisibilityIO = undefined;\n        }\n      }, \n      /**\n       * Set the root to be the host element\n       * This causes the IO callback\n       * to be fired in WebKit as soon as the element\n       * is visible. If we used the default root value\n       * then WebKit would only fire the IO callback\n       * after any animations (such as a modal transition)\n       * finished, and there would potentially be a flicker.\n       */\n      { threshold: 0.01, root: el }));\n      io.observe(notchSpacerEl);\n      return;\n    }\n    /**\n     * If the element is visible then we can set the notch width.\n     * The notch is only visible when the label is scaled,\n     * which is why we multiply the width by 0.75 as this is\n     * the same amount the label element is scaled by in the host CSS.\n     * (See $form-control-label-stacked-scale in ionic.globals.scss).\n     */\n    notchSpacerEl.style.setProperty('width', `${width * 0.75}px`);\n  };\n  const destroy = () => {\n    if (notchVisibilityIO) {\n      notchVisibilityIO.disconnect();\n      notchVisibilityIO = undefined;\n    }\n  };\n  return {\n    calculateNotchWidth,\n    destroy,\n  };\n};\n\nexport { createNotchController as c };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst hostContext = (selector, el) => {\n  return el.closest(selector) !== null;\n};\n/**\n * Create the mode and color classes for the component based on the classes passed in\n */\nconst createColorClasses = (color, cssClassMap) => {\n  return typeof color === 'string' && color.length > 0\n    ? Object.assign({ 'ion-color': true, [`ion-color-${color}`]: true }, cssClassMap) : cssClassMap;\n};\nconst getClassList = (classes) => {\n  if (classes !== undefined) {\n    const array = Array.isArray(classes) ? classes : classes.split(' ');\n    return array\n      .filter((c) => c != null)\n      .map((c) => c.trim())\n      .filter((c) => c !== '');\n  }\n  return [];\n};\nconst getClassMap = (classes) => {\n  const map = {};\n  getClassList(classes).forEach((c) => (map[c] = true));\n  return map;\n};\nconst SCHEME = /^[a-z][a-z0-9+\\-.]*:/;\nconst openURL = async (url, ev, direction, animation) => {\n  if (url != null && url[0] !== '#' && !SCHEME.test(url)) {\n    const router = document.querySelector('ion-router');\n    if (router) {\n      if (ev != null) {\n        ev.preventDefault();\n      }\n      return router.push(url, direction, animation);\n    }\n  }\n  return false;\n};\n\nexport { createColorClasses as c, getClassMap as g, hostContext as h, openURL as o };\n"], "names": ["h", "findItemLabel", "createLegacyFormController", "el", "controlEl", "legacyControl", "hasLegacyControl", "undefined", "hasLabelProp", "label", "hasLabelSlot", "hasAriaLabelAttribute", "hasAttribute", "shadowRoot", "legacyItemLabel", "legacy", "root", "NAMED_LABEL_SLOT_COMPONENTS", "includes", "tagName", "querySelector", "UNNAMED_LABEL_SLOT_COMPONENTS", "textContent", "c", "r", "registerInstance", "d", "createEvent", "H", "Host", "f", "getElement", "i", "forceUpdate", "createNotchController", "k", "inheritAttributes", "focusElement", "renderHiddenInput", "e", "getAriaLabel", "p", "printIonWarning", "popoverController", "b", "actionSheetController", "a", "alertController", "s", "safeCall", "isRTL", "hostContext", "createColorClasses", "g", "getClassMap", "w", "watchForOptions", "chevronExpand", "q", "caretDownSharp", "getIonMode", "selectIosCss", "selectMdCss", "Select", "constructor", "hostRef", "ionChange", "ionCancel", "ion<PERSON><PERSON><PERSON>", "ionFocus", "ionBlur", "ionStyle", "inputId", "selectIds", "inheritedAttributes", "hasLoggedDeprecationWarning", "onClick", "ev", "setFocus", "open", "onFocus", "emit", "onBlur", "isExpanded", "cancelText", "color", "compareWith", "disabled", "fill", "interface", "interfaceOptions", "justify", "labelPlacement", "multiple", "name", "okText", "placeholder", "selectedText", "toggleIcon", "expandedIcon", "shape", "value", "styleChanged", "emitStyle", "setValue", "componentWillLoad", "connectedCallback", "_this", "_asyncToGenerator", "legacyFormController", "notchController", "notchSpacerEl", "labelSlot", "updateOverlayOptions", "mutationO", "disconnectedCallback", "disconnect", "destroy", "event", "_this2", "overlay", "createOverlay", "onDid<PERSON><PERSON><PERSON>", "then", "present", "indexOfSelected", "childOpts", "map", "o", "indexOf", "selectedItem", "interactiveEl", "focus", "selectInterface", "console", "warn", "openActionSheet", "openPopover", "openAlert", "buttons", "createActionSheetButtons", "popover", "options", "createPopoverOptions", "inputType", "inputs", "createAlertInputs", "data", "selectValue", "actionSheetButtons", "option", "getOptionValue", "copyClasses", "Array", "from", "classList", "filter", "cls", "join", "optClass", "OPTION_CLASS", "role", "isOptionSelected", "text", "cssClass", "handler", "push", "alertInputs", "type", "checked", "popoverOptions", "selected", "close", "_this3", "mode", "showBackdrop", "size", "item", "closest", "contains", "Object", "assign", "detail", "ionShadowTarget", "hasFloatingOrStackedLabel", "nativeWrapperEl", "popoverOpts", "alignment", "component", "componentProps", "header", "subHeader", "message", "create", "_this4", "actionSheetOpts", "_this5", "labelText", "get<PERSON><PERSON><PERSON>", "alertOpts", "<PERSON><PERSON><PERSON><PERSON>", "Promise", "resolve", "dismiss", "hasValue", "getText", "querySelectorAll", "generateText", "focusEl", "style", "renderLabel", "class", "<PERSON><PERSON><PERSON><PERSON>", "part", "componentDidRender", "_a", "calculateNotchWidth", "renderLabelContainer", "hasOutlineFill", "ref", "renderSelect", "justifyEnabled", "rtl", "inItem", "should<PERSON>ender<PERSON>ighlight", "parseValue", "id", "renderSelectText", "renderSelectIcon", "renderListbox", "renderLegacySelect", "labelId", "displayValue", "selectText", "displayLabel", "addPlaceholderClass", "selectTextClasses", "textPart", "icon", "defaultIcon", "aria<PERSON><PERSON><PERSON>", "_b", "defined<PERSON>abel", "<PERSON><PERSON><PERSON><PERSON>", "render", "watchers", "currentValue", "compareValue", "isArray", "some", "val", "compareOptions", "toString", "opts", "v", "textForValue", "opt", "selectOpt", "find", "ios", "md", "selectOptionCss", "SelectOption", "selectOptionIds", "selectPopoverIosCss", "selectPopoverMdCss", "SelectPopover", "findOptionFromEvent", "target", "callOptionHandler", "values", "getV<PERSON>ues", "dismissParentPopover", "setChecked", "renderOptions", "renderCheckboxOptions", "renderRadioOptions", "onIonChange", "onKeyUp", "key", "hasSubHeaderOrMessage", "ion_select", "ion_select_option", "ion_select_popover", "win", "raf", "getNotchSpacerEl", "getLabelSlot", "notchVisibilityIO", "needsExplicitNotchWidth", "set<PERSON><PERSON>chWidth", "removeProperty", "width", "scrollWidth", "offsetParent", "io", "IntersectionObserver", "intersectionRatio", "threshold", "observe", "setProperty", "selector", "cssClassMap", "length", "getClassList", "classes", "array", "split", "trim", "for<PERSON>ach", "SCHEME", "openURL", "_ref", "url", "direction", "animation", "test", "router", "document", "preventDefault", "_x", "_x2", "_x3", "_x4", "apply", "arguments"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0, 1, 2, 3]}