{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-route_4_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC6G;AAChC;AACD;AACf;AAE7D,MAAMkB,KAAK,GAAG,MAAM;EAClBC,WAAWA,CAACC,OAAO,EAAE;IACnBnB,qDAAgB,CAAC,IAAI,EAAEmB,OAAO,CAAC;IAC/B,IAAI,CAACC,mBAAmB,GAAGlB,qDAAW,CAAC,IAAI,EAAE,qBAAqB,EAAE,CAAC,CAAC;IACtE,IAAI,CAACmB,GAAG,GAAG,EAAE;IACb,IAAI,CAACC,SAAS,GAAGC,SAAS;IAC1B,IAAI,CAACC,cAAc,GAAGD,SAAS;IAC/B,IAAI,CAACE,WAAW,GAAGF,SAAS;IAC5B,IAAI,CAACG,WAAW,GAAGH,SAAS;EAC9B;EACAI,QAAQA,CAACC,QAAQ,EAAE;IACjB,IAAI,CAACR,mBAAmB,CAACS,IAAI,CAACD,QAAQ,CAAC;EACzC;EACAE,gBAAgBA,CAACF,QAAQ,EAAEG,QAAQ,EAAE;IACnC,IAAIH,QAAQ,KAAKG,QAAQ,EAAE;MACzB;IACF;IACA,MAAMC,KAAK,GAAGJ,QAAQ,GAAGK,MAAM,CAACC,IAAI,CAACN,QAAQ,CAAC,GAAG,EAAE;IACnD,MAAMO,KAAK,GAAGJ,QAAQ,GAAGE,MAAM,CAACC,IAAI,CAACH,QAAQ,CAAC,GAAG,EAAE;IACnD,IAAIC,KAAK,CAACI,MAAM,KAAKD,KAAK,CAACC,MAAM,EAAE;MACjC,IAAI,CAACT,QAAQ,CAACC,QAAQ,CAAC;MACvB;IACF;IACA,KAAK,MAAMS,GAAG,IAAIL,KAAK,EAAE;MACvB,IAAIJ,QAAQ,CAACS,GAAG,CAAC,KAAKN,QAAQ,CAACM,GAAG,CAAC,EAAE;QACnC,IAAI,CAACV,QAAQ,CAACC,QAAQ,CAAC;QACvB;MACF;IACF;EACF;EACAU,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAAClB,mBAAmB,CAACS,IAAI,CAAC,CAAC;EACjC;EACA,WAAWU,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC7B,KAAK,EAAE,CAAC,UAAU,CAAC;MACnB,WAAW,EAAE,CAAC,UAAU,CAAC;MACzB,gBAAgB,EAAE,CAAC,kBAAkB;IACvC,CAAC;EAAE;AACL,CAAC;AAED,MAAMC,aAAa,GAAG,MAAM;EAC1BtB,WAAWA,CAACC,OAAO,EAAE;IACnBnB,qDAAgB,CAAC,IAAI,EAAEmB,OAAO,CAAC;IAC/B,IAAI,CAACsB,uBAAuB,GAAGvC,qDAAW,CAAC,IAAI,EAAE,yBAAyB,EAAE,CAAC,CAAC;IAC9E,IAAI,CAACwC,IAAI,GAAGnB,SAAS;IACrB,IAAI,CAACoB,EAAE,GAAGpB,SAAS;EACrB;EACAqB,aAAaA,CAAA,EAAG;IACd,IAAI,CAACH,uBAAuB,CAACZ,IAAI,CAAC,CAAC;EACrC;EACAS,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAACG,uBAAuB,CAACZ,IAAI,CAAC,CAAC;EACrC;EACA,WAAWU,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC7B,MAAM,EAAE,CAAC,eAAe,CAAC;MACzB,IAAI,EAAE,CAAC,eAAe;IACxB,CAAC;EAAE;AACL,CAAC;AAED,MAAMM,kBAAkB,GAAG,MAAM;AACjC,MAAMC,qBAAqB,GAAG,SAAS;AACvC,MAAMC,kBAAkB,GAAG,MAAM;;AAEjC;AACA,MAAMC,YAAY,GAAIC,QAAQ,IAAK;EACjC,MAAMC,IAAI,GAAGD,QAAQ,CAACE,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAAChB,MAAM,GAAG,CAAC,CAAC,CAACiB,IAAI,CAAC,GAAG,CAAC;EAC3D,OAAO,GAAG,GAAGH,IAAI;AACnB,CAAC;AACD,MAAMI,WAAW,GAAGA,CAACL,QAAQ,EAAEM,OAAO,EAAEC,WAAW,KAAK;EACtD,IAAInC,GAAG,GAAG2B,YAAY,CAACC,QAAQ,CAAC;EAChC,IAAIM,OAAO,EAAE;IACXlC,GAAG,GAAG,GAAG,GAAGA,GAAG;EACjB;EACA,IAAImC,WAAW,KAAKjC,SAAS,EAAE;IAC7BF,GAAG,IAAI,GAAG,GAAGmC,WAAW;EAC1B;EACA,OAAOnC,GAAG;AACZ,CAAC;AACD,MAAMoC,aAAa,GAAGA,CAACC,OAAO,EAAEC,IAAI,EAAEJ,OAAO,EAAEN,QAAQ,EAAEW,SAAS,EAAEC,KAAK,EAAEL,WAAW,KAAK;EACzF,MAAMnC,GAAG,GAAGiC,WAAW,CAAC,CAAC,GAAGQ,SAAS,CAACH,IAAI,CAAC,CAACV,QAAQ,EAAE,GAAGA,QAAQ,CAAC,EAAEM,OAAO,EAAEC,WAAW,CAAC;EACzF,IAAII,SAAS,KAAKd,qBAAqB,EAAE;IACvCY,OAAO,CAACK,SAAS,CAACF,KAAK,EAAE,EAAE,EAAExC,GAAG,CAAC;EACnC,CAAC,MACI;IACHqC,OAAO,CAACM,YAAY,CAACH,KAAK,EAAE,EAAE,EAAExC,GAAG,CAAC;EACtC;AACF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4C,eAAe,GAAIC,KAAK,IAAK;EACjC,MAAMjB,QAAQ,GAAG,EAAE;EACnB,KAAK,MAAMkB,KAAK,IAAID,KAAK,EAAE;IACzB,KAAK,MAAME,OAAO,IAAID,KAAK,CAAClB,QAAQ,EAAE;MACpC,IAAImB,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACtB,MAAMC,KAAK,GAAGF,KAAK,CAACG,MAAM,IAAIH,KAAK,CAACG,MAAM,CAACF,OAAO,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC5D,IAAI,CAACF,KAAK,EAAE;UACV,OAAO,IAAI;QACb;QACApB,QAAQ,CAACuB,IAAI,CAACH,KAAK,CAAC;MACtB,CAAC,MACI,IAAID,OAAO,KAAK,EAAE,EAAE;QACvBnB,QAAQ,CAACuB,IAAI,CAACJ,OAAO,CAAC;MACxB;IACF;EACF;EACA,OAAOnB,QAAQ;AACjB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwB,YAAY,GAAGA,CAACC,MAAM,EAAEzB,QAAQ,KAAK;EACzC,IAAIyB,MAAM,CAACtC,MAAM,GAAGa,QAAQ,CAACb,MAAM,EAAE;IACnC,OAAO,IAAI;EACb;EACA,IAAIsC,MAAM,CAACtC,MAAM,IAAI,CAAC,IAAIsC,MAAM,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;IAC1C,OAAOzB,QAAQ;EACjB;EACA,KAAK,IAAI0B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,MAAM,CAACtC,MAAM,EAAEuC,CAAC,EAAE,EAAE;IACtC,IAAID,MAAM,CAACC,CAAC,CAAC,KAAK1B,QAAQ,CAAC0B,CAAC,CAAC,EAAE;MAC7B,OAAO,IAAI;IACb;EACF;EACA,IAAI1B,QAAQ,CAACb,MAAM,KAAKsC,MAAM,CAACtC,MAAM,EAAE;IACrC,OAAO,CAAC,EAAE,CAAC;EACb;EACA,OAAOa,QAAQ,CAACsB,KAAK,CAACG,MAAM,CAACtC,MAAM,CAAC;AACtC,CAAC;AACD,MAAMwC,YAAY,GAAGA,CAACC,GAAG,EAAElB,IAAI,EAAEJ,OAAO,KAAK;EAC3C,MAAMmB,MAAM,GAAGZ,SAAS,CAACH,IAAI,CAAC,CAACV,QAAQ;EACvC,MAAM6B,QAAQ,GAAGvB,OAAO,GAAGsB,GAAG,CAACE,IAAI,CAACR,KAAK,CAAC,CAAC,CAAC,GAAGM,GAAG,CAACC,QAAQ;EAC3D,MAAM7B,QAAQ,GAAGa,SAAS,CAACgB,QAAQ,CAAC,CAAC7B,QAAQ;EAC7C,OAAOwB,YAAY,CAACC,MAAM,EAAEzB,QAAQ,CAAC;AACvC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMa,SAAS,GAAIZ,IAAI,IAAK;EAC1B,IAAID,QAAQ,GAAG,CAAC,EAAE,CAAC;EACnB,IAAIO,WAAW;EACf,IAAIN,IAAI,IAAI,IAAI,EAAE;IAChB,MAAM8B,OAAO,GAAG9B,IAAI,CAAC+B,OAAO,CAAC,GAAG,CAAC;IACjC,IAAID,OAAO,GAAG,CAAC,CAAC,EAAE;MAChBxB,WAAW,GAAGN,IAAI,CAACgC,SAAS,CAACF,OAAO,GAAG,CAAC,CAAC;MACzC9B,IAAI,GAAGA,IAAI,CAACgC,SAAS,CAAC,CAAC,EAAEF,OAAO,CAAC;IACnC;IACA/B,QAAQ,GAAGC,IAAI,CACZiC,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAAEhC,CAAC,IAAKA,CAAC,CAACiC,IAAI,CAAC,CAAC,CAAC,CACpBlC,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAAChB,MAAM,GAAG,CAAC,CAAC;IAC9B,IAAIa,QAAQ,CAACb,MAAM,KAAK,CAAC,EAAE;MACzBa,QAAQ,GAAG,CAAC,EAAE,CAAC;IACjB;EACF;EACA,OAAO;IAAEA,QAAQ;IAAEO;EAAY,CAAC;AAClC,CAAC;AAED,MAAM8B,WAAW,GAAIC,MAAM,IAAK;EAC9BC,OAAO,CAACC,KAAK,CAAE,qBAAoBF,MAAM,CAACnD,MAAO,GAAE,CAAC;EACpD,KAAK,MAAM8B,KAAK,IAAIqB,MAAM,EAAE;IAC1B,MAAMtC,QAAQ,GAAG,EAAE;IACnBiB,KAAK,CAACwB,OAAO,CAAE3F,CAAC,IAAKkD,QAAQ,CAACuB,IAAI,CAAC,GAAGzE,CAAC,CAACkD,QAAQ,CAAC,CAAC;IAClD,MAAM0C,GAAG,GAAGzB,KAAK,CAACkB,GAAG,CAAErF,CAAC,IAAKA,CAAC,CAAC6F,EAAE,CAAC;IAClCJ,OAAO,CAACK,KAAK,CAAE,MAAK7C,YAAY,CAACC,QAAQ,CAAE,EAAC,EAAE,uCAAuC,EAAE,MAAM,EAAG,IAAG0C,GAAG,CAACtC,IAAI,CAAC,IAAI,CAAE,GAAE,CAAC;EACvH;EACAmC,OAAO,CAACM,QAAQ,CAAC,CAAC;AACpB,CAAC;AACD,MAAMC,cAAc,GAAIC,SAAS,IAAK;EACpCR,OAAO,CAACC,KAAK,CAAE,wBAAuBO,SAAS,CAAC5D,MAAO,GAAE,CAAC;EAC1D,KAAK,MAAM6D,QAAQ,IAAID,SAAS,EAAE;IAChC,IAAIC,QAAQ,CAACtD,EAAE,EAAE;MACf6C,OAAO,CAACK,KAAK,CAAC,QAAQ,EAAG,MAAK7C,YAAY,CAACiD,QAAQ,CAACvD,IAAI,CAAE,EAAC,EAAE,mBAAmB,EAAE,OAAO,EAAG,MAAKM,YAAY,CAACiD,QAAQ,CAACtD,EAAE,CAACM,QAAQ,CAAE,EAAC,EAAE,mBAAmB,CAAC;IAC7J;EACF;EACAuC,OAAO,CAACM,QAAQ,CAAC,CAAC;AACpB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,aAAa;EAAA,IAAAC,IAAA,GAAAC,8KAAA,CAAG,WAAOzC,IAAI,EAAEO,KAAK,EAAEN,SAAS,EAAEyC,KAAK,EAAEC,OAAO,GAAG,KAAK,EAAEC,SAAS,EAAK;IACzF,IAAI;MACF;MACA,MAAMC,MAAM,GAAGC,aAAa,CAAC9C,IAAI,CAAC;MAClC;MACA,IAAI0C,KAAK,IAAInC,KAAK,CAAC9B,MAAM,IAAI,CAACoE,MAAM,EAAE;QACpC,OAAOF,OAAO;MAChB;MACA,MAAM,IAAII,OAAO,CAAEC,OAAO,IAAKlG,uDAAgB,CAAC+F,MAAM,EAAEG,OAAO,CAAC,CAAC;MACjE,MAAMxC,KAAK,GAAGD,KAAK,CAACmC,KAAK,CAAC;MAC1B,MAAMO,MAAM,SAASJ,MAAM,CAACK,UAAU,CAAC1C,KAAK,CAACyB,EAAE,EAAEzB,KAAK,CAACG,MAAM,EAAEV,SAAS,EAAE2C,SAAS,CAAC;MACpF;MACA;MACA,IAAIK,MAAM,CAACN,OAAO,EAAE;QAClB1C,SAAS,GAAGf,kBAAkB;QAC9ByD,OAAO,GAAG,IAAI;MAChB;MACA;MACAA,OAAO,SAASJ,aAAa,CAACU,MAAM,CAACE,OAAO,EAAE5C,KAAK,EAAEN,SAAS,EAAEyC,KAAK,GAAG,CAAC,EAAEC,OAAO,EAAEC,SAAS,CAAC;MAC9F;MACA;MACA,IAAIK,MAAM,CAACG,WAAW,EAAE;QACtB,MAAMH,MAAM,CAACG,WAAW,CAAC,CAAC;MAC5B;MACA,OAAOT,OAAO;IAChB,CAAC,CACD,OAAOU,CAAC,EAAE;MACRxB,OAAO,CAACyB,KAAK,CAACD,CAAC,CAAC;MAChB,OAAO,KAAK;IACd;EACF,CAAC;EAAA,gBA9BKd,aAAaA,CAAAgB,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAlB,IAAA,CAAAmB,KAAA,OAAAC,SAAA;EAAA;AAAA,GA8BlB;AACD;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY;EAAA,IAAAC,KAAA,GAAArB,8KAAA,CAAG,WAAOzC,IAAI,EAAK;IACnC,MAAMgC,GAAG,GAAG,EAAE;IACd,IAAIa,MAAM;IACV,IAAIkB,IAAI,GAAG/D,IAAI;IACf;IACA,OAAQ6C,MAAM,GAAGC,aAAa,CAACiB,IAAI,CAAC,EAAG;MACrC,MAAM9B,EAAE,SAASY,MAAM,CAACmB,UAAU,CAAC,CAAC;MACpC,IAAI/B,EAAE,EAAE;QACN8B,IAAI,GAAG9B,EAAE,CAACkB,OAAO;QACjBlB,EAAE,CAACkB,OAAO,GAAGvF,SAAS;QACtBoE,GAAG,CAACnB,IAAI,CAACoB,EAAE,CAAC;MACd,CAAC,MACI;QACH;MACF;IACF;IACA,OAAO;MAAED,GAAG;MAAEa;IAAO,CAAC;EACxB,CAAC;EAAA,gBAjBKgB,YAAYA,CAAAI,GAAA;IAAA,OAAAH,KAAA,CAAAH,KAAA,OAAAC,SAAA;EAAA;AAAA,GAiBjB;AACD,MAAMM,gBAAgB,GAAGA,CAAA,KAAM;EAC7B,IAAIpB,aAAa,CAACqB,QAAQ,CAACC,IAAI,CAAC,EAAE;IAChC,OAAOrB,OAAO,CAACC,OAAO,CAAC,CAAC;EAC1B;EACA,OAAO,IAAID,OAAO,CAAEC,OAAO,IAAK;IAC9BqB,MAAM,CAACC,gBAAgB,CAAC,gBAAgB,EAAE,MAAMtB,OAAO,CAAC,CAAC,EAAE;MAAEuB,IAAI,EAAE;IAAK,CAAC,CAAC;EAC5E,CAAC,CAAC;AACJ,CAAC;AACD;AACA,MAAMC,eAAe,GAAG,4FAA4F;AACpH,MAAM1B,aAAa,GAAI9C,IAAI,IAAK;EAC9B,IAAI,CAACA,IAAI,EAAE;IACT,OAAOpC,SAAS;EAClB;EACA,IAAIoC,IAAI,CAACyE,OAAO,CAACD,eAAe,CAAC,EAAE;IACjC,OAAOxE,IAAI;EACb;EACA,MAAM6C,MAAM,GAAG7C,IAAI,CAAC0E,aAAa,CAACF,eAAe,CAAC;EAClD,OAAO3B,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAGjF,SAAS;AAClE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+G,eAAe,GAAGA,CAACrF,QAAQ,EAAEgD,QAAQ,KAAK;EAC9C,MAAM;IAAEvD,IAAI;IAAEC;EAAG,CAAC,GAAGsD,QAAQ;EAC7B,IAAItD,EAAE,KAAKpB,SAAS,EAAE;IACpB,OAAO,KAAK;EACd;EACA,IAAImB,IAAI,CAACN,MAAM,GAAGa,QAAQ,CAACb,MAAM,EAAE;IACjC,OAAO,KAAK;EACd;EACA,KAAK,IAAIuC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjC,IAAI,CAACN,MAAM,EAAEuC,CAAC,EAAE,EAAE;IACpC,MAAM4D,QAAQ,GAAG7F,IAAI,CAACiC,CAAC,CAAC;IACxB,IAAI4D,QAAQ,KAAK,GAAG,EAAE;MACpB,OAAO,IAAI;IACb;IACA,IAAIA,QAAQ,KAAKtF,QAAQ,CAAC0B,CAAC,CAAC,EAAE;MAC5B,OAAO,KAAK;IACd;EACF;EACA,OAAOjC,IAAI,CAACN,MAAM,KAAKa,QAAQ,CAACb,MAAM;AACxC,CAAC;AACD;AACA,MAAMoG,iBAAiB,GAAGA,CAACvF,QAAQ,EAAE+C,SAAS,KAAK;EACjD,OAAOA,SAAS,CAACyC,IAAI,CAAExC,QAAQ,IAAKqC,eAAe,CAACrF,QAAQ,EAAEgD,QAAQ,CAAC,CAAC;AAC1E,CAAC;AACD,MAAMyC,UAAU,GAAGA,CAAC/C,GAAG,EAAEzB,KAAK,KAAK;EACjC,MAAMyE,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAClD,GAAG,CAACvD,MAAM,EAAE8B,KAAK,CAAC9B,MAAM,CAAC;EAC9C,IAAI0G,KAAK,GAAG,CAAC;EACb,KAAK,IAAInE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgE,GAAG,EAAEhE,CAAC,EAAE,EAAE;IAC5B,MAAMoE,OAAO,GAAGpD,GAAG,CAAChB,CAAC,CAAC;IACtB,MAAMqE,UAAU,GAAG9E,KAAK,CAACS,CAAC,CAAC;IAC3B;IACA,IAAIoE,OAAO,CAACnD,EAAE,CAACqD,WAAW,CAAC,CAAC,KAAKD,UAAU,CAACpD,EAAE,EAAE;MAC9C;IACF;IACA,IAAImD,OAAO,CAACzE,MAAM,EAAE;MAClB,MAAM4E,aAAa,GAAGjH,MAAM,CAACC,IAAI,CAAC6G,OAAO,CAACzE,MAAM,CAAC;MACjD;MACA,IAAI4E,aAAa,CAAC9G,MAAM,KAAK4G,UAAU,CAAC/F,QAAQ,CAACb,MAAM,EAAE;QACvD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,MAAM+G,cAAc,GAAGD,aAAa,CAAC9D,GAAG,CAAE/C,GAAG,IAAM,IAAGA,GAAI,EAAC,CAAC;QAC5D,KAAK,IAAI+G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,cAAc,CAAC/G,MAAM,EAAEgH,CAAC,EAAE,EAAE;UAC9C;UACA,IAAID,cAAc,CAACC,CAAC,CAAC,CAACH,WAAW,CAAC,CAAC,KAAKD,UAAU,CAAC/F,QAAQ,CAACmG,CAAC,CAAC,EAAE;YAC9D;UACF;UACA;UACAN,KAAK,EAAE;QACT;MACF;IACF;IACA;IACAA,KAAK,EAAE;EACT;EACA,OAAOA,KAAK;AACd,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMO,eAAe,GAAGA,CAACpG,QAAQ,EAAEiB,KAAK,KAAK;EAC3C,MAAMoF,aAAa,GAAG,IAAIC,cAAc,CAACtG,QAAQ,CAAC;EAClD,IAAIuG,cAAc,GAAG,KAAK;EAC1B,IAAIC,SAAS;EACb,KAAK,IAAI9E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,KAAK,CAAC9B,MAAM,EAAEuC,CAAC,EAAE,EAAE;IACrC,MAAM+E,aAAa,GAAGxF,KAAK,CAACS,CAAC,CAAC,CAAC1B,QAAQ;IACvC,IAAIyG,aAAa,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;MAC3BF,cAAc,GAAG,IAAI;IACvB,CAAC,MACI;MACH,KAAK,MAAMpF,OAAO,IAAIsF,aAAa,EAAE;QACnC,MAAMC,IAAI,GAAGL,aAAa,CAACM,IAAI,CAAC,CAAC;QACjC;QACA,IAAIxF,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;UACtB,IAAIuF,IAAI,KAAK,EAAE,EAAE;YACf,OAAO,IAAI;UACb;UACAF,SAAS,GAAGA,SAAS,IAAI,EAAE;UAC3B,MAAMnF,MAAM,GAAGmF,SAAS,CAAC9E,CAAC,CAAC,KAAK8E,SAAS,CAAC9E,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;UAClDL,MAAM,CAACF,OAAO,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGoF,IAAI;QACjC,CAAC,MACI,IAAIA,IAAI,KAAKvF,OAAO,EAAE;UACzB,OAAO,IAAI;QACb;MACF;MACAoF,cAAc,GAAG,KAAK;IACxB;EACF;EACA,MAAMpB,OAAO,GAAGoB,cAAc,GAAGA,cAAc,MAAMF,aAAa,CAACM,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI;EACxF,IAAI,CAACxB,OAAO,EAAE;IACZ,OAAO,IAAI;EACb;EACA,IAAIqB,SAAS,EAAE;IACb,OAAOvF,KAAK,CAACkB,GAAG,CAAC,CAACjB,KAAK,EAAEQ,CAAC,MAAM;MAC9BiB,EAAE,EAAEzB,KAAK,CAACyB,EAAE;MACZ3C,QAAQ,EAAEkB,KAAK,CAAClB,QAAQ;MACxBqB,MAAM,EAAEuF,WAAW,CAAC1F,KAAK,CAACG,MAAM,EAAEmF,SAAS,CAAC9E,CAAC,CAAC,CAAC;MAC/CjD,WAAW,EAAEyC,KAAK,CAACzC,WAAW;MAC9BD,WAAW,EAAE0C,KAAK,CAAC1C;IACrB,CAAC,CAAC,CAAC;EACL;EACA,OAAOyC,KAAK;AACd,CAAC;AACD;AACA;AACA;AACA;AACA,MAAM2F,WAAW,GAAGA,CAACC,CAAC,EAAE/I,CAAC,KAAK;EAC5B,OAAO+I,CAAC,IAAI/I,CAAC,GAAGkB,MAAM,CAAC8H,MAAM,CAAC9H,MAAM,CAAC8H,MAAM,CAAC,CAAC,CAAC,EAAED,CAAC,CAAC,EAAE/I,CAAC,CAAC,GAAGQ,SAAS;AACpE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyI,eAAe,GAAGA,CAACrE,GAAG,EAAEsE,MAAM,KAAK;EACvC,IAAIC,KAAK,GAAG,IAAI;EAChB,IAAIC,UAAU,GAAG,CAAC;EAClB,KAAK,MAAMjG,KAAK,IAAI+F,MAAM,EAAE;IAC1B,MAAMnB,KAAK,GAAGJ,UAAU,CAAC/C,GAAG,EAAEzB,KAAK,CAAC;IACpC,IAAI4E,KAAK,GAAGqB,UAAU,EAAE;MACtBD,KAAK,GAAGhG,KAAK;MACbiG,UAAU,GAAGrB,KAAK;IACpB;EACF;EACA,IAAIoB,KAAK,EAAE;IACT,OAAOA,KAAK,CAAC9E,GAAG,CAAC,CAACjB,KAAK,EAAEQ,CAAC,KAAK;MAC7B,IAAIyF,EAAE;MACN,OAAQ;QACNxE,EAAE,EAAEzB,KAAK,CAACyB,EAAE;QACZ3C,QAAQ,EAAEkB,KAAK,CAAClB,QAAQ;QACxBqB,MAAM,EAAEuF,WAAW,CAAC1F,KAAK,CAACG,MAAM,EAAE,CAAC8F,EAAE,GAAGzE,GAAG,CAAChB,CAAC,CAAC,MAAM,IAAI,IAAIyF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC9F,MAAM;MAChG,CAAC;IACH,CAAC,CAAC;EACJ;EACA,OAAO,IAAI;AACb,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+F,oBAAoB,GAAGA,CAACpH,QAAQ,EAAEgH,MAAM,KAAK;EACjD,IAAIC,KAAK,GAAG,IAAI;EAChB,IAAII,SAAS,GAAG,CAAC;EACjB,KAAK,MAAMpG,KAAK,IAAI+F,MAAM,EAAE;IAC1B,MAAMM,YAAY,GAAGlB,eAAe,CAACpG,QAAQ,EAAEiB,KAAK,CAAC;IACrD,IAAIqG,YAAY,KAAK,IAAI,EAAE;MACzB,MAAMzB,KAAK,GAAG0B,eAAe,CAACD,YAAY,CAAC;MAC3C,IAAIzB,KAAK,GAAGwB,SAAS,EAAE;QACrBA,SAAS,GAAGxB,KAAK;QACjBoB,KAAK,GAAGK,YAAY;MACtB;IACF;EACF;EACA,OAAOL,KAAK;AACd,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,eAAe,GAAItG,KAAK,IAAK;EACjC,IAAI4E,KAAK,GAAG,CAAC;EACb,IAAI2B,KAAK,GAAG,CAAC;EACb,KAAK,MAAMtG,KAAK,IAAID,KAAK,EAAE;IACzB,KAAK,MAAME,OAAO,IAAID,KAAK,CAAClB,QAAQ,EAAE;MACpC,IAAImB,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACtB0E,KAAK,IAAIF,IAAI,CAAC8B,GAAG,CAAC,CAAC,EAAED,KAAK,CAAC;MAC7B,CAAC,MACI,IAAIrG,OAAO,KAAK,EAAE,EAAE;QACvB0E,KAAK,IAAIF,IAAI,CAAC8B,GAAG,CAAC,CAAC,EAAED,KAAK,CAAC;MAC7B;MACAA,KAAK,EAAE;IACT;EACF;EACA,OAAO3B,KAAK;AACd,CAAC;AACD,MAAMS,cAAc,CAAC;EACnBrI,WAAWA,CAAC+B,QAAQ,EAAE;IACpB,IAAI,CAACA,QAAQ,GAAGA,QAAQ,CAACsB,KAAK,CAAC,CAAC;EAClC;EACAqF,IAAIA,CAAA,EAAG;IACL,IAAI,IAAI,CAAC3G,QAAQ,CAACb,MAAM,GAAG,CAAC,EAAE;MAC5B,OAAO,IAAI,CAACa,QAAQ,CAAC0H,KAAK,CAAC,CAAC;IAC9B;IACA,OAAO,EAAE;EACX;AACF;AAEA,MAAMC,QAAQ,GAAGA,CAACC,EAAE,EAAEC,IAAI,KAAK;EAC7B,IAAIA,IAAI,IAAID,EAAE,EAAE;IACd,OAAOA,EAAE,CAACC,IAAI,CAAC;EACjB;EACA,IAAID,EAAE,CAACE,YAAY,CAACD,IAAI,CAAC,EAAE;IACzB,OAAOD,EAAE,CAACG,YAAY,CAACF,IAAI,CAAC;EAC9B;EACA,OAAO,IAAI;AACb,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMG,aAAa,GAAItH,IAAI,IAAK;EAC9B,OAAOuH,KAAK,CAACxI,IAAI,CAACiB,IAAI,CAACwH,QAAQ,CAAC,CAC7BhI,MAAM,CAAE0H,EAAE,IAAKA,EAAE,CAACO,OAAO,KAAK,oBAAoB,CAAC,CACnDhG,GAAG,CAAEyF,EAAE,IAAK;IACb,MAAMlI,EAAE,GAAGiI,QAAQ,CAACC,EAAE,EAAE,IAAI,CAAC;IAC7B,OAAO;MACLnI,IAAI,EAAEoB,SAAS,CAAC8G,QAAQ,CAACC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC5H,QAAQ;MAC9CN,EAAE,EAAEA,EAAE,IAAI,IAAI,GAAGpB,SAAS,GAAGuC,SAAS,CAACnB,EAAE;IAC3C,CAAC;EACH,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAM0I,UAAU,GAAI1H,IAAI,IAAK;EAC3B,OAAO2H,iBAAiB,CAACC,cAAc,CAAC5H,IAAI,CAAC,CAAC;AAChD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAM4H,cAAc,GAAI7D,IAAI,IAAK;EAC/B,OAAOwD,KAAK,CAACxI,IAAI,CAACgF,IAAI,CAACyD,QAAQ,CAAC,CAC7BhI,MAAM,CAAE0H,EAAE,IAAKA,EAAE,CAACO,OAAO,KAAK,WAAW,IAAIP,EAAE,CAACvJ,SAAS,CAAC,CAC1D8D,GAAG,CAAEyF,EAAE,IAAK;IACb,MAAMvJ,SAAS,GAAGsJ,QAAQ,CAACC,EAAE,EAAE,WAAW,CAAC;IAC3C,OAAO;MACL5H,QAAQ,EAAEa,SAAS,CAAC8G,QAAQ,CAACC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC5H,QAAQ;MACjD2C,EAAE,EAAEtE,SAAS,CAAC2H,WAAW,CAAC,CAAC;MAC3B3E,MAAM,EAAEuG,EAAE,CAACrJ,cAAc;MACzBC,WAAW,EAAEoJ,EAAE,CAACpJ,WAAW;MAC3BC,WAAW,EAAEmJ,EAAE,CAACnJ,WAAW;MAC3ByJ,QAAQ,EAAEI,cAAc,CAACV,EAAE;IAC7B,CAAC;EACH,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMS,iBAAiB,GAAIE,KAAK,IAAK;EACnC,MAAMvB,MAAM,GAAG,EAAE;EACjB,KAAK,MAAMvC,IAAI,IAAI8D,KAAK,EAAE;IACxBC,WAAW,CAAC,EAAE,EAAExB,MAAM,EAAEvC,IAAI,CAAC;EAC/B;EACA,OAAOuC,MAAM;AACf,CAAC;AACD;AACA,MAAMwB,WAAW,GAAGA,CAACvH,KAAK,EAAE+F,MAAM,EAAEvC,IAAI,KAAK;EAC3CxD,KAAK,GAAG,CACN,GAAGA,KAAK,EACR;IACE0B,EAAE,EAAE8B,IAAI,CAAC9B,EAAE;IACX3C,QAAQ,EAAEyE,IAAI,CAACzE,QAAQ;IACvBqB,MAAM,EAAEoD,IAAI,CAACpD,MAAM;IACnB7C,WAAW,EAAEiG,IAAI,CAACjG,WAAW;IAC7BC,WAAW,EAAEgG,IAAI,CAAChG;EACpB,CAAC,CACF;EACD,IAAIgG,IAAI,CAACyD,QAAQ,CAAC/I,MAAM,KAAK,CAAC,EAAE;IAC9B6H,MAAM,CAACzF,IAAI,CAACN,KAAK,CAAC;IAClB;EACF;EACA,KAAK,MAAMwH,KAAK,IAAIhE,IAAI,CAACyD,QAAQ,EAAE;IACjCM,WAAW,CAACvH,KAAK,EAAE+F,MAAM,EAAEyB,KAAK,CAAC;EACnC;AACF,CAAC;AAED,MAAMC,MAAM,GAAG,MAAM;EACnBzK,WAAWA,CAACC,OAAO,EAAE;IACnBnB,qDAAgB,CAAC,IAAI,EAAEmB,OAAO,CAAC;IAC/B,IAAI,CAACyK,kBAAkB,GAAG1L,qDAAW,CAAC,IAAI,EAAE,oBAAoB,EAAE,CAAC,CAAC;IACpE,IAAI,CAAC2L,iBAAiB,GAAG3L,qDAAW,CAAC,IAAI,EAAE,mBAAmB,EAAE,CAAC,CAAC;IAClE,IAAI,CAAC4L,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,IAAI,GAAG,KAAK;IACjB,IAAI,CAAClI,KAAK,GAAG,CAAC;IACd,IAAI,CAACmI,SAAS,GAAG,CAAC;IAClB,IAAI,CAACrI,IAAI,GAAG,GAAG;IACf,IAAI,CAACJ,OAAO,GAAG,IAAI;EACrB;EACM0I,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAA9F,8KAAA;MACxB,MAAMyB,gBAAgB,CAAC,CAAC;MACxB,MAAMsE,UAAU,SAASD,KAAI,CAACE,SAAS,CAACF,KAAI,CAACG,WAAW,CAAC,CAAC,CAAC;MAC3D,IAAIF,UAAU,KAAK,IAAI,EAAE;QACvB,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;UAClC,MAAM;YAAElG;UAAS,CAAC,GAAGkG,UAAU;UAC/B,MAAMjJ,IAAI,GAAGY,SAAS,CAACmC,QAAQ,CAAC;UAChCiG,KAAI,CAACI,WAAW,CAACpJ,IAAI,CAACD,QAAQ,EAAEJ,kBAAkB,EAAEK,IAAI,CAACM,WAAW,CAAC;UACrE,MAAM0I,KAAI,CAACK,iBAAiB,CAACrJ,IAAI,CAACD,QAAQ,EAAEJ,kBAAkB,CAAC;QACjE;MACF,CAAC,MACI;QACH,MAAMqJ,KAAI,CAACM,eAAe,CAAC,CAAC;MAC9B;IAAC;EACH;EACAC,gBAAgBA,CAAA,EAAG;IACjBzE,MAAM,CAACC,gBAAgB,CAAC,yBAAyB,EAAEtH,uDAAQ,CAAC,IAAI,CAAC+L,iBAAiB,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;IACnG3E,MAAM,CAACC,gBAAgB,CAAC,qBAAqB,EAAEtH,uDAAQ,CAAC,IAAI,CAAC6L,eAAe,CAACG,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;EAChG;EACMC,UAAUA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAzG,8KAAA;MACjB,MAAMxC,SAAS,GAAGiJ,MAAI,CAACC,gBAAgB,CAAC,CAAC;MACzC,IAAI7J,QAAQ,GAAG4J,MAAI,CAACR,WAAW,CAAC,CAAC;MACjC,MAAMF,UAAU,SAASU,MAAI,CAACT,SAAS,CAACnJ,QAAQ,CAAC;MACjD,IAAIkJ,UAAU,KAAK,IAAI,EAAE;QACvB,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;UAClClJ,QAAQ,GAAGa,SAAS,CAACqI,UAAU,CAAClG,QAAQ,CAAC,CAAChD,QAAQ;QACpD,CAAC,MACI;UACH,OAAO,KAAK;QACd;MACF;MACA,OAAO4J,MAAI,CAACN,iBAAiB,CAACtJ,QAAQ,EAAEW,SAAS,CAAC;IAAC;EACrD;EACAmJ,YAAYA,CAACC,EAAE,EAAE;IACfA,EAAE,CAACC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAGC,kBAAkB,IAAK;MAC5C,IAAI,CAACC,IAAI,CAAC,CAAC;MACXD,kBAAkB,CAAC,CAAC;IACtB,CAAC,CAAC;EACJ;EACA;EACME,aAAaA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAlH,8KAAA;MACpB,MAAM+F,UAAU,SAASmB,MAAI,CAAClB,SAAS,CAAC,CAAC;MACzC,IAAID,UAAU,KAAK,IAAI,EAAE;QACvB,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;UAClC,OAAOA,UAAU,CAAClG,QAAQ;QAC5B,CAAC,MACI;UACH,OAAO,KAAK;QACd;MACF;MACA,OAAO,IAAI;IAAC;EACd;EACA;AACF;AACA;AACA;AACA;AACA;EACQzB,IAAIA,CAACtB,IAAI,EAAEU,SAAS,GAAG,SAAS,EAAE2C,SAAS,EAAE;IAAA,IAAAgH,MAAA;IAAA,OAAAnH,8KAAA;MACjD,IAAIgE,EAAE;MACN,IAAIlH,IAAI,CAACsK,UAAU,CAAC,GAAG,CAAC,EAAE;QACxB,MAAMC,WAAW,GAAG,CAACrD,EAAE,GAAGmD,MAAI,CAACzB,YAAY,MAAM,IAAI,IAAI1B,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,GAAG;QACjF;QACA,MAAM/I,GAAG,GAAG,IAAIqM,GAAG,CAACxK,IAAI,EAAG,gBAAeuK,WAAY,EAAC,CAAC;QACxDvK,IAAI,GAAG7B,GAAG,CAACyD,QAAQ,GAAGzD,GAAG,CAACsM,MAAM;MAClC;MACA,IAAIC,UAAU,GAAG9J,SAAS,CAACZ,IAAI,CAAC;MAChC,MAAMiJ,UAAU,SAASoB,MAAI,CAACnB,SAAS,CAACwB,UAAU,CAAC3K,QAAQ,CAAC;MAC5D,IAAIkJ,UAAU,KAAK,IAAI,EAAE;QACvB,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;UAClCyB,UAAU,GAAG9J,SAAS,CAACqI,UAAU,CAAClG,QAAQ,CAAC;QAC7C,CAAC,MACI;UACH,OAAO,KAAK;QACd;MACF;MACAsH,MAAI,CAACjB,WAAW,CAACsB,UAAU,CAAC3K,QAAQ,EAAEW,SAAS,EAAEgK,UAAU,CAACpK,WAAW,CAAC;MACxE,OAAO+J,MAAI,CAAChB,iBAAiB,CAACqB,UAAU,CAAC3K,QAAQ,EAAEW,SAAS,EAAE2C,SAAS,CAAC;IAAC;EAC3E;EACA;EACA6G,IAAIA,CAAA,EAAG;IACLpF,MAAM,CAACtE,OAAO,CAAC0J,IAAI,CAAC,CAAC;IACrB,OAAO1G,OAAO,CAACC,OAAO,CAAC,IAAI,CAACkH,WAAW,CAAC;EAC1C;EACA;EACMC,UAAUA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA3H,8KAAA;MACjBd,WAAW,CAAC+F,UAAU,CAAC0C,MAAI,CAAClD,EAAE,CAAC,CAAC;MAChC9E,cAAc,CAACkF,aAAa,CAAC8C,MAAI,CAAClD,EAAE,CAAC,CAAC;IAAC;EACzC;EACA;EACMmD,UAAUA,CAACpK,SAAS,EAAE;IAAA,IAAAqK,MAAA;IAAA,OAAA7H,8KAAA;MAC1B,IAAI6H,MAAI,CAAClC,IAAI,EAAE;QACbvG,OAAO,CAAC0I,IAAI,CAAC,uDAAuD,CAAC;QACrE,OAAO,KAAK;MACd;MACA,MAAM;QAAEvI,GAAG;QAAEa;MAAO,CAAC,SAASgB,YAAY,CAACQ,MAAM,CAACF,QAAQ,CAACC,IAAI,CAAC;MAChE,MAAMxC,MAAM,GAAG8F,UAAU,CAAC4C,MAAI,CAACpD,EAAE,CAAC;MAClC,MAAM3G,KAAK,GAAG8F,eAAe,CAACrE,GAAG,EAAEJ,MAAM,CAAC;MAC1C,IAAI,CAACrB,KAAK,EAAE;QACVsB,OAAO,CAAC0I,IAAI,CAAC,mCAAmC,EAAEvI,GAAG,CAACP,GAAG,CAAET,CAAC,IAAKA,CAAC,CAACiB,EAAE,CAAC,CAAC;QACvE,OAAO,KAAK;MACd;MACA,MAAM3C,QAAQ,GAAGgB,eAAe,CAACC,KAAK,CAAC;MACvC,IAAI,CAACjB,QAAQ,EAAE;QACbuC,OAAO,CAAC0I,IAAI,CAAC,iFAAiF,CAAC;QAC/F,OAAO,KAAK;MACd;MACAD,MAAI,CAAC3B,WAAW,CAACrJ,QAAQ,EAAEW,SAAS,CAAC;MACrC,MAAMqK,MAAI,CAACE,iBAAiB,CAAC3H,MAAM,EAAEtC,KAAK,EAAErB,kBAAkB,EAAEI,QAAQ,EAAE,IAAI,EAAE0C,GAAG,CAACvD,MAAM,CAAC;MAC3F,OAAO,IAAI;IAAC;EACd;EACA;EACAsK,iBAAiBA,CAAA,EAAG;IAClB,MAAMzJ,QAAQ,GAAG,IAAI,CAACoJ,WAAW,CAAC,CAAC;IACnC,IAAIpJ,QAAQ,IAAIuF,iBAAiB,CAACvF,QAAQ,EAAEgI,aAAa,CAAC,IAAI,CAACJ,EAAE,CAAC,CAAC,EAAE;MACnE,IAAI,CAAC0B,iBAAiB,CAACtJ,QAAQ,EAAEJ,kBAAkB,CAAC;IACtD;EACF;EACA;EACA2J,eAAeA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACD,iBAAiB,CAAC,IAAI,CAACF,WAAW,CAAC,CAAC,EAAExJ,kBAAkB,CAAC;EACvE;EACAiK,gBAAgBA,CAAA,EAAG;IACjB,IAAI1C,EAAE;IACN,MAAMgE,GAAG,GAAGpG,MAAM;IAClB,IAAIoG,GAAG,CAAC1K,OAAO,CAACG,KAAK,KAAK,IAAI,EAAE;MAC9B,IAAI,CAACA,KAAK,EAAE;MACZuK,GAAG,CAAC1K,OAAO,CAACM,YAAY,CAAC,IAAI,CAACH,KAAK,EAAEuK,GAAG,CAACtG,QAAQ,CAACuG,KAAK,EAAE,CAACjE,EAAE,GAAGgE,GAAG,CAACtG,QAAQ,CAACwG,QAAQ,MAAM,IAAI,IAAIlE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmE,IAAI,CAAC;IACrI;IACA,MAAM1K,KAAK,GAAGuK,GAAG,CAAC1K,OAAO,CAACG,KAAK;IAC/B,MAAMmI,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,IAAI,CAACA,SAAS,GAAGnI,KAAK;IACtB,IAAIA,KAAK,GAAGmI,SAAS,IAAKnI,KAAK,IAAImI,SAAS,IAAIA,SAAS,GAAG,CAAE,EAAE;MAC9D,OAAOlJ,qBAAqB;IAC9B;IACA,IAAIe,KAAK,GAAGmI,SAAS,EAAE;MACrB,OAAOjJ,kBAAkB;IAC3B;IACA,OAAOF,kBAAkB;EAC3B;EACM0J,iBAAiBA,CAACtJ,QAAQ,EAAEW,SAAS,EAAE2C,SAAS,EAAE;IAAA,IAAAiI,MAAA;IAAA,OAAApI,8KAAA;MACtD,IAAI,CAACnD,QAAQ,EAAE;QACbuC,OAAO,CAACyB,KAAK,CAAC,iDAAiD,CAAC;QAChE,OAAO,KAAK;MACd;MACA;MACA,MAAMjB,SAAS,GAAGiF,aAAa,CAACuD,MAAI,CAAC3D,EAAE,CAAC;MACxC,MAAM5E,QAAQ,GAAGuC,iBAAiB,CAACvF,QAAQ,EAAE+C,SAAS,CAAC;MACvD,IAAIyI,YAAY,GAAG,IAAI;MACvB,IAAIxI,QAAQ,EAAE;QACZ,MAAM;UAAEhD,QAAQ,EAAEyL,UAAU;UAAElL;QAAY,CAAC,GAAGyC,QAAQ,CAACtD,EAAE;QACzD6L,MAAI,CAAClC,WAAW,CAACoC,UAAU,EAAE9K,SAAS,EAAEJ,WAAW,CAAC;QACpDiL,YAAY,GAAGxI,QAAQ,CAACvD,IAAI;QAC5BO,QAAQ,GAAGyL,UAAU;MACvB;MACA;MACA,MAAMnJ,MAAM,GAAG8F,UAAU,CAACmD,MAAI,CAAC3D,EAAE,CAAC;MAClC,MAAM3G,KAAK,GAAGmG,oBAAoB,CAACpH,QAAQ,EAAEsC,MAAM,CAAC;MACpD,IAAI,CAACrB,KAAK,EAAE;QACVsB,OAAO,CAACyB,KAAK,CAAC,gDAAgD,CAAC;QAC/D,OAAO,KAAK;MACd;MACA;MACA,OAAOuH,MAAI,CAACL,iBAAiB,CAACrG,QAAQ,CAACC,IAAI,EAAE7D,KAAK,EAAEN,SAAS,EAAEX,QAAQ,EAAEwL,YAAY,EAAE,CAAC,EAAElI,SAAS,CAAC;IAAC;EACvG;EACM4H,iBAAiBA,CAACzG,IAAI,EAAExD,KAAK,EAAEN,SAAS,EAAEX,QAAQ,EAAEwL,YAAY,EAAEpI,KAAK,GAAG,CAAC,EAAEE,SAAS,EAAE;IAAA,IAAAoI,MAAA;IAAA,OAAAvI,8KAAA;MAC5F,MAAMwI,MAAM,SAASD,MAAI,CAACE,IAAI,CAAC,CAAC;MAChC,IAAIvI,OAAO,GAAG,KAAK;MACnB,IAAI;QACFA,OAAO,SAASqI,MAAI,CAACzI,aAAa,CAACwB,IAAI,EAAExD,KAAK,EAAEN,SAAS,EAAEX,QAAQ,EAAEwL,YAAY,EAAEpI,KAAK,EAAEE,SAAS,CAAC;MACtG,CAAC,CACD,OAAOS,CAAC,EAAE;QACRxB,OAAO,CAACyB,KAAK,CAACD,CAAC,CAAC;MAClB;MACA4H,MAAM,CAAC,CAAC;MACR,OAAOtI,OAAO;IAAC;EACjB;EACMuI,IAAIA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA1I,8KAAA;MACX,MAAM2I,CAAC,GAAGD,MAAI,CAACjB,WAAW;MAC1B,IAAIlH,OAAO;MACXmI,MAAI,CAACjB,WAAW,GAAG,IAAInH,OAAO,CAAE3G,CAAC,IAAM4G,OAAO,GAAG5G,CAAE,CAAC;MACpD,IAAIgP,CAAC,KAAKxN,SAAS,EAAE;QACnB,MAAMwN,CAAC;MACT;MACA,OAAOpI,OAAO;IAAC;EACjB;EACA;AACF;AACA;AACA;AACA;AACA;EACQyF,SAASA,CAACzJ,EAAE,GAAG,IAAI,CAAC0J,WAAW,CAAC,CAAC,EAAE3J,IAAI,EAAE;IAAA,IAAAsM,OAAA;IAAA,OAAA5I,8KAAA;MAC7C,IAAI1D,IAAI,KAAKnB,SAAS,EAAE;QACtBmB,IAAI,GAAGoB,SAAS,CAACkL,OAAI,CAAClD,YAAY,CAAC,CAAC7I,QAAQ;MAC9C;MACA,IAAI,CAACN,EAAE,IAAI,CAACD,IAAI,EAAE;QAChB,OAAO,IAAI;MACb;MACA,MAAM6C,MAAM,GAAG8F,UAAU,CAAC2D,OAAI,CAACnE,EAAE,CAAC;MAClC,MAAMoE,SAAS,GAAG5E,oBAAoB,CAAC3H,IAAI,EAAE6C,MAAM,CAAC;MACpD,MAAM2J,eAAe,GAAGD,SAAS,IAAIA,SAAS,CAACA,SAAS,CAAC7M,MAAM,GAAG,CAAC,CAAC,CAACX,WAAW;MAChF,MAAM0N,QAAQ,GAAGD,eAAe,SAASA,eAAe,CAAC,CAAC,GAAG,IAAI;MACjE,IAAIC,QAAQ,KAAK,KAAK,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;QACtD,OAAOA,QAAQ;MACjB;MACA,MAAMC,OAAO,GAAG/E,oBAAoB,CAAC1H,EAAE,EAAE4C,MAAM,CAAC;MAChD,MAAM8J,eAAe,GAAGD,OAAO,IAAIA,OAAO,CAACA,OAAO,CAAChN,MAAM,GAAG,CAAC,CAAC,CAACV,WAAW;MAC1E,OAAO2N,eAAe,GAAGA,eAAe,CAAC,CAAC,GAAG,IAAI;IAAC;EACpD;EACMnJ,aAAaA,CAACwB,IAAI,EAAExD,KAAK,EAAEN,SAAS,EAAEX,QAAQ,EAAEwL,YAAY,EAAEpI,KAAK,GAAG,CAAC,EAAEE,SAAS,EAAE;IAAA,IAAA+I,OAAA;IAAA,OAAAlJ,8KAAA;MACxF,IAAIkJ,OAAI,CAACvD,IAAI,EAAE;QACbvG,OAAO,CAAC0I,IAAI,CAAC,uDAAuD,CAAC;QACrE,OAAO,KAAK;MACd;MACAoB,OAAI,CAACvD,IAAI,GAAG,IAAI;MAChB;MACA,MAAMwD,UAAU,GAAGD,OAAI,CAACE,gBAAgB,CAACvM,QAAQ,EAAEwL,YAAY,CAAC;MAChE,IAAIc,UAAU,EAAE;QACdD,OAAI,CAAC1D,kBAAkB,CAAC/J,IAAI,CAAC0N,UAAU,CAAC;MAC1C;MACA,MAAMjJ,OAAO,SAASJ,aAAa,CAACwB,IAAI,EAAExD,KAAK,EAAEN,SAAS,EAAEyC,KAAK,EAAE,KAAK,EAAEE,SAAS,CAAC;MACpF+I,OAAI,CAACvD,IAAI,GAAG,KAAK;MACjB;MACA,IAAIwD,UAAU,EAAE;QACdD,OAAI,CAACzD,iBAAiB,CAAChK,IAAI,CAAC0N,UAAU,CAAC;MACzC;MACA,OAAOjJ,OAAO;IAAC;EACjB;EACAgG,WAAWA,CAACrJ,QAAQ,EAAEW,SAAS,EAAEJ,WAAW,EAAE;IAC5C,IAAI,CAACK,KAAK,EAAE;IACZJ,aAAa,CAACuE,MAAM,CAACtE,OAAO,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACJ,OAAO,EAAEN,QAAQ,EAAEW,SAAS,EAAE,IAAI,CAACC,KAAK,EAAEL,WAAW,CAAC;EACtG;EACA6I,WAAWA,CAAA,EAAG;IACZ,OAAOzH,YAAY,CAACoD,MAAM,CAACsG,QAAQ,EAAE,IAAI,CAAC3K,IAAI,EAAE,IAAI,CAACJ,OAAO,CAAC;EAC/D;EACAiM,gBAAgBA,CAACd,UAAU,EAAEe,oBAAoB,EAAE;IACjD,MAAM/M,IAAI,GAAG,IAAI,CAACoJ,YAAY;IAC9B,MAAMnJ,EAAE,GAAGK,YAAY,CAAC0L,UAAU,CAAC;IACnC,IAAI,CAAC5C,YAAY,GAAGnJ,EAAE;IACtB,IAAIA,EAAE,KAAKD,IAAI,EAAE;MACf,OAAO,IAAI;IACb;IACA,MAAMgN,cAAc,GAAGD,oBAAoB,GAAGzM,YAAY,CAACyM,oBAAoB,CAAC,GAAG,IAAI;IACvF,OAAO;MACL/M,IAAI;MACJgN,cAAc;MACd/M;IACF,CAAC;EACH;EACA,IAAIkI,EAAEA,CAAA,EAAG;IAAE,OAAOzK,qDAAU,CAAC,IAAI,CAAC;EAAE;AACtC,CAAC;AAED,MAAMuP,aAAa,GAAG,6ZAA6Z;AAEnb,MAAMC,UAAU,GAAG,MAAM;EACvB1O,WAAWA,CAACC,OAAO,EAAE;IACnBnB,qDAAgB,CAAC,IAAI,EAAEmB,OAAO,CAAC;IAC/B,IAAI,CAAC0O,OAAO,GAAI7C,EAAE,IAAK;MACrBnM,qDAAO,CAAC,IAAI,CAAC0N,IAAI,EAAEvB,EAAE,EAAE,IAAI,CAAC8C,eAAe,EAAE,IAAI,CAACC,eAAe,CAAC;IACpE,CAAC;IACD,IAAI,CAACC,KAAK,GAAGzO,SAAS;IACtB,IAAI,CAACgN,IAAI,GAAGhN,SAAS;IACrB,IAAI,CAAC0O,GAAG,GAAG1O,SAAS;IACpB,IAAI,CAACuO,eAAe,GAAG,SAAS;IAChC,IAAI,CAACC,eAAe,GAAGxO,SAAS;IAChC,IAAI,CAAC2O,MAAM,GAAG3O,SAAS;EACzB;EACA4O,MAAMA,CAAA,EAAG;IACP,MAAMC,IAAI,GAAGpP,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAMqP,KAAK,GAAG;MACZ9B,IAAI,EAAE,IAAI,CAACA,IAAI;MACf0B,GAAG,EAAE,IAAI,CAACA,GAAG;MACbC,MAAM,EAAE,IAAI,CAACA;IACf,CAAC;IACD,OAAQ7P,qDAAC,CAACE,iDAAI,EAAE;MAAEsP,OAAO,EAAE,IAAI,CAACA,OAAO;MAAES,KAAK,EAAExP,qDAAkB,CAAC,IAAI,CAACkP,KAAK,EAAE;QAC3E,CAACI,IAAI,GAAG,IAAI;QACZ,iBAAiB,EAAE;MACrB,CAAC;IAAE,CAAC,EAAE/P,qDAAC,CAAC,GAAG,EAAE4B,MAAM,CAAC8H,MAAM,CAAC,CAAC,CAAC,EAAEsG,KAAK,CAAC,EAAEhQ,qDAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;EAC5D;AACF,CAAC;AACDuP,UAAU,CAACW,KAAK,GAAGZ,aAAa;;;;;;;;;;;;;;;;;;;;ACl3BhC;AACA;AACA;AACA,MAAMiB,WAAW,GAAGA,CAACC,QAAQ,EAAEhG,EAAE,KAAK;EACpC,OAAOA,EAAE,CAACiG,OAAO,CAACD,QAAQ,CAAC,KAAK,IAAI;AACtC,CAAC;AACD;AACA;AACA;AACA,MAAM/P,kBAAkB,GAAGA,CAACkP,KAAK,EAAEe,WAAW,KAAK;EACjD,OAAO,OAAOf,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAAC5N,MAAM,GAAG,CAAC,GAChDH,MAAM,CAAC8H,MAAM,CAAC;IAAE,WAAW,EAAE,IAAI;IAAE,CAAE,aAAYiG,KAAM,EAAC,GAAG;EAAK,CAAC,EAAEe,WAAW,CAAC,GAAGA,WAAW;AACnG,CAAC;AACD,MAAMC,YAAY,GAAIC,OAAO,IAAK;EAChC,IAAIA,OAAO,KAAK1P,SAAS,EAAE;IACzB,MAAM2P,KAAK,GAAGhG,KAAK,CAACiG,OAAO,CAACF,OAAO,CAAC,GAAGA,OAAO,GAAGA,OAAO,CAAC9L,KAAK,CAAC,GAAG,CAAC;IACnE,OAAO+L,KAAK,CACT/N,MAAM,CAAE3C,CAAC,IAAKA,CAAC,IAAI,IAAI,CAAC,CACxB4E,GAAG,CAAE5E,CAAC,IAAKA,CAAC,CAAC6E,IAAI,CAAC,CAAC,CAAC,CACpBlC,MAAM,CAAE3C,CAAC,IAAKA,CAAC,KAAK,EAAE,CAAC;EAC5B;EACA,OAAO,EAAE;AACX,CAAC;AACD,MAAM4Q,WAAW,GAAIH,OAAO,IAAK;EAC/B,MAAM7L,GAAG,GAAG,CAAC,CAAC;EACd4L,YAAY,CAACC,OAAO,CAAC,CAACvL,OAAO,CAAElF,CAAC,IAAM4E,GAAG,CAAC5E,CAAC,CAAC,GAAG,IAAK,CAAC;EACrD,OAAO4E,GAAG;AACZ,CAAC;AACD,MAAMiM,MAAM,GAAG,sBAAsB;AACrC,MAAMxQ,OAAO;EAAA,IAAAsF,IAAA,GAAAC,8KAAA,CAAG,WAAO/E,GAAG,EAAE2L,EAAE,EAAEpJ,SAAS,EAAE2C,SAAS,EAAK;IACvD,IAAIlF,GAAG,IAAI,IAAI,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAACgQ,MAAM,CAACC,IAAI,CAACjQ,GAAG,CAAC,EAAE;MACtD,MAAMkQ,MAAM,GAAGzJ,QAAQ,CAACO,aAAa,CAAC,YAAY,CAAC;MACnD,IAAIkJ,MAAM,EAAE;QACV,IAAIvE,EAAE,IAAI,IAAI,EAAE;UACdA,EAAE,CAACwE,cAAc,CAAC,CAAC;QACrB;QACA,OAAOD,MAAM,CAAC/M,IAAI,CAACnD,GAAG,EAAEuC,SAAS,EAAE2C,SAAS,CAAC;MAC/C;IACF;IACA,OAAO,KAAK;EACd,CAAC;EAAA,gBAXK1F,OAAOA,CAAAqG,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAlB,IAAA,CAAAmB,KAAA,OAAAC,SAAA;EAAA;AAAA,GAWZ", "sources": ["./node_modules/@ionic/core/dist/esm/ion-route_4.entry.js", "./node_modules/@ionic/core/dist/esm/theme-17531cdf.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, f as getElement, h, H as Host } from './index-2d388930.js';\nimport { c as componentOnReady, q as debounce } from './helpers-3379ba19.js';\nimport { o as openURL, c as createColorClasses } from './theme-17531cdf.js';\nimport { b as getIonMode } from './ionic-global-b3fc28dd.js';\n\nconst Route = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionRouteDataChanged = createEvent(this, \"ionRouteDataChanged\", 7);\n    this.url = '';\n    this.component = undefined;\n    this.componentProps = undefined;\n    this.beforeLeave = undefined;\n    this.beforeEnter = undefined;\n  }\n  onUpdate(newValue) {\n    this.ionRouteDataChanged.emit(newValue);\n  }\n  onComponentProps(newValue, oldValue) {\n    if (newValue === oldValue) {\n      return;\n    }\n    const keys1 = newValue ? Object.keys(newValue) : [];\n    const keys2 = oldValue ? Object.keys(oldValue) : [];\n    if (keys1.length !== keys2.length) {\n      this.onUpdate(newValue);\n      return;\n    }\n    for (const key of keys1) {\n      if (newValue[key] !== oldValue[key]) {\n        this.onUpdate(newValue);\n        return;\n      }\n    }\n  }\n  connectedCallback() {\n    this.ionRouteDataChanged.emit();\n  }\n  static get watchers() { return {\n    \"url\": [\"onUpdate\"],\n    \"component\": [\"onUpdate\"],\n    \"componentProps\": [\"onComponentProps\"]\n  }; }\n};\n\nconst RouteRedirect = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionRouteRedirectChanged = createEvent(this, \"ionRouteRedirectChanged\", 7);\n    this.from = undefined;\n    this.to = undefined;\n  }\n  propDidChange() {\n    this.ionRouteRedirectChanged.emit();\n  }\n  connectedCallback() {\n    this.ionRouteRedirectChanged.emit();\n  }\n  static get watchers() { return {\n    \"from\": [\"propDidChange\"],\n    \"to\": [\"propDidChange\"]\n  }; }\n};\n\nconst ROUTER_INTENT_NONE = 'root';\nconst ROUTER_INTENT_FORWARD = 'forward';\nconst ROUTER_INTENT_BACK = 'back';\n\n/** Join the non empty segments with \"/\". */\nconst generatePath = (segments) => {\n  const path = segments.filter((s) => s.length > 0).join('/');\n  return '/' + path;\n};\nconst generateUrl = (segments, useHash, queryString) => {\n  let url = generatePath(segments);\n  if (useHash) {\n    url = '#' + url;\n  }\n  if (queryString !== undefined) {\n    url += '?' + queryString;\n  }\n  return url;\n};\nconst writeSegments = (history, root, useHash, segments, direction, state, queryString) => {\n  const url = generateUrl([...parsePath(root).segments, ...segments], useHash, queryString);\n  if (direction === ROUTER_INTENT_FORWARD) {\n    history.pushState(state, '', url);\n  }\n  else {\n    history.replaceState(state, '', url);\n  }\n};\n/**\n * Transforms a chain to a list of segments.\n *\n * Notes:\n * - parameter segments of the form :param are replaced with their value,\n * - null is returned when a value is missing for any parameter segment.\n */\nconst chainToSegments = (chain) => {\n  const segments = [];\n  for (const route of chain) {\n    for (const segment of route.segments) {\n      if (segment[0] === ':') {\n        const param = route.params && route.params[segment.slice(1)];\n        if (!param) {\n          return null;\n        }\n        segments.push(param);\n      }\n      else if (segment !== '') {\n        segments.push(segment);\n      }\n    }\n  }\n  return segments;\n};\n/**\n * Removes the prefix segments from the path segments.\n *\n * Return:\n * - null when the path segments do not start with the passed prefix,\n * - the path segments after the prefix otherwise.\n */\nconst removePrefix = (prefix, segments) => {\n  if (prefix.length > segments.length) {\n    return null;\n  }\n  if (prefix.length <= 1 && prefix[0] === '') {\n    return segments;\n  }\n  for (let i = 0; i < prefix.length; i++) {\n    if (prefix[i] !== segments[i]) {\n      return null;\n    }\n  }\n  if (segments.length === prefix.length) {\n    return [''];\n  }\n  return segments.slice(prefix.length);\n};\nconst readSegments = (loc, root, useHash) => {\n  const prefix = parsePath(root).segments;\n  const pathname = useHash ? loc.hash.slice(1) : loc.pathname;\n  const segments = parsePath(pathname).segments;\n  return removePrefix(prefix, segments);\n};\n/**\n * Parses the path to:\n * - segments an array of '/' separated parts,\n * - queryString (undefined when no query string).\n */\nconst parsePath = (path) => {\n  let segments = [''];\n  let queryString;\n  if (path != null) {\n    const qsStart = path.indexOf('?');\n    if (qsStart > -1) {\n      queryString = path.substring(qsStart + 1);\n      path = path.substring(0, qsStart);\n    }\n    segments = path\n      .split('/')\n      .map((s) => s.trim())\n      .filter((s) => s.length > 0);\n    if (segments.length === 0) {\n      segments = [''];\n    }\n  }\n  return { segments, queryString };\n};\n\nconst printRoutes = (routes) => {\n  console.group(`[ion-core] ROUTES[${routes.length}]`);\n  for (const chain of routes) {\n    const segments = [];\n    chain.forEach((r) => segments.push(...r.segments));\n    const ids = chain.map((r) => r.id);\n    console.debug(`%c ${generatePath(segments)}`, 'font-weight: bold; padding-left: 20px', '=>\\t', `(${ids.join(', ')})`);\n  }\n  console.groupEnd();\n};\nconst printRedirects = (redirects) => {\n  console.group(`[ion-core] REDIRECTS[${redirects.length}]`);\n  for (const redirect of redirects) {\n    if (redirect.to) {\n      console.debug('FROM: ', `$c ${generatePath(redirect.from)}`, 'font-weight: bold', ' TO: ', `$c ${generatePath(redirect.to.segments)}`, 'font-weight: bold');\n    }\n  }\n  console.groupEnd();\n};\n\n/**\n * Activates the passed route chain.\n *\n * There must be exactly one outlet per route entry in the chain.\n *\n * The methods calls setRouteId on each of the outlet with the corresponding route entry in the chain.\n * setRouteId will create or select the view in the outlet.\n */\nconst writeNavState = async (root, chain, direction, index, changed = false, animation) => {\n  try {\n    // find next navigation outlet in the DOM\n    const outlet = searchNavNode(root);\n    // make sure we can continue interacting the DOM, otherwise abort\n    if (index >= chain.length || !outlet) {\n      return changed;\n    }\n    await new Promise((resolve) => componentOnReady(outlet, resolve));\n    const route = chain[index];\n    const result = await outlet.setRouteId(route.id, route.params, direction, animation);\n    // if the outlet changed the page, reset navigation to neutral (no direction)\n    // this means nested outlets will not animate\n    if (result.changed) {\n      direction = ROUTER_INTENT_NONE;\n      changed = true;\n    }\n    // recursively set nested outlets\n    changed = await writeNavState(result.element, chain, direction, index + 1, changed, animation);\n    // once all nested outlets are visible let's make the parent visible too,\n    // using markVisible prevents flickering\n    if (result.markVisible) {\n      await result.markVisible();\n    }\n    return changed;\n  }\n  catch (e) {\n    console.error(e);\n    return false;\n  }\n};\n/**\n * Recursively walks the outlet in the DOM.\n *\n * The function returns a list of RouteID corresponding to each of the outlet and the last outlet without a RouteID.\n */\nconst readNavState = async (root) => {\n  const ids = [];\n  let outlet;\n  let node = root;\n  // eslint-disable-next-line no-cond-assign\n  while ((outlet = searchNavNode(node))) {\n    const id = await outlet.getRouteId();\n    if (id) {\n      node = id.element;\n      id.element = undefined;\n      ids.push(id);\n    }\n    else {\n      break;\n    }\n  }\n  return { ids, outlet };\n};\nconst waitUntilNavNode = () => {\n  if (searchNavNode(document.body)) {\n    return Promise.resolve();\n  }\n  return new Promise((resolve) => {\n    window.addEventListener('ionNavWillLoad', () => resolve(), { once: true });\n  });\n};\n/** Selector for all the outlets supported by the router. */\nconst OUTLET_SELECTOR = ':not([no-router]) ion-nav, :not([no-router]) ion-tabs, :not([no-router]) ion-router-outlet';\nconst searchNavNode = (root) => {\n  if (!root) {\n    return undefined;\n  }\n  if (root.matches(OUTLET_SELECTOR)) {\n    return root;\n  }\n  const outlet = root.querySelector(OUTLET_SELECTOR);\n  return outlet !== null && outlet !== void 0 ? outlet : undefined;\n};\n\n/**\n * Returns whether the given redirect matches the given path segments.\n *\n * A redirect matches when the segments of the path and redirect.from are equal.\n * Note that segments are only checked until redirect.from contains a '*' which matches any path segment.\n * The path ['some', 'path', 'to', 'page'] matches both ['some', 'path', 'to', 'page'] and ['some', 'path', '*'].\n */\nconst matchesRedirect = (segments, redirect) => {\n  const { from, to } = redirect;\n  if (to === undefined) {\n    return false;\n  }\n  if (from.length > segments.length) {\n    return false;\n  }\n  for (let i = 0; i < from.length; i++) {\n    const expected = from[i];\n    if (expected === '*') {\n      return true;\n    }\n    if (expected !== segments[i]) {\n      return false;\n    }\n  }\n  return from.length === segments.length;\n};\n/** Returns the first redirect matching the path segments or undefined when no match found. */\nconst findRouteRedirect = (segments, redirects) => {\n  return redirects.find((redirect) => matchesRedirect(segments, redirect));\n};\nconst matchesIDs = (ids, chain) => {\n  const len = Math.min(ids.length, chain.length);\n  let score = 0;\n  for (let i = 0; i < len; i++) {\n    const routeId = ids[i];\n    const routeChain = chain[i];\n    // Skip results where the route id does not match the chain at the same index\n    if (routeId.id.toLowerCase() !== routeChain.id) {\n      break;\n    }\n    if (routeId.params) {\n      const routeIdParams = Object.keys(routeId.params);\n      // Only compare routes with the chain that have the same number of parameters.\n      if (routeIdParams.length === routeChain.segments.length) {\n        // Maps the route's params into a path based on the path variable names,\n        // to compare against the route chain format.\n        //\n        // Before:\n        // ```ts\n        // {\n        //  params: {\n        //    s1: 'a',\n        //    s2: 'b'\n        //  }\n        // }\n        // ```\n        //\n        // After:\n        // ```ts\n        // [':s1',':s2']\n        // ```\n        //\n        const pathWithParams = routeIdParams.map((key) => `:${key}`);\n        for (let j = 0; j < pathWithParams.length; j++) {\n          // Skip results where the path variable is not a match\n          if (pathWithParams[j].toLowerCase() !== routeChain.segments[j]) {\n            break;\n          }\n          // Weight path matches for the same index higher.\n          score++;\n        }\n      }\n    }\n    // Weight id matches\n    score++;\n  }\n  return score;\n};\n/**\n * Matches the segments against the chain.\n *\n * Returns:\n * - null when there is no match,\n * - a chain with the params properties updated with the parameter segments on match.\n */\nconst matchesSegments = (segments, chain) => {\n  const inputSegments = new RouterSegments(segments);\n  let matchesDefault = false;\n  let allparams;\n  for (let i = 0; i < chain.length; i++) {\n    const chainSegments = chain[i].segments;\n    if (chainSegments[0] === '') {\n      matchesDefault = true;\n    }\n    else {\n      for (const segment of chainSegments) {\n        const data = inputSegments.next();\n        // data param\n        if (segment[0] === ':') {\n          if (data === '') {\n            return null;\n          }\n          allparams = allparams || [];\n          const params = allparams[i] || (allparams[i] = {});\n          params[segment.slice(1)] = data;\n        }\n        else if (data !== segment) {\n          return null;\n        }\n      }\n      matchesDefault = false;\n    }\n  }\n  const matches = matchesDefault ? matchesDefault === (inputSegments.next() === '') : true;\n  if (!matches) {\n    return null;\n  }\n  if (allparams) {\n    return chain.map((route, i) => ({\n      id: route.id,\n      segments: route.segments,\n      params: mergeParams(route.params, allparams[i]),\n      beforeEnter: route.beforeEnter,\n      beforeLeave: route.beforeLeave,\n    }));\n  }\n  return chain;\n};\n/**\n * Merges the route parameter objects.\n * Returns undefined when both parameters are undefined.\n */\nconst mergeParams = (a, b) => {\n  return a || b ? Object.assign(Object.assign({}, a), b) : undefined;\n};\n/**\n * Finds the best match for the ids in the chains.\n *\n * Returns the best match or null when no match is found.\n * When a chain is returned the parameters are updated from the RouteIDs.\n * That is they contain both the componentProps of the <ion-route> and the parameter segment.\n */\nconst findChainForIDs = (ids, chains) => {\n  let match = null;\n  let maxMatches = 0;\n  for (const chain of chains) {\n    const score = matchesIDs(ids, chain);\n    if (score > maxMatches) {\n      match = chain;\n      maxMatches = score;\n    }\n  }\n  if (match) {\n    return match.map((route, i) => {\n      var _a;\n      return ({\n        id: route.id,\n        segments: route.segments,\n        params: mergeParams(route.params, (_a = ids[i]) === null || _a === void 0 ? void 0 : _a.params),\n      });\n    });\n  }\n  return null;\n};\n/**\n * Finds the best match for the segments in the chains.\n *\n * Returns the best match or null when no match is found.\n * When a chain is returned the parameters are updated from the segments.\n * That is they contain both the componentProps of the <ion-route> and the parameter segments.\n */\nconst findChainForSegments = (segments, chains) => {\n  let match = null;\n  let bestScore = 0;\n  for (const chain of chains) {\n    const matchedChain = matchesSegments(segments, chain);\n    if (matchedChain !== null) {\n      const score = computePriority(matchedChain);\n      if (score > bestScore) {\n        bestScore = score;\n        match = matchedChain;\n      }\n    }\n  }\n  return match;\n};\n/**\n * Computes the priority of a chain.\n *\n * Parameter segments are given a lower priority over fixed segments.\n *\n * Considering the following 2 chains matching the path /path/to/page:\n * - /path/to/:where\n * - /path/to/page\n *\n * The second one will be given a higher priority because \"page\" is a fixed segment (vs \":where\", a parameter segment).\n */\nconst computePriority = (chain) => {\n  let score = 1;\n  let level = 1;\n  for (const route of chain) {\n    for (const segment of route.segments) {\n      if (segment[0] === ':') {\n        score += Math.pow(1, level);\n      }\n      else if (segment !== '') {\n        score += Math.pow(2, level);\n      }\n      level++;\n    }\n  }\n  return score;\n};\nclass RouterSegments {\n  constructor(segments) {\n    this.segments = segments.slice();\n  }\n  next() {\n    if (this.segments.length > 0) {\n      return this.segments.shift();\n    }\n    return '';\n  }\n}\n\nconst readProp = (el, prop) => {\n  if (prop in el) {\n    return el[prop];\n  }\n  if (el.hasAttribute(prop)) {\n    return el.getAttribute(prop);\n  }\n  return null;\n};\n/**\n * Extracts the redirects (that is <ion-route-redirect> elements inside the root).\n *\n * The redirects are returned as a list of RouteRedirect.\n */\nconst readRedirects = (root) => {\n  return Array.from(root.children)\n    .filter((el) => el.tagName === 'ION-ROUTE-REDIRECT')\n    .map((el) => {\n    const to = readProp(el, 'to');\n    return {\n      from: parsePath(readProp(el, 'from')).segments,\n      to: to == null ? undefined : parsePath(to),\n    };\n  });\n};\n/**\n * Extracts all the routes (that is <ion-route> elements inside the root).\n *\n * The routes are returned as a list of chains - the flattened tree.\n */\nconst readRoutes = (root) => {\n  return flattenRouterTree(readRouteNodes(root));\n};\n/**\n * Reads the route nodes as a tree modeled after the DOM tree of <ion-route> elements.\n *\n * Note: routes without a component are ignored together with their children.\n */\nconst readRouteNodes = (node) => {\n  return Array.from(node.children)\n    .filter((el) => el.tagName === 'ION-ROUTE' && el.component)\n    .map((el) => {\n    const component = readProp(el, 'component');\n    return {\n      segments: parsePath(readProp(el, 'url')).segments,\n      id: component.toLowerCase(),\n      params: el.componentProps,\n      beforeLeave: el.beforeLeave,\n      beforeEnter: el.beforeEnter,\n      children: readRouteNodes(el),\n    };\n  });\n};\n/**\n * Flattens a RouterTree in a list of chains.\n *\n * Each chain represents a path from the root node to a terminal node.\n */\nconst flattenRouterTree = (nodes) => {\n  const chains = [];\n  for (const node of nodes) {\n    flattenNode([], chains, node);\n  }\n  return chains;\n};\n/** Flattens a route node recursively and push each branch to the chains list. */\nconst flattenNode = (chain, chains, node) => {\n  chain = [\n    ...chain,\n    {\n      id: node.id,\n      segments: node.segments,\n      params: node.params,\n      beforeLeave: node.beforeLeave,\n      beforeEnter: node.beforeEnter,\n    },\n  ];\n  if (node.children.length === 0) {\n    chains.push(chain);\n    return;\n  }\n  for (const child of node.children) {\n    flattenNode(chain, chains, child);\n  }\n};\n\nconst Router = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionRouteWillChange = createEvent(this, \"ionRouteWillChange\", 7);\n    this.ionRouteDidChange = createEvent(this, \"ionRouteDidChange\", 7);\n    this.previousPath = null;\n    this.busy = false;\n    this.state = 0;\n    this.lastState = 0;\n    this.root = '/';\n    this.useHash = true;\n  }\n  async componentWillLoad() {\n    await waitUntilNavNode();\n    const canProceed = await this.runGuards(this.getSegments());\n    if (canProceed !== true) {\n      if (typeof canProceed === 'object') {\n        const { redirect } = canProceed;\n        const path = parsePath(redirect);\n        this.setSegments(path.segments, ROUTER_INTENT_NONE, path.queryString);\n        await this.writeNavStateRoot(path.segments, ROUTER_INTENT_NONE);\n      }\n    }\n    else {\n      await this.onRoutesChanged();\n    }\n  }\n  componentDidLoad() {\n    window.addEventListener('ionRouteRedirectChanged', debounce(this.onRedirectChanged.bind(this), 10));\n    window.addEventListener('ionRouteDataChanged', debounce(this.onRoutesChanged.bind(this), 100));\n  }\n  async onPopState() {\n    const direction = this.historyDirection();\n    let segments = this.getSegments();\n    const canProceed = await this.runGuards(segments);\n    if (canProceed !== true) {\n      if (typeof canProceed === 'object') {\n        segments = parsePath(canProceed.redirect).segments;\n      }\n      else {\n        return false;\n      }\n    }\n    return this.writeNavStateRoot(segments, direction);\n  }\n  onBackButton(ev) {\n    ev.detail.register(0, (processNextHandler) => {\n      this.back();\n      processNextHandler();\n    });\n  }\n  /** @internal */\n  async canTransition() {\n    const canProceed = await this.runGuards();\n    if (canProceed !== true) {\n      if (typeof canProceed === 'object') {\n        return canProceed.redirect;\n      }\n      else {\n        return false;\n      }\n    }\n    return true;\n  }\n  /**\n   * Navigate to the specified path.\n   *\n   * @param path The path to navigate to.\n   * @param direction The direction of the animation. Defaults to `\"forward\"`.\n   */\n  async push(path, direction = 'forward', animation) {\n    var _a;\n    if (path.startsWith('.')) {\n      const currentPath = (_a = this.previousPath) !== null && _a !== void 0 ? _a : '/';\n      // Convert currentPath to an URL by pre-pending a protocol and a host to resolve the relative path.\n      const url = new URL(path, `https://host/${currentPath}`);\n      path = url.pathname + url.search;\n    }\n    let parsedPath = parsePath(path);\n    const canProceed = await this.runGuards(parsedPath.segments);\n    if (canProceed !== true) {\n      if (typeof canProceed === 'object') {\n        parsedPath = parsePath(canProceed.redirect);\n      }\n      else {\n        return false;\n      }\n    }\n    this.setSegments(parsedPath.segments, direction, parsedPath.queryString);\n    return this.writeNavStateRoot(parsedPath.segments, direction, animation);\n  }\n  /** Go back to previous page in the window.history. */\n  back() {\n    window.history.back();\n    return Promise.resolve(this.waitPromise);\n  }\n  /** @internal */\n  async printDebug() {\n    printRoutes(readRoutes(this.el));\n    printRedirects(readRedirects(this.el));\n  }\n  /** @internal */\n  async navChanged(direction) {\n    if (this.busy) {\n      console.warn('[ion-router] router is busy, navChanged was cancelled');\n      return false;\n    }\n    const { ids, outlet } = await readNavState(window.document.body);\n    const routes = readRoutes(this.el);\n    const chain = findChainForIDs(ids, routes);\n    if (!chain) {\n      console.warn('[ion-router] no matching URL for ', ids.map((i) => i.id));\n      return false;\n    }\n    const segments = chainToSegments(chain);\n    if (!segments) {\n      console.warn('[ion-router] router could not match path because some required param is missing');\n      return false;\n    }\n    this.setSegments(segments, direction);\n    await this.safeWriteNavState(outlet, chain, ROUTER_INTENT_NONE, segments, null, ids.length);\n    return true;\n  }\n  /** This handler gets called when a `ion-route-redirect` component is added to the DOM or if the from or to property of such node changes. */\n  onRedirectChanged() {\n    const segments = this.getSegments();\n    if (segments && findRouteRedirect(segments, readRedirects(this.el))) {\n      this.writeNavStateRoot(segments, ROUTER_INTENT_NONE);\n    }\n  }\n  /** This handler gets called when a `ion-route` component is added to the DOM or if the from or to property of such node changes. */\n  onRoutesChanged() {\n    return this.writeNavStateRoot(this.getSegments(), ROUTER_INTENT_NONE);\n  }\n  historyDirection() {\n    var _a;\n    const win = window;\n    if (win.history.state === null) {\n      this.state++;\n      win.history.replaceState(this.state, win.document.title, (_a = win.document.location) === null || _a === void 0 ? void 0 : _a.href);\n    }\n    const state = win.history.state;\n    const lastState = this.lastState;\n    this.lastState = state;\n    if (state > lastState || (state >= lastState && lastState > 0)) {\n      return ROUTER_INTENT_FORWARD;\n    }\n    if (state < lastState) {\n      return ROUTER_INTENT_BACK;\n    }\n    return ROUTER_INTENT_NONE;\n  }\n  async writeNavStateRoot(segments, direction, animation) {\n    if (!segments) {\n      console.error('[ion-router] URL is not part of the routing set');\n      return false;\n    }\n    // lookup redirect rule\n    const redirects = readRedirects(this.el);\n    const redirect = findRouteRedirect(segments, redirects);\n    let redirectFrom = null;\n    if (redirect) {\n      const { segments: toSegments, queryString } = redirect.to;\n      this.setSegments(toSegments, direction, queryString);\n      redirectFrom = redirect.from;\n      segments = toSegments;\n    }\n    // lookup route chain\n    const routes = readRoutes(this.el);\n    const chain = findChainForSegments(segments, routes);\n    if (!chain) {\n      console.error('[ion-router] the path does not match any route');\n      return false;\n    }\n    // write DOM give\n    return this.safeWriteNavState(document.body, chain, direction, segments, redirectFrom, 0, animation);\n  }\n  async safeWriteNavState(node, chain, direction, segments, redirectFrom, index = 0, animation) {\n    const unlock = await this.lock();\n    let changed = false;\n    try {\n      changed = await this.writeNavState(node, chain, direction, segments, redirectFrom, index, animation);\n    }\n    catch (e) {\n      console.error(e);\n    }\n    unlock();\n    return changed;\n  }\n  async lock() {\n    const p = this.waitPromise;\n    let resolve;\n    this.waitPromise = new Promise((r) => (resolve = r));\n    if (p !== undefined) {\n      await p;\n    }\n    return resolve;\n  }\n  /**\n   * Executes the beforeLeave hook of the source route and the beforeEnter hook of the target route if they exist.\n   *\n   * When the beforeLeave hook does not return true (to allow navigating) then that value is returned early and the beforeEnter is executed.\n   * Otherwise the beforeEnterHook hook of the target route is executed.\n   */\n  async runGuards(to = this.getSegments(), from) {\n    if (from === undefined) {\n      from = parsePath(this.previousPath).segments;\n    }\n    if (!to || !from) {\n      return true;\n    }\n    const routes = readRoutes(this.el);\n    const fromChain = findChainForSegments(from, routes);\n    const beforeLeaveHook = fromChain && fromChain[fromChain.length - 1].beforeLeave;\n    const canLeave = beforeLeaveHook ? await beforeLeaveHook() : true;\n    if (canLeave === false || typeof canLeave === 'object') {\n      return canLeave;\n    }\n    const toChain = findChainForSegments(to, routes);\n    const beforeEnterHook = toChain && toChain[toChain.length - 1].beforeEnter;\n    return beforeEnterHook ? beforeEnterHook() : true;\n  }\n  async writeNavState(node, chain, direction, segments, redirectFrom, index = 0, animation) {\n    if (this.busy) {\n      console.warn('[ion-router] router is busy, transition was cancelled');\n      return false;\n    }\n    this.busy = true;\n    // generate route event and emit will change\n    const routeEvent = this.routeChangeEvent(segments, redirectFrom);\n    if (routeEvent) {\n      this.ionRouteWillChange.emit(routeEvent);\n    }\n    const changed = await writeNavState(node, chain, direction, index, false, animation);\n    this.busy = false;\n    // emit did change\n    if (routeEvent) {\n      this.ionRouteDidChange.emit(routeEvent);\n    }\n    return changed;\n  }\n  setSegments(segments, direction, queryString) {\n    this.state++;\n    writeSegments(window.history, this.root, this.useHash, segments, direction, this.state, queryString);\n  }\n  getSegments() {\n    return readSegments(window.location, this.root, this.useHash);\n  }\n  routeChangeEvent(toSegments, redirectFromSegments) {\n    const from = this.previousPath;\n    const to = generatePath(toSegments);\n    this.previousPath = to;\n    if (to === from) {\n      return null;\n    }\n    const redirectedFrom = redirectFromSegments ? generatePath(redirectFromSegments) : null;\n    return {\n      from,\n      redirectedFrom,\n      to,\n    };\n  }\n  get el() { return getElement(this); }\n};\n\nconst routerLinkCss = \":host{--background:transparent;--color:var(--ion-color-primary, #3880ff);background:var(--background);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}a{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit}\";\n\nconst RouterLink = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.onClick = (ev) => {\n      openURL(this.href, ev, this.routerDirection, this.routerAnimation);\n    };\n    this.color = undefined;\n    this.href = undefined;\n    this.rel = undefined;\n    this.routerDirection = 'forward';\n    this.routerAnimation = undefined;\n    this.target = undefined;\n  }\n  render() {\n    const mode = getIonMode(this);\n    const attrs = {\n      href: this.href,\n      rel: this.rel,\n      target: this.target,\n    };\n    return (h(Host, { onClick: this.onClick, class: createColorClasses(this.color, {\n        [mode]: true,\n        'ion-activatable': true,\n      }) }, h(\"a\", Object.assign({}, attrs), h(\"slot\", null))));\n  }\n};\nRouterLink.style = routerLinkCss;\n\nexport { Route as ion_route, RouteRedirect as ion_route_redirect, Router as ion_router, RouterLink as ion_router_link };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst hostContext = (selector, el) => {\n  return el.closest(selector) !== null;\n};\n/**\n * Create the mode and color classes for the component based on the classes passed in\n */\nconst createColorClasses = (color, cssClassMap) => {\n  return typeof color === 'string' && color.length > 0\n    ? Object.assign({ 'ion-color': true, [`ion-color-${color}`]: true }, cssClassMap) : cssClassMap;\n};\nconst getClassList = (classes) => {\n  if (classes !== undefined) {\n    const array = Array.isArray(classes) ? classes : classes.split(' ');\n    return array\n      .filter((c) => c != null)\n      .map((c) => c.trim())\n      .filter((c) => c !== '');\n  }\n  return [];\n};\nconst getClassMap = (classes) => {\n  const map = {};\n  getClassList(classes).forEach((c) => (map[c] = true));\n  return map;\n};\nconst SCHEME = /^[a-z][a-z0-9+\\-.]*:/;\nconst openURL = async (url, ev, direction, animation) => {\n  if (url != null && url[0] !== '#' && !SCHEME.test(url)) {\n    const router = document.querySelector('ion-router');\n    if (router) {\n      if (ev != null) {\n        ev.preventDefault();\n      }\n      return router.push(url, direction, animation);\n    }\n  }\n  return false;\n};\n\nexport { createColorClasses as c, getClassMap as g, hostContext as h, openURL as o };\n"], "names": ["r", "registerInstance", "d", "createEvent", "f", "getElement", "h", "H", "Host", "c", "componentOnReady", "q", "debounce", "o", "openURL", "createColorClasses", "b", "getIonMode", "Route", "constructor", "hostRef", "ionRouteDataChanged", "url", "component", "undefined", "componentProps", "beforeLeave", "beforeEnter", "onUpdate", "newValue", "emit", "onComponentProps", "oldValue", "keys1", "Object", "keys", "keys2", "length", "key", "connectedCallback", "watchers", "RouteRedirect", "ionRouteRedirectChanged", "from", "to", "propDidChange", "ROUTER_INTENT_NONE", "ROUTER_INTENT_FORWARD", "ROUTER_INTENT_BACK", "generatePath", "segments", "path", "filter", "s", "join", "generateUrl", "useHash", "queryString", "writeSegments", "history", "root", "direction", "state", "parsePath", "pushState", "replaceState", "chainToSegments", "chain", "route", "segment", "param", "params", "slice", "push", "removePrefix", "prefix", "i", "readSegments", "loc", "pathname", "hash", "qsStart", "indexOf", "substring", "split", "map", "trim", "printRoutes", "routes", "console", "group", "for<PERSON>ach", "ids", "id", "debug", "groupEnd", "printRedirects", "redirects", "redirect", "writeNavState", "_ref", "_asyncToGenerator", "index", "changed", "animation", "outlet", "searchNavNode", "Promise", "resolve", "result", "setRouteId", "element", "markVisible", "e", "error", "_x", "_x2", "_x3", "_x4", "apply", "arguments", "readNavState", "_ref2", "node", "getRouteId", "_x5", "waitUntilNavNode", "document", "body", "window", "addEventListener", "once", "OUTLET_SELECTOR", "matches", "querySelector", "matchesRedirect", "expected", "findRouteRedirect", "find", "matchesIDs", "len", "Math", "min", "score", "routeId", "routeChain", "toLowerCase", "routeIdParams", "pathWithParams", "j", "matchesSegments", "inputSegments", "RouterSegments", "matchesDefault", "allparams", "chainSegments", "data", "next", "mergeParams", "a", "assign", "findChainForIDs", "chains", "match", "max<PERSON><PERSON><PERSON>", "_a", "findChainForSegments", "bestScore", "<PERSON><PERSON><PERSON><PERSON>", "computePriority", "level", "pow", "shift", "readProp", "el", "prop", "hasAttribute", "getAttribute", "readRedirects", "Array", "children", "tagName", "readRoutes", "flattenRouterTree", "readRouteNodes", "nodes", "flattenNode", "child", "Router", "ionRouteWillChange", "ionRouteDidChange", "previousPath", "busy", "lastState", "componentWillLoad", "_this", "canProceed", "runGuards", "getSegments", "setSegments", "writeNavStateRoot", "onRoutesChanged", "componentDidLoad", "onRedirectChanged", "bind", "onPopState", "_this2", "historyDirection", "onBackButton", "ev", "detail", "register", "processNextHandler", "back", "canTransition", "_this3", "_this4", "startsWith", "currentPath", "URL", "search", "parsed<PERSON><PERSON>", "waitPromise", "printDebug", "_this5", "navChanged", "_this6", "warn", "safeWriteNavState", "win", "title", "location", "href", "_this7", "redirectFrom", "toSegments", "_this8", "unlock", "lock", "_this9", "p", "_this10", "fromChain", "beforeLeaveHook", "canLeave", "<PERSON><PERSON><PERSON><PERSON>", "beforeEnterHook", "_this11", "routeEvent", "routeChangeEvent", "redirectFromSegments", "redirectedFrom", "routerLinkCss", "RouterLink", "onClick", "routerDirection", "routerAnimation", "color", "rel", "target", "render", "mode", "attrs", "class", "style", "ion_route", "ion_route_redirect", "ion_router", "ion_router_link", "hostContext", "selector", "closest", "cssClassMap", "getClassList", "classes", "array", "isArray", "getClassMap", "SCHEME", "test", "router", "preventDefault", "g"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0, 1]}