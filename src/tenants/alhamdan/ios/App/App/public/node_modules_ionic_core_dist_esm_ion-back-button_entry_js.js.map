{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-back-button_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC2F;AACxB;AAC2B;AAClB;AACF;AAE1E,MAAMoB,gBAAgB,GAAG,wyIAAwyI;AAEj0I,MAAMC,eAAe,GAAG,qsJAAqsJ;AAE7tJ,MAAMC,UAAU,GAAG,MAAM;EACvBC,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAC,KAAA;IACnBxB,qDAAgB,CAAC,IAAI,EAAEuB,OAAO,CAAC;IAC/B,IAAI,CAACE,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,OAAO;MAAA,IAAAC,IAAA,GAAAC,8KAAA,CAAG,WAAOC,EAAE,EAAK;QAC3B,MAAMC,GAAG,GAAGN,KAAI,CAACO,EAAE,CAACC,OAAO,CAAC,SAAS,CAAC;QACtCH,EAAE,CAACI,cAAc,CAAC,CAAC;QACnB,IAAIH,GAAG,WAAWA,GAAG,CAACI,SAAS,CAAC,CAAC,CAAC,EAAE;UAClC,OAAOJ,GAAG,CAACK,GAAG,CAAC;YAAEC,gBAAgB,EAAEZ,KAAI,CAACa,eAAe;YAAEC,UAAU,EAAE;UAAK,CAAC,CAAC;QAC9E;QACA,OAAO7B,qDAAO,CAACe,KAAI,CAACe,WAAW,EAAEV,EAAE,EAAE,MAAM,EAAEL,KAAI,CAACa,eAAe,CAAC;MACpE,CAAC;MAAA,iBAAAG,EAAA;QAAA,OAAAb,IAAA,CAAAc,KAAA,OAAAC,SAAA;MAAA;IAAA;IACD,IAAI,CAACC,KAAK,GAAGC,SAAS;IACtB,IAAI,CAACL,WAAW,GAAGK,SAAS;IAC5B,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,IAAI,GAAGF,SAAS;IACrB,IAAI,CAACG,IAAI,GAAGH,SAAS;IACrB,IAAI,CAACI,IAAI,GAAG,QAAQ;IACpB,IAAI,CAACX,eAAe,GAAGO,SAAS;EAClC;EACAK,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAACxB,mBAAmB,GAAGlB,uDAAqB,CAAC,IAAI,CAACwB,EAAE,CAAC;IACzD,IAAI,IAAI,CAACQ,WAAW,KAAKK,SAAS,EAAE;MAClC,IAAI,CAACL,WAAW,GAAGvB,wDAAM,CAACkC,GAAG,CAAC,uBAAuB,CAAC;IACxD;EACF;EACA,IAAIC,cAAcA,CAAA,EAAG;IACnB,MAAML,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,IAAIA,IAAI,IAAI,IAAI,EAAE;MAChB;MACA,OAAOA,IAAI;IACb;IACA,IAAI5B,4DAAU,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE;MAC9B;MACA,OAAOF,wDAAM,CAACkC,GAAG,CAAC,gBAAgB,EAAErC,iDAAW,CAAC;IAClD;IACA;IACA,OAAOG,wDAAM,CAACkC,GAAG,CAAC,gBAAgB,EAAEnC,iDAAc,CAAC;EACrD;EACA,IAAIqC,cAAcA,CAAA,EAAG;IACnB,MAAMC,qBAAqB,GAAGnC,4DAAU,CAAC,IAAI,CAAC,KAAK,KAAK,GAAG,MAAM,GAAG,IAAI;IACxE,OAAO,IAAI,CAAC6B,IAAI,IAAI,IAAI,GAAG,IAAI,CAACA,IAAI,GAAG/B,wDAAM,CAACkC,GAAG,CAAC,gBAAgB,EAAEG,qBAAqB,CAAC;EAC5F;EACA,IAAIC,WAAWA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACH,cAAc,IAAI,CAAC,IAAI,CAACC,cAAc;EACpD;EACA,IAAIG,UAAUA,CAAA,EAAG;IACf;IACA;IACA,IAAI,IAAI,CAACD,WAAW,EAAE;MACpB,OAAO,WAAW;IACpB;IACA,OAAO,SAAS;EAClB;EACAE,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEb,KAAK;MAAEJ,WAAW;MAAEM,QAAQ;MAAEG,IAAI;MAAEM,WAAW;MAAEH,cAAc;MAAEC,cAAc;MAAEN,IAAI;MAAErB;IAAqB,CAAC,GAAG,IAAI;IAC5H,MAAMgC,cAAc,GAAGlB,WAAW,KAAKK,SAAS;IAChD,MAAMc,IAAI,GAAGxC,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAMyC,SAAS,GAAGlC,mBAAmB,CAAC,YAAY,CAAC,IAAI2B,cAAc,IAAI,MAAM;IAC/E,OAAQnD,qDAAC,CAACE,iDAAI,EAAE;MAAEuB,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEkC,KAAK,EAAEjD,qDAAkB,CAACgC,KAAK,EAAE;QACtE,CAACe,IAAI,GAAG,IAAI;QACZG,MAAM,EAAE,IAAI;QACZ,sBAAsB,EAAEhB,QAAQ;QAChC,2BAA2B,EAAES,WAAW;QACxC,YAAY,EAAE1C,qDAAW,CAAC,aAAa,EAAE,IAAI,CAACmB,EAAE,CAAC;QACjD,kBAAkB,EAAEnB,qDAAW,CAAC,oBAAoB,EAAE,IAAI,CAACmB,EAAE,CAAC;QAC9D,iBAAiB,EAAE,IAAI;QACvB,eAAe,EAAE,IAAI;QACrB,kBAAkB,EAAE0B;MACtB,CAAC;IAAE,CAAC,EAAExD,qDAAC,CAAC,QAAQ,EAAE;MAAE+C,IAAI,EAAEA,IAAI;MAAEH,QAAQ,EAAEA,QAAQ;MAAEe,KAAK,EAAE,eAAe;MAAEE,IAAI,EAAE,QAAQ;MAAE,YAAY,EAAEH;IAAU,CAAC,EAAE1D,qDAAC,CAAC,MAAM,EAAE;MAAE2D,KAAK,EAAE;IAAe,CAAC,EAAET,cAAc,IAAKlD,qDAAC,CAAC,UAAU,EAAE;MAAE6D,IAAI,EAAE,MAAM;MAAEhB,IAAI,EAAEK,cAAc;MAAE,aAAa,EAAE,MAAM;MAAEY,IAAI,EAAE,KAAK;MAAE,UAAU,EAAEjB,IAAI,KAAKF;IAAU,CAAC,CAAE,EAAEQ,cAAc,IAAKnD,qDAAC,CAAC,MAAM,EAAE;MAAE6D,IAAI,EAAE,MAAM;MAAE,aAAa,EAAE,MAAM;MAAEF,KAAK,EAAE;IAAc,CAAC,EAAER,cAAc,CAAE,CAAC,EAAEM,IAAI,KAAK,IAAI,IAAIzD,qDAAC,CAAC,mBAAmB,EAAE;MAAE+C,IAAI,EAAE,IAAI,CAACO;IAAW,CAAC,CAAC,CAAC,CAAC;EACle;EACA,IAAIxB,EAAEA,CAAA,EAAG;IAAE,OAAO1B,qDAAU,CAAC,IAAI,CAAC;EAAE;AACtC,CAAC;AACDgB,UAAU,CAAC2C,KAAK,GAAG;EACjBC,GAAG,EAAE9C,gBAAgB;EACrB+C,EAAE,EAAE9C;AACN,CAAC;;;;;;;;;;;;;;;;;;;;ACzFD;AACA;AACA;AACA,MAAMR,WAAW,GAAGA,CAACwD,QAAQ,EAAErC,EAAE,KAAK;EACpC,OAAOA,EAAE,CAACC,OAAO,CAACoC,QAAQ,CAAC,KAAK,IAAI;AACtC,CAAC;AACD;AACA;AACA;AACA,MAAMzD,kBAAkB,GAAGA,CAACgC,KAAK,EAAE0B,WAAW,KAAK;EACjD,OAAO,OAAO1B,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAAC2B,MAAM,GAAG,CAAC,GAChDC,MAAM,CAACC,MAAM,CAAC;IAAE,WAAW,EAAE,IAAI;IAAE,CAAE,aAAY7B,KAAM,EAAC,GAAG;EAAK,CAAC,EAAE0B,WAAW,CAAC,GAAGA,WAAW;AACnG,CAAC;AACD,MAAMI,YAAY,GAAIC,OAAO,IAAK;EAChC,IAAIA,OAAO,KAAK9B,SAAS,EAAE;IACzB,MAAM+B,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACH,OAAO,CAAC,GAAGA,OAAO,GAAGA,OAAO,CAACI,KAAK,CAAC,GAAG,CAAC;IACnE,OAAOH,KAAK,CACTI,MAAM,CAAErE,CAAC,IAAKA,CAAC,IAAI,IAAI,CAAC,CACxBsE,GAAG,CAAEtE,CAAC,IAAKA,CAAC,CAACuE,IAAI,CAAC,CAAC,CAAC,CACpBF,MAAM,CAAErE,CAAC,IAAKA,CAAC,KAAK,EAAE,CAAC;EAC5B;EACA,OAAO,EAAE;AACX,CAAC;AACD,MAAMwE,WAAW,GAAIR,OAAO,IAAK;EAC/B,MAAMM,GAAG,GAAG,CAAC,CAAC;EACdP,YAAY,CAACC,OAAO,CAAC,CAACS,OAAO,CAAEzE,CAAC,IAAMsE,GAAG,CAACtE,CAAC,CAAC,GAAG,IAAK,CAAC;EACrD,OAAOsE,GAAG;AACZ,CAAC;AACD,MAAMI,MAAM,GAAG,sBAAsB;AACrC,MAAM3E,OAAO;EAAA,IAAAkB,IAAA,GAAAC,8KAAA,CAAG,WAAOyD,GAAG,EAAExD,EAAE,EAAEyD,SAAS,EAAEC,SAAS,EAAK;IACvD,IAAIF,GAAG,IAAI,IAAI,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAACD,MAAM,CAACI,IAAI,CAACH,GAAG,CAAC,EAAE;MACtD,MAAMI,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,YAAY,CAAC;MACnD,IAAIF,MAAM,EAAE;QACV,IAAI5D,EAAE,IAAI,IAAI,EAAE;UACdA,EAAE,CAACI,cAAc,CAAC,CAAC;QACrB;QACA,OAAOwD,MAAM,CAACG,IAAI,CAACP,GAAG,EAAEC,SAAS,EAAEC,SAAS,CAAC;MAC/C;IACF;IACA,OAAO,KAAK;EACd,CAAC;EAAA,gBAXK9E,OAAOA,CAAA+B,EAAA,EAAAqD,GAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAApE,IAAA,CAAAc,KAAA,OAAAC,SAAA;EAAA;AAAA,GAWZ", "sources": ["./node_modules/@ionic/core/dist/esm/ion-back-button.entry.js", "./node_modules/@ionic/core/dist/esm/theme-17531cdf.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, H as Host, f as getElement } from './index-2d388930.js';\nimport { i as inheritAriaAttributes } from './helpers-3379ba19.js';\nimport { o as openURL, c as createColorClasses, h as hostContext } from './theme-17531cdf.js';\nimport { c as chevronBack, a as arrowBackSharp } from './index-ecfc2c9f.js';\nimport { c as config, b as getIonMode } from './ionic-global-b3fc28dd.js';\n\nconst backButtonIosCss = \":host{--background:transparent;--color-focused:currentColor;--color-hover:currentColor;--icon-margin-top:0;--icon-margin-bottom:0;--icon-padding-top:0;--icon-padding-end:0;--icon-padding-bottom:0;--icon-padding-start:0;--margin-top:0;--margin-end:0;--margin-bottom:0;--margin-start:0;--min-width:auto;--min-height:auto;--padding-top:0;--padding-end:0;--padding-bottom:0;--padding-start:0;--opacity:1;--ripple-color:currentColor;--transition:background-color, opacity 100ms linear;display:none;min-width:var(--min-width);min-height:var(--min-height);color:var(--color);font-family:var(--ion-font-family, inherit);text-align:center;text-decoration:none;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-font-kerning:none;font-kerning:none}ion-ripple-effect{color:var(--ripple-color)}:host(.ion-color) .button-native{color:var(--ion-color-base)}:host(.show-back-button){display:block}:host(.back-button-disabled){cursor:default;opacity:0.5;pointer-events:none}.button-native{border-radius:var(--border-radius);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:block;position:relative;width:100%;height:100%;min-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border:0;outline:none;background:var(--background);line-height:1;cursor:pointer;opacity:var(--opacity);overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}ion-icon{-webkit-padding-start:var(--icon-padding-start);padding-inline-start:var(--icon-padding-start);-webkit-padding-end:var(--icon-padding-end);padding-inline-end:var(--icon-padding-end);padding-top:var(--icon-padding-top);padding-bottom:var(--icon-padding-bottom);-webkit-margin-start:var(--icon-margin-start);margin-inline-start:var(--icon-margin-start);-webkit-margin-end:var(--icon-margin-end);margin-inline-end:var(--icon-margin-end);margin-top:var(--icon-margin-top);margin-bottom:var(--icon-margin-bottom);display:inherit;font-size:var(--icon-font-size);font-weight:var(--icon-font-weight);pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}}:host(.ion-color.ion-focused) .button-native{color:var(--ion-color-base)}@media (any-hover: hover){:host(.ion-color:hover) .button-native{color:var(--ion-color-base)}}:host(.in-toolbar:not(.in-toolbar-color)){color:var(--ion-toolbar-color, var(--color))}:host{--background-hover:transparent;--background-hover-opacity:1;--background-focused:currentColor;--background-focused-opacity:.1;--border-radius:4px;--color:var(--ion-color-primary, #3880ff);--icon-margin-end:1px;--icon-margin-start:-4px;--icon-font-size:1.6em;--min-height:32px;font-size:17px}.button-native{-webkit-transform:translateZ(0);transform:translateZ(0);overflow:visible;z-index:99}:host(.ion-activated) .button-native{opacity:0.4}@media (any-hover: hover){:host(:hover){opacity:0.6}}\";\n\nconst backButtonMdCss = \":host{--background:transparent;--color-focused:currentColor;--color-hover:currentColor;--icon-margin-top:0;--icon-margin-bottom:0;--icon-padding-top:0;--icon-padding-end:0;--icon-padding-bottom:0;--icon-padding-start:0;--margin-top:0;--margin-end:0;--margin-bottom:0;--margin-start:0;--min-width:auto;--min-height:auto;--padding-top:0;--padding-end:0;--padding-bottom:0;--padding-start:0;--opacity:1;--ripple-color:currentColor;--transition:background-color, opacity 100ms linear;display:none;min-width:var(--min-width);min-height:var(--min-height);color:var(--color);font-family:var(--ion-font-family, inherit);text-align:center;text-decoration:none;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-font-kerning:none;font-kerning:none}ion-ripple-effect{color:var(--ripple-color)}:host(.ion-color) .button-native{color:var(--ion-color-base)}:host(.show-back-button){display:block}:host(.back-button-disabled){cursor:default;opacity:0.5;pointer-events:none}.button-native{border-radius:var(--border-radius);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:block;position:relative;width:100%;height:100%;min-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border:0;outline:none;background:var(--background);line-height:1;cursor:pointer;opacity:var(--opacity);overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}ion-icon{-webkit-padding-start:var(--icon-padding-start);padding-inline-start:var(--icon-padding-start);-webkit-padding-end:var(--icon-padding-end);padding-inline-end:var(--icon-padding-end);padding-top:var(--icon-padding-top);padding-bottom:var(--icon-padding-bottom);-webkit-margin-start:var(--icon-margin-start);margin-inline-start:var(--icon-margin-start);-webkit-margin-end:var(--icon-margin-end);margin-inline-end:var(--icon-margin-end);margin-top:var(--icon-margin-top);margin-bottom:var(--icon-margin-bottom);display:inherit;font-size:var(--icon-font-size);font-weight:var(--icon-font-weight);pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}}:host(.ion-color.ion-focused) .button-native{color:var(--ion-color-base)}@media (any-hover: hover){:host(.ion-color:hover) .button-native{color:var(--ion-color-base)}}:host(.in-toolbar:not(.in-toolbar-color)){color:var(--ion-toolbar-color, var(--color))}:host{--border-radius:4px;--background-focused:currentColor;--background-focused-opacity:.12;--background-hover:currentColor;--background-hover-opacity:0.04;--color:currentColor;--icon-margin-end:0;--icon-margin-start:0;--icon-font-size:24px;--icon-font-weight:normal;--min-height:32px;--min-width:44px;--padding-start:12px;--padding-end:12px;font-size:14px;font-weight:500;text-transform:uppercase}:host(.back-button-has-icon-only){--border-radius:50%;min-width:48px;height:48px}.button-native{-webkit-box-shadow:none;box-shadow:none}.button-text{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:0;padding-bottom:0}ion-icon{line-height:0.67;text-align:start}@media (any-hover: hover){:host(.ion-color:hover) .button-native::after{background:var(--ion-color-base)}}:host(.ion-color.ion-focused) .button-native::after{background:var(--ion-color-base)}\";\n\nconst BackButton = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.inheritedAttributes = {};\n    this.onClick = async (ev) => {\n      const nav = this.el.closest('ion-nav');\n      ev.preventDefault();\n      if (nav && (await nav.canGoBack())) {\n        return nav.pop({ animationBuilder: this.routerAnimation, skipIfBusy: true });\n      }\n      return openURL(this.defaultHref, ev, 'back', this.routerAnimation);\n    };\n    this.color = undefined;\n    this.defaultHref = undefined;\n    this.disabled = false;\n    this.icon = undefined;\n    this.text = undefined;\n    this.type = 'button';\n    this.routerAnimation = undefined;\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAriaAttributes(this.el);\n    if (this.defaultHref === undefined) {\n      this.defaultHref = config.get('backButtonDefaultHref');\n    }\n  }\n  get backButtonIcon() {\n    const icon = this.icon;\n    if (icon != null) {\n      // icon is set on the component or by the config\n      return icon;\n    }\n    if (getIonMode(this) === 'ios') {\n      // default ios back button icon\n      return config.get('backButtonIcon', chevronBack);\n    }\n    // default md back button icon\n    return config.get('backButtonIcon', arrowBackSharp);\n  }\n  get backButtonText() {\n    const defaultBackButtonText = getIonMode(this) === 'ios' ? 'Back' : null;\n    return this.text != null ? this.text : config.get('backButtonText', defaultBackButtonText);\n  }\n  get hasIconOnly() {\n    return this.backButtonIcon && !this.backButtonText;\n  }\n  get rippleType() {\n    // If the button only has an icon we use the unbounded\n    // \"circular\" ripple effect\n    if (this.hasIconOnly) {\n      return 'unbounded';\n    }\n    return 'bounded';\n  }\n  render() {\n    const { color, defaultHref, disabled, type, hasIconOnly, backButtonIcon, backButtonText, icon, inheritedAttributes, } = this;\n    const showBackButton = defaultHref !== undefined;\n    const mode = getIonMode(this);\n    const ariaLabel = inheritedAttributes['aria-label'] || backButtonText || 'back';\n    return (h(Host, { onClick: this.onClick, class: createColorClasses(color, {\n        [mode]: true,\n        button: true,\n        'back-button-disabled': disabled,\n        'back-button-has-icon-only': hasIconOnly,\n        'in-toolbar': hostContext('ion-toolbar', this.el),\n        'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n        'ion-activatable': true,\n        'ion-focusable': true,\n        'show-back-button': showBackButton,\n      }) }, h(\"button\", { type: type, disabled: disabled, class: \"button-native\", part: \"native\", \"aria-label\": ariaLabel }, h(\"span\", { class: \"button-inner\" }, backButtonIcon && (h(\"ion-icon\", { part: \"icon\", icon: backButtonIcon, \"aria-hidden\": \"true\", lazy: false, \"flip-rtl\": icon === undefined })), backButtonText && (h(\"span\", { part: \"text\", \"aria-hidden\": \"true\", class: \"button-text\" }, backButtonText))), mode === 'md' && h(\"ion-ripple-effect\", { type: this.rippleType }))));\n  }\n  get el() { return getElement(this); }\n};\nBackButton.style = {\n  ios: backButtonIosCss,\n  md: backButtonMdCss\n};\n\nexport { BackButton as ion_back_button };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst hostContext = (selector, el) => {\n  return el.closest(selector) !== null;\n};\n/**\n * Create the mode and color classes for the component based on the classes passed in\n */\nconst createColorClasses = (color, cssClassMap) => {\n  return typeof color === 'string' && color.length > 0\n    ? Object.assign({ 'ion-color': true, [`ion-color-${color}`]: true }, cssClassMap) : cssClassMap;\n};\nconst getClassList = (classes) => {\n  if (classes !== undefined) {\n    const array = Array.isArray(classes) ? classes : classes.split(' ');\n    return array\n      .filter((c) => c != null)\n      .map((c) => c.trim())\n      .filter((c) => c !== '');\n  }\n  return [];\n};\nconst getClassMap = (classes) => {\n  const map = {};\n  getClassList(classes).forEach((c) => (map[c] = true));\n  return map;\n};\nconst SCHEME = /^[a-z][a-z0-9+\\-.]*:/;\nconst openURL = async (url, ev, direction, animation) => {\n  if (url != null && url[0] !== '#' && !SCHEME.test(url)) {\n    const router = document.querySelector('ion-router');\n    if (router) {\n      if (ev != null) {\n        ev.preventDefault();\n      }\n      return router.push(url, direction, animation);\n    }\n  }\n  return false;\n};\n\nexport { createColorClasses as c, getClassMap as g, hostContext as h, openURL as o };\n"], "names": ["r", "registerInstance", "h", "H", "Host", "f", "getElement", "i", "inheritAriaAttributes", "o", "openURL", "c", "createColorClasses", "hostContext", "chevronBack", "a", "arrowBackSharp", "config", "b", "getIonMode", "backButtonIosCss", "backButtonMdCss", "BackButton", "constructor", "hostRef", "_this", "inheritedAttributes", "onClick", "_ref", "_asyncToGenerator", "ev", "nav", "el", "closest", "preventDefault", "canGoBack", "pop", "animationBuilder", "routerAnimation", "skipIfBusy", "defaultHref", "_x", "apply", "arguments", "color", "undefined", "disabled", "icon", "text", "type", "componentWillLoad", "get", "backButtonIcon", "backButtonText", "defaultBackButtonText", "hasIconOnly", "rippleType", "render", "showBackButton", "mode", "aria<PERSON><PERSON><PERSON>", "class", "button", "part", "lazy", "style", "ios", "md", "ion_back_button", "selector", "cssClassMap", "length", "Object", "assign", "getClassList", "classes", "array", "Array", "isArray", "split", "filter", "map", "trim", "getClassMap", "for<PERSON>ach", "SCHEME", "url", "direction", "animation", "test", "router", "document", "querySelector", "push", "_x2", "_x3", "_x4", "g"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0, 1]}