{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-fab_3_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC6G;AAChD;AACM;AAC2B;AAC7C;AAEjD,MAAMoB,MAAM,GAAG,4zDAA4zD;AAE30D,MAAMC,GAAG,GAAG,MAAM;EAChBC,WAAWA,CAACC,OAAO,EAAE;IACnBtB,qDAAgB,CAAC,IAAI,EAAEsB,OAAO,CAAC;IAC/B,IAAI,CAACC,UAAU,GAAGC,SAAS;IAC3B,IAAI,CAACC,QAAQ,GAAGD,SAAS;IACzB,IAAI,CAACE,IAAI,GAAG,KAAK;IACjB,IAAI,CAACC,SAAS,GAAG,KAAK;EACxB;EACAC,gBAAgBA,CAAA,EAAG;IACjB,MAAMD,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,MAAME,GAAG,GAAG,IAAI,CAACC,MAAM,CAAC,CAAC;IACzB,IAAID,GAAG,EAAE;MACPA,GAAG,CAACF,SAAS,GAAGA,SAAS;IAC3B;IACAI,KAAK,CAACC,IAAI,CAAC,IAAI,CAACC,EAAE,CAACC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAACC,OAAO,CAAEC,IAAI,IAAK;MACrEA,IAAI,CAACT,SAAS,GAAGA,SAAS;IAC5B,CAAC,CAAC;EACJ;EACAU,gBAAgBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACV,SAAS,EAAE;MAClB,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACzB;EACF;EACA;AACF;AACA;EACQV,KAAKA,CAAA,EAAG;IAAA,IAAAoB,KAAA;IAAA,OAAAC,8KAAA;MACZD,KAAI,CAACX,SAAS,GAAG,KAAK;IAAC;EACzB;EACAG,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACG,EAAE,CAACO,aAAa,CAAC,gBAAgB,CAAC;EAChD;EACA;AACF;AACA;AACA;EACQC,MAAMA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAH,8KAAA;MACb,MAAMI,OAAO,GAAG,CAAC,CAACD,MAAI,CAACT,EAAE,CAACO,aAAa,CAAC,cAAc,CAAC;MACvD,IAAIG,OAAO,EAAE;QACXD,MAAI,CAACf,SAAS,GAAG,CAACe,MAAI,CAACf,SAAS;MAClC;IAAC;EACH;EACAiB,MAAMA,CAAA,EAAG;IACP,MAAM;MAAErB,UAAU;MAAEE,QAAQ;MAAEC;IAAK,CAAC,GAAG,IAAI;IAC3C,MAAMmB,IAAI,GAAGpC,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQR,qDAAC,CAACE,iDAAI,EAAE;MAAE2C,KAAK,EAAE;QACrB,CAACD,IAAI,GAAG,IAAI;QACZ,CAAE,kBAAiBtB,UAAW,EAAC,GAAGA,UAAU,KAAKC,SAAS;QAC1D,CAAE,gBAAeC,QAAS,EAAC,GAAGA,QAAQ,KAAKD,SAAS;QACpD,UAAU,EAAEE;MACd;IAAE,CAAC,EAAEzB,qDAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;EACzB;EACA,IAAIgC,EAAEA,CAAA,EAAG;IAAE,OAAO5B,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW0C,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC7B,WAAW,EAAE,CAAC,kBAAkB;IAClC,CAAC;EAAE;AACL,CAAC;AACD3B,GAAG,CAAC4B,KAAK,GAAG7B,MAAM;AAElB,MAAM8B,eAAe,GAAG,yqOAAyqO;AAEjsO,MAAMC,cAAc,GAAG,yqMAAyqM;AAEhsM,MAAMC,SAAS,GAAG,MAAM;EACtB9B,WAAWA,CAACC,OAAO,EAAE;IACnBtB,qDAAgB,CAAC,IAAI,EAAEsB,OAAO,CAAC;IAC/B,IAAI,CAAC8B,QAAQ,GAAG7C,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC8C,OAAO,GAAG9C,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAACsB,GAAG,GAAG,IAAI;IACf,IAAI,CAACyB,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,OAAO,GAAG,MAAM;MACnB,IAAI,CAACH,QAAQ,CAACI,IAAI,CAAC,CAAC;IACtB,CAAC;IACD,IAAI,CAACC,MAAM,GAAG,MAAM;MAClB,IAAI,CAACJ,OAAO,CAACG,IAAI,CAAC,CAAC;IACrB,CAAC;IACD,IAAI,CAACE,OAAO,GAAG,MAAM;MACnB,MAAM;QAAE7B;MAAI,CAAC,GAAG,IAAI;MACpB,IAAI,CAACA,GAAG,EAAE;QACR;MACF;MACAA,GAAG,CAACY,MAAM,CAAC,CAAC;IACd,CAAC;IACD,IAAI,CAACkB,KAAK,GAAGnC,SAAS;IACtB,IAAI,CAACG,SAAS,GAAG,KAAK;IACtB,IAAI,CAACiC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAGrC,SAAS;IACzB,IAAI,CAACsC,IAAI,GAAGtC,SAAS;IACrB,IAAI,CAACuC,GAAG,GAAGvC,SAAS;IACpB,IAAI,CAACwC,eAAe,GAAG,SAAS;IAChC,IAAI,CAACC,eAAe,GAAGzC,SAAS;IAChC,IAAI,CAAC0C,MAAM,GAAG1C,SAAS;IACvB,IAAI,CAAC2C,IAAI,GAAG,KAAK;IACjB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,IAAI,GAAG,QAAQ;IACpB,IAAI,CAACC,IAAI,GAAG9C,SAAS;IACrB,IAAI,CAAC+C,SAAS,GAAGrD,iDAAK;EACxB;EACAsD,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAAC3C,GAAG,GAAG,IAAI,CAACI,EAAE,CAACwC,OAAO,CAAC,SAAS,CAAC;EACvC;EACAC,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAACpB,mBAAmB,GAAG3C,uDAAqB,CAAC,IAAI,CAACsB,EAAE,CAAC;EAC3D;EACAW,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEX,EAAE;MAAE2B,QAAQ;MAAED,KAAK;MAAEG,IAAI;MAAEnC,SAAS;MAAEwC,IAAI;MAAEC,WAAW;MAAEE,IAAI;MAAEhB;IAAoB,CAAC,GAAG,IAAI;IACnG,MAAMqB,MAAM,GAAG/D,qDAAW,CAAC,cAAc,EAAEqB,EAAE,CAAC;IAC9C,MAAMY,IAAI,GAAGpC,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAMmE,OAAO,GAAGd,IAAI,KAAKtC,SAAS,GAAG,QAAQ,GAAG,GAAG;IACnD,MAAMqD,KAAK,GAAGD,OAAO,KAAK,QAAQ,GAC9B;MAAEP,IAAI,EAAE,IAAI,CAACA;IAAK,CAAC,GACnB;MACAR,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,IAAI;MACJC,GAAG,EAAE,IAAI,CAACA,GAAG;MACbG,MAAM,EAAE,IAAI,CAACA;IACf,CAAC;IACH,OAAQjE,qDAAC,CAACE,iDAAI,EAAE;MAAEuD,OAAO,EAAE,IAAI,CAACA,OAAO;MAAE,eAAe,EAAEE,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAEd,KAAK,EAAE9B,qDAAkB,CAAC2C,KAAK,EAAE;QACjH,CAACd,IAAI,GAAG,IAAI;QACZ,oBAAoB,EAAE8B,MAAM;QAC5B,gCAAgC,EAAEA,MAAM,IAAIP,WAAW;QACvD,yBAAyB,EAAEzC,SAAS;QACpC,iBAAiB,EAAEwC,IAAI;QACvB,qBAAqB,EAAEP,QAAQ;QAC/B,wBAAwB,EAAEQ,WAAW;QACrC,iBAAiB,EAAE,IAAI;QACvB,eAAe,EAAE,IAAI;QACrB,CAAE,cAAaE,IAAK,EAAC,GAAGA,IAAI,KAAK9C;MACnC,CAAC;IAAE,CAAC,EAAEvB,qDAAC,CAAC2E,OAAO,EAAEE,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,KAAK,EAAE;MAAE/B,KAAK,EAAE,eAAe;MAAEkC,IAAI,EAAE,QAAQ;MAAEpB,QAAQ,EAAEA,QAAQ;MAAEL,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEE,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEC,OAAO,EAAGuB,EAAE,IAAKnE,qDAAO,CAACgD,IAAI,EAAEmB,EAAE,EAAE,IAAI,CAACjB,eAAe,EAAE,IAAI,CAACC,eAAe;IAAE,CAAC,EAAEX,mBAAmB,CAAC,EAAErD,qDAAC,CAAC,UAAU,EAAE;MAAE,aAAa,EAAE,MAAM;MAAEiF,IAAI,EAAE,IAAI,CAACX,SAAS;MAAES,IAAI,EAAE,YAAY;MAAElC,KAAK,EAAE,YAAY;MAAEqC,IAAI,EAAE;IAAM,CAAC,CAAC,EAAElF,qDAAC,CAAC,MAAM,EAAE;MAAE6C,KAAK,EAAE;IAAe,CAAC,EAAE7C,qDAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,EAAE4C,IAAI,KAAK,IAAI,IAAI5C,qDAAC,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC,CAAC;EAC5d;EACA,IAAIgC,EAAEA,CAAA,EAAG;IAAE,OAAO5B,qDAAU,CAAC,IAAI,CAAC;EAAE;AACtC,CAAC;AACD8C,SAAS,CAACH,KAAK,GAAG;EAChBoC,GAAG,EAAEnC,eAAe;EACpBoC,EAAE,EAAEnC;AACN,CAAC;AAED,MAAMoC,UAAU,GAAG,ivEAAivE;AAEpwE,MAAMC,OAAO,GAAG,MAAM;EACpBlE,WAAWA,CAACC,OAAO,EAAE;IACnBtB,qDAAgB,CAAC,IAAI,EAAEsB,OAAO,CAAC;IAC/B,IAAI,CAACK,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC6D,IAAI,GAAG,QAAQ;EACtB;EACA5D,gBAAgBA,CAACD,SAAS,EAAE;IAC1B,MAAM8D,IAAI,GAAG1D,KAAK,CAACC,IAAI,CAAC,IAAI,CAACC,EAAE,CAACC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;IACnE;IACA,MAAMwD,OAAO,GAAG/D,SAAS,GAAG,EAAE,GAAG,CAAC;IAClC8D,IAAI,CAACtD,OAAO,CAAC,CAACN,GAAG,EAAEnB,CAAC,KAAK;MACvBiF,UAAU,CAAC,MAAO9D,GAAG,CAACsC,IAAI,GAAGxC,SAAU,EAAEjB,CAAC,GAAGgF,OAAO,CAAC;IACvD,CAAC,CAAC;EACJ;EACA9C,MAAMA,CAAA,EAAG;IACP,MAAMC,IAAI,GAAGpC,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQR,qDAAC,CAACE,iDAAI,EAAE;MAAE2C,KAAK,EAAE;QACrB,CAACD,IAAI,GAAG,IAAI;QACZ,iBAAiB,EAAE,IAAI,CAAClB,SAAS;QACjC,CAAE,iBAAgB,IAAI,CAAC6D,IAAK,EAAC,GAAG;MAClC;IAAE,CAAC,EAAEvF,qDAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;EACzB;EACA,IAAIgC,EAAEA,CAAA,EAAG;IAAE,OAAO5B,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW0C,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC7B,WAAW,EAAE,CAAC,kBAAkB;IAClC,CAAC;EAAE;AACL,CAAC;AACDwC,OAAO,CAACvC,KAAK,GAAGsC,UAAU;;;;;;;;;;;;;;;;;;;;ACjL1B;AACA;AACA;AACA,MAAM1E,WAAW,GAAGA,CAACmF,QAAQ,EAAE9D,EAAE,KAAK;EACpC,OAAOA,EAAE,CAACwC,OAAO,CAACsB,QAAQ,CAAC,KAAK,IAAI;AACtC,CAAC;AACD;AACA;AACA;AACA,MAAM/E,kBAAkB,GAAGA,CAAC2C,KAAK,EAAEqC,WAAW,KAAK;EACjD,OAAO,OAAOrC,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACsC,MAAM,GAAG,CAAC,GAChDnB,MAAM,CAACC,MAAM,CAAC;IAAE,WAAW,EAAE,IAAI;IAAE,CAAE,aAAYpB,KAAM,EAAC,GAAG;EAAK,CAAC,EAAEqC,WAAW,CAAC,GAAGA,WAAW;AACnG,CAAC;AACD,MAAME,YAAY,GAAIC,OAAO,IAAK;EAChC,IAAIA,OAAO,KAAK3E,SAAS,EAAE;IACzB,MAAM4E,KAAK,GAAGrE,KAAK,CAACsE,OAAO,CAACF,OAAO,CAAC,GAAGA,OAAO,GAAGA,OAAO,CAACG,KAAK,CAAC,GAAG,CAAC;IACnE,OAAOF,KAAK,CACTG,MAAM,CAAExF,CAAC,IAAKA,CAAC,IAAI,IAAI,CAAC,CACxByF,GAAG,CAAEzF,CAAC,IAAKA,CAAC,CAAC0F,IAAI,CAAC,CAAC,CAAC,CACpBF,MAAM,CAAExF,CAAC,IAAKA,CAAC,KAAK,EAAE,CAAC;EAC5B;EACA,OAAO,EAAE;AACX,CAAC;AACD,MAAM2F,WAAW,GAAIP,OAAO,IAAK;EAC/B,MAAMK,GAAG,GAAG,CAAC,CAAC;EACdN,YAAY,CAACC,OAAO,CAAC,CAAChE,OAAO,CAAEpB,CAAC,IAAMyF,GAAG,CAACzF,CAAC,CAAC,GAAG,IAAK,CAAC;EACrD,OAAOyF,GAAG;AACZ,CAAC;AACD,MAAMG,MAAM,GAAG,sBAAsB;AACrC,MAAM7F,OAAO;EAAA,IAAA8F,IAAA,GAAArE,8KAAA,CAAG,WAAOsE,GAAG,EAAE5B,EAAE,EAAE6B,SAAS,EAAEC,SAAS,EAAK;IACvD,IAAIF,GAAG,IAAI,IAAI,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAACF,MAAM,CAACK,IAAI,CAACH,GAAG,CAAC,EAAE;MACtD,MAAMI,MAAM,GAAGC,QAAQ,CAAC1E,aAAa,CAAC,YAAY,CAAC;MACnD,IAAIyE,MAAM,EAAE;QACV,IAAIhC,EAAE,IAAI,IAAI,EAAE;UACdA,EAAE,CAACkC,cAAc,CAAC,CAAC;QACrB;QACA,OAAOF,MAAM,CAACG,IAAI,CAACP,GAAG,EAAEC,SAAS,EAAEC,SAAS,CAAC;MAC/C;IACF;IACA,OAAO,KAAK;EACd,CAAC;EAAA,gBAXKjG,OAAOA,CAAAuG,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAZ,IAAA,CAAAa,KAAA,OAAAC,SAAA;EAAA;AAAA,GAWZ", "sources": ["./node_modules/@ionic/core/dist/esm/ion-fab_3.entry.js", "./node_modules/@ionic/core/dist/esm/theme-17531cdf.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, H as Host, f as getElement, d as createEvent } from './index-2d388930.js';\nimport { b as getIonMode } from './ionic-global-b3fc28dd.js';\nimport { i as inheritAriaAttributes } from './helpers-3379ba19.js';\nimport { h as hostContext, o as openURL, c as createColorClasses } from './theme-17531cdf.js';\nimport { t as close } from './index-ecfc2c9f.js';\n\nconst fabCss = \":host{position:absolute;z-index:999}:host(.fab-horizontal-center){-webkit-margin-start:-28px;margin-inline-start:-28px}@supports (inset-inline-start: 0){:host(.fab-horizontal-center){inset-inline-start:50%}}@supports not (inset-inline-start: 0){:host(.fab-horizontal-center){left:50%}:host-context([dir=rtl]):host(.fab-horizontal-center),:host-context([dir=rtl]).fab-horizontal-center{left:unset;right:unset;right:50%}@supports selector(:dir(rtl)){:host(.fab-horizontal-center):dir(rtl){left:unset;right:unset;right:50%}}}@supports (inset-inline-start: 0){:host(.fab-horizontal-start){inset-inline-start:calc(10px + var(--ion-safe-area-left, 0px))}}@supports not (inset-inline-start: 0){:host(.fab-horizontal-start){left:calc(10px + var(--ion-safe-area-left, 0px))}:host-context([dir=rtl]):host(.fab-horizontal-start),:host-context([dir=rtl]).fab-horizontal-start{left:unset;right:unset;right:calc(10px + var(--ion-safe-area-left, 0px))}@supports selector(:dir(rtl)){:host(.fab-horizontal-start):dir(rtl){left:unset;right:unset;right:calc(10px + var(--ion-safe-area-left, 0px))}}}@supports (inset-inline-start: 0){:host(.fab-horizontal-end){inset-inline-end:calc(10px + var(--ion-safe-area-right, 0px))}}@supports not (inset-inline-start: 0){:host(.fab-horizontal-end){right:calc(10px + var(--ion-safe-area-right, 0px))}:host-context([dir=rtl]):host(.fab-horizontal-end),:host-context([dir=rtl]).fab-horizontal-end{left:unset;right:unset;left:calc(10px + var(--ion-safe-area-right, 0px))}@supports selector(:dir(rtl)){:host(.fab-horizontal-end):dir(rtl){left:unset;right:unset;left:calc(10px + var(--ion-safe-area-right, 0px))}}}:host(.fab-vertical-top){top:10px}:host(.fab-vertical-top.fab-edge){top:-28px}:host(.fab-vertical-bottom){bottom:10px}:host(.fab-vertical-bottom.fab-edge){bottom:-28px}:host(.fab-vertical-center){margin-top:-28px;top:50%}\";\n\nconst Fab = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.horizontal = undefined;\n    this.vertical = undefined;\n    this.edge = false;\n    this.activated = false;\n  }\n  activatedChanged() {\n    const activated = this.activated;\n    const fab = this.getFab();\n    if (fab) {\n      fab.activated = activated;\n    }\n    Array.from(this.el.querySelectorAll('ion-fab-list')).forEach((list) => {\n      list.activated = activated;\n    });\n  }\n  componentDidLoad() {\n    if (this.activated) {\n      this.activatedChanged();\n    }\n  }\n  /**\n   * Close an active FAB list container.\n   */\n  async close() {\n    this.activated = false;\n  }\n  getFab() {\n    return this.el.querySelector('ion-fab-button');\n  }\n  /**\n   * Opens/Closes the FAB list container.\n   * @internal\n   */\n  async toggle() {\n    const hasList = !!this.el.querySelector('ion-fab-list');\n    if (hasList) {\n      this.activated = !this.activated;\n    }\n  }\n  render() {\n    const { horizontal, vertical, edge } = this;\n    const mode = getIonMode(this);\n    return (h(Host, { class: {\n        [mode]: true,\n        [`fab-horizontal-${horizontal}`]: horizontal !== undefined,\n        [`fab-vertical-${vertical}`]: vertical !== undefined,\n        'fab-edge': edge,\n      } }, h(\"slot\", null)));\n  }\n  get el() { return getElement(this); }\n  static get watchers() { return {\n    \"activated\": [\"activatedChanged\"]\n  }; }\n};\nFab.style = fabCss;\n\nconst fabButtonIosCss = \":host{--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--background-hover:var(--ion-color-primary-contrast, #fff);--background-hover-opacity:.08;--transition:background-color, opacity 100ms linear;--ripple-color:currentColor;--border-radius:50%;--border-width:0;--border-style:none;--border-color:initial;--padding-top:0;--padding-end:0;--padding-bottom:0;--padding-start:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:block;width:56px;height:56px;font-size:14px;text-align:center;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:var(--border-radius);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:block;position:relative;width:100%;height:100%;-webkit-transform:var(--transform);transform:var(--transform);-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);background-clip:padding-box;color:var(--color);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);contain:strict;cursor:pointer;overflow:hidden;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none;-webkit-box-sizing:border-box;box-sizing:border-box}::slotted(ion-icon){line-height:1}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}.button-inner{left:0;right:0;top:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;height:100%;-webkit-transition:all ease-in-out 300ms;transition:all ease-in-out 300ms;-webkit-transition-property:opacity, -webkit-transform;transition-property:opacity, -webkit-transform;transition-property:transform, opacity;transition-property:transform, opacity, -webkit-transform;z-index:1}:host(.fab-button-disabled){cursor:default;opacity:0.5;pointer-events:none}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(.ion-activated) .button-native{color:var(--color-activated)}:host(.ion-activated) .button-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}::slotted(ion-icon){line-height:1}:host(.fab-button-small){-webkit-margin-start:8px;margin-inline-start:8px;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:8px;margin-bottom:8px;width:40px;height:40px}.close-icon{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;left:0;right:0;top:0;position:absolute;height:100%;-webkit-transform:scale(0.4) rotateZ(-45deg);transform:scale(0.4) rotateZ(-45deg);-webkit-transition:all ease-in-out 300ms;transition:all ease-in-out 300ms;-webkit-transition-property:opacity, -webkit-transform;transition-property:opacity, -webkit-transform;transition-property:transform, opacity;transition-property:transform, opacity, -webkit-transform;font-size:var(--close-icon-font-size);opacity:0;z-index:1}:host(.fab-button-close-active) .close-icon{-webkit-transform:scale(1) rotateZ(0deg);transform:scale(1) rotateZ(0deg);opacity:1}:host(.fab-button-close-active) .button-inner{-webkit-transform:scale(0.4) rotateZ(45deg);transform:scale(0.4) rotateZ(45deg);opacity:0}ion-ripple-effect{color:var(--ripple-color)}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.fab-button-translucent) .button-native{-webkit-backdrop-filter:var(--backdrop-filter);backdrop-filter:var(--backdrop-filter)}}:host(.ion-color) .button-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host{--background:var(--ion-color-primary, #3880ff);--background-activated:var(--ion-color-primary-shade, #3171e0);--background-focused:var(--ion-color-primary-shade, #3171e0);--background-hover:var(--ion-color-primary-tint, #4c8dff);--background-activated-opacity:1;--background-focused-opacity:1;--background-hover-opacity:1;--color:var(--ion-color-primary-contrast, #fff);--box-shadow:0 4px 16px rgba(0, 0, 0, 0.12);--transition:0.2s transform cubic-bezier(0.25, 1.11, 0.78, 1.59);--close-icon-font-size:28px}:host(.ion-activated){--box-shadow:0 4px 16px rgba(0, 0, 0, 0.12);--transform:scale(1.1);--transition:0.2s transform ease-out}::slotted(ion-icon){font-size:28px}:host(.fab-button-in-list){--background:var(--ion-color-light, #f4f5f8);--background-activated:var(--ion-color-light-shade, #d7d8da);--background-focused:var(--background-activated);--background-hover:var(--ion-color-light-tint, #f5f6f9);--color:var(--ion-color-light-contrast, #000);--color-activated:var(--ion-color-light-contrast, #000);--color-focused:var(--color-activated);--transition:transform 200ms ease 10ms, opacity 200ms ease 10ms}:host(.fab-button-in-list) ::slotted(ion-icon){font-size:18px}:host(.ion-color.ion-focused) .button-native::after{background:var(--ion-color-shade)}:host(.ion-color.ion-focused) .button-native,:host(.ion-color.ion-activated) .button-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-focused) .button-native::after,:host(.ion-color.ion-activated) .button-native::after{background:var(--ion-color-shade)}@media (any-hover: hover){:host(.ion-color:hover) .button-native{color:var(--ion-color-contrast)}:host(.ion-color:hover) .button-native::after{background:var(--ion-color-tint)}}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.fab-button-translucent){--background:rgba(var(--ion-color-primary-rgb, 56, 128, 255), 0.9);--background-hover:rgba(var(--ion-color-primary-rgb, 56, 128, 255), 0.8);--background-focused:rgba(var(--ion-color-primary-rgb, 56, 128, 255), 0.82);--backdrop-filter:saturate(180%) blur(20px)}:host(.fab-button-translucent-in-list){--background:rgba(var(--ion-color-light-rgb, 244, 245, 248), 0.9);--background-hover:rgba(var(--ion-color-light-rgb, 244, 245, 248), 0.8);--background-focused:rgba(var(--ion-color-light-rgb, 244, 245, 248), 0.82)}}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){@media (any-hover: hover){:host(.fab-button-translucent.ion-color:hover) .button-native{background:rgba(var(--ion-color-base-rgb), 0.8)}}:host(.ion-color.fab-button-translucent) .button-native{background:rgba(var(--ion-color-base-rgb), 0.9)}:host(.ion-color.ion-focused.fab-button-translucent) .button-native,:host(.ion-color.ion-activated.fab-button-translucent) .button-native{background:var(--ion-color-base)}}\";\n\nconst fabButtonMdCss = \":host{--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--background-hover:var(--ion-color-primary-contrast, #fff);--background-hover-opacity:.08;--transition:background-color, opacity 100ms linear;--ripple-color:currentColor;--border-radius:50%;--border-width:0;--border-style:none;--border-color:initial;--padding-top:0;--padding-end:0;--padding-bottom:0;--padding-start:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:block;width:56px;height:56px;font-size:14px;text-align:center;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:var(--border-radius);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:block;position:relative;width:100%;height:100%;-webkit-transform:var(--transform);transform:var(--transform);-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);background-clip:padding-box;color:var(--color);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);contain:strict;cursor:pointer;overflow:hidden;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none;-webkit-box-sizing:border-box;box-sizing:border-box}::slotted(ion-icon){line-height:1}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}.button-inner{left:0;right:0;top:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;height:100%;-webkit-transition:all ease-in-out 300ms;transition:all ease-in-out 300ms;-webkit-transition-property:opacity, -webkit-transform;transition-property:opacity, -webkit-transform;transition-property:transform, opacity;transition-property:transform, opacity, -webkit-transform;z-index:1}:host(.fab-button-disabled){cursor:default;opacity:0.5;pointer-events:none}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(.ion-activated) .button-native{color:var(--color-activated)}:host(.ion-activated) .button-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}::slotted(ion-icon){line-height:1}:host(.fab-button-small){-webkit-margin-start:8px;margin-inline-start:8px;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:8px;margin-bottom:8px;width:40px;height:40px}.close-icon{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;left:0;right:0;top:0;position:absolute;height:100%;-webkit-transform:scale(0.4) rotateZ(-45deg);transform:scale(0.4) rotateZ(-45deg);-webkit-transition:all ease-in-out 300ms;transition:all ease-in-out 300ms;-webkit-transition-property:opacity, -webkit-transform;transition-property:opacity, -webkit-transform;transition-property:transform, opacity;transition-property:transform, opacity, -webkit-transform;font-size:var(--close-icon-font-size);opacity:0;z-index:1}:host(.fab-button-close-active) .close-icon{-webkit-transform:scale(1) rotateZ(0deg);transform:scale(1) rotateZ(0deg);opacity:1}:host(.fab-button-close-active) .button-inner{-webkit-transform:scale(0.4) rotateZ(45deg);transform:scale(0.4) rotateZ(45deg);opacity:0}ion-ripple-effect{color:var(--ripple-color)}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.fab-button-translucent) .button-native{-webkit-backdrop-filter:var(--backdrop-filter);backdrop-filter:var(--backdrop-filter)}}:host(.ion-color) .button-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host{--background:var(--ion-color-primary, #3880ff);--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor;--background-activated-opacity:0;--background-focused-opacity:.24;--background-hover-opacity:.08;--color:var(--ion-color-primary-contrast, #fff);--box-shadow:0 3px 5px -1px rgba(0, 0, 0, 0.2), 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12);--transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1), background-color 280ms cubic-bezier(0.4, 0, 0.2, 1), color 280ms cubic-bezier(0.4, 0, 0.2, 1), opacity 15ms linear 30ms, transform 270ms cubic-bezier(0, 0, 0.2, 1) 0ms;--close-icon-font-size:24px}:host(.ion-activated){--box-shadow:0 7px 8px -4px rgba(0, 0, 0, 0.2), 0 12px 17px 2px rgba(0, 0, 0, 0.14), 0 5px 22px 4px rgba(0, 0, 0, 0.12)}::slotted(ion-icon){font-size:24px}:host(.fab-button-in-list){--color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.54);--color-activated:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.54);--color-focused:var(--color-activated);--background:var(--ion-color-light, #f4f5f8);--background-activated:transparent;--background-focused:var(--ion-color-light-shade, #d7d8da);--background-hover:var(--ion-color-light-tint, #f5f6f9)}:host(.fab-button-in-list) ::slotted(ion-icon){font-size:18px}:host(.ion-color.ion-focused) .button-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-focused) .button-native::after{background:var(--ion-color-contrast)}:host(.ion-color.ion-activated) .button-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-activated) .button-native::after{background:transparent}@media (any-hover: hover){:host(.ion-color:hover) .button-native{color:var(--ion-color-contrast)}:host(.ion-color:hover) .button-native::after{background:var(--ion-color-contrast)}}\";\n\nconst FabButton = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.fab = null;\n    this.inheritedAttributes = {};\n    this.onFocus = () => {\n      this.ionFocus.emit();\n    };\n    this.onBlur = () => {\n      this.ionBlur.emit();\n    };\n    this.onClick = () => {\n      const { fab } = this;\n      if (!fab) {\n        return;\n      }\n      fab.toggle();\n    };\n    this.color = undefined;\n    this.activated = false;\n    this.disabled = false;\n    this.download = undefined;\n    this.href = undefined;\n    this.rel = undefined;\n    this.routerDirection = 'forward';\n    this.routerAnimation = undefined;\n    this.target = undefined;\n    this.show = false;\n    this.translucent = false;\n    this.type = 'button';\n    this.size = undefined;\n    this.closeIcon = close;\n  }\n  connectedCallback() {\n    this.fab = this.el.closest('ion-fab');\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAriaAttributes(this.el);\n  }\n  render() {\n    const { el, disabled, color, href, activated, show, translucent, size, inheritedAttributes } = this;\n    const inList = hostContext('ion-fab-list', el);\n    const mode = getIonMode(this);\n    const TagType = href === undefined ? 'button' : 'a';\n    const attrs = TagType === 'button'\n      ? { type: this.type }\n      : {\n        download: this.download,\n        href,\n        rel: this.rel,\n        target: this.target,\n      };\n    return (h(Host, { onClick: this.onClick, \"aria-disabled\": disabled ? 'true' : null, class: createColorClasses(color, {\n        [mode]: true,\n        'fab-button-in-list': inList,\n        'fab-button-translucent-in-list': inList && translucent,\n        'fab-button-close-active': activated,\n        'fab-button-show': show,\n        'fab-button-disabled': disabled,\n        'fab-button-translucent': translucent,\n        'ion-activatable': true,\n        'ion-focusable': true,\n        [`fab-button-${size}`]: size !== undefined,\n      }) }, h(TagType, Object.assign({}, attrs, { class: \"button-native\", part: \"native\", disabled: disabled, onFocus: this.onFocus, onBlur: this.onBlur, onClick: (ev) => openURL(href, ev, this.routerDirection, this.routerAnimation) }, inheritedAttributes), h(\"ion-icon\", { \"aria-hidden\": \"true\", icon: this.closeIcon, part: \"close-icon\", class: \"close-icon\", lazy: false }), h(\"span\", { class: \"button-inner\" }, h(\"slot\", null)), mode === 'md' && h(\"ion-ripple-effect\", null))));\n  }\n  get el() { return getElement(this); }\n};\nFabButton.style = {\n  ios: fabButtonIosCss,\n  md: fabButtonMdCss\n};\n\nconst fabListCss = \":host{margin-left:0;margin-right:0;margin-top:66px;margin-bottom:66px;display:none;position:absolute;top:0;-ms-flex-direction:column;flex-direction:column;-ms-flex-align:center;align-items:center;min-width:56px;min-height:56px}:host(.fab-list-active){display:-ms-flexbox;display:flex}::slotted(.fab-button-in-list){margin-left:0;margin-right:0;margin-top:8px;margin-bottom:8px;width:40px;height:40px;-webkit-transform:scale(0);transform:scale(0);opacity:0;visibility:hidden}:host(.fab-list-side-top) ::slotted(.fab-button-in-list),:host(.fab-list-side-bottom) ::slotted(.fab-button-in-list){margin-left:0;margin-right:0;margin-top:5px;margin-bottom:5px}:host(.fab-list-side-start) ::slotted(.fab-button-in-list),:host(.fab-list-side-end) ::slotted(.fab-button-in-list){-webkit-margin-start:5px;margin-inline-start:5px;-webkit-margin-end:5px;margin-inline-end:5px;margin-top:0;margin-bottom:0}::slotted(.fab-button-in-list.fab-button-show){-webkit-transform:scale(1);transform:scale(1);opacity:1;visibility:visible}:host(.fab-list-side-top){top:auto;bottom:0;-ms-flex-direction:column-reverse;flex-direction:column-reverse}:host(.fab-list-side-start){-webkit-margin-start:66px;margin-inline-start:66px;-webkit-margin-end:66px;margin-inline-end:66px;margin-top:0;margin-bottom:0;-ms-flex-direction:row-reverse;flex-direction:row-reverse}@supports (inset-inline-start: 0){:host(.fab-list-side-start){inset-inline-end:0}}@supports not (inset-inline-start: 0){:host(.fab-list-side-start){right:0}:host-context([dir=rtl]):host(.fab-list-side-start),:host-context([dir=rtl]).fab-list-side-start{left:unset;right:unset;left:0}@supports selector(:dir(rtl)){:host(.fab-list-side-start):dir(rtl){left:unset;right:unset;left:0}}}:host(.fab-list-side-end){-webkit-margin-start:66px;margin-inline-start:66px;-webkit-margin-end:66px;margin-inline-end:66px;margin-top:0;margin-bottom:0;-ms-flex-direction:row;flex-direction:row}@supports (inset-inline-start: 0){:host(.fab-list-side-end){inset-inline-start:0}}@supports not (inset-inline-start: 0){:host(.fab-list-side-end){left:0}:host-context([dir=rtl]):host(.fab-list-side-end),:host-context([dir=rtl]).fab-list-side-end{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(.fab-list-side-end):dir(rtl){left:unset;right:unset;right:0}}}\";\n\nconst FabList = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.activated = false;\n    this.side = 'bottom';\n  }\n  activatedChanged(activated) {\n    const fabs = Array.from(this.el.querySelectorAll('ion-fab-button'));\n    // if showing the fabs add a timeout, else show immediately\n    const timeout = activated ? 30 : 0;\n    fabs.forEach((fab, i) => {\n      setTimeout(() => (fab.show = activated), i * timeout);\n    });\n  }\n  render() {\n    const mode = getIonMode(this);\n    return (h(Host, { class: {\n        [mode]: true,\n        'fab-list-active': this.activated,\n        [`fab-list-side-${this.side}`]: true,\n      } }, h(\"slot\", null)));\n  }\n  get el() { return getElement(this); }\n  static get watchers() { return {\n    \"activated\": [\"activatedChanged\"]\n  }; }\n};\nFabList.style = fabListCss;\n\nexport { Fab as ion_fab, FabButton as ion_fab_button, FabList as ion_fab_list };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst hostContext = (selector, el) => {\n  return el.closest(selector) !== null;\n};\n/**\n * Create the mode and color classes for the component based on the classes passed in\n */\nconst createColorClasses = (color, cssClassMap) => {\n  return typeof color === 'string' && color.length > 0\n    ? Object.assign({ 'ion-color': true, [`ion-color-${color}`]: true }, cssClassMap) : cssClassMap;\n};\nconst getClassList = (classes) => {\n  if (classes !== undefined) {\n    const array = Array.isArray(classes) ? classes : classes.split(' ');\n    return array\n      .filter((c) => c != null)\n      .map((c) => c.trim())\n      .filter((c) => c !== '');\n  }\n  return [];\n};\nconst getClassMap = (classes) => {\n  const map = {};\n  getClassList(classes).forEach((c) => (map[c] = true));\n  return map;\n};\nconst SCHEME = /^[a-z][a-z0-9+\\-.]*:/;\nconst openURL = async (url, ev, direction, animation) => {\n  if (url != null && url[0] !== '#' && !SCHEME.test(url)) {\n    const router = document.querySelector('ion-router');\n    if (router) {\n      if (ev != null) {\n        ev.preventDefault();\n      }\n      return router.push(url, direction, animation);\n    }\n  }\n  return false;\n};\n\nexport { createColorClasses as c, getClassMap as g, hostContext as h, openURL as o };\n"], "names": ["r", "registerInstance", "h", "H", "Host", "f", "getElement", "d", "createEvent", "b", "getIonMode", "i", "inheritAriaAttributes", "hostContext", "o", "openURL", "c", "createColorClasses", "t", "close", "fabCss", "Fab", "constructor", "hostRef", "horizontal", "undefined", "vertical", "edge", "activated", "activatedChanged", "fab", "getFab", "Array", "from", "el", "querySelectorAll", "for<PERSON>ach", "list", "componentDidLoad", "_this", "_asyncToGenerator", "querySelector", "toggle", "_this2", "hasList", "render", "mode", "class", "watchers", "style", "fabButtonIosCss", "fabButtonMdCss", "FabButton", "ionFocus", "ionBlur", "inheritedAttributes", "onFocus", "emit", "onBlur", "onClick", "color", "disabled", "download", "href", "rel", "routerDirection", "routerAnimation", "target", "show", "translucent", "type", "size", "closeIcon", "connectedCallback", "closest", "componentWillLoad", "inList", "TagType", "attrs", "Object", "assign", "part", "ev", "icon", "lazy", "ios", "md", "fabListCss", "FabList", "side", "fabs", "timeout", "setTimeout", "ion_fab", "ion_fab_button", "ion_fab_list", "selector", "cssClassMap", "length", "getClassList", "classes", "array", "isArray", "split", "filter", "map", "trim", "getClassMap", "SCHEME", "_ref", "url", "direction", "animation", "test", "router", "document", "preventDefault", "push", "_x", "_x2", "_x3", "_x4", "apply", "arguments", "g"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0, 1]}