{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-infinite-scroll_2_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC4I;AACtB;AAC5C;AACsB;AACjE;AACF;AAE7B,MAAM0B,iBAAiB,GAAG,qFAAqF;AAE/G,MAAMC,cAAc,GAAG,MAAM;EAC3BC,WAAWA,CAACC,OAAO,EAAE;IACnB5B,qDAAgB,CAAC,IAAI,EAAE4B,OAAO,CAAC;IAC/B,IAAI,CAACC,WAAW,GAAG3B,qDAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IACtD,IAAI,CAAC4B,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,QAAQ,GAAG,MAAM;MACpB,MAAMC,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC9B,IAAI,CAACA,QAAQ,IAAI,CAAC,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE;QACjC,OAAO,CAAC;MACV;MACA,MAAMC,cAAc,GAAG,IAAI,CAACC,EAAE,CAACC,YAAY;MAC3C,IAAIF,cAAc,KAAK,CAAC,EAAE;QACxB;QACA,OAAO,CAAC;MACV;MACA,MAAMG,SAAS,GAAGL,QAAQ,CAACK,SAAS;MACpC,MAAMC,YAAY,GAAGN,QAAQ,CAACM,YAAY;MAC1C,MAAMC,MAAM,GAAGP,QAAQ,CAACI,YAAY;MACpC,MAAMI,SAAS,GAAG,IAAI,CAACZ,KAAK,KAAK,CAAC,GAAGW,MAAM,GAAG,IAAI,CAACX,KAAK,GAAG,IAAI,CAACD,KAAK;MACrE,MAAMc,oBAAoB,GAAG,IAAI,CAACC,QAAQ,KAAK,QAAQ,GACnDJ,YAAY,GAAGJ,cAAc,GAAGG,SAAS,GAAGG,SAAS,GAAGD,MAAM,GAC9DF,SAAS,GAAGH,cAAc,GAAGM,SAAS;MAC1C,IAAIC,oBAAoB,GAAG,CAAC,EAAE;QAC5B,IAAI,CAAC,IAAI,CAACZ,OAAO,EAAE;UACjB,IAAI,CAACc,SAAS,GAAG,IAAI;UACrB,IAAI,CAACd,OAAO,GAAG,IAAI;UACnB,IAAI,CAACH,WAAW,CAACkB,IAAI,CAAC,CAAC;UACvB,OAAO,CAAC;QACV;MACF,CAAC,MACI;QACH,IAAI,CAACf,OAAO,GAAG,KAAK;MACtB;MACA,OAAO,CAAC;IACV,CAAC;IACD,IAAI,CAACc,SAAS,GAAG,KAAK;IACtB,IAAI,CAACH,SAAS,GAAG,KAAK;IACtB,IAAI,CAACK,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACH,QAAQ,GAAG,QAAQ;EAC1B;EACAI,gBAAgBA,CAAA,EAAG;IACjB,MAAMC,GAAG,GAAG,IAAI,CAACP,SAAS;IAC1B,IAAIO,GAAG,CAACC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;MAC7B,IAAI,CAACrB,KAAK,GAAG,CAAC;MACd,IAAI,CAACC,KAAK,GAAGqB,UAAU,CAACF,GAAG,CAAC,GAAG,GAAG;IACpC,CAAC,MACI;MACH,IAAI,CAACpB,KAAK,GAAGsB,UAAU,CAACF,GAAG,CAAC;MAC5B,IAAI,CAACnB,KAAK,GAAG,CAAC;IAChB;EACF;EACAsB,eAAeA,CAAA,EAAG;IAChB,MAAML,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,IAAIA,QAAQ,EAAE;MACZ,IAAI,CAACF,SAAS,GAAG,KAAK;MACtB,IAAI,CAACb,MAAM,GAAG,KAAK;IACrB;IACA,IAAI,CAACqB,kBAAkB,CAAC,CAACN,QAAQ,CAAC;EACpC;EACMO,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,8KAAA;MACxB,MAAMC,SAAS,GAAG9C,qDAAqB,CAAC4C,KAAI,CAAClB,EAAE,CAAC;MAChD,IAAI,CAACoB,SAAS,EAAE;QACd5C,qDAAuB,CAAC0C,KAAI,CAAClB,EAAE,CAAC;QAChC;MACF;MACAkB,KAAI,CAACrB,QAAQ,SAASnB,qDAAgB,CAAC0C,SAAS,CAAC;MACjDF,KAAI,CAACP,gBAAgB,CAAC,CAAC;MACvBO,KAAI,CAACH,eAAe,CAAC,CAAC;MACtB,IAAIG,KAAI,CAACX,QAAQ,KAAK,KAAK,EAAE;QAC3BzC,qDAAS,CAAC,MAAM;UACd,IAAIoD,KAAI,CAACrB,QAAQ,EAAE;YACjBqB,KAAI,CAACrB,QAAQ,CAACK,SAAS,GAAGgB,KAAI,CAACrB,QAAQ,CAACM,YAAY,GAAGe,KAAI,CAACrB,QAAQ,CAACwB,YAAY;UACnF;QACF,CAAC,CAAC;MACJ;IAAC;EACH;EACAC,oBAAoBA,CAAA,EAAG;IACrB,IAAI,CAACN,kBAAkB,CAAC,KAAK,CAAC;IAC9B,IAAI,CAACnB,QAAQ,GAAG0B,SAAS;EAC3B;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACQC,QAAQA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAN,8KAAA;MACf,MAAMtB,QAAQ,GAAG4B,MAAI,CAAC5B,QAAQ;MAC9B,IAAI,CAAC4B,MAAI,CAACjB,SAAS,IAAI,CAACX,QAAQ,EAAE;QAChC;MACF;MACA4B,MAAI,CAACjB,SAAS,GAAG,KAAK;MACtB,IAAIiB,MAAI,CAAClB,QAAQ,KAAK,KAAK,EAAE;QAC3B;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACMkB,MAAI,CAAC9B,MAAM,GAAG,IAAI;QAClB;QACA;QACA,MAAM+B,IAAI,GAAG7B,QAAQ,CAACM,YAAY,GAAGN,QAAQ,CAACK,SAAS;QACvD;QACAyB,qBAAqB,CAAC,MAAM;UAC1B3D,qDAAQ,CAAC,MAAM;YACb;YACA,MAAMmC,YAAY,GAAGN,QAAQ,CAACM,YAAY;YAC1C;YACA,MAAMyB,YAAY,GAAGzB,YAAY,GAAGuB,IAAI;YACxC;YACAC,qBAAqB,CAAC,MAAM;cAC1B7D,qDAAS,CAAC,MAAM;gBACd+B,QAAQ,CAACK,SAAS,GAAG0B,YAAY;gBACjCH,MAAI,CAAC9B,MAAM,GAAG,KAAK;cACrB,CAAC,CAAC;YACJ,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IAAC;EACH;EACAG,QAAQA,CAAA,EAAG;IACT,OAAO,CAAC,IAAI,CAACY,QAAQ,IAAI,CAAC,IAAI,CAACf,MAAM,IAAI,CAAC,CAAC,IAAI,CAACE,QAAQ,IAAI,CAAC,IAAI,CAACW,SAAS;EAC7E;EACAQ,kBAAkBA,CAACa,YAAY,EAAE;IAC/B,IAAI,IAAI,CAAChC,QAAQ,EAAE;MACjB,IAAIgC,YAAY,EAAE;QAChB,IAAI,CAAChC,QAAQ,CAACiC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAClC,QAAQ,CAAC;MACzD,CAAC,MACI;QACH,IAAI,CAACC,QAAQ,CAACkC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACnC,QAAQ,CAAC;MAC5D;IACF;EACF;EACAoC,MAAMA,CAAA,EAAG;IACP,MAAMC,IAAI,GAAGrD,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM8B,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,OAAQzC,qDAAC,CAACI,iDAAI,EAAE;MAAE6D,KAAK,EAAE;QACrB,CAACD,IAAI,GAAG,IAAI;QACZ,yBAAyB,EAAE,IAAI,CAACzB,SAAS;QACzC,yBAAyB,EAAE,CAACE;MAC9B;IAAE,CAAC,CAAC;EACR;EACA,IAAIV,EAAEA,CAAA,EAAG;IAAE,OAAO7B,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWgE,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC7B,WAAW,EAAE,CAAC,kBAAkB,CAAC;MACjC,UAAU,EAAE,CAAC,iBAAiB;IAChC,CAAC;EAAE;AACL,CAAC;AACD/C,cAAc,CAACgD,KAAK,GAAGjD,iBAAiB;AAExC,MAAMkD,2BAA2B,GAAG,mvCAAmvC;AAEvxC,MAAMC,0BAA0B,GAAG,0uCAA0uC;AAE7wC,MAAMC,qBAAqB,GAAG,MAAM;EAClClD,WAAWA,CAACC,OAAO,EAAE;IACnB5B,qDAAgB,CAAC,IAAI,EAAE4B,OAAO,CAAC;IAC/B,IAAI,CAACkD,iBAAiB,GAAG1D,wDAAM,CAAC2D,GAAG,CAAC,2BAA2B,EAAEzD,kDAA2B,CAAC;IAC7F,IAAI,CAAC0D,cAAc,GAAGnB,SAAS;IAC/B,IAAI,CAACoB,WAAW,GAAGpB,SAAS;EAC9B;EACAqB,gBAAgBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACF,cAAc,KAAKnB,SAAS,EAAE;MACrC,MAAMU,IAAI,GAAGrD,4DAAU,CAAC,IAAI,CAAC;MAC7B,IAAI,CAAC8D,cAAc,GAAG5D,wDAAM,CAAC2D,GAAG,CAAC,wBAAwB,EAAE3D,wDAAM,CAAC2D,GAAG,CAAC,SAAS,EAAER,IAAI,KAAK,KAAK,GAAG,OAAO,GAAG,UAAU,CAAC,CAAC;IAC1H;EACF;EACAY,iBAAiBA,CAAA,EAAG;IAClB,MAAM;MAAEL,iBAAiB;MAAEG;IAAY,CAAC,GAAG,IAAI;IAC/C,IAAIH,iBAAiB,EAAE;MACrB,OAAOvE,qDAAC,CAAC,KAAK,EAAE;QAAEiE,KAAK,EAAE,uBAAuB;QAAEY,SAAS,EAAE5D,sDAAiB,CAACyD,WAAW;MAAE,CAAC,CAAC;IAChG;IACA,OAAO1E,qDAAC,CAAC,KAAK,EAAE;MAAEiE,KAAK,EAAE;IAAwB,CAAC,EAAE,IAAI,CAACS,WAAW,CAAC;EACvE;EACAX,MAAMA,CAAA,EAAG;IACP,MAAMC,IAAI,GAAGrD,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQX,qDAAC,CAACI,iDAAI,EAAE;MAAE6D,KAAK,EAAE;QACrB,CAACD,IAAI,GAAG,IAAI;QACZ;QACA,CAAE,2BAA0BA,IAAK,EAAC,GAAG;MACvC;IAAE,CAAC,EAAEhE,qDAAC,CAAC,KAAK,EAAE;MAAEiE,KAAK,EAAE;IAAmB,CAAC,EAAE,IAAI,CAACQ,cAAc,IAAKzE,qDAAC,CAAC,KAAK,EAAE;MAAEiE,KAAK,EAAE;IAA2B,CAAC,EAAEjE,qDAAC,CAAC,aAAa,EAAE;MAAE8E,IAAI,EAAE,IAAI,CAACL;IAAe,CAAC,CAAC,CAAE,EAAE,IAAI,CAACC,WAAW,KAAKpB,SAAS,IAAI,IAAI,CAACsB,iBAAiB,CAAC,CAAC,CAAC,CAAC;EACxO;AACF,CAAC;AACDN,qBAAqB,CAACH,KAAK,GAAG;EAC5BY,GAAG,EAAEX,2BAA2B;EAChCY,EAAE,EAAEX;AACN,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-infinite-scroll_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, w as writeTask, e as readTask, h, f as getElement, H as Host } from './index-2d388930.js';\nimport { f as findClosestIonContent, p as printIonContentErrorMsg, g as getScrollElement } from './index-746a238e.js';\nimport { b as getIonMode, c as config } from './ionic-global-b3fc28dd.js';\nimport { E as ENABLE_HTML_CONTENT_DEFAULT, a as sanitizeDOMString } from './config-96c9ace3.js';\nimport './helpers-3379ba19.js';\nimport './index-595d62c9.js';\n\nconst infiniteScrollCss = \"ion-infinite-scroll{display:none;width:100%}.infinite-scroll-enabled{display:block}\";\n\nconst InfiniteScroll = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionInfinite = createEvent(this, \"ionInfinite\", 7);\n    this.thrPx = 0;\n    this.thrPc = 0;\n    this.didFire = false;\n    this.isBusy = false;\n    this.onScroll = () => {\n      const scrollEl = this.scrollEl;\n      if (!scrollEl || !this.canStart()) {\n        return 1;\n      }\n      const infiniteHeight = this.el.offsetHeight;\n      if (infiniteHeight === 0) {\n        // if there is no height of this element then do nothing\n        return 2;\n      }\n      const scrollTop = scrollEl.scrollTop;\n      const scrollHeight = scrollEl.scrollHeight;\n      const height = scrollEl.offsetHeight;\n      const threshold = this.thrPc !== 0 ? height * this.thrPc : this.thrPx;\n      const distanceFromInfinite = this.position === 'bottom'\n        ? scrollHeight - infiniteHeight - scrollTop - threshold - height\n        : scrollTop - infiniteHeight - threshold;\n      if (distanceFromInfinite < 0) {\n        if (!this.didFire) {\n          this.isLoading = true;\n          this.didFire = true;\n          this.ionInfinite.emit();\n          return 3;\n        }\n      }\n      else {\n        this.didFire = false;\n      }\n      return 4;\n    };\n    this.isLoading = false;\n    this.threshold = '15%';\n    this.disabled = false;\n    this.position = 'bottom';\n  }\n  thresholdChanged() {\n    const val = this.threshold;\n    if (val.lastIndexOf('%') > -1) {\n      this.thrPx = 0;\n      this.thrPc = parseFloat(val) / 100;\n    }\n    else {\n      this.thrPx = parseFloat(val);\n      this.thrPc = 0;\n    }\n  }\n  disabledChanged() {\n    const disabled = this.disabled;\n    if (disabled) {\n      this.isLoading = false;\n      this.isBusy = false;\n    }\n    this.enableScrollEvents(!disabled);\n  }\n  async connectedCallback() {\n    const contentEl = findClosestIonContent(this.el);\n    if (!contentEl) {\n      printIonContentErrorMsg(this.el);\n      return;\n    }\n    this.scrollEl = await getScrollElement(contentEl);\n    this.thresholdChanged();\n    this.disabledChanged();\n    if (this.position === 'top') {\n      writeTask(() => {\n        if (this.scrollEl) {\n          this.scrollEl.scrollTop = this.scrollEl.scrollHeight - this.scrollEl.clientHeight;\n        }\n      });\n    }\n  }\n  disconnectedCallback() {\n    this.enableScrollEvents(false);\n    this.scrollEl = undefined;\n  }\n  /**\n   * Call `complete()` within the `ionInfinite` output event handler when\n   * your async operation has completed. For example, the `loading`\n   * state is while the app is performing an asynchronous operation,\n   * such as receiving more data from an AJAX request to add more items\n   * to a data list. Once the data has been received and UI updated, you\n   * then call this method to signify that the loading has completed.\n   * This method will change the infinite scroll's state from `loading`\n   * to `enabled`.\n   */\n  async complete() {\n    const scrollEl = this.scrollEl;\n    if (!this.isLoading || !scrollEl) {\n      return;\n    }\n    this.isLoading = false;\n    if (this.position === 'top') {\n      /**\n       * New content is being added at the top, but the scrollTop position stays the same,\n       * which causes a scroll jump visually. This algorithm makes sure to prevent this.\n       * (Frame 1)\n       *    - complete() is called, but the UI hasn't had time to update yet.\n       *    - Save the current content dimensions.\n       *    - Wait for the next frame using _dom.read, so the UI will be updated.\n       * (Frame 2)\n       *    - Read the new content dimensions.\n       *    - Calculate the height difference and the new scroll position.\n       *    - Delay the scroll position change until other possible dom reads are done using _dom.write to be performant.\n       * (Still frame 2, if I'm correct)\n       *    - Change the scroll position (= visually maintain the scroll position).\n       *    - Change the state to re-enable the InfiniteScroll.\n       *    - This should be after changing the scroll position, or it could\n       *    cause the InfiniteScroll to be triggered again immediately.\n       * (Frame 3)\n       *    Done.\n       */\n      this.isBusy = true;\n      // ******** DOM READ ****************\n      // Save the current content dimensions before the UI updates\n      const prev = scrollEl.scrollHeight - scrollEl.scrollTop;\n      // ******** DOM READ ****************\n      requestAnimationFrame(() => {\n        readTask(() => {\n          // UI has updated, save the new content dimensions\n          const scrollHeight = scrollEl.scrollHeight;\n          // New content was added on top, so the scroll position should be changed immediately to prevent it from jumping around\n          const newScrollTop = scrollHeight - prev;\n          // ******** DOM WRITE ****************\n          requestAnimationFrame(() => {\n            writeTask(() => {\n              scrollEl.scrollTop = newScrollTop;\n              this.isBusy = false;\n            });\n          });\n        });\n      });\n    }\n  }\n  canStart() {\n    return !this.disabled && !this.isBusy && !!this.scrollEl && !this.isLoading;\n  }\n  enableScrollEvents(shouldListen) {\n    if (this.scrollEl) {\n      if (shouldListen) {\n        this.scrollEl.addEventListener('scroll', this.onScroll);\n      }\n      else {\n        this.scrollEl.removeEventListener('scroll', this.onScroll);\n      }\n    }\n  }\n  render() {\n    const mode = getIonMode(this);\n    const disabled = this.disabled;\n    return (h(Host, { class: {\n        [mode]: true,\n        'infinite-scroll-loading': this.isLoading,\n        'infinite-scroll-enabled': !disabled,\n      } }));\n  }\n  get el() { return getElement(this); }\n  static get watchers() { return {\n    \"threshold\": [\"thresholdChanged\"],\n    \"disabled\": [\"disabledChanged\"]\n  }; }\n};\nInfiniteScroll.style = infiniteScrollCss;\n\nconst infiniteScrollContentIosCss = \"ion-infinite-scroll-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;min-height:84px;text-align:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.infinite-loading{margin-left:0;margin-right:0;margin-top:0;margin-bottom:32px;display:none;width:100%}.infinite-loading-text{-webkit-margin-start:32px;margin-inline-start:32px;-webkit-margin-end:32px;margin-inline-end:32px;margin-top:4px;margin-bottom:0}.infinite-scroll-loading ion-infinite-scroll-content>.infinite-loading{display:block}.infinite-scroll-content-ios .infinite-loading-text{color:var(--ion-color-step-600, #666666)}.infinite-scroll-content-ios .infinite-loading-spinner .spinner-lines-ios line,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-lines-small-ios line,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-crescent circle{stroke:var(--ion-color-step-600, #666666)}.infinite-scroll-content-ios .infinite-loading-spinner .spinner-bubbles circle,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-circles circle,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-dots circle{fill:var(--ion-color-step-600, #666666)}\";\n\nconst infiniteScrollContentMdCss = \"ion-infinite-scroll-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;min-height:84px;text-align:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.infinite-loading{margin-left:0;margin-right:0;margin-top:0;margin-bottom:32px;display:none;width:100%}.infinite-loading-text{-webkit-margin-start:32px;margin-inline-start:32px;-webkit-margin-end:32px;margin-inline-end:32px;margin-top:4px;margin-bottom:0}.infinite-scroll-loading ion-infinite-scroll-content>.infinite-loading{display:block}.infinite-scroll-content-md .infinite-loading-text{color:var(--ion-color-step-600, #666666)}.infinite-scroll-content-md .infinite-loading-spinner .spinner-lines-md line,.infinite-scroll-content-md .infinite-loading-spinner .spinner-lines-small-md line,.infinite-scroll-content-md .infinite-loading-spinner .spinner-crescent circle{stroke:var(--ion-color-step-600, #666666)}.infinite-scroll-content-md .infinite-loading-spinner .spinner-bubbles circle,.infinite-scroll-content-md .infinite-loading-spinner .spinner-circles circle,.infinite-scroll-content-md .infinite-loading-spinner .spinner-dots circle{fill:var(--ion-color-step-600, #666666)}\";\n\nconst InfiniteScrollContent = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.customHTMLEnabled = config.get('innerHTMLTemplatesEnabled', ENABLE_HTML_CONTENT_DEFAULT);\n    this.loadingSpinner = undefined;\n    this.loadingText = undefined;\n  }\n  componentDidLoad() {\n    if (this.loadingSpinner === undefined) {\n      const mode = getIonMode(this);\n      this.loadingSpinner = config.get('infiniteLoadingSpinner', config.get('spinner', mode === 'ios' ? 'lines' : 'crescent'));\n    }\n  }\n  renderLoadingText() {\n    const { customHTMLEnabled, loadingText } = this;\n    if (customHTMLEnabled) {\n      return h(\"div\", { class: \"infinite-loading-text\", innerHTML: sanitizeDOMString(loadingText) });\n    }\n    return h(\"div\", { class: \"infinite-loading-text\" }, this.loadingText);\n  }\n  render() {\n    const mode = getIonMode(this);\n    return (h(Host, { class: {\n        [mode]: true,\n        // Used internally for styling\n        [`infinite-scroll-content-${mode}`]: true,\n      } }, h(\"div\", { class: \"infinite-loading\" }, this.loadingSpinner && (h(\"div\", { class: \"infinite-loading-spinner\" }, h(\"ion-spinner\", { name: this.loadingSpinner }))), this.loadingText !== undefined && this.renderLoadingText())));\n  }\n};\nInfiniteScrollContent.style = {\n  ios: infiniteScrollContentIosCss,\n  md: infiniteScrollContentMdCss\n};\n\nexport { InfiniteScroll as ion_infinite_scroll, InfiniteScrollContent as ion_infinite_scroll_content };\n"], "names": ["r", "registerInstance", "d", "createEvent", "w", "writeTask", "e", "readTask", "h", "f", "getElement", "H", "Host", "findClosestIonContent", "p", "printIonContentErrorMsg", "g", "getScrollElement", "b", "getIonMode", "c", "config", "E", "ENABLE_HTML_CONTENT_DEFAULT", "a", "sanitizeDOMString", "infiniteScrollCss", "InfiniteScroll", "constructor", "hostRef", "ionInfinite", "thrPx", "thrPc", "<PERSON><PERSON><PERSON>", "isBusy", "onScroll", "scrollEl", "canStart", "infiniteHeight", "el", "offsetHeight", "scrollTop", "scrollHeight", "height", "threshold", "distanceFromInfinite", "position", "isLoading", "emit", "disabled", "thresholdChanged", "val", "lastIndexOf", "parseFloat", "disabled<PERSON><PERSON>ed", "enableScrollEvents", "connectedCallback", "_this", "_asyncToGenerator", "contentEl", "clientHeight", "disconnectedCallback", "undefined", "complete", "_this2", "prev", "requestAnimationFrame", "newScrollTop", "<PERSON><PERSON><PERSON><PERSON>", "addEventListener", "removeEventListener", "render", "mode", "class", "watchers", "style", "infiniteScrollContentIosCss", "infiniteScrollContentMdCss", "InfiniteScrollContent", "customHTMLEnabled", "get", "loadingSpinner", "loadingText", "componentDidLoad", "renderLoadingText", "innerHTML", "name", "ios", "md", "ion_infinite_scroll", "ion_infinite_scroll_content"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}