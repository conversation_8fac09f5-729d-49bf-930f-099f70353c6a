{"version": 3, "file": "node_modules_ionicons_dist_esm-es5_loader_js.js", "mappings": ";;;;;;;;;;;;;;AAAwE;AAAA,IAAII,QAAQ,GAAC,SAAAA,CAAA,EAAU;EAAC,OAAOH,qDAAc,CAAC,CAAC;AAAA,CAAC;AAAC,IAAII,oBAAoB,GAAC,SAAAA,CAASC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAG,OAAOC,MAAM,KAAG,WAAW,EAAC,OAAOC,OAAO,CAACC,OAAO,CAAC,CAAC;EAAC,OAAON,QAAQ,CAAC,CAAC,CAACO,IAAI,CAAE,YAAU;IAAC,OAAOR,qDAAa,CAAC,CAAC,CAAC,UAAU,EAAC,CAAC,CAAC,CAAC,EAAC,UAAU,EAAC;MAACS,IAAI,EAAC,CAAC,IAAI,CAAC;MAACC,KAAK,EAAC,CAAC,CAAC,CAAC;MAACC,GAAG,EAAC,CAAC,CAAC,CAAC;MAACC,EAAE,EAAC,CAAC,CAAC,CAAC;MAACC,OAAO,EAAC,CAAC,CAAC,EAAC,UAAU,CAAC;MAACC,IAAI,EAAC,CAAC,GAAG,CAAC;MAACC,GAAG,EAAC,CAAC,CAAC,CAAC;MAACC,IAAI,EAAC,CAAC,CAAC,CAAC;MAACC,IAAI,EAAC,CAAC,CAAC,CAAC;MAACC,IAAI,EAAC,CAAC,CAAC,CAAC;MAACC,QAAQ,EAAC,CAAC,CAAC,CAAC;MAACC,UAAU,EAAC,CAAC,EAAE,CAAC;MAACC,SAAS,EAAC,CAAC,EAAE,CAAC;MAACC,SAAS,EAAC,CAAC,EAAE;IAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAClB,CAAC,CAAC;EAAA,CAAE,CAAC;AAAA,CAAC", "sources": ["./node_modules/ionicons/dist/esm-es5/loader.js"], "sourcesContent": ["import{p as promiseResolve,b as bootstrapLazy}from\"./index-5514a13d.js\";var patchEsm=function(){return promiseResolve()};var defineCustomElements=function(e,o){if(typeof window===\"undefined\")return Promise.resolve();return patchEsm().then((function(){return bootstrapLazy([[\"ion-icon\",[[1,\"ion-icon\",{mode:[1025],color:[1],ios:[1],md:[1],flipRtl:[4,\"flip-rtl\"],name:[513],src:[1],icon:[8],size:[1],lazy:[4],sanitize:[4],svgContent:[32],isVisible:[32],ariaLabel:[32]}]]]],o)}))};export{defineCustomElements};"], "names": ["p", "promiseResolve", "b", "bootstrapLazy", "patchEsm", "defineCustomElements", "e", "o", "window", "Promise", "resolve", "then", "mode", "color", "ios", "md", "flipRtl", "name", "src", "icon", "size", "lazy", "sanitize", "svgContent", "isVisible", "aria<PERSON><PERSON><PERSON>"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}