{"version": 3, "file": "default-node_modules_ionic_core_dist_esm_button-active-513e9fff_js-node_modules_ionic_core_di-7c8767.js", "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACqD;AACkE;AACnE;AAEpD,MAAMS,yBAAyB,GAAGA,CAACC,EAAE,EAAEC,QAAQ,KAAK;EAClD,IAAIC,oBAAoB;EACxB,IAAIC,oBAAoB;EACxB,MAAMC,qBAAqB,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAEC,gBAAgB,KAAK;IACxD,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;MACnC;IACF;IACA,MAAMC,MAAM,GAAGD,QAAQ,CAACE,gBAAgB,CAACL,CAAC,EAAEC,CAAC,CAAC;IAC9C,IAAI,CAACG,MAAM,IAAI,CAACR,QAAQ,CAACQ,MAAM,CAAC,EAAE;MAChCE,iBAAiB,CAAC,CAAC;MACnB;IACF;IACA,IAAIF,MAAM,KAAKP,oBAAoB,EAAE;MACnCS,iBAAiB,CAAC,CAAC;MACnBC,eAAe,CAACH,MAAM,EAAEF,gBAAgB,CAAC;IAC3C;EACF,CAAC;EACD,MAAMK,eAAe,GAAGA,CAACC,MAAM,EAAEN,gBAAgB,KAAK;IACpDL,oBAAoB,GAAGW,MAAM;IAC7B,IAAI,CAACV,oBAAoB,EAAE;MACzBA,oBAAoB,GAAGD,oBAAoB;IAC7C;IACA,MAAMY,cAAc,GAAGZ,oBAAoB;IAC3CX,qDAAS,CAAC,MAAMuB,cAAc,CAACC,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC,CAAC;IAC9DT,gBAAgB,CAAC,CAAC;EACpB,CAAC;EACD,MAAMI,iBAAiB,GAAGA,CAACM,aAAa,GAAG,KAAK,KAAK;IACnD,IAAI,CAACf,oBAAoB,EAAE;MACzB;IACF;IACA,MAAMY,cAAc,GAAGZ,oBAAoB;IAC3CX,qDAAS,CAAC,MAAMuB,cAAc,CAACC,SAAS,CAACG,MAAM,CAAC,eAAe,CAAC,CAAC;IACjE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAID,aAAa,IAAId,oBAAoB,KAAKD,oBAAoB,EAAE;MAClEA,oBAAoB,CAACiB,KAAK,CAAC,CAAC;IAC9B;IACAjB,oBAAoB,GAAGkB,SAAS;EAClC,CAAC;EACD,OAAOtB,iEAAa,CAAC;IACnBE,EAAE;IACFqB,WAAW,EAAE,kBAAkB;IAC/BC,SAAS,EAAE,CAAC;IACZC,OAAO,EAAGC,EAAE,IAAKpB,qBAAqB,CAACoB,EAAE,CAACC,QAAQ,EAAED,EAAE,CAACE,QAAQ,EAAE/B,kDAAoB,CAAC;IACtFgC,MAAM,EAAGH,EAAE,IAAKpB,qBAAqB,CAACoB,EAAE,CAACC,QAAQ,EAAED,EAAE,CAACE,QAAQ,EAAE7B,kDAAsB,CAAC;IACvF+B,KAAK,EAAEA,CAAA,KAAM;MACXjB,iBAAiB,CAAC,IAAI,CAAC;MACvBlB,sDAAkB,CAAC,CAAC;MACpBU,oBAAoB,GAAGiB,SAAS;IAClC;EACF,CAAC,CAAC;AACJ,CAAC;;;;;;;;;;;;;;;;AChED;AACA;AACA;AAC+C;AAE/C,MAAMW,YAAY,GAAGA,CAAA,KAAM;EACzB,IAAID,iDAAG,KAAKV,SAAS,EAAE;IACrB,OAAOU,iDAAG,CAACE,SAAS;EACtB;EACA,OAAOZ,SAAS;AAClB,CAAC;;;;;;;;;;;;;;;;;;;;;ACVD;AACA;AACA;AAC4D;AAE5D,IAAIc,WAAW;AACf,CAAC,UAAUA,WAAW,EAAE;EACtB;AACF;AACA;AACA;AACA;EACEA,WAAW,CAAC,OAAO,CAAC,GAAG,OAAO;EAC9B;AACF;AACA;AACA;AACA;EACEA,WAAW,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAChC;AACF;AACA;AACA;AACA;EACEA,WAAW,CAAC,OAAO,CAAC,GAAG,OAAO;AAChC,CAAC,EAAEA,WAAW,KAAKA,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;AACrC,IAAIC,gBAAgB;AACpB,CAAC,UAAUA,gBAAgB,EAAE;EAC3B;AACF;AACA;AACA;AACA;EACEA,gBAAgB,CAAC,SAAS,CAAC,GAAG,SAAS;EACvC;AACF;AACA;AACA;AACA;EACEA,gBAAgB,CAAC,SAAS,CAAC,GAAG,SAAS;EACvC;AACF;AACA;AACA;AACA;EACEA,gBAAgB,CAAC,OAAO,CAAC,GAAG,OAAO;AACrC,CAAC,EAAEA,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C,MAAMC,YAAY,GAAG;EACnBC,SAASA,CAAA,EAAG;IACV,MAAMC,YAAY,GAAGC,MAAM,CAACC,YAAY;IACxC,IAAIF,YAAY,EAAE;MAChB;MACA;MACA,OAAOA,YAAY;IACrB;IACA,MAAMG,SAAS,GAAGV,yDAAY,CAAC,CAAC;IAChC,IAAIU,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACC,iBAAiB,CAAC,SAAS,CAAC,EAAE;MAChG;MACA,OAAOD,SAAS,CAACE,OAAO,CAACC,OAAO;IAClC;IACA,OAAOxB,SAAS;EAClB,CAAC;EACDyB,SAASA,CAAA,EAAG;IACV,MAAMC,MAAM,GAAG,IAAI,CAACT,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACS,MAAM,EAAE;MACX,OAAO,KAAK;IACd;IACA,MAAML,SAAS,GAAGV,yDAAY,CAAC,CAAC;IAChC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACU,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACM,WAAW,CAAC,CAAC,MAAM,KAAK,EAAE;MAC7F,OAAO,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACC,OAAO,KAAK7B,SAAS;IAC5E;IACA,OAAO,IAAI;EACb,CAAC;EACD8B,SAASA,CAAA,EAAG;IACV,OAAOX,MAAM,CAACC,YAAY,KAAKpB,SAAS;EAC1C,CAAC;EACD+B,WAAWA,CAAA,EAAG;IACZ,OAAOpB,yDAAY,CAAC,CAAC,KAAKX,SAAS;EACrC,CAAC;EACDgC,MAAMA,CAACC,OAAO,EAAE;IACd,MAAMP,MAAM,GAAG,IAAI,CAACT,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACS,MAAM,EAAE;MACX;IACF;IACA;AACJ;AACA;AACA;AACA;AACA;IACI,MAAMQ,KAAK,GAAG,IAAI,CAACH,WAAW,CAAC,CAAC,GAAGE,OAAO,CAACC,KAAK,GAAGD,OAAO,CAACC,KAAK,CAACC,WAAW,CAAC,CAAC;IAC9ET,MAAM,CAACM,MAAM,CAAC;MAAEE;IAAM,CAAC,CAAC;EAC1B,CAAC;EACDE,YAAYA,CAACH,OAAO,EAAE;IACpB,MAAMP,MAAM,GAAG,IAAI,CAACT,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACS,MAAM,EAAE;MACX;IACF;IACA;AACJ;AACA;AACA;AACA;AACA;IACI,MAAMW,IAAI,GAAG,IAAI,CAACN,WAAW,CAAC,CAAC,GAAGE,OAAO,CAACI,IAAI,GAAGJ,OAAO,CAACI,IAAI,CAACF,WAAW,CAAC,CAAC;IAC3ET,MAAM,CAACU,YAAY,CAAC;MAAEC;IAAK,CAAC,CAAC;EAC/B,CAAC;EACDC,SAASA,CAAA,EAAG;IACV;AACJ;AACA;AACA;AACA;AACA;IACI,MAAMJ,KAAK,GAAG,IAAI,CAACH,WAAW,CAAC,CAAC,GAAGjB,WAAW,CAACyB,KAAK,GAAG,OAAO;IAC9D,IAAI,CAACP,MAAM,CAAC;MAAEE;IAAM,CAAC,CAAC;EACxB,CAAC;EACDM,cAAcA,CAAA,EAAG;IACf,MAAMd,MAAM,GAAG,IAAI,CAACT,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACS,MAAM,EAAE;MACX;IACF;IACA,IAAI,IAAI,CAACK,WAAW,CAAC,CAAC,EAAE;MACtBL,MAAM,CAACc,cAAc,CAAC,CAAC;IACzB,CAAC,MACI;MACHd,MAAM,CAACe,qBAAqB,CAAC,CAAC;IAChC;EACF,CAAC;EACDC,gBAAgBA,CAAA,EAAG;IACjB,MAAMhB,MAAM,GAAG,IAAI,CAACT,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACS,MAAM,EAAE;MACX;IACF;IACA,IAAI,IAAI,CAACK,WAAW,CAAC,CAAC,EAAE;MACtBL,MAAM,CAACgB,gBAAgB,CAAC,CAAC;IAC3B,CAAC,MACI;MACHhB,MAAM,CAACiB,uBAAuB,CAAC,CAAC;IAClC;EACF,CAAC;EACDC,YAAYA,CAAA,EAAG;IACb,MAAMlB,MAAM,GAAG,IAAI,CAACT,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACS,MAAM,EAAE;MACX;IACF;IACA,IAAI,IAAI,CAACK,WAAW,CAAC,CAAC,EAAE;MACtBL,MAAM,CAACkB,YAAY,CAAC,CAAC;IACvB,CAAC,MACI;MACHlB,MAAM,CAACmB,mBAAmB,CAAC,CAAC;IAC9B;EACF;AACF,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC5B,OAAO9B,YAAY,CAACS,SAAS,CAAC,CAAC;AACjC,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMsB,eAAe,GAAGA,CAAA,KAAM;EAC5BD,eAAe,CAAC,CAAC,IAAI9B,YAAY,CAACsB,SAAS,CAAC,CAAC;AAC/C,CAAC;AACD;AACA;AACA;AACA,MAAM/D,oBAAoB,GAAGA,CAAA,KAAM;EACjCuE,eAAe,CAAC,CAAC,IAAI9B,YAAY,CAACwB,cAAc,CAAC,CAAC;AACpD,CAAC;AACD;AACA;AACA;AACA,MAAM/D,sBAAsB,GAAGA,CAAA,KAAM;EACnCqE,eAAe,CAAC,CAAC,IAAI9B,YAAY,CAAC0B,gBAAgB,CAAC,CAAC;AACtD,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMrE,kBAAkB,GAAGA,CAAA,KAAM;EAC/ByE,eAAe,CAAC,CAAC,IAAI9B,YAAY,CAAC4B,YAAY,CAAC,CAAC;AAClD,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMI,YAAY,GAAIf,OAAO,IAAK;EAChCa,eAAe,CAAC,CAAC,IAAI9B,YAAY,CAACgB,MAAM,CAACC,OAAO,CAAC;AACnD,CAAC;;;;;;;;;;;;;;;;;AC1MD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkB,oBAAoB,GAAGA,CAAA,KAAM;EACjC,IAAIC,WAAW;EACf;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMC,IAAI;IAAA,IAAAC,IAAA,GAAAC,8KAAA,CAAG,aAAY;MACvB,MAAMC,CAAC,GAAGJ,WAAW;MACrB,IAAIK,OAAO;MACXL,WAAW,GAAG,IAAIM,OAAO,CAAEC,CAAC,IAAMF,OAAO,GAAGE,CAAE,CAAC;MAC/C,IAAIH,CAAC,KAAKxD,SAAS,EAAE;QACnB,MAAMwD,CAAC;MACT;MACA,OAAOC,OAAO;IAChB,CAAC;IAAA,gBARKJ,IAAIA,CAAA;MAAA,OAAAC,IAAA,CAAAM,KAAA,OAAAC,SAAA;IAAA;EAAA,GAQT;EACD,OAAO;IACLR;EACF,CAAC;AACH,CAAC;;;;;;;;;;;;;;;;;;;;ACnCD;AACA;AACA;AACA,MAAMS,WAAW,GAAGA,CAACC,QAAQ,EAAEnF,EAAE,KAAK;EACpC,OAAOA,EAAE,CAACoF,OAAO,CAACD,QAAQ,CAAC,KAAK,IAAI;AACtC,CAAC;AACD;AACA;AACA;AACA,MAAME,kBAAkB,GAAGA,CAACC,KAAK,EAAEC,WAAW,KAAK;EACjD,OAAO,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACE,MAAM,GAAG,CAAC,GAChDC,MAAM,CAACC,MAAM,CAAC;IAAE,WAAW,EAAE,IAAI;IAAE,CAAE,aAAYJ,KAAM,EAAC,GAAG;EAAK,CAAC,EAAEC,WAAW,CAAC,GAAGA,WAAW;AACnG,CAAC;AACD,MAAMI,YAAY,GAAIC,OAAO,IAAK;EAChC,IAAIA,OAAO,KAAKxE,SAAS,EAAE;IACzB,MAAMyE,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACH,OAAO,CAAC,GAAGA,OAAO,GAAGA,OAAO,CAACI,KAAK,CAAC,GAAG,CAAC;IACnE,OAAOH,KAAK,CACTI,MAAM,CAAEpE,CAAC,IAAKA,CAAC,IAAI,IAAI,CAAC,CACxBqE,GAAG,CAAErE,CAAC,IAAKA,CAAC,CAACsE,IAAI,CAAC,CAAC,CAAC,CACpBF,MAAM,CAAEpE,CAAC,IAAKA,CAAC,KAAK,EAAE,CAAC;EAC5B;EACA,OAAO,EAAE;AACX,CAAC;AACD,MAAMuE,WAAW,GAAIR,OAAO,IAAK;EAC/B,MAAMM,GAAG,GAAG,CAAC,CAAC;EACdP,YAAY,CAACC,OAAO,CAAC,CAACS,OAAO,CAAExE,CAAC,IAAMqE,GAAG,CAACrE,CAAC,CAAC,GAAG,IAAK,CAAC;EACrD,OAAOqE,GAAG;AACZ,CAAC;AACD,MAAMI,MAAM,GAAG,sBAAsB;AACrC,MAAMC,OAAO;EAAA,IAAA7B,IAAA,GAAAC,8KAAA,CAAG,WAAO6B,GAAG,EAAEhF,EAAE,EAAEiF,SAAS,EAAEC,SAAS,EAAK;IACvD,IAAIF,GAAG,IAAI,IAAI,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAACF,MAAM,CAACK,IAAI,CAACH,GAAG,CAAC,EAAE;MACtD,MAAMI,MAAM,GAAGpG,QAAQ,CAACqG,aAAa,CAAC,YAAY,CAAC;MACnD,IAAID,MAAM,EAAE;QACV,IAAIpF,EAAE,IAAI,IAAI,EAAE;UACdA,EAAE,CAACsF,cAAc,CAAC,CAAC;QACrB;QACA,OAAOF,MAAM,CAACG,IAAI,CAACP,GAAG,EAAEC,SAAS,EAAEC,SAAS,CAAC;MAC/C;IACF;IACA,OAAO,KAAK;EACd,CAAC;EAAA,gBAXKH,OAAOA,CAAAS,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAzC,IAAA,CAAAM,KAAA,OAAAC,SAAA;EAAA;AAAA,GAWZ", "sources": ["./node_modules/@ionic/core/dist/esm/button-active-513e9fff.js", "./node_modules/@ionic/core/dist/esm/capacitor-b4979570.js", "./node_modules/@ionic/core/dist/esm/haptic-6447af60.js", "./node_modules/@ionic/core/dist/esm/lock-controller-e8c6c051.js", "./node_modules/@ionic/core/dist/esm/theme-17531cdf.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as writeTask } from './index-2d388930.js';\nimport { h as hapticSelectionEnd, a as hapticSelectionStart, b as hapticSelectionChanged } from './haptic-6447af60.js';\nimport { createGesture } from './index-ff313b19.js';\n\nconst createButtonActiveGesture = (el, isButton) => {\n  let currentTouchedButton;\n  let initialTouchedButton;\n  const activateButtonAtPoint = (x, y, hapticFeedbackFn) => {\n    if (typeof document === 'undefined') {\n      return;\n    }\n    const target = document.elementFromPoint(x, y);\n    if (!target || !isButton(target)) {\n      clearActiveButton();\n      return;\n    }\n    if (target !== currentTouchedButton) {\n      clearActiveButton();\n      setActiveButton(target, hapticFeedbackFn);\n    }\n  };\n  const setActiveButton = (button, hapticFeedbackFn) => {\n    currentTouchedButton = button;\n    if (!initialTouchedButton) {\n      initialTouchedButton = currentTouchedButton;\n    }\n    const buttonToModify = currentTouchedButton;\n    writeTask(() => buttonToModify.classList.add('ion-activated'));\n    hapticFeedbackFn();\n  };\n  const clearActiveButton = (dispatchClick = false) => {\n    if (!currentTouchedButton) {\n      return;\n    }\n    const buttonToModify = currentTouchedButton;\n    writeTask(() => buttonToModify.classList.remove('ion-activated'));\n    /**\n     * Clicking on one button, but releasing on another button\n     * does not dispatch a click event in browsers, so we\n     * need to do it manually here. Some browsers will\n     * dispatch a click if clicking on one button, dragging over\n     * another button, and releasing on the original button. In that\n     * case, we need to make sure we do not cause a double click there.\n     */\n    if (dispatchClick && initialTouchedButton !== currentTouchedButton) {\n      currentTouchedButton.click();\n    }\n    currentTouchedButton = undefined;\n  };\n  return createGesture({\n    el,\n    gestureName: 'buttonActiveDrag',\n    threshold: 0,\n    onStart: (ev) => activateButtonAtPoint(ev.currentX, ev.currentY, hapticSelectionStart),\n    onMove: (ev) => activateButtonAtPoint(ev.currentX, ev.currentY, hapticSelectionChanged),\n    onEnd: () => {\n      clearActiveButton(true);\n      hapticSelectionEnd();\n      initialTouchedButton = undefined;\n    },\n  });\n};\n\nexport { createButtonActiveGesture as c };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index-7a14ecec.js';\n\nconst getCapacitor = () => {\n  if (win !== undefined) {\n    return win.Capacitor;\n  }\n  return undefined;\n};\n\nexport { getCapacitor as g };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { g as getCapacitor } from './capacitor-b4979570.js';\n\nvar ImpactStyle;\n(function (ImpactStyle) {\n  /**\n   * A collision between large, heavy user interface elements\n   *\n   * @since 1.0.0\n   */\n  ImpactStyle[\"Heavy\"] = \"HEAVY\";\n  /**\n   * A collision between moderately sized user interface elements\n   *\n   * @since 1.0.0\n   */\n  ImpactStyle[\"Medium\"] = \"MEDIUM\";\n  /**\n   * A collision between small, light user interface elements\n   *\n   * @since 1.0.0\n   */\n  ImpactStyle[\"Light\"] = \"LIGHT\";\n})(ImpactStyle || (ImpactStyle = {}));\nvar NotificationType;\n(function (NotificationType) {\n  /**\n   * A notification feedback type indicating that a task has completed successfully\n   *\n   * @since 1.0.0\n   */\n  NotificationType[\"Success\"] = \"SUCCESS\";\n  /**\n   * A notification feedback type indicating that a task has produced a warning\n   *\n   * @since 1.0.0\n   */\n  NotificationType[\"Warning\"] = \"WARNING\";\n  /**\n   * A notification feedback type indicating that a task has failed\n   *\n   * @since 1.0.0\n   */\n  NotificationType[\"Error\"] = \"ERROR\";\n})(NotificationType || (NotificationType = {}));\nconst HapticEngine = {\n  getEngine() {\n    const tapticEngine = window.TapticEngine;\n    if (tapticEngine) {\n      // Cordova\n      // TODO FW-4707 - Remove this in Ionic 8\n      return tapticEngine;\n    }\n    const capacitor = getCapacitor();\n    if (capacitor === null || capacitor === void 0 ? void 0 : capacitor.isPluginAvailable('Haptics')) {\n      // Capacitor\n      return capacitor.Plugins.Haptics;\n    }\n    return undefined;\n  },\n  available() {\n    const engine = this.getEngine();\n    if (!engine) {\n      return false;\n    }\n    const capacitor = getCapacitor();\n    /**\n     * Developers can manually import the\n     * Haptics plugin in their app which will cause\n     * getEngine to return the Haptics engine. However,\n     * the Haptics engine will throw an error if\n     * used in a web browser that does not support\n     * the Vibrate API. This check avoids that error\n     * if the browser does not support the Vibrate API.\n     */\n    if ((capacitor === null || capacitor === void 0 ? void 0 : capacitor.getPlatform()) === 'web') {\n      return typeof navigator !== 'undefined' && navigator.vibrate !== undefined;\n    }\n    return true;\n  },\n  isCordova() {\n    return window.TapticEngine !== undefined;\n  },\n  isCapacitor() {\n    return getCapacitor() !== undefined;\n  },\n  impact(options) {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    /**\n     * To provide backwards compatibility with Cordova apps,\n     * we convert the style to lowercase.\n     *\n     * TODO: FW-4707 - Remove this in Ionic 8\n     */\n    const style = this.isCapacitor() ? options.style : options.style.toLowerCase();\n    engine.impact({ style });\n  },\n  notification(options) {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    /**\n     * To provide backwards compatibility with Cordova apps,\n     * we convert the style to lowercase.\n     *\n     * TODO: FW-4707 - Remove this in Ionic 8\n     */\n    const type = this.isCapacitor() ? options.type : options.type.toLowerCase();\n    engine.notification({ type });\n  },\n  selection() {\n    /**\n     * To provide backwards compatibility with Cordova apps,\n     * we convert the style to lowercase.\n     *\n     * TODO: FW-4707 - Remove this in Ionic 8\n     */\n    const style = this.isCapacitor() ? ImpactStyle.Light : 'light';\n    this.impact({ style });\n  },\n  selectionStart() {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    if (this.isCapacitor()) {\n      engine.selectionStart();\n    }\n    else {\n      engine.gestureSelectionStart();\n    }\n  },\n  selectionChanged() {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    if (this.isCapacitor()) {\n      engine.selectionChanged();\n    }\n    else {\n      engine.gestureSelectionChanged();\n    }\n  },\n  selectionEnd() {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    if (this.isCapacitor()) {\n      engine.selectionEnd();\n    }\n    else {\n      engine.gestureSelectionEnd();\n    }\n  },\n};\n/**\n * Check to see if the Haptic Plugin is available\n * @return Returns `true` or false if the plugin is available\n */\nconst hapticAvailable = () => {\n  return HapticEngine.available();\n};\n/**\n * Trigger a selection changed haptic event. Good for one-time events\n * (not for gestures)\n */\nconst hapticSelection = () => {\n  hapticAvailable() && HapticEngine.selection();\n};\n/**\n * Tell the haptic engine that a gesture for a selection change is starting.\n */\nconst hapticSelectionStart = () => {\n  hapticAvailable() && HapticEngine.selectionStart();\n};\n/**\n * Tell the haptic engine that a selection changed during a gesture.\n */\nconst hapticSelectionChanged = () => {\n  hapticAvailable() && HapticEngine.selectionChanged();\n};\n/**\n * Tell the haptic engine we are done with a gesture. This needs to be\n * called lest resources are not properly recycled.\n */\nconst hapticSelectionEnd = () => {\n  hapticAvailable() && HapticEngine.selectionEnd();\n};\n/**\n * Use this to indicate success/failure/warning to the user.\n * options should be of the type `{ style: ImpactStyle.LIGHT }` (or `MEDIUM`/`HEAVY`)\n */\nconst hapticImpact = (options) => {\n  hapticAvailable() && HapticEngine.impact(options);\n};\n\nexport { ImpactStyle as I, hapticSelectionStart as a, hapticSelectionChanged as b, hapticSelection as c, hapticImpact as d, hapticSelectionEnd as h };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n/**\n * Creates a lock controller.\n *\n * Claiming a lock means that nothing else can acquire the lock until it is released.\n * This can momentarily prevent execution of code that needs to wait for the earlier code to finish.\n * For example, this can be used to prevent multiple transitions from occurring at the same time.\n */\nconst createLockController = () => {\n  let waitPromise;\n  /**\n   * When lock() is called, the lock is claimed.\n   * Once a lock has been claimed, it cannot be claimed again until it is released.\n   * When this function gets resolved, the lock is released, allowing it to be claimed again.\n   *\n   * @example ```tsx\n   * const unlock = await this.lockController.lock();\n   * // do other stuff\n   * unlock();\n   * ```\n   */\n  const lock = async () => {\n    const p = waitPromise;\n    let resolve;\n    waitPromise = new Promise((r) => (resolve = r));\n    if (p !== undefined) {\n      await p;\n    }\n    return resolve;\n  };\n  return {\n    lock,\n  };\n};\n\nexport { createLockController as c };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst hostContext = (selector, el) => {\n  return el.closest(selector) !== null;\n};\n/**\n * Create the mode and color classes for the component based on the classes passed in\n */\nconst createColorClasses = (color, cssClassMap) => {\n  return typeof color === 'string' && color.length > 0\n    ? Object.assign({ 'ion-color': true, [`ion-color-${color}`]: true }, cssClassMap) : cssClassMap;\n};\nconst getClassList = (classes) => {\n  if (classes !== undefined) {\n    const array = Array.isArray(classes) ? classes : classes.split(' ');\n    return array\n      .filter((c) => c != null)\n      .map((c) => c.trim())\n      .filter((c) => c !== '');\n  }\n  return [];\n};\nconst getClassMap = (classes) => {\n  const map = {};\n  getClassList(classes).forEach((c) => (map[c] = true));\n  return map;\n};\nconst SCHEME = /^[a-z][a-z0-9+\\-.]*:/;\nconst openURL = async (url, ev, direction, animation) => {\n  if (url != null && url[0] !== '#' && !SCHEME.test(url)) {\n    const router = document.querySelector('ion-router');\n    if (router) {\n      if (ev != null) {\n        ev.preventDefault();\n      }\n      return router.push(url, direction, animation);\n    }\n  }\n  return false;\n};\n\nexport { createColorClasses as c, getClassMap as g, hostContext as h, openURL as o };\n"], "names": ["w", "writeTask", "h", "hapticSelectionEnd", "a", "hapticSelectionStart", "b", "hapticSelectionChanged", "createGesture", "createButtonActiveGesture", "el", "isButton", "currentTouchedButton", "initialTouchedButton", "activateButtonAtPoint", "x", "y", "hapticFeedbackFn", "document", "target", "elementFromPoint", "clearActiveButton", "setActiveButton", "button", "buttonToModify", "classList", "add", "dispatchClick", "remove", "click", "undefined", "<PERSON><PERSON><PERSON>", "threshold", "onStart", "ev", "currentX", "currentY", "onMove", "onEnd", "c", "win", "getCapacitor", "Capacitor", "g", "ImpactStyle", "NotificationType", "HapticEngine", "getEngine", "tapticEngine", "window", "TapticEngine", "capacitor", "isPluginAvailable", "Plugins", "Haptics", "available", "engine", "getPlatform", "navigator", "vibrate", "<PERSON><PERSON><PERSON><PERSON>", "isCapacitor", "impact", "options", "style", "toLowerCase", "notification", "type", "selection", "Light", "selectionStart", "gestureSelectionStart", "selectionChanged", "gestureSelectionChanged", "selectionEnd", "gestureSelectionEnd", "hapticAvailable", "hapticSelection", "hapticImpact", "I", "d", "createLockController", "waitPromise", "lock", "_ref", "_asyncToGenerator", "p", "resolve", "Promise", "r", "apply", "arguments", "hostContext", "selector", "closest", "createColorClasses", "color", "cssClassMap", "length", "Object", "assign", "getClassList", "classes", "array", "Array", "isArray", "split", "filter", "map", "trim", "getClassMap", "for<PERSON>ach", "SCHEME", "openURL", "url", "direction", "animation", "test", "router", "querySelector", "preventDefault", "push", "_x", "_x2", "_x3", "_x4", "o"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0, 1, 2, 3, 4]}