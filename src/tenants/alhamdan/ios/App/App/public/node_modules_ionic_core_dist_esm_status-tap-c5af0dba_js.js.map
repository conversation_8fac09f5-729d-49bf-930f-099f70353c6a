{"version": 3, "file": "node_modules_ionic_core_dist_esm_status-tap-c5af0dba_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACoE;AACe;AACrB;AACjC;AAE7B,MAAMU,cAAc,GAAGA,CAAA,KAAM;EAC3B,MAAMC,GAAG,GAAGC,MAAM;EAClBD,GAAG,CAACE,gBAAgB,CAAC,WAAW,EAAE,MAAM;IACtCZ,qDAAQ,CAAC,MAAM;MACb,MAAMa,KAAK,GAAGH,GAAG,CAACI,UAAU;MAC5B,MAAMC,MAAM,GAAGL,GAAG,CAACM,WAAW;MAC9B,MAAMC,EAAE,GAAGC,QAAQ,CAACC,gBAAgB,CAACN,KAAK,GAAG,CAAC,EAAEE,MAAM,GAAG,CAAC,CAAC;MAC3D,IAAI,CAACE,EAAE,EAAE;QACP;MACF;MACA,MAAMG,SAAS,GAAGhB,qDAAqB,CAACa,EAAE,CAAC;MAC3C,IAAIG,SAAS,EAAE;QACb,IAAIC,OAAO,CAAEC,OAAO,IAAKd,uDAAgB,CAACY,SAAS,EAAEE,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;UACxErB,qDAAS,eAAAsB,8KAAA,CAAC,aAAY;YACpB;AACZ;AACA;AACA;AACA;AACA;AACA;YACYJ,SAAS,CAACK,KAAK,CAACC,WAAW,CAAC,YAAY,EAAE,QAAQ,CAAC;YACnD,MAAMpB,qDAAW,CAACc,SAAS,EAAE,GAAG,CAAC;YACjCA,SAAS,CAACK,KAAK,CAACE,cAAc,CAAC,YAAY,CAAC;UAC9C,CAAC,EAAC;QACJ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/status-tap-c5af0dba.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { e as readTask, w as writeTask } from './index-2d388930.js';\nimport { f as findClosestIonContent, s as scrollToTop } from './index-746a238e.js';\nimport { c as componentOnReady } from './helpers-3379ba19.js';\nimport './index-595d62c9.js';\n\nconst startStatusTap = () => {\n  const win = window;\n  win.addEventListener('statusTap', () => {\n    readTask(() => {\n      const width = win.innerWidth;\n      const height = win.innerHeight;\n      const el = document.elementFromPoint(width / 2, height / 2);\n      if (!el) {\n        return;\n      }\n      const contentEl = findClosestIonContent(el);\n      if (contentEl) {\n        new Promise((resolve) => componentOnReady(contentEl, resolve)).then(() => {\n          writeTask(async () => {\n            /**\n             * If scrolling and user taps status bar,\n             * only calling scrollToTop is not enough\n             * as engines like WebKit will jump the\n             * scroll position back down and complete\n             * any in-progress momentum scrolling.\n             */\n            contentEl.style.setProperty('--overflow', 'hidden');\n            await scrollToTop(contentEl, 300);\n            contentEl.style.removeProperty('--overflow');\n          });\n        });\n      }\n    });\n  });\n};\n\nexport { startStatusTap };\n"], "names": ["e", "readTask", "w", "writeTask", "f", "findClosestIonContent", "s", "scrollToTop", "c", "componentOnReady", "startStatusTap", "win", "window", "addEventListener", "width", "innerWidth", "height", "innerHeight", "el", "document", "elementFromPoint", "contentEl", "Promise", "resolve", "then", "_asyncToGenerator", "style", "setProperty", "removeProperty"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}