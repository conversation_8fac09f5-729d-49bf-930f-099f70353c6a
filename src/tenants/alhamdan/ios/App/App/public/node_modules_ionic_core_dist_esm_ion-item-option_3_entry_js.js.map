{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-item-option_3_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC6G;AAC/C;AACD;AACN;AACgE;AACpD;AACtC;AAE7B,MAAMoB,gBAAgB,GAAG,i1FAAi1F;AAE12F,MAAMC,eAAe,GAAG,+uFAA+uF;AAEvwF,MAAMC,UAAU,GAAG,MAAM;EACvBC,WAAWA,CAACC,OAAO,EAAE;IACnBvB,qDAAgB,CAAC,IAAI,EAAEuB,OAAO,CAAC;IAC/B,IAAI,CAACC,OAAO,GAAIC,EAAE,IAAK;MACrB,MAAMC,EAAE,GAAGD,EAAE,CAACE,MAAM,CAACC,OAAO,CAAC,iBAAiB,CAAC;MAC/C,IAAIF,EAAE,EAAE;QACND,EAAE,CAACI,cAAc,CAAC,CAAC;MACrB;IACF,CAAC;IACD,IAAI,CAACC,KAAK,GAAGC,SAAS;IACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAGF,SAAS;IACzB,IAAI,CAACG,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,IAAI,GAAGJ,SAAS;IACrB,IAAI,CAACK,GAAG,GAAGL,SAAS;IACpB,IAAI,CAACJ,MAAM,GAAGI,SAAS;IACvB,IAAI,CAACM,IAAI,GAAG,QAAQ;EACtB;EACAC,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEN,QAAQ;MAAEE,UAAU;MAAEC;IAAK,CAAC,GAAG,IAAI;IAC3C,MAAMI,OAAO,GAAGJ,IAAI,KAAKJ,SAAS,GAAG,QAAQ,GAAG,GAAG;IACnD,MAAMS,IAAI,GAAG7B,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM8B,KAAK,GAAGF,OAAO,KAAK,QAAQ,GAC9B;MAAEF,IAAI,EAAE,IAAI,CAACA;IAAK,CAAC,GACnB;MACAJ,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBE,IAAI,EAAE,IAAI,CAACA,IAAI;MACfR,MAAM,EAAE,IAAI,CAACA;IACf,CAAC;IACH,OAAQ1B,qDAAC,CAACE,iDAAI,EAAE;MAAEqB,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEkB,KAAK,EAAEjC,qDAAkB,CAAC,IAAI,CAACqB,KAAK,EAAE;QAC3E,CAACU,IAAI,GAAG,IAAI;QACZ,sBAAsB,EAAER,QAAQ;QAChC,wBAAwB,EAAEE,UAAU;QACpC,iBAAiB,EAAE;MACrB,CAAC;IAAE,CAAC,EAAEjC,qDAAC,CAACsC,OAAO,EAAEI,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,KAAK,EAAE;MAAEC,KAAK,EAAE,eAAe;MAAEG,IAAI,EAAE,QAAQ;MAAEb,QAAQ,EAAEA;IAAS,CAAC,CAAC,EAAE/B,qDAAC,CAAC,MAAM,EAAE;MAAEyC,KAAK,EAAE;IAAe,CAAC,EAAEzC,qDAAC,CAAC,MAAM,EAAE;MAAE6C,IAAI,EAAE;IAAM,CAAC,CAAC,EAAE7C,qDAAC,CAAC,KAAK,EAAE;MAAEyC,KAAK,EAAE;IAAqB,CAAC,EAAEzC,qDAAC,CAAC,MAAM,EAAE;MAAE6C,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAE7C,qDAAC,CAAC,MAAM,EAAE;MAAE6C,IAAI,EAAE;IAAY,CAAC,CAAC,EAAE7C,qDAAC,CAAC,MAAM,EAAE,IAAI,CAAC,EAAEA,qDAAC,CAAC,MAAM,EAAE;MAAE6C,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC,EAAE7C,qDAAC,CAAC,MAAM,EAAE;MAAE6C,IAAI,EAAE;IAAS,CAAC,CAAC,CAAC,EAAEN,IAAI,KAAK,IAAI,IAAIvC,qDAAC,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC,CAAC;EACvZ;EACA,IAAIyB,EAAEA,CAAA,EAAG;IAAE,OAAOrB,qDAAU,CAAC,IAAI,CAAC;EAAE;AACtC,CAAC;AACDgB,UAAU,CAAC0B,KAAK,GAAG;EACjBC,GAAG,EAAE7B,gBAAgB;EACrB8B,EAAE,EAAE7B;AACN,CAAC;AAED,MAAM8B,iBAAiB,GAAG,uoFAAuoF;AAEjqF,MAAMC,gBAAgB,GAAG,2kFAA2kF;AAEpmF,MAAMC,WAAW,GAAG,MAAM;EACxB9B,WAAWA,CAACC,OAAO,EAAE;IACnBvB,qDAAgB,CAAC,IAAI,EAAEuB,OAAO,CAAC;IAC/B,IAAI,CAAC8B,QAAQ,GAAG9C,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC+C,IAAI,GAAG,KAAK;EACnB;EACA;EACMC,cAAcA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,8KAAA;MACrBD,KAAI,CAACH,QAAQ,CAACK,IAAI,CAAC;QACjBJ,IAAI,EAAEE,KAAI,CAACF;MACb,CAAC,CAAC;IAAC;EACL;EACAhB,MAAMA,CAAA,EAAG;IACP,MAAME,IAAI,GAAG7B,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAMgD,KAAK,GAAG9C,uDAAS,CAAC,IAAI,CAACyC,IAAI,CAAC;IAClC,OAAQrD,qDAAC,CAACE,iDAAI,EAAE;MAAEuC,KAAK,EAAE;QACrB,CAACF,IAAI,GAAG,IAAI;QACZ;QACA,CAAE,gBAAeA,IAAK,EAAC,GAAG,IAAI;QAC9B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;QACQ,oBAAoB,EAAE,CAACmB,KAAK;QAC5B,kBAAkB,EAAEA;MACtB;IAAE,CAAC,CAAC;EACR;EACA,IAAIjC,EAAEA,CAAA,EAAG;IAAE,OAAOrB,qDAAU,CAAC,IAAI,CAAC;EAAE;AACtC,CAAC;AACD+C,WAAW,CAACL,KAAK,GAAG;EAClBC,GAAG,EAAEE,iBAAiB;EACtBD,EAAE,EAAEE;AACN,CAAC;AAED,MAAMS,cAAc,GAAG,++DAA++D;AAEtgE,MAAMC,YAAY,GAAG,EAAE;AACvB,MAAMC,cAAc,GAAG,IAAI;AAC3B,IAAIC,eAAe;AACnB,MAAMC,WAAW,GAAG,MAAM;EACxB1C,WAAWA,CAACC,OAAO,EAAE;IACnBvB,qDAAgB,CAAC,IAAI,EAAEuB,OAAO,CAAC;IAC/B,IAAI,CAAC0C,OAAO,GAAG1D,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAAC2D,IAAI,GAAG,IAAI;IAChB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,qBAAqB,GAAG,IAAI;IACjC,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAAC3C,QAAQ,GAAG,KAAK;EACvB;EACA4C,eAAeA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACC,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACC,MAAM,CAAC,CAAC,IAAI,CAAC9C,QAAQ,CAAC;IACrC;EACF;EACM+C,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAvB,8KAAA;MACxB,MAAM;QAAE/B;MAAG,CAAC,GAAGsD,MAAI;MACnBA,MAAI,CAACd,IAAI,GAAGxC,EAAE,CAACuD,aAAa,CAAC,UAAU,CAAC;MACxCD,MAAI,CAACP,SAAS,GAAG3D,qDAAqB,CAACY,EAAE,CAAC;MAC1C;AACJ;AACA;AACA;AACA;AACA;AACA;MACIsD,MAAI,CAACE,gBAAgB,GAAGhE,6DAAe,CAACQ,EAAE,EAAE,iBAAiB,eAAA+B,8KAAA,CAAE,aAAY;QACzE,MAAMuB,MAAI,CAACG,aAAa,CAAC,CAAC;MAC5B,CAAC,EAAC;MACF,MAAMH,MAAI,CAACG,aAAa,CAAC,CAAC;MAC1BH,MAAI,CAACH,OAAO,GAAG,OAAO,sHAA6B,EAAEO,aAAa,CAAC;QACjE1D,EAAE;QACF2D,WAAW,EAAE,YAAY;QACzBC,eAAe,EAAE,GAAG;QACpBC,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAG/D,EAAE,IAAKuD,MAAI,CAACQ,QAAQ,CAAC/D,EAAE,CAAC;QACnCgE,OAAO,EAAEA,CAAA,KAAMT,MAAI,CAACS,OAAO,CAAC,CAAC;QAC7BC,MAAM,EAAGjE,EAAE,IAAKuD,MAAI,CAACU,MAAM,CAACjE,EAAE,CAAC;QAC/BkE,KAAK,EAAGlE,EAAE,IAAKuD,MAAI,CAACW,KAAK,CAAClE,EAAE;MAC9B,CAAC,CAAC;MACFuD,MAAI,CAACJ,eAAe,CAAC,CAAC;IAAC;EACzB;EACAgB,oBAAoBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACf,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACgB,OAAO,CAAC,CAAC;MACtB,IAAI,CAAChB,OAAO,GAAG9C,SAAS;IAC1B;IACA,IAAI,CAACmC,IAAI,GAAG,IAAI;IAChB,IAAI,CAAC4B,WAAW,GAAG,IAAI,CAACC,YAAY,GAAGhE,SAAS;IAChD,IAAIgC,eAAe,KAAK,IAAI,CAACrC,EAAE,EAAE;MAC/BqC,eAAe,GAAGhC,SAAS;IAC7B;IACA,IAAI,IAAI,CAACmD,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACc,UAAU,CAAC,CAAC;MAClC,IAAI,CAACd,gBAAgB,GAAGnD,SAAS;IACnC;EACF;EACA;AACF;AACA;EACEkE,aAAaA,CAAA,EAAG;IACd,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAI,CAAChC,UAAU,CAAC;EACzC;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACEiC,eAAeA,CAAA,EAAG;IAChB,OAAOF,OAAO,CAACC,OAAO,CAAC,IAAI,CAACE,mBAAmB,CAAC,CAAC,CAAC;EACpD;EACA;AACF;AACA;AACA;AACA;EACQC,IAAIA,CAAChD,IAAI,EAAE;IAAA,IAAAiD,MAAA;IAAA,OAAA9C,8KAAA;MACf,IAAI+C,EAAE;MACN;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;MACI,MAAMtC,IAAI,GAAIqC,MAAI,CAACrC,IAAI,GAAG,CAACsC,EAAE,GAAGD,MAAI,CAACrC,IAAI,MAAM,IAAI,IAAIsC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGD,MAAI,CAAC7E,EAAE,CAACuD,aAAa,CAAC,UAAU,CAAE;MAC9G,IAAIf,IAAI,KAAK,IAAI,EAAE;QACjB;MACF;MACA,MAAMuC,aAAa,GAAGF,MAAI,CAACG,UAAU,CAACpD,IAAI,CAAC;MAC3C,IAAI,CAACmD,aAAa,EAAE;QAClB;MACF;MACA;AACJ;AACA;AACA;MACI,IAAInD,IAAI,KAAKvB,SAAS,EAAE;QACtBuB,IAAI,GAAGmD,aAAa,KAAKF,MAAI,CAACT,WAAW,GAAG,OAAO,GAAG,KAAK;MAC7D;MACA;MACAxC,IAAI,GAAGzC,uDAAS,CAACyC,IAAI,CAAC,GAAG,KAAK,GAAG,OAAO;MACxC,MAAMqD,WAAW,GAAGJ,MAAI,CAACpC,UAAU,GAAG,CAAC;MACvC,MAAMyC,SAAS,GAAGL,MAAI,CAACpC,UAAU,GAAG,CAAC;MACrC;AACJ;AACA;AACA;MACI,IAAIwC,WAAW,IAAIF,aAAa,KAAKF,MAAI,CAACT,WAAW,EAAE;QACrD;MACF;MACA,IAAIc,SAAS,IAAIH,aAAa,KAAKF,MAAI,CAACR,YAAY,EAAE;QACpD;MACF;MACAQ,MAAI,CAACM,WAAW,CAAC,CAAC;MAClBN,MAAI,CAAC5B,KAAK,GAAG,CAAC,CAAC;MACfmC,qBAAqB,CAAC,MAAM;QAC1BP,MAAI,CAACQ,kBAAkB,CAAC,CAAC;QACzB,MAAMC,KAAK,GAAG1D,IAAI,KAAK,KAAK,GAAGiD,MAAI,CAAClC,kBAAkB,GAAG,CAACkC,MAAI,CAACjC,iBAAiB;QAChFP,eAAe,GAAGwC,MAAI,CAAC7E,EAAE;QACzB6E,MAAI,CAACU,aAAa,CAACD,KAAK,EAAE,KAAK,CAAC;QAChCT,MAAI,CAAC5B,KAAK,GAAGrB,IAAI,KAAK,KAAK,GAAG,CAAC,CAAC,yBAAyB,EAAE,CAAC;MAC9D,CAAC,CAAC;IAAC;EACL;EACA;AACF;AACA;EACQ4D,KAAKA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA1D,8KAAA;MACZ0D,MAAI,CAACF,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC;IAAC;EAC9B;EACA;AACF;AACA;EACQJ,WAAWA,CAAA,EAAG;IAAA,OAAApD,8KAAA;MAClB,IAAIM,eAAe,KAAKhC,SAAS,EAAE;QACjCgC,eAAe,CAACmD,KAAK,CAAC,CAAC;QACvBnD,eAAe,GAAGhC,SAAS;QAC3B,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IAAC;EACf;EACA;AACF;AACA;AACA;AACA;AACA;EACE2E,UAAUA,CAACpD,IAAI,EAAE;IACf,IAAIA,IAAI,KAAKvB,SAAS,EAAE;MACtB,OAAO,IAAI,CAAC+D,WAAW,IAAI,IAAI,CAACC,YAAY;IAC9C,CAAC,MACI,IAAIzC,IAAI,KAAK,OAAO,EAAE;MACzB,OAAO,IAAI,CAACwC,WAAW;IACzB,CAAC,MACI;MACH,OAAO,IAAI,CAACC,YAAY;IAC1B;EACF;EACMZ,aAAaA,CAAA,EAAG;IAAA,IAAAiC,MAAA;IAAA,OAAA3D,8KAAA;MACpB,MAAM4D,OAAO,GAAGD,MAAI,CAAC1F,EAAE,CAAC4F,gBAAgB,CAAC,kBAAkB,CAAC;MAC5D,IAAI/C,KAAK,GAAG,CAAC;MACb;MACA6C,MAAI,CAACtB,WAAW,GAAGsB,MAAI,CAACrB,YAAY,GAAGhE,SAAS;MAChD,KAAK,IAAIwF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QACvC,MAAMrD,IAAI,GAAGmD,OAAO,CAACnD,IAAI,CAACqD,CAAC,CAAC;QAC5B;AACN;AACA;AACA;AACA;QACM;QACA,MAAME,MAAM,GAAGvD,IAAI,CAACwD,gBAAgB,KAAK3F,SAAS,SAASmC,IAAI,CAACwD,gBAAgB,CAAC,CAAC,GAAGxD,IAAI;QACzF,MAAMZ,IAAI,GAAGzC,uDAAS,CAAC4G,MAAM,CAACnE,IAAI,CAAC,GAAG,KAAK,GAAG,OAAO;QACrD,IAAIA,IAAI,KAAK,OAAO,EAAE;UACpB8D,MAAI,CAACtB,WAAW,GAAG2B,MAAM;UACzBlD,KAAK,IAAI,CAAC,CAAC;QACb,CAAC,MACI;UACH6C,MAAI,CAACrB,YAAY,GAAG0B,MAAM;UAC1BlD,KAAK,IAAI,CAAC,CAAC;QACb;MACF;;MACA6C,MAAI,CAAC5C,SAAS,GAAG,IAAI;MACrB4C,MAAI,CAAC7C,KAAK,GAAGA,KAAK;IAAC;EACrB;EACAiB,QAAQA,CAACX,OAAO,EAAE;IAChB;AACJ;AACA;AACA;AACA;IACI,MAAM8C,GAAG,GAAGC,QAAQ,CAACC,GAAG,KAAK,KAAK;IAClC,MAAMC,MAAM,GAAGH,GAAG,GAAGI,MAAM,CAACC,UAAU,GAAGnD,OAAO,CAACoD,MAAM,GAAG,EAAE,GAAGpD,OAAO,CAACoD,MAAM,GAAG,EAAE;IAClF,IAAIH,MAAM,EAAE;MACV,OAAO,KAAK;IACd;IACA,MAAMI,QAAQ,GAAGnE,eAAe;IAChC,IAAImE,QAAQ,IAAIA,QAAQ,KAAK,IAAI,CAACxG,EAAE,EAAE;MACpC,IAAI,CAACmF,WAAW,CAAC,CAAC;IACpB;IACA,OAAO,CAAC,EAAE,IAAI,CAACd,YAAY,IAAI,IAAI,CAACD,WAAW,CAAC;EAClD;EACAL,OAAOA,CAAA,EAAG;IACR;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACvB,IAAI,GAAG,IAAI,CAACxC,EAAE,CAACuD,aAAa,CAAC,UAAU,CAAC;IAC7C,MAAM;MAAER;IAAU,CAAC,GAAG,IAAI;IAC1B,IAAIA,SAAS,EAAE;MACb,IAAI,CAACC,qBAAqB,GAAG3D,qDAAqB,CAAC0D,SAAS,CAAC;IAC/D;IACAV,eAAe,GAAG,IAAI,CAACrC,EAAE;IACzB,IAAI,IAAI,CAACyG,GAAG,KAAKpG,SAAS,EAAE;MAC1BqG,YAAY,CAAC,IAAI,CAACD,GAAG,CAAC;MACtB,IAAI,CAACA,GAAG,GAAGpG,SAAS;IACtB;IACA,IAAI,IAAI,CAACoC,UAAU,KAAK,CAAC,EAAE;MACzB,IAAI,CAACK,SAAS,GAAG,IAAI;MACrB,IAAI,CAACG,KAAK,GAAG,CAAC,CAAC;IACjB;;IACA,IAAI,CAACP,iBAAiB,GAAG,IAAI,CAACD,UAAU;IACxC,IAAI,IAAI,CAACD,IAAI,EAAE;MACb,IAAI,CAACA,IAAI,CAACnB,KAAK,CAACsF,UAAU,GAAG,MAAM;IACrC;EACF;EACA3C,MAAMA,CAACb,OAAO,EAAE;IACd,IAAI,IAAI,CAACL,SAAS,EAAE;MAClB,IAAI,CAACuC,kBAAkB,CAAC,CAAC;IAC3B;IACA,IAAI5C,UAAU,GAAG,IAAI,CAACC,iBAAiB,GAAGS,OAAO,CAACyD,MAAM;IACxD,QAAQ,IAAI,CAAC/D,KAAK;MAChB,KAAK,CAAC,CAAC;QACLJ,UAAU,GAAGoE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAErE,UAAU,CAAC;QACpC;MACF,KAAK,CAAC,CAAC;QACLA,UAAU,GAAGoE,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEtE,UAAU,CAAC;QACpC;MACF,KAAK,CAAC,CAAC;QACL;MACF,KAAK,CAAC,CAAC;QACL;MACF;QACEuE,OAAO,CAACC,IAAI,CAAC,6BAA6B,EAAE,IAAI,CAACpE,KAAK,CAAC;QACvD;IACJ;IACA,IAAIqE,SAAS;IACb,IAAIzE,UAAU,GAAG,IAAI,CAACE,kBAAkB,EAAE;MACxCuE,SAAS,GAAG,IAAI,CAACvE,kBAAkB;MACnCF,UAAU,GAAGyE,SAAS,GAAG,CAACzE,UAAU,GAAGyE,SAAS,IAAI9E,cAAc;IACpE,CAAC,MACI,IAAIK,UAAU,GAAG,CAAC,IAAI,CAACG,iBAAiB,EAAE;MAC7CsE,SAAS,GAAG,CAAC,IAAI,CAACtE,iBAAiB;MACnCH,UAAU,GAAGyE,SAAS,GAAG,CAACzE,UAAU,GAAGyE,SAAS,IAAI9E,cAAc;IACpE;IACA,IAAI,CAACmD,aAAa,CAAC9C,UAAU,EAAE,KAAK,CAAC;EACvC;EACAwB,KAAKA,CAACd,OAAO,EAAE;IACb,MAAM;MAAEJ,SAAS;MAAEC;IAAsB,CAAC,GAAG,IAAI;IACjD,IAAID,SAAS,EAAE;MACbzD,qDAAmB,CAACyD,SAAS,EAAEC,qBAAqB,CAAC;IACvD;IACA,MAAMmE,QAAQ,GAAGhE,OAAO,CAACiE,SAAS;IAClC,IAAIC,YAAY,GAAG,IAAI,CAAC5E,UAAU,GAAG,CAAC,GAAG,IAAI,CAACE,kBAAkB,GAAG,CAAC,IAAI,CAACC,iBAAiB;IAC1F;IACA;IACA,MAAM0E,gBAAgB,GAAG,IAAI,CAAC7E,UAAU,GAAG,CAAC,KAAK,EAAE0E,QAAQ,GAAG,CAAC,CAAC;IAChE,MAAMI,YAAY,GAAGV,IAAI,CAACW,GAAG,CAACL,QAAQ,CAAC,GAAG,GAAG;IAC7C,MAAMM,aAAa,GAAGZ,IAAI,CAACW,GAAG,CAAC,IAAI,CAAC/E,UAAU,CAAC,GAAGoE,IAAI,CAACW,GAAG,CAACH,YAAY,GAAG,CAAC,CAAC;IAC5E,IAAIK,gBAAgB,CAACJ,gBAAgB,EAAEC,YAAY,EAAEE,aAAa,CAAC,EAAE;MACnEJ,YAAY,GAAG,CAAC;IAClB;IACA,MAAMpE,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAI,CAACsC,aAAa,CAAC8B,YAAY,EAAE,IAAI,CAAC;IACtC,IAAI,CAACpE,KAAK,GAAG,EAAE,CAAC,iCAAiC,CAAC,IAAI,IAAI,CAACoB,YAAY,EAAE;MACvE,IAAI,CAACA,YAAY,CAACxC,cAAc,CAAC,CAAC;IACpC,CAAC,MACI,IAAI,CAACoB,KAAK,GAAG,EAAE,CAAC,mCAAmC,CAAC,IAAI,IAAI,CAACmB,WAAW,EAAE;MAC7E,IAAI,CAACA,WAAW,CAACvC,cAAc,CAAC,CAAC;IACnC;EACF;EACAwD,kBAAkBA,CAAA,EAAG;IACnB,IAAI,CAAC1C,kBAAkB,GAAG,CAAC;IAC3B,IAAI,IAAI,CAAC0B,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAAChD,KAAK,CAACsG,OAAO,GAAG,MAAM;MACxC,IAAI,CAAChF,kBAAkB,GAAG,IAAI,CAAC0B,YAAY,CAACuD,WAAW;MACvD,IAAI,CAACvD,YAAY,CAAChD,KAAK,CAACsG,OAAO,GAAG,EAAE;IACtC;IACA,IAAI,CAAC/E,iBAAiB,GAAG,CAAC;IAC1B,IAAI,IAAI,CAACwB,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAAC/C,KAAK,CAACsG,OAAO,GAAG,MAAM;MACvC,IAAI,CAAC/E,iBAAiB,GAAG,IAAI,CAACwB,WAAW,CAACwD,WAAW;MACrD,IAAI,CAACxD,WAAW,CAAC/C,KAAK,CAACsG,OAAO,GAAG,EAAE;IACrC;IACA,IAAI,CAAC7E,SAAS,GAAG,KAAK;EACxB;EACAyC,aAAaA,CAAC9C,UAAU,EAAEoF,OAAO,EAAE;IACjC,IAAI,IAAI,CAACpB,GAAG,KAAKpG,SAAS,EAAE;MAC1BqG,YAAY,CAAC,IAAI,CAACD,GAAG,CAAC;MACtB,IAAI,CAACA,GAAG,GAAGpG,SAAS;IACtB;IACA,IAAI,CAAC,IAAI,CAACmC,IAAI,EAAE;MACd;IACF;IACA,MAAM;MAAExC;IAAG,CAAC,GAAG,IAAI;IACnB,MAAMqB,KAAK,GAAG,IAAI,CAACmB,IAAI,CAACnB,KAAK;IAC7B,IAAI,CAACoB,UAAU,GAAGA,UAAU;IAC5B,IAAIoF,OAAO,EAAE;MACXxG,KAAK,CAACsF,UAAU,GAAG,EAAE;IACvB;IACA,IAAIlE,UAAU,GAAG,CAAC,EAAE;MAClB,IAAI,CAACQ,KAAK,GACRR,UAAU,IAAI,IAAI,CAACE,kBAAkB,GAAGR,YAAY,GAChD,CAAC,CAAC,yBAAyB,EAAE,CAAC,8BAC9B,CAAC,CAAC;IACV,CAAC,MACI,IAAIM,UAAU,GAAG,CAAC,EAAE;MACvB,IAAI,CAACQ,KAAK,GACRR,UAAU,IAAI,CAAC,IAAI,CAACG,iBAAiB,GAAGT,YAAY,GAChD,EAAE,CAAC,2BAA2B,EAAE,CAAC,gCACjC,EAAE,CAAC;IACX,CAAC,MACI;MACH;AACN;AACA;AACA;MACMnC,EAAE,CAAC8H,SAAS,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACxC;AACN;AACA;AACA;AACA;AACA;AACA;MACM,IAAI,IAAI,CAAC5E,OAAO,EAAE;QAChB,IAAI,CAACA,OAAO,CAACC,MAAM,CAAC,KAAK,CAAC;MAC5B;MACA,IAAI,CAACqD,GAAG,GAAGuB,UAAU,CAAC,MAAM;QAC1B,IAAI,CAAC/E,KAAK,GAAG,CAAC,CAAC;QACf,IAAI,CAACwD,GAAG,GAAGpG,SAAS;QACpB,IAAI,IAAI,CAAC8C,OAAO,EAAE;UAChB,IAAI,CAACA,OAAO,CAACC,MAAM,CAAC,CAAC,IAAI,CAAC9C,QAAQ,CAAC;QACrC;QACAN,EAAE,CAAC8H,SAAS,CAACG,MAAM,CAAC,sBAAsB,CAAC;MAC7C,CAAC,EAAE,GAAG,CAAC;MACP5F,eAAe,GAAGhC,SAAS;MAC3BgB,KAAK,CAAC6G,SAAS,GAAG,EAAE;MACpB;IACF;IACA7G,KAAK,CAAC6G,SAAS,GAAI,eAAc,CAACzF,UAAW,SAAQ;IACrD,IAAI,CAACF,OAAO,CAACP,IAAI,CAAC;MAChBmG,MAAM,EAAE1F,UAAU;MAClB2F,KAAK,EAAE,IAAI,CAACzD,mBAAmB,CAAC;IAClC,CAAC,CAAC;EACJ;EACAA,mBAAmBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAAClC,UAAU,GAAG,CAAC,EAAE;MACvB,OAAO,IAAI,CAACA,UAAU,GAAG,IAAI,CAACE,kBAAkB;IAClD,CAAC,MACI,IAAI,IAAI,CAACF,UAAU,GAAG,CAAC,EAAE;MAC5B,OAAO,IAAI,CAACA,UAAU,GAAG,IAAI,CAACG,iBAAiB;IACjD,CAAC,MACI;MACH,OAAO,CAAC;IACV;EACF;EACAhC,MAAMA,CAAA,EAAG;IACP,MAAME,IAAI,GAAG7B,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQV,qDAAC,CAACE,iDAAI,EAAE;MAAEuC,KAAK,EAAE;QACrB,CAACF,IAAI,GAAG,IAAI;QACZ,2BAA2B,EAAE,IAAI,CAACmC,KAAK,KAAK,CAAC,CAAC;QAC9C,iCAAiC,EAAE,CAAC,IAAI,CAACA,KAAK,GAAG,CAAC,CAAC,4BAA4B,CAAC;QAChF,mCAAmC,EAAE,CAAC,IAAI,CAACA,KAAK,GAAG,EAAE,CAAC,8BAA8B,CAAC;QACrF,+BAA+B,EAAE,CAAC,IAAI,CAACA,KAAK,GAAG,EAAE,CAAC,iCAAiC,CAAC;QACpF,iCAAiC,EAAE,CAAC,IAAI,CAACA,KAAK,GAAG,EAAE,CAAC,mCAAmC;MACzF;IAAE,CAAC,CAAC;EACR;EACA,IAAIjD,EAAEA,CAAA,EAAG;IAAE,OAAOrB,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW0J,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC7B,UAAU,EAAE,CAAC,iBAAiB;IAChC,CAAC;EAAE;AACL,CAAC;AACD,MAAMX,gBAAgB,GAAGA,CAACJ,gBAAgB,EAAEC,YAAY,EAAEe,aAAa,KAAK;EAC1E;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,OAAQ,CAACf,YAAY,IAAIe,aAAa,IAAMhB,gBAAgB,IAAIC,YAAa;AAC/E,CAAC;AACDjF,WAAW,CAACjB,KAAK,GAAGa,cAAc;;;;;;;;;;;;;;;;;;;;ACngBlC;AACA;AACA;AACA,MAAMwG,WAAW,GAAGA,CAACC,QAAQ,EAAE3I,EAAE,KAAK;EACpC,OAAOA,EAAE,CAACE,OAAO,CAACyI,QAAQ,CAAC,KAAK,IAAI;AACtC,CAAC;AACD;AACA;AACA;AACA,MAAM5J,kBAAkB,GAAGA,CAACqB,KAAK,EAAEwI,WAAW,KAAK;EACjD,OAAO,OAAOxI,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAAC0F,MAAM,GAAG,CAAC,GAChD7E,MAAM,CAACC,MAAM,CAAC;IAAE,WAAW,EAAE,IAAI;IAAE,CAAE,aAAYd,KAAM,EAAC,GAAG;EAAK,CAAC,EAAEwI,WAAW,CAAC,GAAGA,WAAW;AACnG,CAAC;AACD,MAAMC,YAAY,GAAIC,OAAO,IAAK;EAChC,IAAIA,OAAO,KAAKzI,SAAS,EAAE;IACzB,MAAM0I,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACH,OAAO,CAAC,GAAGA,OAAO,GAAGA,OAAO,CAACI,KAAK,CAAC,GAAG,CAAC;IACnE,OAAOH,KAAK,CACTI,MAAM,CAAErK,CAAC,IAAKA,CAAC,IAAI,IAAI,CAAC,CACxBsK,GAAG,CAAEtK,CAAC,IAAKA,CAAC,CAACuK,IAAI,CAAC,CAAC,CAAC,CACpBF,MAAM,CAAErK,CAAC,IAAKA,CAAC,KAAK,EAAE,CAAC;EAC5B;EACA,OAAO,EAAE;AACX,CAAC;AACD,MAAMwK,WAAW,GAAIR,OAAO,IAAK;EAC/B,MAAMM,GAAG,GAAG,CAAC,CAAC;EACdP,YAAY,CAACC,OAAO,CAAC,CAACS,OAAO,CAAEzK,CAAC,IAAMsK,GAAG,CAACtK,CAAC,CAAC,GAAG,IAAK,CAAC;EACrD,OAAOsK,GAAG;AACZ,CAAC;AACD,MAAMI,MAAM,GAAG,sBAAsB;AACrC,MAAMC,OAAO;EAAA,IAAAC,IAAA,GAAA3H,8KAAA,CAAG,WAAO4H,GAAG,EAAE5J,EAAE,EAAE6J,SAAS,EAAEC,SAAS,EAAK;IACvD,IAAIF,GAAG,IAAI,IAAI,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAACH,MAAM,CAACM,IAAI,CAACH,GAAG,CAAC,EAAE;MACtD,MAAMI,MAAM,GAAG7D,QAAQ,CAAC3C,aAAa,CAAC,YAAY,CAAC;MACnD,IAAIwG,MAAM,EAAE;QACV,IAAIhK,EAAE,IAAI,IAAI,EAAE;UACdA,EAAE,CAACI,cAAc,CAAC,CAAC;QACrB;QACA,OAAO4J,MAAM,CAACC,IAAI,CAACL,GAAG,EAAEC,SAAS,EAAEC,SAAS,CAAC;MAC/C;IACF;IACA,OAAO,KAAK;EACd,CAAC;EAAA,gBAXKJ,OAAOA,CAAAQ,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAV,IAAA,CAAAW,KAAA,OAAAC,SAAA;EAAA;AAAA,GAWZ", "sources": ["./node_modules/@ionic/core/dist/esm/ion-item-option_3.entry.js", "./node_modules/@ionic/core/dist/esm/theme-17531cdf.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, H as Host, f as getElement, d as createEvent } from './index-2d388930.js';\nimport { c as createColorClasses } from './theme-17531cdf.js';\nimport { b as getIonMode } from './ionic-global-b3fc28dd.js';\nimport { p as isEndSide } from './helpers-3379ba19.js';\nimport { f as findClosestIonContent, d as disableContentScrollY, r as resetContentScrollY } from './index-746a238e.js';\nimport { w as watchForOptions } from './watch-options-355a920a.js';\nimport './index-595d62c9.js';\n\nconst itemOptionIosCss = \":host{--background:var(--ion-color-primary, #3880ff);--color:var(--ion-color-primary-contrast, #fff);background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit)}:host(.in-list.item-options-end:last-child){-webkit-padding-end:calc(0.7em + var(--ion-safe-area-right));padding-inline-end:calc(0.7em + var(--ion-safe-area-right))}:host(.in-list.item-options-start:first-child){-webkit-padding-start:calc(0.7em + var(--ion-safe-area-left));padding-inline-start:calc(0.7em + var(--ion-safe-area-left))}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}.button-native{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;-webkit-padding-start:0.7em;padding-inline-start:0.7em;-webkit-padding-end:0.7em;padding-inline-end:0.7em;padding-top:0;padding-bottom:0;display:inline-block;position:relative;width:100%;height:100%;border:0;outline:none;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;-webkit-box-sizing:border-box;box-sizing:border-box}.button-inner{display:-ms-flexbox;display:flex;-ms-flex-flow:column nowrap;flex-flow:column nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%}.horizontal-wrapper{display:-ms-flexbox;display:flex;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%}::slotted(*){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:5px;margin-inline-end:5px;margin-top:0;margin-bottom:0}::slotted([slot=end]){-webkit-margin-start:5px;margin-inline-start:5px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}::slotted([slot=icon-only]){padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;-webkit-margin-start:10px;margin-inline-start:10px;-webkit-margin-end:10px;margin-inline-end:10px;margin-top:0;margin-bottom:0;min-width:0.9em;font-size:1.8em}:host(.item-option-expandable){-ms-flex-negative:0;flex-shrink:0;-webkit-transition-duration:0;transition-duration:0;-webkit-transition-property:none;transition-property:none;-webkit-transition-timing-function:cubic-bezier(0.65, 0.05, 0.36, 1);transition-timing-function:cubic-bezier(0.65, 0.05, 0.36, 1)}:host(.item-option-disabled){pointer-events:none}:host(.item-option-disabled) .button-native{cursor:default;opacity:0.5;pointer-events:none}:host{font-size:16px}:host(.ion-activated){background:var(--ion-color-primary-shade, #3171e0)}:host(.ion-color.ion-activated){background:var(--ion-color-shade)}\";\n\nconst itemOptionMdCss = \":host{--background:var(--ion-color-primary, #3880ff);--color:var(--ion-color-primary-contrast, #fff);background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit)}:host(.in-list.item-options-end:last-child){-webkit-padding-end:calc(0.7em + var(--ion-safe-area-right));padding-inline-end:calc(0.7em + var(--ion-safe-area-right))}:host(.in-list.item-options-start:first-child){-webkit-padding-start:calc(0.7em + var(--ion-safe-area-left));padding-inline-start:calc(0.7em + var(--ion-safe-area-left))}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}.button-native{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;-webkit-padding-start:0.7em;padding-inline-start:0.7em;-webkit-padding-end:0.7em;padding-inline-end:0.7em;padding-top:0;padding-bottom:0;display:inline-block;position:relative;width:100%;height:100%;border:0;outline:none;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;-webkit-box-sizing:border-box;box-sizing:border-box}.button-inner{display:-ms-flexbox;display:flex;-ms-flex-flow:column nowrap;flex-flow:column nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%}.horizontal-wrapper{display:-ms-flexbox;display:flex;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%}::slotted(*){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:5px;margin-inline-end:5px;margin-top:0;margin-bottom:0}::slotted([slot=end]){-webkit-margin-start:5px;margin-inline-start:5px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}::slotted([slot=icon-only]){padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;-webkit-margin-start:10px;margin-inline-start:10px;-webkit-margin-end:10px;margin-inline-end:10px;margin-top:0;margin-bottom:0;min-width:0.9em;font-size:1.8em}:host(.item-option-expandable){-ms-flex-negative:0;flex-shrink:0;-webkit-transition-duration:0;transition-duration:0;-webkit-transition-property:none;transition-property:none;-webkit-transition-timing-function:cubic-bezier(0.65, 0.05, 0.36, 1);transition-timing-function:cubic-bezier(0.65, 0.05, 0.36, 1)}:host(.item-option-disabled){pointer-events:none}:host(.item-option-disabled) .button-native{cursor:default;opacity:0.5;pointer-events:none}:host{font-size:14px;font-weight:500;text-transform:uppercase}\";\n\nconst ItemOption = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.onClick = (ev) => {\n      const el = ev.target.closest('ion-item-option');\n      if (el) {\n        ev.preventDefault();\n      }\n    };\n    this.color = undefined;\n    this.disabled = false;\n    this.download = undefined;\n    this.expandable = false;\n    this.href = undefined;\n    this.rel = undefined;\n    this.target = undefined;\n    this.type = 'button';\n  }\n  render() {\n    const { disabled, expandable, href } = this;\n    const TagType = href === undefined ? 'button' : 'a';\n    const mode = getIonMode(this);\n    const attrs = TagType === 'button'\n      ? { type: this.type }\n      : {\n        download: this.download,\n        href: this.href,\n        target: this.target,\n      };\n    return (h(Host, { onClick: this.onClick, class: createColorClasses(this.color, {\n        [mode]: true,\n        'item-option-disabled': disabled,\n        'item-option-expandable': expandable,\n        'ion-activatable': true,\n      }) }, h(TagType, Object.assign({}, attrs, { class: \"button-native\", part: \"native\", disabled: disabled }), h(\"span\", { class: \"button-inner\" }, h(\"slot\", { name: \"top\" }), h(\"div\", { class: \"horizontal-wrapper\" }, h(\"slot\", { name: \"start\" }), h(\"slot\", { name: \"icon-only\" }), h(\"slot\", null), h(\"slot\", { name: \"end\" })), h(\"slot\", { name: \"bottom\" })), mode === 'md' && h(\"ion-ripple-effect\", null))));\n  }\n  get el() { return getElement(this); }\n};\nItemOption.style = {\n  ios: itemOptionIosCss,\n  md: itemOptionMdCss\n};\n\nconst itemOptionsIosCss = \"ion-item-options{top:0;right:0;-ms-flex-pack:end;justify-content:flex-end;display:none;position:absolute;height:100%;font-size:14px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1}:host-context([dir=rtl]) ion-item-options{-ms-flex-pack:start;justify-content:flex-start}:host-context([dir=rtl]) ion-item-options:not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}[dir=rtl] ion-item-options{-ms-flex-pack:start;justify-content:flex-start}[dir=rtl] ion-item-options:not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}@supports selector(:dir(rtl)){ion-item-options:dir(rtl){-ms-flex-pack:start;justify-content:flex-start}ion-item-options:dir(rtl):not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}}.item-options-start{right:auto;left:0;-ms-flex-pack:start;justify-content:flex-start}:host-context([dir=rtl]) .item-options-start{-ms-flex-pack:end;justify-content:flex-end}[dir=rtl] .item-options-start{-ms-flex-pack:end;justify-content:flex-end}@supports selector(:dir(rtl)){.item-options-start:dir(rtl){-ms-flex-pack:end;justify-content:flex-end}}[dir=ltr] .item-options-start ion-item-option:first-child,[dir=rtl] .item-options-start ion-item-option:last-child{padding-left:var(--ion-safe-area-left)}[dir=ltr] .item-options-end ion-item-option:last-child,[dir=rtl] .item-options-end ion-item-option:first-child{padding-right:var(--ion-safe-area-right)}:host-context([dir=rtl]) .item-sliding-active-slide.item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}[dir=rtl] .item-sliding-active-slide.item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}@supports selector(:dir(rtl)){.item-sliding-active-slide:dir(rtl).item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}}.item-sliding-active-slide ion-item-options{display:-ms-flexbox;display:flex;visibility:hidden}.item-sliding-active-slide.item-sliding-active-options-start .item-options-start,.item-sliding-active-slide.item-sliding-active-options-end ion-item-options:not(.item-options-start){width:100%;visibility:visible}.item-options-ios{border-bottom-width:0;border-bottom-style:solid;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)))}.item-options-ios.item-options-end{border-bottom-width:0.55px}.list-ios-lines-none .item-options-ios{border-bottom-width:0}.list-ios-lines-full .item-options-ios,.list-ios-lines-inset .item-options-ios.item-options-end{border-bottom-width:0.55px}\";\n\nconst itemOptionsMdCss = \"ion-item-options{top:0;right:0;-ms-flex-pack:end;justify-content:flex-end;display:none;position:absolute;height:100%;font-size:14px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1}:host-context([dir=rtl]) ion-item-options{-ms-flex-pack:start;justify-content:flex-start}:host-context([dir=rtl]) ion-item-options:not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}[dir=rtl] ion-item-options{-ms-flex-pack:start;justify-content:flex-start}[dir=rtl] ion-item-options:not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}@supports selector(:dir(rtl)){ion-item-options:dir(rtl){-ms-flex-pack:start;justify-content:flex-start}ion-item-options:dir(rtl):not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}}.item-options-start{right:auto;left:0;-ms-flex-pack:start;justify-content:flex-start}:host-context([dir=rtl]) .item-options-start{-ms-flex-pack:end;justify-content:flex-end}[dir=rtl] .item-options-start{-ms-flex-pack:end;justify-content:flex-end}@supports selector(:dir(rtl)){.item-options-start:dir(rtl){-ms-flex-pack:end;justify-content:flex-end}}[dir=ltr] .item-options-start ion-item-option:first-child,[dir=rtl] .item-options-start ion-item-option:last-child{padding-left:var(--ion-safe-area-left)}[dir=ltr] .item-options-end ion-item-option:last-child,[dir=rtl] .item-options-end ion-item-option:first-child{padding-right:var(--ion-safe-area-right)}:host-context([dir=rtl]) .item-sliding-active-slide.item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}[dir=rtl] .item-sliding-active-slide.item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}@supports selector(:dir(rtl)){.item-sliding-active-slide:dir(rtl).item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}}.item-sliding-active-slide ion-item-options{display:-ms-flexbox;display:flex;visibility:hidden}.item-sliding-active-slide.item-sliding-active-options-start .item-options-start,.item-sliding-active-slide.item-sliding-active-options-end ion-item-options:not(.item-options-start){width:100%;visibility:visible}.item-options-md{border-bottom-width:0;border-bottom-style:solid;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))))}.list-md-lines-none .item-options-md{border-bottom-width:0}.list-md-lines-full .item-options-md,.list-md-lines-inset .item-options-md.item-options-end{border-bottom-width:1px}\";\n\nconst ItemOptions = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionSwipe = createEvent(this, \"ionSwipe\", 7);\n    this.side = 'end';\n  }\n  /** @internal */\n  async fireSwipeEvent() {\n    this.ionSwipe.emit({\n      side: this.side,\n    });\n  }\n  render() {\n    const mode = getIonMode(this);\n    const isEnd = isEndSide(this.side);\n    return (h(Host, { class: {\n        [mode]: true,\n        // Used internally for styling\n        [`item-options-${mode}`]: true,\n        /**\n         * Note: The \"start\" and \"end\" terms refer to the\n         * direction ion-item-option instances within ion-item-options flow.\n         * They do not refer to how ion-item-options flows within ion-item-sliding.\n         * As a result, \"item-options-start\" means the ion-item-options container\n         * always appears on the left, and \"item-options-end\" means the ion-item-options\n         * container always appears on the right.\n         */\n        'item-options-start': !isEnd,\n        'item-options-end': isEnd,\n      } }));\n  }\n  get el() { return getElement(this); }\n};\nItemOptions.style = {\n  ios: itemOptionsIosCss,\n  md: itemOptionsMdCss\n};\n\nconst itemSlidingCss = \"ion-item-sliding{display:block;position:relative;width:100%;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}ion-item-sliding .item{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.item-sliding-active-slide .item{position:relative;-webkit-transition:-webkit-transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1);transition:-webkit-transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1);transition:transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1);transition:transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1), -webkit-transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1);opacity:1;z-index:2;pointer-events:none;will-change:transform}.item-sliding-closing ion-item-options{pointer-events:none}.item-sliding-active-swipe-end .item-options-end .item-option-expandable{padding-left:100%;-ms-flex-order:1;order:1;-webkit-transition-duration:0.6s;transition-duration:0.6s;-webkit-transition-property:padding-left;transition-property:padding-left}:host-context([dir=rtl]) .item-sliding-active-swipe-end .item-options-end .item-option-expandable{-ms-flex-order:-1;order:-1}[dir=rtl] .item-sliding-active-swipe-end .item-options-end .item-option-expandable{-ms-flex-order:-1;order:-1}@supports selector(:dir(rtl)){.item-sliding-active-swipe-end .item-options-end .item-option-expandable:dir(rtl){-ms-flex-order:-1;order:-1}}.item-sliding-active-swipe-start .item-options-start .item-option-expandable{padding-right:100%;-ms-flex-order:-1;order:-1;-webkit-transition-duration:0.6s;transition-duration:0.6s;-webkit-transition-property:padding-right;transition-property:padding-right}:host-context([dir=rtl]) .item-sliding-active-swipe-start .item-options-start .item-option-expandable{-ms-flex-order:1;order:1}[dir=rtl] .item-sliding-active-swipe-start .item-options-start .item-option-expandable{-ms-flex-order:1;order:1}@supports selector(:dir(rtl)){.item-sliding-active-swipe-start .item-options-start .item-option-expandable:dir(rtl){-ms-flex-order:1;order:1}}\";\n\nconst SWIPE_MARGIN = 30;\nconst ELASTIC_FACTOR = 0.55;\nlet openSlidingItem;\nconst ItemSliding = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionDrag = createEvent(this, \"ionDrag\", 7);\n    this.item = null;\n    this.openAmount = 0;\n    this.initialOpenAmount = 0;\n    this.optsWidthRightSide = 0;\n    this.optsWidthLeftSide = 0;\n    this.sides = 0 /* ItemSide.None */;\n    this.optsDirty = true;\n    this.contentEl = null;\n    this.initialContentScrollY = true;\n    this.state = 2 /* SlidingState.Disabled */;\n    this.disabled = false;\n  }\n  disabledChanged() {\n    if (this.gesture) {\n      this.gesture.enable(!this.disabled);\n    }\n  }\n  async connectedCallback() {\n    const { el } = this;\n    this.item = el.querySelector('ion-item');\n    this.contentEl = findClosestIonContent(el);\n    /**\n     * The MutationObserver needs to be added before we\n     * call updateOptions below otherwise we may miss\n     * ion-item-option elements that are added to the DOM\n     * while updateOptions is running and before the MutationObserver\n     * has been initialized.\n     */\n    this.mutationObserver = watchForOptions(el, 'ion-item-option', async () => {\n      await this.updateOptions();\n    });\n    await this.updateOptions();\n    this.gesture = (await import('./index-ff313b19.js')).createGesture({\n      el,\n      gestureName: 'item-swipe',\n      gesturePriority: 100,\n      threshold: 5,\n      canStart: (ev) => this.canStart(ev),\n      onStart: () => this.onStart(),\n      onMove: (ev) => this.onMove(ev),\n      onEnd: (ev) => this.onEnd(ev),\n    });\n    this.disabledChanged();\n  }\n  disconnectedCallback() {\n    if (this.gesture) {\n      this.gesture.destroy();\n      this.gesture = undefined;\n    }\n    this.item = null;\n    this.leftOptions = this.rightOptions = undefined;\n    if (openSlidingItem === this.el) {\n      openSlidingItem = undefined;\n    }\n    if (this.mutationObserver) {\n      this.mutationObserver.disconnect();\n      this.mutationObserver = undefined;\n    }\n  }\n  /**\n   * Get the amount the item is open in pixels.\n   */\n  getOpenAmount() {\n    return Promise.resolve(this.openAmount);\n  }\n  /**\n   * Get the ratio of the open amount of the item compared to the width of the options.\n   * If the number returned is positive, then the options on the right side are open.\n   * If the number returned is negative, then the options on the left side are open.\n   * If the absolute value of the number is greater than 1, the item is open more than\n   * the width of the options.\n   */\n  getSlidingRatio() {\n    return Promise.resolve(this.getSlidingRatioSync());\n  }\n  /**\n   * Open the sliding item.\n   *\n   * @param side The side of the options to open. If a side is not provided, it will open the first set of options it finds within the item.\n   */\n  async open(side) {\n    var _a;\n    /**\n     * It is possible for the item to be added to the DOM\n     * after the item-sliding component was created. As a result,\n     * if this.item is null, then we should attempt to\n     * query for the ion-item again.\n     * However, if the item is already defined then\n     * we do not query for it again.\n     */\n    const item = (this.item = (_a = this.item) !== null && _a !== void 0 ? _a : this.el.querySelector('ion-item'));\n    if (item === null) {\n      return;\n    }\n    const optionsToOpen = this.getOptions(side);\n    if (!optionsToOpen) {\n      return;\n    }\n    /**\n     * If side is not set, we need to infer the side\n     * so we know which direction to move the options\n     */\n    if (side === undefined) {\n      side = optionsToOpen === this.leftOptions ? 'start' : 'end';\n    }\n    // In RTL we want to switch the sides\n    side = isEndSide(side) ? 'end' : 'start';\n    const isStartOpen = this.openAmount < 0;\n    const isEndOpen = this.openAmount > 0;\n    /**\n     * If a side is open and a user tries to\n     * re-open the same side, we should not do anything\n     */\n    if (isStartOpen && optionsToOpen === this.leftOptions) {\n      return;\n    }\n    if (isEndOpen && optionsToOpen === this.rightOptions) {\n      return;\n    }\n    this.closeOpened();\n    this.state = 4 /* SlidingState.Enabled */;\n    requestAnimationFrame(() => {\n      this.calculateOptsWidth();\n      const width = side === 'end' ? this.optsWidthRightSide : -this.optsWidthLeftSide;\n      openSlidingItem = this.el;\n      this.setOpenAmount(width, false);\n      this.state = side === 'end' ? 8 /* SlidingState.End */ : 16 /* SlidingState.Start */;\n    });\n  }\n  /**\n   * Close the sliding item. Items can also be closed from the [List](./list).\n   */\n  async close() {\n    this.setOpenAmount(0, true);\n  }\n  /**\n   * Close all of the sliding items in the list. Items can also be closed from the [List](./list).\n   */\n  async closeOpened() {\n    if (openSlidingItem !== undefined) {\n      openSlidingItem.close();\n      openSlidingItem = undefined;\n      return true;\n    }\n    return false;\n  }\n  /**\n   * Given an optional side, return the ion-item-options element.\n   *\n   * @param side This side of the options to get. If a side is not provided it will\n   * return the first one available.\n   */\n  getOptions(side) {\n    if (side === undefined) {\n      return this.leftOptions || this.rightOptions;\n    }\n    else if (side === 'start') {\n      return this.leftOptions;\n    }\n    else {\n      return this.rightOptions;\n    }\n  }\n  async updateOptions() {\n    const options = this.el.querySelectorAll('ion-item-options');\n    let sides = 0;\n    // Reset left and right options in case they were removed\n    this.leftOptions = this.rightOptions = undefined;\n    for (let i = 0; i < options.length; i++) {\n      const item = options.item(i);\n      /**\n       * We cannot use the componentOnReady helper\n       * util here since we need to wait for all of these items\n       * to be ready before we set `this.sides` and `this.optsDirty`.\n       */\n      // eslint-disable-next-line custom-rules/no-component-on-ready-method\n      const option = item.componentOnReady !== undefined ? await item.componentOnReady() : item;\n      const side = isEndSide(option.side) ? 'end' : 'start';\n      if (side === 'start') {\n        this.leftOptions = option;\n        sides |= 1 /* ItemSide.Start */;\n      }\n      else {\n        this.rightOptions = option;\n        sides |= 2 /* ItemSide.End */;\n      }\n    }\n    this.optsDirty = true;\n    this.sides = sides;\n  }\n  canStart(gesture) {\n    /**\n     * If very close to start of the screen\n     * do not open left side so swipe to go\n     * back will still work.\n     */\n    const rtl = document.dir === 'rtl';\n    const atEdge = rtl ? window.innerWidth - gesture.startX < 15 : gesture.startX < 15;\n    if (atEdge) {\n      return false;\n    }\n    const selected = openSlidingItem;\n    if (selected && selected !== this.el) {\n      this.closeOpened();\n    }\n    return !!(this.rightOptions || this.leftOptions);\n  }\n  onStart() {\n    /**\n     * We need to query for the ion-item\n     * every time the gesture starts. Developers\n     * may toggle ion-item elements via *ngIf.\n     */\n    this.item = this.el.querySelector('ion-item');\n    const { contentEl } = this;\n    if (contentEl) {\n      this.initialContentScrollY = disableContentScrollY(contentEl);\n    }\n    openSlidingItem = this.el;\n    if (this.tmr !== undefined) {\n      clearTimeout(this.tmr);\n      this.tmr = undefined;\n    }\n    if (this.openAmount === 0) {\n      this.optsDirty = true;\n      this.state = 4 /* SlidingState.Enabled */;\n    }\n    this.initialOpenAmount = this.openAmount;\n    if (this.item) {\n      this.item.style.transition = 'none';\n    }\n  }\n  onMove(gesture) {\n    if (this.optsDirty) {\n      this.calculateOptsWidth();\n    }\n    let openAmount = this.initialOpenAmount - gesture.deltaX;\n    switch (this.sides) {\n      case 2 /* ItemSide.End */:\n        openAmount = Math.max(0, openAmount);\n        break;\n      case 1 /* ItemSide.Start */:\n        openAmount = Math.min(0, openAmount);\n        break;\n      case 3 /* ItemSide.Both */:\n        break;\n      case 0 /* ItemSide.None */:\n        return;\n      default:\n        console.warn('invalid ItemSideFlags value', this.sides);\n        break;\n    }\n    let optsWidth;\n    if (openAmount > this.optsWidthRightSide) {\n      optsWidth = this.optsWidthRightSide;\n      openAmount = optsWidth + (openAmount - optsWidth) * ELASTIC_FACTOR;\n    }\n    else if (openAmount < -this.optsWidthLeftSide) {\n      optsWidth = -this.optsWidthLeftSide;\n      openAmount = optsWidth + (openAmount - optsWidth) * ELASTIC_FACTOR;\n    }\n    this.setOpenAmount(openAmount, false);\n  }\n  onEnd(gesture) {\n    const { contentEl, initialContentScrollY } = this;\n    if (contentEl) {\n      resetContentScrollY(contentEl, initialContentScrollY);\n    }\n    const velocity = gesture.velocityX;\n    let restingPoint = this.openAmount > 0 ? this.optsWidthRightSide : -this.optsWidthLeftSide;\n    // Check if the drag didn't clear the buttons mid-point\n    // and we aren't moving fast enough to swipe open\n    const isResetDirection = this.openAmount > 0 === !(velocity < 0);\n    const isMovingFast = Math.abs(velocity) > 0.3;\n    const isOnCloseZone = Math.abs(this.openAmount) < Math.abs(restingPoint / 2);\n    if (swipeShouldReset(isResetDirection, isMovingFast, isOnCloseZone)) {\n      restingPoint = 0;\n    }\n    const state = this.state;\n    this.setOpenAmount(restingPoint, true);\n    if ((state & 32 /* SlidingState.SwipeEnd */) !== 0 && this.rightOptions) {\n      this.rightOptions.fireSwipeEvent();\n    }\n    else if ((state & 64 /* SlidingState.SwipeStart */) !== 0 && this.leftOptions) {\n      this.leftOptions.fireSwipeEvent();\n    }\n  }\n  calculateOptsWidth() {\n    this.optsWidthRightSide = 0;\n    if (this.rightOptions) {\n      this.rightOptions.style.display = 'flex';\n      this.optsWidthRightSide = this.rightOptions.offsetWidth;\n      this.rightOptions.style.display = '';\n    }\n    this.optsWidthLeftSide = 0;\n    if (this.leftOptions) {\n      this.leftOptions.style.display = 'flex';\n      this.optsWidthLeftSide = this.leftOptions.offsetWidth;\n      this.leftOptions.style.display = '';\n    }\n    this.optsDirty = false;\n  }\n  setOpenAmount(openAmount, isFinal) {\n    if (this.tmr !== undefined) {\n      clearTimeout(this.tmr);\n      this.tmr = undefined;\n    }\n    if (!this.item) {\n      return;\n    }\n    const { el } = this;\n    const style = this.item.style;\n    this.openAmount = openAmount;\n    if (isFinal) {\n      style.transition = '';\n    }\n    if (openAmount > 0) {\n      this.state =\n        openAmount >= this.optsWidthRightSide + SWIPE_MARGIN\n          ? 8 /* SlidingState.End */ | 32 /* SlidingState.SwipeEnd */\n          : 8 /* SlidingState.End */;\n    }\n    else if (openAmount < 0) {\n      this.state =\n        openAmount <= -this.optsWidthLeftSide - SWIPE_MARGIN\n          ? 16 /* SlidingState.Start */ | 64 /* SlidingState.SwipeStart */\n          : 16 /* SlidingState.Start */;\n    }\n    else {\n      /**\n       * The sliding options should not be\n       * clickable while the item is closing.\n       */\n      el.classList.add('item-sliding-closing');\n      /**\n       * Item sliding cannot be interrupted\n       * while closing the item. If it did,\n       * it would allow the item to get into an\n       * inconsistent state where multiple\n       * items are then open at the same time.\n       */\n      if (this.gesture) {\n        this.gesture.enable(false);\n      }\n      this.tmr = setTimeout(() => {\n        this.state = 2 /* SlidingState.Disabled */;\n        this.tmr = undefined;\n        if (this.gesture) {\n          this.gesture.enable(!this.disabled);\n        }\n        el.classList.remove('item-sliding-closing');\n      }, 600);\n      openSlidingItem = undefined;\n      style.transform = '';\n      return;\n    }\n    style.transform = `translate3d(${-openAmount}px,0,0)`;\n    this.ionDrag.emit({\n      amount: openAmount,\n      ratio: this.getSlidingRatioSync(),\n    });\n  }\n  getSlidingRatioSync() {\n    if (this.openAmount > 0) {\n      return this.openAmount / this.optsWidthRightSide;\n    }\n    else if (this.openAmount < 0) {\n      return this.openAmount / this.optsWidthLeftSide;\n    }\n    else {\n      return 0;\n    }\n  }\n  render() {\n    const mode = getIonMode(this);\n    return (h(Host, { class: {\n        [mode]: true,\n        'item-sliding-active-slide': this.state !== 2 /* SlidingState.Disabled */,\n        'item-sliding-active-options-end': (this.state & 8 /* SlidingState.End */) !== 0,\n        'item-sliding-active-options-start': (this.state & 16 /* SlidingState.Start */) !== 0,\n        'item-sliding-active-swipe-end': (this.state & 32 /* SlidingState.SwipeEnd */) !== 0,\n        'item-sliding-active-swipe-start': (this.state & 64 /* SlidingState.SwipeStart */) !== 0,\n      } }));\n  }\n  get el() { return getElement(this); }\n  static get watchers() { return {\n    \"disabled\": [\"disabledChanged\"]\n  }; }\n};\nconst swipeShouldReset = (isResetDirection, isMovingFast, isOnResetZone) => {\n  // The logic required to know when the sliding item should close (openAmount=0)\n  // depends on three booleans (isResetDirection, isMovingFast, isOnResetZone)\n  // and it ended up being too complicated to be written manually without errors\n  // so the truth table is attached below: (0=false, 1=true)\n  // isResetDirection | isMovingFast | isOnResetZone || shouldClose\n  //         0        |       0      |       0       ||    0\n  //         0        |       0      |       1       ||    1\n  //         0        |       1      |       0       ||    0\n  //         0        |       1      |       1       ||    0\n  //         1        |       0      |       0       ||    0\n  //         1        |       0      |       1       ||    1\n  //         1        |       1      |       0       ||    1\n  //         1        |       1      |       1       ||    1\n  // The resulting expression was generated by resolving the K-map (Karnaugh map):\n  return (!isMovingFast && isOnResetZone) || (isResetDirection && isMovingFast);\n};\nItemSliding.style = itemSlidingCss;\n\nexport { ItemOption as ion_item_option, ItemOptions as ion_item_options, ItemSliding as ion_item_sliding };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst hostContext = (selector, el) => {\n  return el.closest(selector) !== null;\n};\n/**\n * Create the mode and color classes for the component based on the classes passed in\n */\nconst createColorClasses = (color, cssClassMap) => {\n  return typeof color === 'string' && color.length > 0\n    ? Object.assign({ 'ion-color': true, [`ion-color-${color}`]: true }, cssClassMap) : cssClassMap;\n};\nconst getClassList = (classes) => {\n  if (classes !== undefined) {\n    const array = Array.isArray(classes) ? classes : classes.split(' ');\n    return array\n      .filter((c) => c != null)\n      .map((c) => c.trim())\n      .filter((c) => c !== '');\n  }\n  return [];\n};\nconst getClassMap = (classes) => {\n  const map = {};\n  getClassList(classes).forEach((c) => (map[c] = true));\n  return map;\n};\nconst SCHEME = /^[a-z][a-z0-9+\\-.]*:/;\nconst openURL = async (url, ev, direction, animation) => {\n  if (url != null && url[0] !== '#' && !SCHEME.test(url)) {\n    const router = document.querySelector('ion-router');\n    if (router) {\n      if (ev != null) {\n        ev.preventDefault();\n      }\n      return router.push(url, direction, animation);\n    }\n  }\n  return false;\n};\n\nexport { createColorClasses as c, getClassMap as g, hostContext as h, openURL as o };\n"], "names": ["r", "registerInstance", "h", "H", "Host", "f", "getElement", "d", "createEvent", "c", "createColorClasses", "b", "getIonMode", "p", "isEndSide", "findClosestIonContent", "disableContentScrollY", "resetContentScrollY", "w", "watchForOptions", "itemOptionIosCss", "itemOptionMdCss", "ItemOption", "constructor", "hostRef", "onClick", "ev", "el", "target", "closest", "preventDefault", "color", "undefined", "disabled", "download", "expandable", "href", "rel", "type", "render", "TagType", "mode", "attrs", "class", "Object", "assign", "part", "name", "style", "ios", "md", "itemOptionsIosCss", "itemOptionsMdCss", "ItemOptions", "ionSwipe", "side", "fireSwipeEvent", "_this", "_asyncToGenerator", "emit", "isEnd", "itemSlidingCss", "SWIPE_MARGIN", "ELASTIC_FACTOR", "openSlidingItem", "ItemSliding", "ionDrag", "item", "openAmount", "initialOpenAmount", "optsWidthRightSide", "optsWidthLeftSide", "sides", "optsDirty", "contentEl", "initialContentScrollY", "state", "disabled<PERSON><PERSON>ed", "gesture", "enable", "connectedCallback", "_this2", "querySelector", "mutationObserver", "updateOptions", "createGesture", "<PERSON><PERSON><PERSON>", "gesturePriority", "threshold", "canStart", "onStart", "onMove", "onEnd", "disconnectedCallback", "destroy", "leftOptions", "rightOptions", "disconnect", "getOpenAmount", "Promise", "resolve", "getSlidingRatio", "getSlidingRatioSync", "open", "_this3", "_a", "optionsToOpen", "getOptions", "isStartOpen", "isEndOpen", "closeOpened", "requestAnimationFrame", "calculateOptsWidth", "width", "setOpenAmount", "close", "_this4", "_this5", "options", "querySelectorAll", "i", "length", "option", "componentOnReady", "rtl", "document", "dir", "atEdge", "window", "innerWidth", "startX", "selected", "tmr", "clearTimeout", "transition", "deltaX", "Math", "max", "min", "console", "warn", "optsWidth", "velocity", "velocityX", "restingPoint", "isResetDirection", "isMovingFast", "abs", "isOnCloseZone", "swipeShouldReset", "display", "offsetWidth", "isFinal", "classList", "add", "setTimeout", "remove", "transform", "amount", "ratio", "watchers", "isOnResetZone", "ion_item_option", "ion_item_options", "ion_item_sliding", "hostContext", "selector", "cssClassMap", "getClassList", "classes", "array", "Array", "isArray", "split", "filter", "map", "trim", "getClassMap", "for<PERSON>ach", "SCHEME", "openURL", "_ref", "url", "direction", "animation", "test", "router", "push", "_x", "_x2", "_x3", "_x4", "apply", "arguments", "g", "o"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0, 1]}