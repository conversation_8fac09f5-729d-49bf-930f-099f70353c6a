{"version": 3, "file": "node_modules_ionicons_dist_esm-es5_ion-icon_entry_js.js", "mappings": ";;;;;;;;;;;;;;;AAAmF;AAAuG;AAAA,IAAIiB,eAAe,GAAC,SAAAA,CAASC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EAACF,CAAC,CAACG,SAAS,GAACJ,CAAC;EAAC,KAAI,IAAIX,CAAC,GAACY,CAAC,CAACI,UAAU,CAACC,MAAM,GAAC,CAAC,EAACjB,CAAC,IAAE,CAAC,EAACA,CAAC,EAAE,EAAC;IAAC,IAAGY,CAAC,CAACI,UAAU,CAAChB,CAAC,CAAC,CAACkB,QAAQ,CAACC,WAAW,CAAC,CAAC,KAAG,KAAK,EAAC;MAACP,CAAC,CAACQ,WAAW,CAACR,CAAC,CAACI,UAAU,CAAChB,CAAC,CAAC,CAAC;IAAA;EAAC;EAAC,IAAIqB,CAAC,GAACT,CAAC,CAACU,iBAAiB;EAAC,IAAGD,CAAC,IAAEA,CAAC,CAACH,QAAQ,CAACC,WAAW,CAAC,CAAC,KAAG,KAAK,EAAC;IAAC,IAAII,CAAC,GAACF,CAAC,CAACG,YAAY,CAAC,OAAO,CAAC,IAAE,EAAE;IAACH,CAAC,CAACI,YAAY,CAAC,OAAO,EAAC,CAACF,CAAC,GAAC,aAAa,EAAEG,IAAI,CAAC,CAAC,CAAC;IAAC,IAAGC,OAAO,CAACN,CAAC,CAAC,EAAC;MAAC,OAAOT,CAAC,CAACG,SAAS;IAAA;EAAC;EAAC,OAAM,EAAE;AAAA,CAAC;AAAC,IAAIY,OAAO,GAAC,SAAAA,CAAShB,CAAC,EAAC;EAAC,IAAGA,CAAC,CAACiB,QAAQ,KAAG,CAAC,EAAC;IAAC,IAAGjB,CAAC,CAACO,QAAQ,CAACC,WAAW,CAAC,CAAC,KAAG,QAAQ,EAAC;MAAC,OAAO,KAAK;IAAA;IAAC,KAAI,IAAIP,CAAC,GAAC,CAAC,EAACA,CAAC,GAACD,CAAC,CAACkB,UAAU,CAACZ,MAAM,EAACL,CAAC,EAAE,EAAC;MAAC,IAAIZ,CAAC,GAACW,CAAC,CAACkB,UAAU,CAACjB,CAAC,CAAC,CAACkB,IAAI;MAAC,IAAG7B,qDAAK,CAACD,CAAC,CAAC,IAAEA,CAAC,CAACmB,WAAW,CAAC,CAAC,CAACY,OAAO,CAAC,IAAI,CAAC,KAAG,CAAC,EAAC;QAAC,OAAO,KAAK;MAAA;IAAC;IAAC,KAAI,IAAInB,CAAC,GAAC,CAAC,EAACA,CAAC,GAACD,CAAC,CAACK,UAAU,CAACC,MAAM,EAACL,CAAC,EAAE,EAAC;MAAC,IAAG,CAACe,OAAO,CAAChB,CAAC,CAACK,UAAU,CAACJ,CAAC,CAAC,CAAC,EAAC;QAAC,OAAO,KAAK;MAAA;IAAC;EAAC;EAAC,OAAO,IAAI;AAAA,CAAC;AAAC,IAAIoB,YAAY,GAAC,SAAAA,CAASrB,CAAC,EAAC;EAAC,OAAOA,CAAC,CAACsB,UAAU,CAAC,oBAAoB,CAAC;AAAA,CAAC;AAAC,IAAIC,gBAAgB,GAAC,SAAAA,CAASvB,CAAC,EAAC;EAAC,OAAOA,CAAC,CAACoB,OAAO,CAAC,QAAQ,CAAC,KAAG,CAAC,CAAC;AAAA,CAAC;AAAC,IAAII,cAAc,GAAC,IAAIC,GAAG,CAAD,CAAC;AAAC,IAAIC,QAAQ,GAAC,IAAID,GAAG,CAAD,CAAC;AAAC,IAAIE,MAAM;AAAC,IAAIC,aAAa,GAAC,SAAAA,CAAS5B,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIZ,CAAC,GAACqC,QAAQ,CAACG,GAAG,CAAC7B,CAAC,CAAC;EAAC,IAAG,CAACX,CAAC,EAAC;IAAC,IAAG,OAAOyC,KAAK,KAAG,WAAW,IAAE,OAAO5B,QAAQ,KAAG,WAAW,EAAC;MAAC,IAAGmB,YAAY,CAACrB,CAAC,CAAC,IAAEuB,gBAAgB,CAACvB,CAAC,CAAC,EAAC;QAAC,IAAG,CAAC2B,MAAM,EAAC;UAACA,MAAM,GAAC,IAAII,SAAS,CAAD,CAAC;QAAA;QAAC,IAAIrB,CAAC,GAACiB,MAAM,CAACK,eAAe,CAAChC,CAAC,EAAC,WAAW,CAAC;QAAC,IAAIY,CAAC,GAACF,CAAC,CAACuB,aAAa,CAAC,KAAK,CAAC;QAAC,IAAGrB,CAAC,EAAC;UAACY,cAAc,CAACU,GAAG,CAAClC,CAAC,EAACY,CAAC,CAACuB,SAAS,CAAC;QAAA;QAAC,OAAOC,OAAO,CAACC,OAAO,CAAC,CAAC;MAAA,CAAC,MAAI;QAAChD,CAAC,GAACyC,KAAK,CAAC9B,CAAC,CAAC,CAACsC,IAAI,CAAE,UAASjD,CAAC,EAAC;UAAC,IAAGA,CAAC,CAACkD,EAAE,EAAC;YAAC,OAAOlD,CAAC,CAACmD,IAAI,CAAC,CAAC,CAACF,IAAI,CAAE,UAASjD,CAAC,EAAC;cAAC,IAAGA,CAAC,IAAEY,CAAC,KAAG,KAAK,EAAC;gBAACZ,CAAC,GAACU,eAAe,CAACV,CAAC,CAAC;cAAA;cAACmC,cAAc,CAACU,GAAG,CAAClC,CAAC,EAACX,CAAC,IAAE,EAAE,CAAC;YAAA,CAAE,CAAC;UAAA;UAACmC,cAAc,CAACU,GAAG,CAAClC,CAAC,EAAC,EAAE,CAAC;QAAA,CAAE,CAAC;QAAC0B,QAAQ,CAACQ,GAAG,CAAClC,CAAC,EAACX,CAAC,CAAC;MAAA;IAAC,CAAC,MAAI;MAACmC,cAAc,CAACU,GAAG,CAAClC,CAAC,EAAC,EAAE,CAAC;MAAC,OAAOoC,OAAO,CAACC,OAAO,CAAC,CAAC;IAAA;EAAC;EAAC,OAAOhD,CAAC;AAAA,CAAC;AAAC,IAAIoD,OAAO,GAAC,mwCAAmwC;AAAC,IAAIC,IAAI,GAAC,YAAU;EAAC,SAAS1C,CAACA,CAACA,CAAC,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAI;IAAClB,qDAAgB,CAAC,IAAI,EAACiB,CAAC,CAAC;IAAC,IAAI,CAAC2C,QAAQ,GAAC,IAAI;IAAC,IAAI,CAACC,mBAAmB,GAAC,CAAC,CAAC;IAAC,IAAI,CAACC,SAAS,GAAC,KAAK;IAAC,IAAI,CAACC,IAAI,GAACC,UAAU,CAAC,CAAC;IAAC,IAAI,CAACC,IAAI,GAAC,KAAK;IAAC,IAAI,CAACC,QAAQ,GAAC,IAAI;IAAC,IAAI,CAACC,aAAa,GAAC,YAAU;MAAC,IAAIlD,CAAC,GAACC,CAAC,CAACkD,EAAE;MAAC,OAAOnD,CAAC,CAACoD,YAAY,CAAC,aAAa,CAAC,IAAEpD,CAAC,CAACa,YAAY,CAAC,aAAa,CAAC,KAAG,MAAM;IAAA,CAAC;EAAA;EAACb,CAAC,CAACqD,SAAS,CAACC,iBAAiB,GAAC,YAAU;IAAC,IAAI,CAACV,mBAAmB,GAACpD,qDAAiB,CAAC,IAAI,CAAC2D,EAAE,EAAC,CAAC,YAAY,CAAC,CAAC;EAAA,CAAC;EAACnD,CAAC,CAACqD,SAAS,CAACE,iBAAiB,GAAC,YAAU;IAAC,IAAIvD,CAAC,GAAC,IAAI;IAAC,IAAI,CAACwD,gBAAgB,CAAC,IAAI,CAACL,EAAE,EAAC,MAAM,EAAE,YAAU;MAACnD,CAAC,CAAC6C,SAAS,GAAC,IAAI;MAAC7C,CAAC,CAACyD,QAAQ,CAAC,CAAC;IAAA,CAAE,CAAC;EAAA,CAAC;EAACzD,CAAC,CAACqD,SAAS,CAACK,oBAAoB,GAAC,YAAU;IAAC,IAAG,IAAI,CAACC,EAAE,EAAC;MAAC,IAAI,CAACA,EAAE,CAACC,UAAU,CAAC,CAAC;MAAC,IAAI,CAACD,EAAE,GAACE,SAAS;IAAA;EAAC,CAAC;EAAC7D,CAAC,CAACqD,SAAS,CAACG,gBAAgB,GAAC,UAASxD,CAAC,EAACC,CAAC,EAACZ,CAAC,EAAC;IAAC,IAAIqB,CAAC,GAAC,IAAI;IAAC,IAAG,IAAI,CAACsC,IAAI,IAAE,OAAOc,MAAM,KAAG,WAAW,IAAEA,MAAM,CAACC,oBAAoB,EAAC;MAAC,IAAInD,CAAC,GAAC,IAAI,CAAC+C,EAAE,GAAC,IAAIG,MAAM,CAACC,oBAAoB,CAAE,UAAS/D,CAAC,EAAC;QAAC,IAAGA,CAAC,CAAC,CAAC,CAAC,CAACgE,cAAc,EAAC;UAACpD,CAAC,CAACgD,UAAU,CAAC,CAAC;UAAClD,CAAC,CAACiD,EAAE,GAACE,SAAS;UAACxE,CAAC,CAAC,CAAC;QAAA;MAAC,CAAC,EAAE;QAAC4E,UAAU,EAAChE;MAAC,CAAC,CAAC;MAACW,CAAC,CAACsD,OAAO,CAAClE,CAAC,CAAC;IAAA,CAAC,MAAI;MAACX,CAAC,CAAC,CAAC;IAAA;EAAC,CAAC;EAACW,CAAC,CAACqD,SAAS,CAACI,QAAQ,GAAC,YAAU;IAAC,IAAIzD,CAAC,GAAC,IAAI;IAAC,IAAG,IAAI,CAAC6C,SAAS,EAAC;MAAC,IAAI5C,CAAC,GAACP,qDAAM,CAAC,IAAI,CAAC;MAAC,IAAGO,CAAC,EAAC;QAAC,IAAGuB,cAAc,CAAC2C,GAAG,CAAClE,CAAC,CAAC,EAAC;UAAC,IAAI,CAACmE,UAAU,GAAC5C,cAAc,CAACK,GAAG,CAAC5B,CAAC,CAAC;QAAA,CAAC,MAAI;UAAC2B,aAAa,CAAC3B,CAAC,EAAC,IAAI,CAACgD,QAAQ,CAAC,CAACX,IAAI,CAAE,YAAU;YAAC,OAAOtC,CAAC,CAACoE,UAAU,GAAC5C,cAAc,CAACK,GAAG,CAAC5B,CAAC,CAAC;UAAA,CAAE,CAAC;QAAA;MAAC;IAAC;IAAC,IAAIZ,CAAC,GAAC,IAAI,CAACsD,QAAQ,GAAC/C,qDAAO,CAAC,IAAI,CAACuB,IAAI,EAAC,IAAI,CAACkD,IAAI,EAAC,IAAI,CAACvB,IAAI,EAAC,IAAI,CAACwB,GAAG,EAAC,IAAI,CAACC,EAAE,CAAC;IAAC,IAAGlF,CAAC,EAAC;MAAC,IAAI,CAACmF,SAAS,GAACnF,CAAC,CAACoF,OAAO,CAAC,KAAK,EAAC,GAAG,CAAC;IAAA;EAAC,CAAC;EAACzE,CAAC,CAACqD,SAAS,CAACqB,MAAM,GAAC,YAAU;IAAC,IAAI1E,CAAC,EAACC,CAAC;IAAC,IAAIZ,CAAC,GAAC,IAAI;MAACqB,CAAC,GAACrB,CAAC,CAACsD,QAAQ;MAAC/B,CAAC,GAACvB,CAAC,CAACmF,SAAS;MAAC1F,CAAC,GAACO,CAAC,CAAC8D,EAAE;MAACwB,CAAC,GAACtF,CAAC,CAACuD,mBAAmB;IAAC,IAAIzD,CAAC,GAAC,IAAI,CAAC2D,IAAI,IAAE,IAAI;IAAC,IAAInD,CAAC,GAAC,IAAI,CAACiF,OAAO,IAAElE,CAAC,KAAGA,CAAC,CAACU,OAAO,CAAC,OAAO,CAAC,GAAC,CAAC,CAAC,IAAEV,CAAC,CAACU,OAAO,CAAC,SAAS,CAAC,GAAC,CAAC,CAAC,CAAC,IAAE,IAAI,CAACwD,OAAO,KAAG,KAAK;IAAC,OAAO5F,qDAAC,CAACE,iDAAI,EAAC2F,MAAM,CAACC,MAAM,CAAC;MAAC,YAAY,EAAClE,CAAC,KAAGiD,SAAS,IAAE,CAAC,IAAI,CAACX,aAAa,CAAC,CAAC,GAACtC,CAAC,GAAC,IAAI;MAACmE,IAAI,EAAC,KAAK;MAACC,KAAK,EAACH,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,EAAE9E,CAAC,GAAC,CAAC,CAAC,EAACA,CAAC,CAACb,CAAC,CAAC,GAAC,IAAI,EAACa,CAAC,GAAEiF,kBAAkB,CAAC,IAAI,CAACC,KAAK,CAAC,CAAC,GAAEjF,CAAC,GAAC,CAAC,CAAC,EAACA,CAAC,CAAC,OAAO,CAACkF,MAAM,CAAC,IAAI,CAACC,IAAI,CAAC,CAAC,GAAC,CAAC,CAAC,IAAI,CAACA,IAAI,EAACnF,CAAC,CAAC,UAAU,CAAC,GAAC,CAAC,CAACN,CAAC,IAAEG,qDAAK,CAAChB,CAAC,CAAC,EAACmB,CAAC,CAAC;IAAC,CAAC,EAAC0E,CAAC,CAAC,EAAC,IAAI,CAACP,UAAU,GAACpF,qDAAC,CAAC,KAAK,EAAC;MAACgG,KAAK,EAAC,YAAY;MAAC5E,SAAS,EAAC,IAAI,CAACgE;IAAU,CAAC,CAAC,GAACpF,qDAAC,CAAC,KAAK,EAAC;MAACgG,KAAK,EAAC;IAAY,CAAC,CAAC,CAAC;EAAA,CAAC;EAACH,MAAM,CAACQ,cAAc,CAACrF,CAAC,EAAC,YAAY,EAAC;IAAC6B,GAAG,EAAC,SAAAA,CAAA,EAAU;MAAC,OAAM,CAAC,KAAK,CAAC;IAAA,CAAC;IAACyD,UAAU,EAAC,KAAK;IAACC,YAAY,EAAC;EAAI,CAAC,CAAC;EAACV,MAAM,CAACQ,cAAc,CAACrF,CAAC,CAACqD,SAAS,EAAC,IAAI,EAAC;IAACxB,GAAG,EAAC,SAAAA,CAAA,EAAU;MAAC,OAAOzC,qDAAU,CAAC,IAAI,CAAC;IAAA,CAAC;IAACkG,UAAU,EAAC,KAAK;IAACC,YAAY,EAAC;EAAI,CAAC,CAAC;EAACV,MAAM,CAACQ,cAAc,CAACrF,CAAC,EAAC,UAAU,EAAC;IAAC6B,GAAG,EAAC,SAAAA,CAAA,EAAU;MAAC,OAAM;QAACV,IAAI,EAAC,CAAC,UAAU,CAAC;QAACqE,GAAG,EAAC,CAAC,UAAU,CAAC;QAACnB,IAAI,EAAC,CAAC,UAAU,CAAC;QAACC,GAAG,EAAC,CAAC,UAAU,CAAC;QAACC,EAAE,EAAC,CAAC,UAAU;MAAC,CAAC;IAAA,CAAC;IAACe,UAAU,EAAC,KAAK;IAACC,YAAY,EAAC;EAAI,CAAC,CAAC;EAAC,OAAOvF,CAAC;AAAA,CAAC,CAAC,CAAC;AAAC,IAAI+C,UAAU,GAAC,SAAAA,CAAA,EAAU;EAAC,OAAO,OAAO7C,QAAQ,KAAG,WAAW,IAAEA,QAAQ,CAACuF,eAAe,CAAC5E,YAAY,CAAC,MAAM,CAAC,IAAE,IAAI;AAAA,CAAC;AAAC,IAAIoE,kBAAkB,GAAC,SAAAA,CAASjF,CAAC,EAAC;EAAC,IAAIC,CAAC;EAAC,OAAOD,CAAC,IAAEC,CAAC,GAAC;IAAC,WAAW,EAAC;EAAI,CAAC,EAACA,CAAC,CAAC,YAAY,CAACkF,MAAM,CAACnF,CAAC,CAAC,CAAC,GAAC,IAAI,EAACC,CAAC,IAAE,IAAI;AAAA,CAAC;AAACyC,IAAI,CAACgD,KAAK,GAACjD,OAAO", "sources": ["./node_modules/ionicons/dist/esm-es5/ion-icon.entry.js"], "sourcesContent": ["import{r as registerInstance,h,H as Host,a as getElement}from\"./index-5514a13d.js\";import{i as isStr,b as inheritAttributes,g as getUrl,c as getName,d as isRTL}from\"./utils-ccb924b9.js\";var validateContent=function(e){var t=document.createElement(\"div\");t.innerHTML=e;for(var i=t.childNodes.length-1;i>=0;i--){if(t.childNodes[i].nodeName.toLowerCase()!==\"svg\"){t.removeChild(t.childNodes[i])}}var o=t.firstElementChild;if(o&&o.nodeName.toLowerCase()===\"svg\"){var n=o.getAttribute(\"class\")||\"\";o.setAttribute(\"class\",(n+\" s-ion-icon\").trim());if(isValid(o)){return t.innerHTML}}return\"\"};var isValid=function(e){if(e.nodeType===1){if(e.nodeName.toLowerCase()===\"script\"){return false}for(var t=0;t<e.attributes.length;t++){var i=e.attributes[t].name;if(isStr(i)&&i.toLowerCase().indexOf(\"on\")===0){return false}}for(var t=0;t<e.childNodes.length;t++){if(!isValid(e.childNodes[t])){return false}}}return true};var isSvgDataUrl=function(e){return e.startsWith(\"data:image/svg+xml\")};var isEncodedDataUrl=function(e){return e.indexOf(\";utf8,\")!==-1};var ioniconContent=new Map;var requests=new Map;var parser;var getSvgContent=function(e,t){var i=requests.get(e);if(!i){if(typeof fetch!==\"undefined\"&&typeof document!==\"undefined\"){if(isSvgDataUrl(e)&&isEncodedDataUrl(e)){if(!parser){parser=new DOMParser}var o=parser.parseFromString(e,\"text/html\");var n=o.querySelector(\"svg\");if(n){ioniconContent.set(e,n.outerHTML)}return Promise.resolve()}else{i=fetch(e).then((function(i){if(i.ok){return i.text().then((function(i){if(i&&t!==false){i=validateContent(i)}ioniconContent.set(e,i||\"\")}))}ioniconContent.set(e,\"\")}));requests.set(e,i)}}else{ioniconContent.set(e,\"\");return Promise.resolve()}}return i};var iconCss=\":host{display:inline-block;width:1em;height:1em;contain:strict;fill:currentColor;-webkit-box-sizing:content-box !important;box-sizing:content-box !important}:host .ionicon{stroke:currentColor}.ionicon-fill-none{fill:none}.ionicon-stroke-width{stroke-width:32px;stroke-width:var(--ionicon-stroke-width, 32px)}.icon-inner,.ionicon,svg{display:block;height:100%;width:100%}:host(.flip-rtl) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}:host(.icon-small){font-size:18px !important}:host(.icon-large){font-size:32px !important}:host(.ion-color){color:var(--ion-color-base) !important}:host(.ion-color-primary){--ion-color-base:var(--ion-color-primary, #3880ff)}:host(.ion-color-secondary){--ion-color-base:var(--ion-color-secondary, #0cd1e8)}:host(.ion-color-tertiary){--ion-color-base:var(--ion-color-tertiary, #f4a942)}:host(.ion-color-success){--ion-color-base:var(--ion-color-success, #10dc60)}:host(.ion-color-warning){--ion-color-base:var(--ion-color-warning, #ffce00)}:host(.ion-color-danger){--ion-color-base:var(--ion-color-danger, #f14141)}:host(.ion-color-light){--ion-color-base:var(--ion-color-light, #f4f5f8)}:host(.ion-color-medium){--ion-color-base:var(--ion-color-medium, #989aa2)}:host(.ion-color-dark){--ion-color-base:var(--ion-color-dark, #222428)}\";var Icon=function(){function e(e){var t=this;registerInstance(this,e);this.iconName=null;this.inheritedAttributes={};this.isVisible=false;this.mode=getIonMode();this.lazy=false;this.sanitize=true;this.hasAriaHidden=function(){var e=t.el;return e.hasAttribute(\"aria-hidden\")&&e.getAttribute(\"aria-hidden\")===\"true\"}}e.prototype.componentWillLoad=function(){this.inheritedAttributes=inheritAttributes(this.el,[\"aria-label\"])};e.prototype.connectedCallback=function(){var e=this;this.waitUntilVisible(this.el,\"50px\",(function(){e.isVisible=true;e.loadIcon()}))};e.prototype.disconnectedCallback=function(){if(this.io){this.io.disconnect();this.io=undefined}};e.prototype.waitUntilVisible=function(e,t,i){var o=this;if(this.lazy&&typeof window!==\"undefined\"&&window.IntersectionObserver){var n=this.io=new window.IntersectionObserver((function(e){if(e[0].isIntersecting){n.disconnect();o.io=undefined;i()}}),{rootMargin:t});n.observe(e)}else{i()}};e.prototype.loadIcon=function(){var e=this;if(this.isVisible){var t=getUrl(this);if(t){if(ioniconContent.has(t)){this.svgContent=ioniconContent.get(t)}else{getSvgContent(t,this.sanitize).then((function(){return e.svgContent=ioniconContent.get(t)}))}}}var i=this.iconName=getName(this.name,this.icon,this.mode,this.ios,this.md);if(i){this.ariaLabel=i.replace(/\\-/g,\" \")}};e.prototype.render=function(){var e,t;var i=this,o=i.iconName,n=i.ariaLabel,r=i.el,s=i.inheritedAttributes;var a=this.mode||\"md\";var c=this.flipRtl||o&&(o.indexOf(\"arrow\")>-1||o.indexOf(\"chevron\")>-1)&&this.flipRtl!==false;return h(Host,Object.assign({\"aria-label\":n!==undefined&&!this.hasAriaHidden()?n:null,role:\"img\",class:Object.assign(Object.assign((e={},e[a]=true,e),createColorClasses(this.color)),(t={},t[\"icon-\".concat(this.size)]=!!this.size,t[\"flip-rtl\"]=!!c&&isRTL(r),t))},s),this.svgContent?h(\"div\",{class:\"icon-inner\",innerHTML:this.svgContent}):h(\"div\",{class:\"icon-inner\"}))};Object.defineProperty(e,\"assetsDirs\",{get:function(){return[\"svg\"]},enumerable:false,configurable:true});Object.defineProperty(e.prototype,\"el\",{get:function(){return getElement(this)},enumerable:false,configurable:true});Object.defineProperty(e,\"watchers\",{get:function(){return{name:[\"loadIcon\"],src:[\"loadIcon\"],icon:[\"loadIcon\"],ios:[\"loadIcon\"],md:[\"loadIcon\"]}},enumerable:false,configurable:true});return e}();var getIonMode=function(){return typeof document!==\"undefined\"&&document.documentElement.getAttribute(\"mode\")||\"md\"};var createColorClasses=function(e){var t;return e?(t={\"ion-color\":true},t[\"ion-color-\".concat(e)]=true,t):null};Icon.style=iconCss;export{Icon as ion_icon};"], "names": ["r", "registerInstance", "h", "H", "Host", "a", "getElement", "i", "isStr", "b", "inheritAttributes", "g", "getUrl", "c", "getName", "d", "isRTL", "validateContent", "e", "t", "document", "createElement", "innerHTML", "childNodes", "length", "nodeName", "toLowerCase", "<PERSON><PERSON><PERSON><PERSON>", "o", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "n", "getAttribute", "setAttribute", "trim", "<PERSON><PERSON><PERSON><PERSON>", "nodeType", "attributes", "name", "indexOf", "isSvgDataUrl", "startsWith", "isEncodedDataUrl", "ioniconContent", "Map", "requests", "parser", "getSvgContent", "get", "fetch", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "querySelector", "set", "outerHTML", "Promise", "resolve", "then", "ok", "text", "iconCss", "Icon", "iconName", "inheritedAttributes", "isVisible", "mode", "getIonMode", "lazy", "sanitize", "hasAriaHidden", "el", "hasAttribute", "prototype", "componentWillLoad", "connectedCallback", "waitUntilVisible", "loadIcon", "disconnectedCallback", "io", "disconnect", "undefined", "window", "IntersectionObserver", "isIntersecting", "rootMargin", "observe", "has", "svgContent", "icon", "ios", "md", "aria<PERSON><PERSON><PERSON>", "replace", "render", "s", "flipRtl", "Object", "assign", "role", "class", "createColorClasses", "color", "concat", "size", "defineProperty", "enumerable", "configurable", "src", "documentElement", "style", "ion_icon"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}