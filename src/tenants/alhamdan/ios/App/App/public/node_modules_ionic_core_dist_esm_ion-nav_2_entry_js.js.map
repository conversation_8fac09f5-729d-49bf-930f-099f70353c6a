{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-nav_2_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC6G;AACnC;AACM;AAC2F;AACjG;AACF;AAExE,MAAM6B,cAAc,GAAG,CAAC;AACxB,MAAMC,mBAAmB,GAAG,CAAC;AAC7B,MAAMC,oBAAoB,GAAG,CAAC;AAC9B;AACA,MAAMC,cAAc,CAAC;EACnBC,WAAWA,CAACC,SAAS,EAAEC,MAAM,EAAE;IAC7B,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGP,cAAc;EAC7B;EACMQ,IAAIA,CAACC,SAAS,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,8KAAA;MACpBD,KAAI,CAACH,KAAK,GAAGN,mBAAmB;MAChC,IAAI,CAACS,KAAI,CAACE,OAAO,EAAE;QACjB,MAAMP,SAAS,GAAGK,KAAI,CAACL,SAAS;QAChCK,KAAI,CAACE,OAAO,SAASb,kEAAe,CAACW,KAAI,CAACG,QAAQ,EAAEJ,SAAS,EAAEJ,SAAS,EAAE,CAAC,UAAU,EAAE,oBAAoB,CAAC,EAAEK,KAAI,CAACJ,MAAM,CAAC;MAC5H;IAAC;EACH;EACA;AACF;AACA;EACEQ,QAAQA,CAAA,EAAG;IACT/B,uDAAM,CAAC,IAAI,CAACwB,KAAK,KAAKL,oBAAoB,EAAE,6BAA6B,CAAC;IAC1E,MAAMU,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,IAAIA,OAAO,EAAE;MACX,IAAI,IAAI,CAACC,QAAQ,EAAE;QACjB,IAAI,CAACA,QAAQ,CAACE,iBAAiB,CAACH,OAAO,CAACI,aAAa,EAAEJ,OAAO,CAAC;MACjE,CAAC,MACI;QACHA,OAAO,CAACK,MAAM,CAAC,CAAC;MAClB;IACF;IACA,IAAI,CAACC,GAAG,GAAGC,SAAS;IACpB,IAAI,CAACZ,KAAK,GAAGL,oBAAoB;EACnC;AACF;AACA,MAAMkB,OAAO,GAAGA,CAACC,IAAI,EAAEC,EAAE,EAAEhB,MAAM,KAAK;EACpC,IAAI,CAACe,IAAI,EAAE;IACT,OAAO,KAAK;EACd;EACA,IAAIA,IAAI,CAAChB,SAAS,KAAKiB,EAAE,EAAE;IACzB,OAAO,KAAK;EACd;EACA,OAAOrC,uDAAqB,CAACoC,IAAI,CAACf,MAAM,EAAEA,MAAM,CAAC;AACnD,CAAC;AACD,MAAMiB,aAAa,GAAGA,CAACC,IAAI,EAAElB,MAAM,KAAK;EACtC,IAAI,CAACkB,IAAI,EAAE;IACT,OAAO,IAAI;EACb;EACA,IAAIA,IAAI,YAAYrB,cAAc,EAAE;IAClC,OAAOqB,IAAI;EACb;EACA,OAAO,IAAIrB,cAAc,CAACqB,IAAI,EAAElB,MAAM,CAAC;AACzC,CAAC;AACD,MAAMmB,cAAc,GAAIC,KAAK,IAAK;EAChC,OAAOA,KAAK,CACTC,GAAG,CAAEH,IAAI,IAAK;IACf,IAAIA,IAAI,YAAYrB,cAAc,EAAE;MAClC,OAAOqB,IAAI;IACb;IACA,IAAI,WAAW,IAAIA,IAAI,EAAE;MACvB,OAAOD,aAAa,CAACC,IAAI,CAACnB,SAAS,EAAEmB,IAAI,CAACI,cAAc,KAAK,IAAI,GAAGT,SAAS,GAAGK,IAAI,CAACI,cAAc,CAAC;IACtG;IACA,OAAOL,aAAa,CAACC,IAAI,EAAEL,SAAS,CAAC;EACvC,CAAC,CAAC,CACCU,MAAM,CAAEC,CAAC,IAAKA,CAAC,KAAK,IAAI,CAAC;AAC9B,CAAC;AAED,MAAMC,MAAM,GAAG,4GAA4G;AAE3H,MAAMC,GAAG,GAAG,MAAM;EAChB5B,WAAWA,CAAC6B,OAAO,EAAE;IACnB7D,qDAAgB,CAAC,IAAI,EAAE6D,OAAO,CAAC;IAC/B,IAAI,CAACC,cAAc,GAAG5D,qDAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAAC6D,gBAAgB,GAAG7D,qDAAW,CAAC,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAC;IAChE,IAAI,CAAC8D,eAAe,GAAG9D,qDAAW,CAAC,IAAI,EAAE,iBAAiB,EAAE,CAAC,CAAC;IAC9D,IAAI,CAAC+D,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,4BAA4B,GAAG,KAAK;IACzC,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAAC9B,QAAQ,GAAGM,SAAS;IACzB,IAAI,CAACyB,YAAY,GAAGzB,SAAS;IAC7B,IAAI,CAAC0B,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,SAAS,GAAG3B,SAAS;IAC1B,IAAI,CAAC4B,UAAU,GAAG5B,SAAS;IAC3B,IAAI,CAAC6B,IAAI,GAAG7B,SAAS;EACvB;EACA8B,mBAAmBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACC,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACC,MAAM,CAAC,IAAI,CAACP,YAAY,KAAK,IAAI,CAAC;IACjD;EACF;EACAQ,WAAWA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACJ,IAAI,KAAK7B,SAAS,EAAE;MAC3B;IACF;IACA,IAAI,IAAI,CAACwB,OAAO,KAAK,KAAK,EAAE;MAC1B;AACN;AACA;AACA;MACM;IACF;IACA,IAAI,CAAC,IAAI,CAACJ,SAAS,EAAE;MACnB,IAAI,IAAI,CAACS,IAAI,KAAK7B,SAAS,EAAE;QAC3B,IAAI,CAACkC,OAAO,CAAC,IAAI,CAACL,IAAI,EAAE,IAAI,CAACD,UAAU,CAAC;MAC1C;IACF;EACF;EACAO,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAACf,SAAS,GAAGgB,QAAQ,CAACC,aAAa,CAAC,YAAY,CAAC,KAAK,IAAI,IAAI,IAAI,CAACC,EAAE,CAACC,OAAO,CAAC,aAAa,CAAC,KAAK,IAAI;IACzG,IAAI,IAAI,CAACd,YAAY,KAAKzB,SAAS,EAAE;MACnC,MAAMwC,IAAI,GAAG/D,4DAAU,CAAC,IAAI,CAAC;MAC7B,IAAI,CAACgD,YAAY,GAAG/C,wDAAM,CAAC+D,UAAU,CAAC,kBAAkB,EAAED,IAAI,KAAK,KAAK,CAAC;IAC3E;IACA,IAAI,CAACzB,cAAc,CAAC2B,IAAI,CAAC,CAAC;EAC5B;EACMC,gBAAgBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAApD,8KAAA;MACvB;MACAoD,MAAI,CAACpB,OAAO,GAAG,IAAI;MACnBoB,MAAI,CAACX,WAAW,CAAC,CAAC;MAClBW,MAAI,CAACb,OAAO,GAAG,OAAO,0IAAkC,EAAEc,sBAAsB,CAACD,MAAI,CAACN,EAAE,EAAEM,MAAI,CAACE,QAAQ,CAACC,IAAI,CAACH,MAAI,CAAC,EAAEA,MAAI,CAACI,OAAO,CAACD,IAAI,CAACH,MAAI,CAAC,EAAEA,MAAI,CAACK,MAAM,CAACF,IAAI,CAACH,MAAI,CAAC,EAAEA,MAAI,CAACM,KAAK,CAACH,IAAI,CAACH,MAAI,CAAC,CAAC;MAC3LA,MAAI,CAACd,mBAAmB,CAAC,CAAC;IAAC;EAC7B;EACAqB,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAAC7B,SAAS,GAAG,KAAK;EACxB;EACA8B,oBAAoBA,CAAA,EAAG;IACrB,KAAK,MAAMlD,IAAI,IAAI,IAAI,CAACqB,KAAK,EAAE;MAC7BvD,qDAAS,CAACkC,IAAI,CAACT,OAAO,EAAErB,iDAAqB,CAAC;MAC9C8B,IAAI,CAACP,QAAQ,CAAC,CAAC;IACjB;IACA;IACA,IAAI,IAAI,CAACoC,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACsB,OAAO,CAAC,CAAC;MACtB,IAAI,CAACtB,OAAO,GAAG/B,SAAS;IAC1B;IACA,IAAI,CAACkB,UAAU,CAACoC,MAAM,GAAG,CAAC;IAC1B,IAAI,CAAC/B,KAAK,CAAC+B,MAAM,GAAG,CAAC;IACrB,IAAI,CAAChC,SAAS,GAAG,IAAI;EACvB;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEiC,IAAIA,CAACrE,SAAS,EAAEuB,cAAc,EAAE+C,IAAI,EAAEC,IAAI,EAAE;IAC1C,OAAO,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,EAAExE,SAAS,EAAEuB,cAAc,EAAE+C,IAAI,EAAEC,IAAI,CAAC;EAC/D;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,MAAMA,CAACC,WAAW,EAAEzE,SAAS,EAAEuB,cAAc,EAAE+C,IAAI,EAAEC,IAAI,EAAE;IACzD,OAAO,IAAI,CAACG,WAAW,CAACD,WAAW,EAAE,CAAC;MAAEzE,SAAS;MAAEuB;IAAe,CAAC,CAAC,EAAE+C,IAAI,EAAEC,IAAI,CAAC;EACnF;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEG,WAAWA,CAACD,WAAW,EAAEE,gBAAgB,EAAEL,IAAI,EAAEC,IAAI,EAAE;IACrD,OAAO,IAAI,CAACK,SAAS,CAAC;MACpBC,WAAW,EAAEJ,WAAW;MACxBK,WAAW,EAAEH,gBAAgB;MAC7BL;IACF,CAAC,EAAEC,IAAI,CAAC;EACV;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACEQ,GAAGA,CAACT,IAAI,EAAEC,IAAI,EAAE;IACd,OAAO,IAAI,CAACS,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,EAAEV,IAAI,EAAEC,IAAI,CAAC;EAC5C;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACEU,KAAKA,CAACC,eAAe,EAAEZ,IAAI,EAAEC,IAAI,EAAE;IACjC,MAAMY,EAAE,GAAG;MACTC,WAAW,EAAE,CAAC,CAAC;MACfC,WAAW,EAAE,CAAC,CAAC;MACff;IACF,CAAC;IACD,IAAI,OAAOY,eAAe,KAAK,QAAQ,IAAIA,eAAe,CAAClF,SAAS,EAAE;MACpEmF,EAAE,CAACG,UAAU,GAAGJ,eAAe;MAC/BC,EAAE,CAACC,WAAW,GAAG,CAAC;IACpB,CAAC,MACI,IAAI,OAAOF,eAAe,KAAK,QAAQ,EAAE;MAC5CC,EAAE,CAACC,WAAW,GAAGF,eAAe,GAAG,CAAC;IACtC;IACA,OAAO,IAAI,CAACN,SAAS,CAACO,EAAE,EAAEZ,IAAI,CAAC;EACjC;EACA;AACF;AACA;AACA;AACA;AACA;EACEgB,SAASA,CAACjB,IAAI,EAAEC,IAAI,EAAE;IACpB,OAAO,IAAI,CAACS,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEV,IAAI,EAAEC,IAAI,CAAC;EAC5C;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACES,WAAWA,CAACQ,UAAU,EAAEH,WAAW,GAAG,CAAC,EAAEf,IAAI,EAAEC,IAAI,EAAE;IACnD,OAAO,IAAI,CAACK,SAAS,CAAC;MACpBQ,WAAW,EAAEI,UAAU;MACvBH,WAAW;MACXf;IACF,CAAC,EAAEC,IAAI,CAAC;EACV;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEvB,OAAOA,CAAChD,SAAS,EAAEuB,cAAc,EAAE+C,IAAI,EAAEC,IAAI,EAAE;IAC7C,OAAO,IAAI,CAACkB,QAAQ,CAAC,CAAC;MAAEzF,SAAS;MAAEuB;IAAe,CAAC,CAAC,EAAE+C,IAAI,EAAEC,IAAI,CAAC;EACnE;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEkB,QAAQA,CAACpD,KAAK,EAAEiC,IAAI,EAAEC,IAAI,EAAE;IAC1BD,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAIA,IAAI,GAAG,CAAC,CAAE;IACrD;IACA,IAAIA,IAAI,CAAC9B,QAAQ,KAAK,IAAI,EAAE;MAC1B8B,IAAI,CAAC9B,QAAQ,GAAG,KAAK;IACvB;IACA,OAAO,IAAI,CAACoC,SAAS,CAAC;MACpBC,WAAW,EAAE,CAAC;MACdC,WAAW,EAAEzC,KAAK;MAClB+C,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE,CAAC,CAAC;MACff;IACF,CAAC,EAAEC,IAAI,CAAC;EACV;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEmB,UAAUA,CAACzE,EAAE,EAAEhB,MAAM,EAAE0F,SAAS,EAAElD,SAAS,EAAE;IAC3C,MAAMmD,MAAM,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;IACnC,IAAI9E,OAAO,CAAC6E,MAAM,EAAE3E,EAAE,EAAEhB,MAAM,CAAC,EAAE;MAC/B,OAAO6F,OAAO,CAACC,OAAO,CAAC;QACrBC,OAAO,EAAE,KAAK;QACdzF,OAAO,EAAEqF,MAAM,CAACrF;MAClB,CAAC,CAAC;IACJ;IACA,IAAIwF,OAAO;IACX,MAAME,OAAO,GAAG,IAAIH,OAAO,CAAEhI,CAAC,IAAMiI,OAAO,GAAGjI,CAAE,CAAC;IACjD,IAAIoI,MAAM;IACV,MAAMC,UAAU,GAAG;MACjBC,SAAS,EAAE,KAAK;MAChBC,WAAW,EAAGC,UAAU,IAAK;QAC3B,IAAIC,IAAI;QACR,MAAMC,CAAC,GAAG,IAAIV,OAAO,CAAEhI,CAAC,IAAMyI,IAAI,GAAGzI,CAAE,CAAC;QACxCiI,OAAO,CAAC;UACNC,OAAO,EAAE,IAAI;UACbzF,OAAO,EAAE+F,UAAU;UACnBG,WAAW;YAAA,IAAAC,IAAA,GAAApG,8KAAA,CAAE,aAAY;cACvBiG,IAAI,CAAC,CAAC;cACN,MAAML,MAAM;YACd,CAAC;YAAA,gBAAAO,YAAA;cAAA,OAAAC,IAAA,CAAAC,KAAA,OAAAC,SAAA;YAAA;UAAA;QACH,CAAC,CAAC;QACF,OAAOJ,CAAC;MACV;IACF,CAAC;IACD,IAAIb,SAAS,KAAK,MAAM,EAAE;MACxBO,MAAM,GAAG,IAAI,CAAClD,OAAO,CAAC/B,EAAE,EAAEhB,MAAM,EAAEkG,UAAU,CAAC;IAC/C,CAAC,MACI;MACH;MACA,MAAMU,cAAc,GAAG,IAAI,CAACxE,KAAK,CAACyE,IAAI,CAAErF,CAAC,IAAKV,OAAO,CAACU,CAAC,EAAER,EAAE,EAAEhB,MAAM,CAAC,CAAC;MACrE,IAAI4G,cAAc,EAAE;QAClBX,MAAM,GAAG,IAAI,CAACjB,KAAK,CAAC4B,cAAc,EAAEE,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEb,UAAU,CAAC,EAAE;UAAER,SAAS,EAAE,MAAM;UAAEsB,gBAAgB,EAAExE;QAAU,CAAC,CAAC,CAAC;MACvI,CAAC,MACI,IAAIkD,SAAS,KAAK,SAAS,EAAE;QAChCO,MAAM,GAAG,IAAI,CAAC7B,IAAI,CAACpD,EAAE,EAAEhB,MAAM,EAAE8G,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEb,UAAU,CAAC,EAAE;UAAEc,gBAAgB,EAAExE;QAAU,CAAC,CAAC,CAAC;MAC/G,CAAC,MACI,IAAIkD,SAAS,KAAK,MAAM,EAAE;QAC7BO,MAAM,GAAG,IAAI,CAAClD,OAAO,CAAC/B,EAAE,EAAEhB,MAAM,EAAE8G,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEb,UAAU,CAAC,EAAE;UAAER,SAAS,EAAE,MAAM;UAAEnD,QAAQ,EAAE,IAAI;UAAEyE,gBAAgB,EAAExE;QAAU,CAAC,CAAC,CAAC;MACrJ;IACF;IACA,OAAOwD,OAAO;EAChB;EACA;AACF;AACA;AACA;AACA;EACQiB,UAAUA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA7G,8KAAA;MACjB,MAAMsF,MAAM,GAAGuB,MAAI,CAACtB,aAAa,CAAC,CAAC;MACnC,IAAID,MAAM,EAAE;QACV,OAAO;UACL3E,EAAE,EAAE2E,MAAM,CAACrF,OAAO,CAAC6G,OAAO;UAC1BnH,MAAM,EAAE2F,MAAM,CAAC3F,MAAM;UACrBM,OAAO,EAAEqF,MAAM,CAACrF;QAClB,CAAC;MACH;MACA,OAAOO,SAAS;IAAC;EACnB;EACA;AACF;AACA;EACQuG,SAASA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAhH,8KAAA;MAChB,OAAOgH,MAAI,CAACzB,aAAa,CAAC,CAAC;IAAC;EAC9B;EACA;AACF;AACA;AACA;AACA;EACQ0B,UAAUA,CAACC,KAAK,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAnH,8KAAA;MACtB,OAAOmH,MAAI,CAACpF,KAAK,CAACmF,KAAK,CAAC;IAAC;EAC3B;EACA;AACF;AACA;AACA;AACA;EACQE,SAASA,CAAC1G,IAAI,EAAE;IAAA,IAAA2G,MAAA;IAAA,OAAArH,8KAAA;MACpB,OAAOqH,MAAI,CAACC,aAAa,CAAC5G,IAAI,CAAC;IAAC;EAClC;EACA;AACF;AACA;AACA;AACA;EACQ6G,WAAWA,CAAC7G,IAAI,EAAE;IAAA,IAAA8G,MAAA;IAAA,OAAAxH,8KAAA;MACtB,OAAOwH,MAAI,CAACC,eAAe,CAAC/G,IAAI,CAAC;IAAC;EACpC;EACAgH,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC3F,KAAK,CAAC+B,MAAM;EAC1B;EACAyB,aAAaA,CAAA,EAAG;IACd,OAAO,IAAI,CAACxD,KAAK,CAAC,IAAI,CAACA,KAAK,CAAC+B,MAAM,GAAG,CAAC,CAAC;EAC1C;EACAwD,aAAaA,CAAC5G,IAAI,GAAG,IAAI,CAAC6E,aAAa,CAAC,CAAC,EAAE;IACzC,OAAO,CAAC,EAAE7E,IAAI,IAAI,IAAI,CAAC+G,eAAe,CAAC/G,IAAI,CAAC,CAAC;EAC/C;EACA+G,eAAeA,CAAC/G,IAAI,GAAG,IAAI,CAAC6E,aAAa,CAAC,CAAC,EAAE;IAC3C,IAAI,CAAC7E,IAAI,EAAE;MACT,OAAOF,SAAS;IAClB;IACA,MAAMuB,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMmF,KAAK,GAAGnF,KAAK,CAAC4F,OAAO,CAACjH,IAAI,CAAC;IACjC,OAAOwG,KAAK,GAAG,CAAC,GAAGnF,KAAK,CAACmF,KAAK,GAAG,CAAC,CAAC,GAAG1G,SAAS;EACjD;EACA;AACF;AACA;AACA;AACA;EACQ8D,SAASA,CAACO,EAAE,EAAEZ,IAAI,EAAE;IAAA,IAAA2D,MAAA;IAAA,OAAA5H,8KAAA;MACxB,IAAI6H,EAAE,EAAEC,EAAE;MACV,IAAIF,MAAI,CAAC/F,eAAe,KAAK,CAACgG,EAAE,GAAGhD,EAAE,CAACb,IAAI,MAAM,IAAI,IAAI6D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,UAAU,CAAC,EAAE;QAC/F,OAAO,KAAK;MACd;MACA,MAAMpC,OAAO,GAAG,IAAIH,OAAO,CAAC,CAACC,OAAO,EAAEuC,MAAM,KAAK;QAC/CnD,EAAE,CAACY,OAAO,GAAGA,OAAO;QACpBZ,EAAE,CAACmD,MAAM,GAAGA,MAAM;MACpB,CAAC,CAAC;MACFnD,EAAE,CAACZ,IAAI,GAAGA,IAAI;MACd;AACJ;AACA;AACA;AACA;AACA;MACI,IAAIY,EAAE,CAACb,IAAI,IAAIa,EAAE,CAACb,IAAI,CAAC8B,SAAS,KAAK,KAAK,IAAI8B,MAAI,CAAChG,SAAS,EAAE;QAC5D,MAAMqG,MAAM,GAAGrF,QAAQ,CAACC,aAAa,CAAC,YAAY,CAAC;QACnD,IAAIoF,MAAM,EAAE;UACV,MAAMC,aAAa,SAASD,MAAM,CAACC,aAAa,CAAC,CAAC;UAClD,IAAIA,aAAa,KAAK,KAAK,EAAE;YAC3B,OAAO,KAAK;UACd;UACA,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;YACrCD,MAAM,CAAClE,IAAI,CAACmE,aAAa,EAAErD,EAAE,CAACb,IAAI,CAACqB,SAAS,IAAI,MAAM,CAAC;YACvD,OAAO,KAAK;UACd;QACF;MACF;MACA;MACA,IAAI,CAAC,CAACyC,EAAE,GAAGjD,EAAE,CAACL,WAAW,MAAM,IAAI,IAAIsD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAChE,MAAM,MAAM,CAAC,EAAE;QAChFe,EAAE,CAACL,WAAW,GAAGhE,SAAS;MAC5B;MACA;MACAoH,MAAI,CAAClG,UAAU,CAACqC,IAAI,CAACc,EAAE,CAAC;MACxB;MACA;MACA+C,MAAI,CAACO,QAAQ,CAAC,CAAC;MACf,OAAOxC,OAAO;IAAC;EACjB;EACAyC,OAAOA,CAACC,MAAM,EAAExD,EAAE,EAAE;IAClB,IAAI,IAAI,CAAC/C,SAAS,EAAE;MAClB,IAAI,CAACwG,SAAS,CAAC,8BAA8B,EAAEzD,EAAE,CAAC;MAClD;IACF;IACA,IAAIA,EAAE,CAACZ,IAAI,EAAE;MACXY,EAAE,CAACZ,IAAI,CAACoE,MAAM,CAACE,YAAY,EAAEF,MAAM,CAACG,kBAAkB,EAAEH,MAAM,CAACI,YAAY,EAAEJ,MAAM,CAACK,WAAW,EAAEL,MAAM,CAAChD,SAAS,CAAC;IACpH;IACAR,EAAE,CAACY,OAAO,CAAC4C,MAAM,CAACE,YAAY,CAAC;IAC/B,IAAI1D,EAAE,CAACb,IAAI,CAAC8B,SAAS,KAAK,KAAK,IAAI,IAAI,CAAClE,SAAS,EAAE;MACjD,MAAMqG,MAAM,GAAGrF,QAAQ,CAACC,aAAa,CAAC,YAAY,CAAC;MACnD,IAAIoF,MAAM,EAAE;QACV,MAAM5C,SAAS,GAAGgD,MAAM,CAAChD,SAAS,KAAK,MAAM,GAAG,MAAM,GAAG,SAAS;QAClE4C,MAAM,CAACU,UAAU,CAACtD,SAAS,CAAC;MAC9B;IACF;EACF;EACAuD,MAAMA,CAACC,YAAY,EAAEhE,EAAE,EAAE;IACvB,IAAI,IAAI,CAAC/C,SAAS,EAAE;MAClB,IAAI,CAACwG,SAAS,CAAC,8BAA8B,EAAEzD,EAAE,CAAC;MAClD;IACF;IACA,IAAI,CAACnD,UAAU,CAACoC,MAAM,GAAG,CAAC;IAC1B,IAAI,CAACwE,SAAS,CAACO,YAAY,EAAEhE,EAAE,CAAC;EAClC;EACAyD,SAASA,CAACO,YAAY,EAAEhE,EAAE,EAAE;IAC1B,IAAIA,EAAE,CAACZ,IAAI,EAAE;MACXY,EAAE,CAACZ,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE4E,YAAY,CAAC;IACrC;IACA,IAAIhE,EAAE,CAACmD,MAAM,IAAI,CAAC,IAAI,CAAClG,SAAS,EAAE;MAChC+C,EAAE,CAACmD,MAAM,CAACa,YAAY,CAAC;IACzB,CAAC,MACI;MACHhE,EAAE,CAACY,OAAO,CAAC,KAAK,CAAC;IACnB;EACF;EACA;AACF;AACA;AACA;AACA;EACE0C,QAAQA,CAAA,EAAG;IACT;IACA;IACA,IAAI,IAAI,CAACtG,eAAe,EAAE;MACxB,OAAO,KAAK;IACd;IACA;IACA,MAAMgD,EAAE,GAAG,IAAI,CAACnD,UAAU,CAACoH,KAAK,CAAC,CAAC;IAClC,IAAI,CAACjE,EAAE,EAAE;MACP,OAAO,KAAK;IACd;IACA,IAAI,CAACkE,aAAa,CAAClE,EAAE,CAAC;IACtB,OAAO,IAAI;EACb;EACA;EACMkE,aAAaA,CAAClE,EAAE,EAAE;IAAA,IAAAmE,MAAA;IAAA,OAAAhJ,8KAAA;MACtB,IAAI;QACF;QACAgJ,MAAI,CAACxH,gBAAgB,CAAC0B,IAAI,CAAC,CAAC;QAC5B8F,MAAI,CAACnH,eAAe,GAAG,IAAI;QAC3BmH,MAAI,CAACC,SAAS,CAACpE,EAAE,CAAC;QAClB,MAAM6D,WAAW,GAAGM,MAAI,CAACzD,aAAa,CAAC,CAAC;QACxC,MAAMkD,YAAY,GAAGO,MAAI,CAACE,eAAe,CAACrE,EAAE,EAAE6D,WAAW,CAAC;QAC1D,IAAI,CAACA,WAAW,IAAI,CAACD,YAAY,EAAE;UACjC,MAAM,IAAIU,KAAK,CAAC,qCAAqC,CAAC;QACxD;QACA,IAAIV,YAAY,IAAIA,YAAY,CAAC7I,KAAK,KAAKP,cAAc,EAAE;UACzD,MAAMoJ,YAAY,CAAC5I,IAAI,CAACmJ,MAAI,CAAClG,EAAE,CAAC;QAClC;QACAkG,MAAI,CAACI,YAAY,CAACX,YAAY,EAAEC,WAAW,EAAE7D,EAAE,CAAC;QAChD;QACA,MAAM2D,kBAAkB,GAAG,CAAC3D,EAAE,CAACwE,0BAA0B,IAAIxE,EAAE,CAACyE,yBAAyB,KAAKb,YAAY,KAAKC,WAAW;QAC1H,IAAIF,kBAAkB,IAAI3D,EAAE,CAACb,IAAI,IAAI0E,WAAW,EAAE;UAChD,MAAMa,eAAe,GAAG1E,EAAE,CAACb,IAAI,CAACqB,SAAS,KAAK,MAAM;UACpD;AACR;AACA;AACA;UACQ,IAAIkE,eAAe,EAAE;YACnB1E,EAAE,CAACb,IAAI,CAAC2C,gBAAgB,GAAG9B,EAAE,CAACb,IAAI,CAAC2C,gBAAgB,KAAK8B,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC9B,gBAAgB,CAAC;UACpJ;UACA+B,WAAW,CAAC/B,gBAAgB,GAAG9B,EAAE,CAACb,IAAI,CAAC2C,gBAAgB;QACzD;QACA,IAAI0B,MAAM;QACV,IAAIG,kBAAkB,EAAE;UACtBH,MAAM,SAASW,MAAI,CAACtK,UAAU,CAAC+J,YAAY,EAAEC,WAAW,EAAE7D,EAAE,CAAC;QAC/D,CAAC,MACI;UACH;UACA;UACA;UACA;UACAwD,MAAM,GAAG;YACPE,YAAY,EAAE,IAAI;YAClBC,kBAAkB,EAAE;UACtB,CAAC;QACH;QACAQ,MAAI,CAACZ,OAAO,CAACC,MAAM,EAAExD,EAAE,CAAC;QACxBmE,MAAI,CAACvH,eAAe,CAACyB,IAAI,CAAC,CAAC;MAC7B,CAAC,CACD,OAAO2F,YAAY,EAAE;QACnBG,MAAI,CAACJ,MAAM,CAACC,YAAY,EAAEhE,EAAE,CAAC;MAC/B;MACAmE,MAAI,CAACnH,eAAe,GAAG,KAAK;MAC5BmH,MAAI,CAACb,QAAQ,CAAC,CAAC;IAAC;EAClB;EACAc,SAASA,CAACpE,EAAE,EAAE;IACZ,IAAIgD,EAAE,EAAEC,EAAE;IACV,IAAI0B,EAAE;IACN,MAAMC,WAAW,GAAG,IAAI,CAAC1H,KAAK,CAAC+B,MAAM;IACrC,CAAC+D,EAAE,GAAGhD,EAAE,CAACb,IAAI,MAAM,IAAI,IAAI6D,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAIhD,EAAE,CAACb,IAAI,GAAG,CAAC,CAAE;IAC9D,CAAC8D,EAAE,GAAG,CAAC0B,EAAE,GAAG3E,EAAE,CAACb,IAAI,EAAE9D,QAAQ,MAAM,IAAI,IAAI4H,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAI0B,EAAE,CAACtJ,QAAQ,GAAG,IAAI,CAACA,QAAS;IAC7F,IAAI2E,EAAE,CAACG,UAAU,KAAKxE,SAAS,EAAE;MAC/BpC,uDAAM,CAACyG,EAAE,CAACC,WAAW,KAAKtE,SAAS,EAAE,8BAA8B,CAAC;MACpEpC,uDAAM,CAACyG,EAAE,CAACE,WAAW,KAAKvE,SAAS,EAAE,8BAA8B,CAAC;MACpE,MAAM0G,KAAK,GAAG,IAAI,CAACnF,KAAK,CAAC4F,OAAO,CAAC9C,EAAE,CAACG,UAAU,CAAC;MAC/C,IAAIkC,KAAK,GAAG,CAAC,EAAE;QACb,MAAM,IAAIiC,KAAK,CAAC,0BAA0B,CAAC;MAC7C;MACAtE,EAAE,CAACC,WAAW,IAAIoC,KAAK;IACzB;IACA,IAAIrC,EAAE,CAACC,WAAW,KAAKtE,SAAS,EAAE;MAChC,IAAIqE,EAAE,CAACC,WAAW,GAAG,CAAC,EAAE;QACtBD,EAAE,CAACC,WAAW,GAAG2E,WAAW,GAAG,CAAC;MAClC;MACA,IAAI5E,EAAE,CAACE,WAAW,GAAG,CAAC,EAAE;QACtBF,EAAE,CAACE,WAAW,GAAG0E,WAAW,GAAG5E,EAAE,CAACC,WAAW;MAC/C;MACAD,EAAE,CAACyE,yBAAyB,GAAGzE,EAAE,CAACE,WAAW,GAAG,CAAC,IAAIF,EAAE,CAACC,WAAW,GAAGD,EAAE,CAACE,WAAW,KAAK0E,WAAW;IACtG;IACA,IAAI5E,EAAE,CAACL,WAAW,EAAE;MAClB;MACA;MACA,IAAIK,EAAE,CAACN,WAAW,GAAG,CAAC,IAAIM,EAAE,CAACN,WAAW,GAAGkF,WAAW,EAAE;QACtD5E,EAAE,CAACN,WAAW,GAAGkF,WAAW;MAC9B;MACA5E,EAAE,CAACwE,0BAA0B,GAAGxE,EAAE,CAACN,WAAW,KAAKkF,WAAW;IAChE;IACA,MAAMjF,WAAW,GAAGK,EAAE,CAACL,WAAW;IAClC,IAAI,CAACA,WAAW,EAAE;MAChB;IACF;IACApG,uDAAM,CAACoG,WAAW,CAACV,MAAM,GAAG,CAAC,EAAE,wBAAwB,CAAC;IACxD,MAAM4F,eAAe,GAAG5I,cAAc,CAAC0D,WAAW,CAAC;IACnD,IAAIkF,eAAe,CAAC5F,MAAM,KAAK,CAAC,EAAE;MAChC,MAAM,IAAIqF,KAAK,CAAC,yBAAyB,CAAC;IAC5C;IACA;IACA,KAAK,MAAMzI,IAAI,IAAIgJ,eAAe,EAAE;MAClChJ,IAAI,CAACR,QAAQ,GAAG2E,EAAE,CAACb,IAAI,CAAC9D,QAAQ;MAChC,MAAMK,GAAG,GAAGG,IAAI,CAACH,GAAG;MACpB,IAAIA,GAAG,IAAIA,GAAG,KAAK,IAAI,EAAE;QACvB,MAAM,IAAI4I,KAAK,CAAC,oCAAoC,CAAC;MACvD;MACA,IAAIzI,IAAI,CAACd,KAAK,KAAKL,oBAAoB,EAAE;QACvC,MAAM,IAAI4J,KAAK,CAAC,qCAAqC,CAAC;MACxD;IACF;IACAtE,EAAE,CAACL,WAAW,GAAGkF,eAAe;EAClC;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACER,eAAeA,CAACrE,EAAE,EAAE6D,WAAW,EAAE;IAC/B;IACA,MAAMlE,WAAW,GAAGK,EAAE,CAACL,WAAW;IAClC,IAAIA,WAAW,KAAKhE,SAAS,EAAE;MAC7B,OAAOgE,WAAW,CAACA,WAAW,CAACV,MAAM,GAAG,CAAC,CAAC;IAC5C;IACA;IACA,MAAMgB,WAAW,GAAGD,EAAE,CAACC,WAAW;IAClC,IAAIA,WAAW,KAAKtE,SAAS,EAAE;MAC7B,MAAMuB,KAAK,GAAG,IAAI,CAACA,KAAK;MACxB,MAAM4H,SAAS,GAAG7E,WAAW,GAAGD,EAAE,CAACE,WAAW;MAC9C,KAAK,IAAI6E,CAAC,GAAG7H,KAAK,CAAC+B,MAAM,GAAG,CAAC,EAAE8F,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC1C,MAAMlJ,IAAI,GAAGqB,KAAK,CAAC6H,CAAC,CAAC;QACrB,IAAI,CAACA,CAAC,GAAG9E,WAAW,IAAI8E,CAAC,IAAID,SAAS,KAAKjJ,IAAI,KAAKgI,WAAW,EAAE;UAC/D,OAAOhI,IAAI;QACb;MACF;IACF;IACA,OAAOF,SAAS;EAClB;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACE4I,YAAYA,CAACX,YAAY,EAAEC,WAAW,EAAE7D,EAAE,EAAE;IAC1C,IAAIgD,EAAE,EAAEC,EAAE,EAAE0B,EAAE;IACdpL,uDAAM,CAACsK,WAAW,IAAID,YAAY,EAAE,4CAA4C,CAAC;IACjFrK,uDAAM,CAACyG,EAAE,CAACY,OAAO,EAAE,uBAAuB,CAAC;IAC3CrH,uDAAM,CAACyG,EAAE,CAACmD,MAAM,EAAE,sBAAsB,CAAC;IACzC;IACA,MAAMhE,IAAI,GAAGa,EAAE,CAACb,IAAI;IACpB,MAAM;MAAEQ,WAAW;MAAEM,WAAW;MAAEC;IAAY,CAAC,GAAGF,EAAE;IACpD;IACA,IAAIgF,YAAY;IAChB;IACA,IAAI/E,WAAW,KAAKtE,SAAS,IAAIuE,WAAW,KAAKvE,SAAS,EAAE;MAC1DpC,uDAAM,CAAC0G,WAAW,IAAI,CAAC,EAAE,iCAAiC,CAAC;MAC3D1G,uDAAM,CAAC2G,WAAW,IAAI,CAAC,EAAE,iCAAiC,CAAC;MAC3D8E,YAAY,GAAG,EAAE;MACjB,KAAK,IAAID,CAAC,GAAG9E,WAAW,EAAE8E,CAAC,GAAG9E,WAAW,GAAGC,WAAW,EAAE6E,CAAC,EAAE,EAAE;QAC5D,MAAMlJ,IAAI,GAAG,IAAI,CAACqB,KAAK,CAAC6H,CAAC,CAAC;QAC1B,IAAIlJ,IAAI,KAAKF,SAAS,IAAIE,IAAI,KAAK+H,YAAY,IAAI/H,IAAI,KAAKgI,WAAW,EAAE;UACvEmB,YAAY,CAAC9F,IAAI,CAACrD,IAAI,CAAC;QACzB;MACF;MACA;MACA,CAACmH,EAAE,GAAG7D,IAAI,CAACqB,SAAS,MAAM,IAAI,IAAIwC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAI7D,IAAI,CAACqB,SAAS,GAAG,MAAO;IAClF;IACA,MAAMyE,aAAa,GAAG,IAAI,CAAC/H,KAAK,CAAC+B,MAAM,IAAI,CAACgE,EAAE,GAAGtD,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACV,MAAM,MAAM,IAAI,IAAIgE,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC,IAAI/C,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAG,CAAC,CAAC;IACvO3G,uDAAM,CAAC0L,aAAa,IAAI,CAAC,EAAE,mCAAmC,CAAC;IAC/D,IAAIA,aAAa,KAAK,CAAC,EAAE;MACvBC,OAAO,CAACC,IAAI,CAAE,sGAAqG,EAAE,IAAI,EAAE,IAAI,CAAClH,EAAE,CAAC;MACnI,MAAM,IAAIqG,KAAK,CAAC,+CAA+C,CAAC;IAClE;IACA;IACA;IACA,IAAI3E,WAAW,EAAE;MACf;MACA,IAAIL,WAAW,GAAGU,EAAE,CAACN,WAAW;MAChC,KAAK,MAAM7D,IAAI,IAAI8D,WAAW,EAAE;QAC9B,IAAI,CAACyF,YAAY,CAACvJ,IAAI,EAAEyD,WAAW,CAAC;QACpCA,WAAW,EAAE;MACf;MACA,IAAIU,EAAE,CAACwE,0BAA0B,EAAE;QACjC;QACA,CAACG,EAAE,GAAGxF,IAAI,CAACqB,SAAS,MAAM,IAAI,IAAImE,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAIxF,IAAI,CAACqB,SAAS,GAAG,SAAU;MACrF;IACF;IACA;IACA;IACA;IACA;IACA;IACA,IAAIwE,YAAY,IAAIA,YAAY,CAAC/F,MAAM,GAAG,CAAC,EAAE;MAC3C,KAAK,MAAMpD,IAAI,IAAImJ,YAAY,EAAE;QAC/BrL,qDAAS,CAACkC,IAAI,CAACT,OAAO,EAAEnB,iDAAoB,CAAC;QAC7CN,qDAAS,CAACkC,IAAI,CAACT,OAAO,EAAEjB,iDAAmB,CAAC;QAC5CR,qDAAS,CAACkC,IAAI,CAACT,OAAO,EAAErB,iDAAqB,CAAC;MAChD;MACA;MACA,KAAK,MAAM8B,IAAI,IAAImJ,YAAY,EAAE;QAC/B,IAAI,CAACK,WAAW,CAACxJ,IAAI,CAAC;MACxB;IACF;EACF;EACMhC,UAAUA,CAAC+J,YAAY,EAAEC,WAAW,EAAE7D,EAAE,EAAE;IAAA,IAAAsF,OAAA;IAAA,OAAAnK,8KAAA;MAC9C;MACA;MACA,MAAMgE,IAAI,GAAGa,EAAE,CAACb,IAAI;MACpB,MAAMoG,gBAAgB,GAAGpG,IAAI,CAACqG,iBAAiB,GAC1CC,GAAG,IAAK;QACT;AACR;AACA;AACA;AACA;AACA;AACA;QACQ,IAAIA,GAAG,KAAK9J,SAAS,IAAI,CAAC2J,OAAI,CAACxI,4BAA4B,EAAE;UAC3DwI,OAAI,CAACxI,4BAA4B,GAAG,IAAI;UACxC2I,GAAG,CAACC,QAAQ,CAAC,MAAM;YACjBJ,OAAI,CAACxI,4BAA4B,GAAG,KAAK;UAC3C,CAAC,EAAE;YAAE6I,eAAe,EAAE;UAAK,CAAC,CAAC;UAC7B;AACV;AACA;AACA;AACA;AACA;UACUF,GAAG,CAACG,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC1B,CAAC,MACI;UACHN,OAAI,CAACO,KAAK,GAAGJ,GAAG;QAClB;MACF,CAAC,GACC9J,SAAS;MACb,MAAMwC,IAAI,GAAG/D,4DAAU,CAACkL,OAAI,CAAC;MAC7B,MAAMnE,UAAU,GAAGyC,YAAY,CAACxI,OAAO;MACvC,MAAM0K,SAAS,GAAGjC,WAAW,IAAIA,WAAW,CAACzI,OAAO;MACpD,MAAM2K,aAAa,GAAGnE,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAE1D,IAAI;QAAE6H,UAAU,EAAEV,OAAI,CAAC7C,aAAa,CAACmB,YAAY,CAAC;QAAEqC,MAAM,EAAEX,OAAI,CAACrH,EAAE;QAAEsH,gBAAgB;QAAElI,QAAQ,EAAEiI,OAAI,CAACjI,QAAQ,IAAIhD,wDAAM,CAAC+D,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC;QAAE+C,UAAU;QACnN2E;MAAU,CAAC,EAAE3G,IAAI,CAAC,EAAE;QAAE2C,gBAAgB,EAAE3C,IAAI,CAAC2C,gBAAgB,IAAIwD,OAAI,CAAChI,SAAS,IAAIjD,wDAAM,CAAC6L,GAAG,CAAC,cAAc;MAAE,CAAC,CAAC;MAClH,MAAM;QAAExC;MAAa,CAAC,SAAS7J,qDAAU,CAACkM,aAAa,CAAC;MACxD,OAAOT,OAAI,CAACa,gBAAgB,CAACzC,YAAY,EAAEE,YAAY,EAAEC,WAAW,EAAE1E,IAAI,CAAC;IAAC;EAC9E;EACAgH,gBAAgBA,CAACzC,YAAY,EAAEE,YAAY,EAAEC,WAAW,EAAE1E,IAAI,EAAE;IAC9D;AACJ;AACA;AACA;IACI,MAAMiH,UAAU,GAAG1C,YAAY,GAAGE,YAAY,GAAGC,WAAW;IAC5D,IAAIuC,UAAU,EAAE;MACd,IAAI,CAACC,oBAAoB,CAACD,UAAU,CAAC;IACvC;IACA,OAAO;MACL1C,YAAY;MACZC,kBAAkB,EAAE,IAAI;MACxBC,YAAY;MACZC,WAAW;MACXrD,SAAS,EAAErB,IAAI,CAACqB;IAClB,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE4E,YAAYA,CAACvJ,IAAI,EAAEwG,KAAK,EAAE;IACxB,MAAMnF,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMoJ,aAAa,GAAGpJ,KAAK,CAAC4F,OAAO,CAACjH,IAAI,CAAC;IACzC,IAAIyK,aAAa,GAAG,CAAC,CAAC,EAAE;MACtB/M,uDAAM,CAACsC,IAAI,CAACH,GAAG,KAAK,IAAI,EAAE,6BAA6B,CAAC;MACxD;MACAwB,KAAK,CAACqJ,MAAM,CAACD,aAAa,EAAE,CAAC,CAAC;MAC9B;MACApJ,KAAK,CAACqJ,MAAM,CAAClE,KAAK,EAAE,CAAC,EAAExG,IAAI,CAAC;IAC9B,CAAC,MACI;MACHtC,uDAAM,CAAC,CAACsC,IAAI,CAACH,GAAG,EAAE,aAAa,CAAC;MAChC;MACA;MACAG,IAAI,CAACH,GAAG,GAAG,IAAI;MACfwB,KAAK,CAACqJ,MAAM,CAAClE,KAAK,EAAE,CAAC,EAAExG,IAAI,CAAC;IAC9B;EACF;EACA;AACF;AACA;AACA;AACA;EACEsE,UAAUA,CAACtE,IAAI,EAAE;IACftC,uDAAM,CAACsC,IAAI,CAACd,KAAK,KAAKN,mBAAmB,IAAIoB,IAAI,CAACd,KAAK,KAAKL,oBAAoB,EAAE,0CAA0C,CAAC;IAC7H,MAAMwC,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMmF,KAAK,GAAGnF,KAAK,CAAC4F,OAAO,CAACjH,IAAI,CAAC;IACjCtC,uDAAM,CAAC8I,KAAK,GAAG,CAAC,CAAC,EAAE,gCAAgC,CAAC;IACpD,IAAIA,KAAK,IAAI,CAAC,EAAE;MACdnF,KAAK,CAACqJ,MAAM,CAAClE,KAAK,EAAE,CAAC,CAAC;IACxB;EACF;EACAgD,WAAWA,CAACxJ,IAAI,EAAE;IAChBA,IAAI,CAACP,QAAQ,CAAC,CAAC;IACf,IAAI,CAAC6E,UAAU,CAACtE,IAAI,CAAC;EACvB;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACEwK,oBAAoBA,CAACD,UAAU,EAAE;IAC/B;IACA;IACA;IACA,IAAI,IAAI,CAACnJ,SAAS,EAAE;MAClB;IACF;IACA,MAAMC,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMsJ,eAAe,GAAGtJ,KAAK,CAAC4F,OAAO,CAACsD,UAAU,CAAC;IACjD,KAAK,IAAIrB,CAAC,GAAG7H,KAAK,CAAC+B,MAAM,GAAG,CAAC,EAAE8F,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1C,MAAMlJ,IAAI,GAAGqB,KAAK,CAAC6H,CAAC,CAAC;MACrB;AACN;AACA;AACA;AACA;AACA;AACA;AACA;MACM,MAAM3J,OAAO,GAAGS,IAAI,CAACT,OAAO;MAC5B,IAAIA,OAAO,EAAE;QACX,IAAI2J,CAAC,GAAGyB,eAAe,EAAE;UACvB;UACA;UACA7M,qDAAS,CAACyB,OAAO,EAAErB,iDAAqB,CAAC;UACzC,IAAI,CAACsL,WAAW,CAACxJ,IAAI,CAAC;QACxB,CAAC,MACI,IAAIkJ,CAAC,GAAGyB,eAAe,EAAE;UAC5B;UACA;UACA1M,qDAAa,CAACsB,OAAO,EAAE,IAAI,CAAC;QAC9B;MACF;IACF;EACF;EACAqD,QAAQA,CAAA,EAAG;IACT,OAAQ,CAAC,IAAI,CAAC3B,4BAA4B,IACxC,CAAC,CAAC,IAAI,CAACM,YAAY,IACnB,CAAC,IAAI,CAACJ,eAAe,IACrB,IAAI,CAACH,UAAU,CAACoC,MAAM,KAAK,CAAC,IAC5B,IAAI,CAACwD,aAAa,CAAC,CAAC;EACxB;EACA9D,OAAOA,CAAA,EAAG;IACR,IAAI,CAAC7B,4BAA4B,GAAG,IAAI;IACxC,IAAI,CAAC8C,GAAG,CAAC;MAAEY,SAAS,EAAE,MAAM;MAAEgF,iBAAiB,EAAE;IAAK,CAAC,CAAC;EAC1D;EACA5G,MAAMA,CAAC6H,SAAS,EAAE;IAChB,IAAI,IAAI,CAACZ,KAAK,EAAE;MACd,IAAI,CAACA,KAAK,CAACa,YAAY,CAACD,SAAS,CAAC;IACpC;EACF;EACA5H,KAAKA,CAAC8H,cAAc,EAAEF,SAAS,EAAEG,GAAG,EAAE;IACpC,IAAI,IAAI,CAACf,KAAK,EAAE;MACd,IAAI,CAACA,KAAK,CAACH,QAAQ,CAAC,MAAM;QACxB,IAAI,CAAC5I,4BAA4B,GAAG,KAAK;MAC3C,CAAC,EAAE;QAAE6I,eAAe,EAAE;MAAK,CAAC,CAAC;MAC7B;MACA,IAAIkB,YAAY,GAAGF,cAAc,GAAG,CAAC,KAAK,GAAG,KAAK;MAClD;AACN;AACA;AACA;AACA;AACA;AACA;AACA;MACM,IAAI,CAACA,cAAc,EAAE;QACnB,IAAI,CAACd,KAAK,CAACiB,MAAM,CAAC,gCAAgC,CAAC;QACnDD,YAAY,IAAIxN,4DAAuB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEoN,SAAS,CAAC,CAAC,CAAC,CAAC;MAC7F,CAAC,MACI;QACHI,YAAY,IAAIxN,4DAAuB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEoN,SAAS,CAAC,CAAC,CAAC,CAAC;MAC7F;MACA,IAAI,CAACZ,KAAK,CAACD,WAAW,CAACe,cAAc,GAAG,CAAC,GAAG,CAAC,EAAEE,YAAY,EAAED,GAAG,CAAC;IACnE,CAAC,MACI;MACH,IAAI,CAAC9J,4BAA4B,GAAG,KAAK;IAC3C;EACF;EACAiK,MAAMA,CAAA,EAAG;IACP,OAAOhO,qDAAC,CAAC,MAAM,EAAE,IAAI,CAAC;EACxB;EACA,IAAIkF,EAAEA,CAAA,EAAG;IAAE,OAAOhF,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW+N,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC7B,cAAc,EAAE,CAAC,qBAAqB,CAAC;MACvC,MAAM,EAAE,CAAC,aAAa;IACxB,CAAC;EAAE;AACL,CAAC;AACDxK,GAAG,CAACyK,KAAK,GAAG1K,MAAM;AAElB,MAAM2K,OAAO,GAAGA,CAACjJ,EAAE,EAAEkJ,eAAe,EAAEtM,SAAS,EAAEuB,cAAc,EAAEgL,eAAe,KAAK;EACnF,MAAM1L,GAAG,GAAGuC,EAAE,CAACC,OAAO,CAAC,SAAS,CAAC;EACjC,IAAIxC,GAAG,EAAE;IACP,IAAIyL,eAAe,KAAK,SAAS,EAAE;MACjC,IAAItM,SAAS,KAAKc,SAAS,EAAE;QAC3B,OAAOD,GAAG,CAACwD,IAAI,CAACrE,SAAS,EAAEuB,cAAc,EAAE;UAAE8G,UAAU,EAAE,IAAI;UAAEpB,gBAAgB,EAAEsF;QAAgB,CAAC,CAAC;MACrG;IACF,CAAC,MACI,IAAID,eAAe,KAAK,MAAM,EAAE;MACnC,IAAItM,SAAS,KAAKc,SAAS,EAAE;QAC3B,OAAOD,GAAG,CAACmC,OAAO,CAAChD,SAAS,EAAEuB,cAAc,EAAE;UAAE8G,UAAU,EAAE,IAAI;UAAEpB,gBAAgB,EAAEsF;QAAgB,CAAC,CAAC;MACxG;IACF,CAAC,MACI,IAAID,eAAe,KAAK,MAAM,EAAE;MACnC,OAAOzL,GAAG,CAACkE,GAAG,CAAC;QAAEsD,UAAU,EAAE,IAAI;QAAEpB,gBAAgB,EAAEsF;MAAgB,CAAC,CAAC;IACzE;EACF;EACA,OAAOzG,OAAO,CAACC,OAAO,CAAC,KAAK,CAAC;AAC/B,CAAC;AAED,MAAMyG,OAAO,GAAG,MAAM;EACpBzM,WAAWA,CAAC6B,OAAO,EAAE;IACnB7D,qDAAgB,CAAC,IAAI,EAAE6D,OAAO,CAAC;IAC/B,IAAI,CAAC6K,OAAO,GAAG,MAAM;MACnB,OAAOJ,OAAO,CAAC,IAAI,CAACjJ,EAAE,EAAE,IAAI,CAACkJ,eAAe,EAAE,IAAI,CAACtM,SAAS,EAAE,IAAI,CAACuB,cAAc,EAAE,IAAI,CAACgL,eAAe,CAAC;IAC1G,CAAC;IACD,IAAI,CAACvM,SAAS,GAAGc,SAAS;IAC1B,IAAI,CAACS,cAAc,GAAGT,SAAS;IAC/B,IAAI,CAACwL,eAAe,GAAG,SAAS;IAChC,IAAI,CAACC,eAAe,GAAGzL,SAAS;EAClC;EACAoL,MAAMA,CAAA,EAAG;IACP,OAAOhO,qDAAC,CAACI,iDAAI,EAAE;MAAEmO,OAAO,EAAE,IAAI,CAACA;IAAQ,CAAC,CAAC;EAC3C;EACA,IAAIrJ,EAAEA,CAAA,EAAG;IAAE,OAAOhF,qDAAU,CAAC,IAAI,CAAC;EAAE;AACtC,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-nav_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, f as getElement, H as Host } from './index-2d388930.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-66542bc5.js';\nimport { o as assert, s as shallowEqualStringMap } from './helpers-3379ba19.js';\nimport { l as lifecycle, t as transition, s as set<PERSON>age<PERSON><PERSON><PERSON>, d as LIFECYCLE_WILL_UNLOAD, b as LIFECYCLE_WILL_LEAVE, c as LIFECYCLE_DID_LEAVE } from './index-87684bba.js';\nimport { b as getIonMode, c as config } from './ionic-global-b3fc28dd.js';\nimport { a as attachComponent } from './framework-delegate-aa433dea.js';\n\nconst VIEW_STATE_NEW = 1;\nconst VIEW_STATE_ATTACHED = 2;\nconst VIEW_STATE_DESTROYED = 3;\n// TODO(FW-2832): types\nclass ViewController {\n  constructor(component, params) {\n    this.component = component;\n    this.params = params;\n    this.state = VIEW_STATE_NEW;\n  }\n  async init(container) {\n    this.state = VIEW_STATE_ATTACHED;\n    if (!this.element) {\n      const component = this.component;\n      this.element = await attachComponent(this.delegate, container, component, ['ion-page', 'ion-page-invisible'], this.params);\n    }\n  }\n  /**\n   * DOM WRITE\n   */\n  _destroy() {\n    assert(this.state !== VIEW_STATE_DESTROYED, 'view state must be ATTACHED');\n    const element = this.element;\n    if (element) {\n      if (this.delegate) {\n        this.delegate.removeViewFromDom(element.parentElement, element);\n      }\n      else {\n        element.remove();\n      }\n    }\n    this.nav = undefined;\n    this.state = VIEW_STATE_DESTROYED;\n  }\n}\nconst matches = (view, id, params) => {\n  if (!view) {\n    return false;\n  }\n  if (view.component !== id) {\n    return false;\n  }\n  return shallowEqualStringMap(view.params, params);\n};\nconst convertToView = (page, params) => {\n  if (!page) {\n    return null;\n  }\n  if (page instanceof ViewController) {\n    return page;\n  }\n  return new ViewController(page, params);\n};\nconst convertToViews = (pages) => {\n  return pages\n    .map((page) => {\n    if (page instanceof ViewController) {\n      return page;\n    }\n    if ('component' in page) {\n      return convertToView(page.component, page.componentProps === null ? undefined : page.componentProps);\n    }\n    return convertToView(page, undefined);\n  })\n    .filter((v) => v !== null);\n};\n\nconst navCss = \":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;overflow:hidden;z-index:0}\";\n\nconst Nav = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionNavWillLoad = createEvent(this, \"ionNavWillLoad\", 7);\n    this.ionNavWillChange = createEvent(this, \"ionNavWillChange\", 3);\n    this.ionNavDidChange = createEvent(this, \"ionNavDidChange\", 3);\n    this.transInstr = [];\n    this.gestureOrAnimationInProgress = false;\n    this.useRouter = false;\n    this.isTransitioning = false;\n    this.destroyed = false;\n    this.views = [];\n    this.didLoad = false;\n    this.delegate = undefined;\n    this.swipeGesture = undefined;\n    this.animated = true;\n    this.animation = undefined;\n    this.rootParams = undefined;\n    this.root = undefined;\n  }\n  swipeGestureChanged() {\n    if (this.gesture) {\n      this.gesture.enable(this.swipeGesture === true);\n    }\n  }\n  rootChanged() {\n    if (this.root === undefined) {\n      return;\n    }\n    if (this.didLoad === false) {\n      /**\n       * If the component has not loaded yet, we can skip setting up the root component.\n       * It will be called when `componentDidLoad` fires.\n       */\n      return;\n    }\n    if (!this.useRouter) {\n      if (this.root !== undefined) {\n        this.setRoot(this.root, this.rootParams);\n      }\n    }\n  }\n  componentWillLoad() {\n    this.useRouter = document.querySelector('ion-router') !== null && this.el.closest('[no-router]') === null;\n    if (this.swipeGesture === undefined) {\n      const mode = getIonMode(this);\n      this.swipeGesture = config.getBoolean('swipeBackEnabled', mode === 'ios');\n    }\n    this.ionNavWillLoad.emit();\n  }\n  async componentDidLoad() {\n    // We want to set this flag before any watch callbacks are manually called\n    this.didLoad = true;\n    this.rootChanged();\n    this.gesture = (await import('./swipe-back-6d4c0a4e.js')).createSwipeBackGesture(this.el, this.canStart.bind(this), this.onStart.bind(this), this.onMove.bind(this), this.onEnd.bind(this));\n    this.swipeGestureChanged();\n  }\n  connectedCallback() {\n    this.destroyed = false;\n  }\n  disconnectedCallback() {\n    for (const view of this.views) {\n      lifecycle(view.element, LIFECYCLE_WILL_UNLOAD);\n      view._destroy();\n    }\n    // Release swipe back gesture and transition.\n    if (this.gesture) {\n      this.gesture.destroy();\n      this.gesture = undefined;\n    }\n    this.transInstr.length = 0;\n    this.views.length = 0;\n    this.destroyed = true;\n  }\n  /**\n   * Push a new component onto the current navigation stack. Pass any additional\n   * information along as an object. This additional information is accessible\n   * through NavParams.\n   *\n   * @param component The component to push onto the navigation stack.\n   * @param componentProps Any properties of the component.\n   * @param opts The navigation options.\n   * @param done The transition complete function.\n   */\n  push(component, componentProps, opts, done) {\n    return this.insert(-1, component, componentProps, opts, done);\n  }\n  /**\n   * Inserts a component into the navigation stack at the specified index.\n   * This is useful to add a component at any point in the navigation stack.\n   *\n   * @param insertIndex The index to insert the component at in the stack.\n   * @param component The component to insert into the navigation stack.\n   * @param componentProps Any properties of the component.\n   * @param opts The navigation options.\n   * @param done The transition complete function.\n   */\n  insert(insertIndex, component, componentProps, opts, done) {\n    return this.insertPages(insertIndex, [{ component, componentProps }], opts, done);\n  }\n  /**\n   * Inserts an array of components into the navigation stack at the specified index.\n   * The last component in the array will become instantiated as a view, and animate\n   * in to become the active view.\n   *\n   * @param insertIndex The index to insert the components at in the stack.\n   * @param insertComponents The components to insert into the navigation stack.\n   * @param opts The navigation options.\n   * @param done The transition complete function.\n   */\n  insertPages(insertIndex, insertComponents, opts, done) {\n    return this.queueTrns({\n      insertStart: insertIndex,\n      insertViews: insertComponents,\n      opts,\n    }, done);\n  }\n  /**\n   * Pop a component off of the navigation stack. Navigates back from the current\n   * component.\n   *\n   * @param opts The navigation options.\n   * @param done The transition complete function.\n   */\n  pop(opts, done) {\n    return this.removeIndex(-1, 1, opts, done);\n  }\n  /**\n   * Pop to a specific index in the navigation stack.\n   *\n   * @param indexOrViewCtrl The index or view controller to pop to.\n   * @param opts The navigation options.\n   * @param done The transition complete function.\n   */\n  popTo(indexOrViewCtrl, opts, done) {\n    const ti = {\n      removeStart: -1,\n      removeCount: -1,\n      opts,\n    };\n    if (typeof indexOrViewCtrl === 'object' && indexOrViewCtrl.component) {\n      ti.removeView = indexOrViewCtrl;\n      ti.removeStart = 1;\n    }\n    else if (typeof indexOrViewCtrl === 'number') {\n      ti.removeStart = indexOrViewCtrl + 1;\n    }\n    return this.queueTrns(ti, done);\n  }\n  /**\n   * Navigate back to the root of the stack, no matter how far back that is.\n   *\n   * @param opts The navigation options.\n   * @param done The transition complete function.\n   */\n  popToRoot(opts, done) {\n    return this.removeIndex(1, -1, opts, done);\n  }\n  /**\n   * Removes a component from the navigation stack at the specified index.\n   *\n   * @param startIndex The number to begin removal at.\n   * @param removeCount The number of components to remove.\n   * @param opts The navigation options.\n   * @param done The transition complete function.\n   */\n  removeIndex(startIndex, removeCount = 1, opts, done) {\n    return this.queueTrns({\n      removeStart: startIndex,\n      removeCount,\n      opts,\n    }, done);\n  }\n  /**\n   * Set the root for the current navigation stack to a component.\n   *\n   * @param component The component to set as the root of the navigation stack.\n   * @param componentProps Any properties of the component.\n   * @param opts The navigation options.\n   * @param done The transition complete function.\n   */\n  setRoot(component, componentProps, opts, done) {\n    return this.setPages([{ component, componentProps }], opts, done);\n  }\n  /**\n   * Set the views of the current navigation stack and navigate to the last view.\n   * By default animations are disabled, but they can be enabled by passing options\n   * to the navigation controller. Navigation parameters can also be passed to the\n   * individual pages in the array.\n   *\n   * @param views The list of views to set as the navigation stack.\n   * @param opts The navigation options.\n   * @param done The transition complete function.\n   */\n  setPages(views, opts, done) {\n    opts !== null && opts !== void 0 ? opts : (opts = {});\n    // if animation wasn't set to true then default it to NOT animate\n    if (opts.animated !== true) {\n      opts.animated = false;\n    }\n    return this.queueTrns({\n      insertStart: 0,\n      insertViews: views,\n      removeStart: 0,\n      removeCount: -1,\n      opts,\n    }, done);\n  }\n  /**\n   * Called by the router to update the view.\n   *\n   * @param id The component tag.\n   * @param params The component params.\n   * @param direction A direction hint.\n   * @param animation an AnimationBuilder.\n   *\n   * @return the status.\n   * @internal\n   */\n  setRouteId(id, params, direction, animation) {\n    const active = this.getActiveSync();\n    if (matches(active, id, params)) {\n      return Promise.resolve({\n        changed: false,\n        element: active.element,\n      });\n    }\n    let resolve;\n    const promise = new Promise((r) => (resolve = r));\n    let finish;\n    const commonOpts = {\n      updateURL: false,\n      viewIsReady: (enteringEl) => {\n        let mark;\n        const p = new Promise((r) => (mark = r));\n        resolve({\n          changed: true,\n          element: enteringEl,\n          markVisible: async () => {\n            mark();\n            await finish;\n          },\n        });\n        return p;\n      },\n    };\n    if (direction === 'root') {\n      finish = this.setRoot(id, params, commonOpts);\n    }\n    else {\n      // Look for a view matching the target in the view stack.\n      const viewController = this.views.find((v) => matches(v, id, params));\n      if (viewController) {\n        finish = this.popTo(viewController, Object.assign(Object.assign({}, commonOpts), { direction: 'back', animationBuilder: animation }));\n      }\n      else if (direction === 'forward') {\n        finish = this.push(id, params, Object.assign(Object.assign({}, commonOpts), { animationBuilder: animation }));\n      }\n      else if (direction === 'back') {\n        finish = this.setRoot(id, params, Object.assign(Object.assign({}, commonOpts), { direction: 'back', animated: true, animationBuilder: animation }));\n      }\n    }\n    return promise;\n  }\n  /**\n   * Called by <ion-router> to retrieve the current component.\n   *\n   * @internal\n   */\n  async getRouteId() {\n    const active = this.getActiveSync();\n    if (active) {\n      return {\n        id: active.element.tagName,\n        params: active.params,\n        element: active.element,\n      };\n    }\n    return undefined;\n  }\n  /**\n   * Get the active view.\n   */\n  async getActive() {\n    return this.getActiveSync();\n  }\n  /**\n   * Get the view at the specified index.\n   *\n   * @param index The index of the view.\n   */\n  async getByIndex(index) {\n    return this.views[index];\n  }\n  /**\n   * Returns `true` if the current view can go back.\n   *\n   * @param view The view to check.\n   */\n  async canGoBack(view) {\n    return this.canGoBackSync(view);\n  }\n  /**\n   * Get the previous view.\n   *\n   * @param view The view to get.\n   */\n  async getPrevious(view) {\n    return this.getPreviousSync(view);\n  }\n  getLength() {\n    return this.views.length;\n  }\n  getActiveSync() {\n    return this.views[this.views.length - 1];\n  }\n  canGoBackSync(view = this.getActiveSync()) {\n    return !!(view && this.getPreviousSync(view));\n  }\n  getPreviousSync(view = this.getActiveSync()) {\n    if (!view) {\n      return undefined;\n    }\n    const views = this.views;\n    const index = views.indexOf(view);\n    return index > 0 ? views[index - 1] : undefined;\n  }\n  /**\n   * Adds a navigation stack change to the queue and schedules it to run.\n   *\n   * @returns Whether the transition succeeds.\n   */\n  async queueTrns(ti, done) {\n    var _a, _b;\n    if (this.isTransitioning && ((_a = ti.opts) === null || _a === void 0 ? void 0 : _a.skipIfBusy)) {\n      return false;\n    }\n    const promise = new Promise((resolve, reject) => {\n      ti.resolve = resolve;\n      ti.reject = reject;\n    });\n    ti.done = done;\n    /**\n     * If using router, check to see if navigation hooks\n     * will allow us to perform this transition. This\n     * is required in order for hooks to work with\n     * the ion-back-button or swipe to go back.\n     */\n    if (ti.opts && ti.opts.updateURL !== false && this.useRouter) {\n      const router = document.querySelector('ion-router');\n      if (router) {\n        const canTransition = await router.canTransition();\n        if (canTransition === false) {\n          return false;\n        }\n        if (typeof canTransition === 'string') {\n          router.push(canTransition, ti.opts.direction || 'back');\n          return false;\n        }\n      }\n    }\n    // Normalize empty\n    if (((_b = ti.insertViews) === null || _b === void 0 ? void 0 : _b.length) === 0) {\n      ti.insertViews = undefined;\n    }\n    // Enqueue transition instruction\n    this.transInstr.push(ti);\n    // if there isn't a transition already happening\n    // then this will kick off this transition\n    this.nextTrns();\n    return promise;\n  }\n  success(result, ti) {\n    if (this.destroyed) {\n      this.fireError('nav controller was destroyed', ti);\n      return;\n    }\n    if (ti.done) {\n      ti.done(result.hasCompleted, result.requiresTransition, result.enteringView, result.leavingView, result.direction);\n    }\n    ti.resolve(result.hasCompleted);\n    if (ti.opts.updateURL !== false && this.useRouter) {\n      const router = document.querySelector('ion-router');\n      if (router) {\n        const direction = result.direction === 'back' ? 'back' : 'forward';\n        router.navChanged(direction);\n      }\n    }\n  }\n  failed(rejectReason, ti) {\n    if (this.destroyed) {\n      this.fireError('nav controller was destroyed', ti);\n      return;\n    }\n    this.transInstr.length = 0;\n    this.fireError(rejectReason, ti);\n  }\n  fireError(rejectReason, ti) {\n    if (ti.done) {\n      ti.done(false, false, rejectReason);\n    }\n    if (ti.reject && !this.destroyed) {\n      ti.reject(rejectReason);\n    }\n    else {\n      ti.resolve(false);\n    }\n  }\n  /**\n   * Consumes the next transition in the queue.\n   *\n   * @returns whether the transition is executed.\n   */\n  nextTrns() {\n    // this is the framework's bread 'n butta function\n    // only one transition is allowed at any given time\n    if (this.isTransitioning) {\n      return false;\n    }\n    // there is no transition happening right now, executes the next instructions.\n    const ti = this.transInstr.shift();\n    if (!ti) {\n      return false;\n    }\n    this.runTransition(ti);\n    return true;\n  }\n  /** Executes all the transition instruction from the queue. */\n  async runTransition(ti) {\n    try {\n      // set that this nav is actively transitioning\n      this.ionNavWillChange.emit();\n      this.isTransitioning = true;\n      this.prepareTI(ti);\n      const leavingView = this.getActiveSync();\n      const enteringView = this.getEnteringView(ti, leavingView);\n      if (!leavingView && !enteringView) {\n        throw new Error('no views in the stack to be removed');\n      }\n      if (enteringView && enteringView.state === VIEW_STATE_NEW) {\n        await enteringView.init(this.el);\n      }\n      this.postViewInit(enteringView, leavingView, ti);\n      // Needs transition?\n      const requiresTransition = (ti.enteringRequiresTransition || ti.leavingRequiresTransition) && enteringView !== leavingView;\n      if (requiresTransition && ti.opts && leavingView) {\n        const isBackDirection = ti.opts.direction === 'back';\n        /**\n         * If heading back, use the entering page's animation\n         * unless otherwise specified by the developer.\n         */\n        if (isBackDirection) {\n          ti.opts.animationBuilder = ti.opts.animationBuilder || (enteringView === null || enteringView === void 0 ? void 0 : enteringView.animationBuilder);\n        }\n        leavingView.animationBuilder = ti.opts.animationBuilder;\n      }\n      let result;\n      if (requiresTransition) {\n        result = await this.transition(enteringView, leavingView, ti);\n      }\n      else {\n        // transition is not required, so we are already done!\n        // they're inserting/removing the views somewhere in the middle or\n        // beginning, so visually nothing needs to animate/transition\n        // resolve immediately because there's no animation that's happening\n        result = {\n          hasCompleted: true,\n          requiresTransition: false,\n        };\n      }\n      this.success(result, ti);\n      this.ionNavDidChange.emit();\n    }\n    catch (rejectReason) {\n      this.failed(rejectReason, ti);\n    }\n    this.isTransitioning = false;\n    this.nextTrns();\n  }\n  prepareTI(ti) {\n    var _a, _b;\n    var _c;\n    const viewsLength = this.views.length;\n    (_a = ti.opts) !== null && _a !== void 0 ? _a : (ti.opts = {});\n    (_b = (_c = ti.opts).delegate) !== null && _b !== void 0 ? _b : (_c.delegate = this.delegate);\n    if (ti.removeView !== undefined) {\n      assert(ti.removeStart !== undefined, 'removeView needs removeStart');\n      assert(ti.removeCount !== undefined, 'removeView needs removeCount');\n      const index = this.views.indexOf(ti.removeView);\n      if (index < 0) {\n        throw new Error('removeView was not found');\n      }\n      ti.removeStart += index;\n    }\n    if (ti.removeStart !== undefined) {\n      if (ti.removeStart < 0) {\n        ti.removeStart = viewsLength - 1;\n      }\n      if (ti.removeCount < 0) {\n        ti.removeCount = viewsLength - ti.removeStart;\n      }\n      ti.leavingRequiresTransition = ti.removeCount > 0 && ti.removeStart + ti.removeCount === viewsLength;\n    }\n    if (ti.insertViews) {\n      // allow -1 to be passed in to auto push it on the end\n      // and clean up the index if it's larger then the size of the stack\n      if (ti.insertStart < 0 || ti.insertStart > viewsLength) {\n        ti.insertStart = viewsLength;\n      }\n      ti.enteringRequiresTransition = ti.insertStart === viewsLength;\n    }\n    const insertViews = ti.insertViews;\n    if (!insertViews) {\n      return;\n    }\n    assert(insertViews.length > 0, 'length can not be zero');\n    const viewControllers = convertToViews(insertViews);\n    if (viewControllers.length === 0) {\n      throw new Error('invalid views to insert');\n    }\n    // Check all the inserted view are correct\n    for (const view of viewControllers) {\n      view.delegate = ti.opts.delegate;\n      const nav = view.nav;\n      if (nav && nav !== this) {\n        throw new Error('inserted view was already inserted');\n      }\n      if (view.state === VIEW_STATE_DESTROYED) {\n        throw new Error('inserted view was already destroyed');\n      }\n    }\n    ti.insertViews = viewControllers;\n  }\n  /**\n   * Returns the view that will be entered considering the transition instructions.\n   *\n   * @param ti The instructions.\n   * @param leavingView The view being left or undefined if none.\n   *\n   * @returns The view that will be entered, undefined if none.\n   */\n  getEnteringView(ti, leavingView) {\n    // The last inserted view will be entered when view are inserted.\n    const insertViews = ti.insertViews;\n    if (insertViews !== undefined) {\n      return insertViews[insertViews.length - 1];\n    }\n    // When views are deleted, we will enter the last view that is not removed and not the view being left.\n    const removeStart = ti.removeStart;\n    if (removeStart !== undefined) {\n      const views = this.views;\n      const removeEnd = removeStart + ti.removeCount;\n      for (let i = views.length - 1; i >= 0; i--) {\n        const view = views[i];\n        if ((i < removeStart || i >= removeEnd) && view !== leavingView) {\n          return view;\n        }\n      }\n    }\n    return undefined;\n  }\n  /**\n   * Adds and Removes the views from the navigation stack.\n   *\n   * @param enteringView The view being entered.\n   * @param leavingView The view being left.\n   * @param ti The instructions.\n   */\n  postViewInit(enteringView, leavingView, ti) {\n    var _a, _b, _c;\n    assert(leavingView || enteringView, 'Both leavingView and enteringView are null');\n    assert(ti.resolve, 'resolve must be valid');\n    assert(ti.reject, 'reject must be valid');\n    // Compute the views to remove.\n    const opts = ti.opts;\n    const { insertViews, removeStart, removeCount } = ti;\n    /** Records the view to destroy */\n    let destroyQueue;\n    // there are views to remove\n    if (removeStart !== undefined && removeCount !== undefined) {\n      assert(removeStart >= 0, 'removeStart can not be negative');\n      assert(removeCount >= 0, 'removeCount can not be negative');\n      destroyQueue = [];\n      for (let i = removeStart; i < removeStart + removeCount; i++) {\n        const view = this.views[i];\n        if (view !== undefined && view !== enteringView && view !== leavingView) {\n          destroyQueue.push(view);\n        }\n      }\n      // default the direction to \"back\"\n      (_a = opts.direction) !== null && _a !== void 0 ? _a : (opts.direction = 'back');\n    }\n    const finalNumViews = this.views.length + ((_b = insertViews === null || insertViews === void 0 ? void 0 : insertViews.length) !== null && _b !== void 0 ? _b : 0) - (removeCount !== null && removeCount !== void 0 ? removeCount : 0);\n    assert(finalNumViews >= 0, 'final balance can not be negative');\n    if (finalNumViews === 0) {\n      console.warn(`You can't remove all the pages in the navigation stack. nav.pop() is probably called too many times.`, this, this.el);\n      throw new Error('navigation stack needs at least one root page');\n    }\n    // At this point the transition can not be rejected, any throw should be an error\n    // Insert the new views in the stack.\n    if (insertViews) {\n      // add the views to the\n      let insertIndex = ti.insertStart;\n      for (const view of insertViews) {\n        this.insertViewAt(view, insertIndex);\n        insertIndex++;\n      }\n      if (ti.enteringRequiresTransition) {\n        // default to forward if not already set\n        (_c = opts.direction) !== null && _c !== void 0 ? _c : (opts.direction = 'forward');\n      }\n    }\n    // if the views to be removed are in the beginning or middle\n    // and there is not a view that needs to visually transition out\n    // then just destroy them and don't transition anything\n    // batch all of lifecycles together\n    // let's make sure, callbacks are zoned\n    if (destroyQueue && destroyQueue.length > 0) {\n      for (const view of destroyQueue) {\n        lifecycle(view.element, LIFECYCLE_WILL_LEAVE);\n        lifecycle(view.element, LIFECYCLE_DID_LEAVE);\n        lifecycle(view.element, LIFECYCLE_WILL_UNLOAD);\n      }\n      // once all lifecycle events has been delivered, we can safely detroy the views\n      for (const view of destroyQueue) {\n        this.destroyView(view);\n      }\n    }\n  }\n  async transition(enteringView, leavingView, ti) {\n    // we should animate (duration > 0) if the pushed page is not the first one (startup)\n    // or if it is a portal (modal, actionsheet, etc.)\n    const opts = ti.opts;\n    const progressCallback = opts.progressAnimation\n      ? (ani) => {\n        /**\n         * Because this progress callback is called asynchronously\n         * it is possible for the gesture to start and end before\n         * the animation is ever set. In that scenario, we should\n         * immediately call progressEnd so that the transition promise\n         * resolves and the gesture does not get locked up.\n         */\n        if (ani !== undefined && !this.gestureOrAnimationInProgress) {\n          this.gestureOrAnimationInProgress = true;\n          ani.onFinish(() => {\n            this.gestureOrAnimationInProgress = false;\n          }, { oneTimeCallback: true });\n          /**\n           * Playing animation to beginning\n           * with a duration of 0 prevents\n           * any flickering when the animation\n           * is later cleaned up.\n           */\n          ani.progressEnd(0, 0, 0);\n        }\n        else {\n          this.sbAni = ani;\n        }\n      }\n      : undefined;\n    const mode = getIonMode(this);\n    const enteringEl = enteringView.element;\n    const leavingEl = leavingView && leavingView.element;\n    const animationOpts = Object.assign(Object.assign({ mode, showGoBack: this.canGoBackSync(enteringView), baseEl: this.el, progressCallback, animated: this.animated && config.getBoolean('animated', true), enteringEl,\n      leavingEl }, opts), { animationBuilder: opts.animationBuilder || this.animation || config.get('navAnimation') });\n    const { hasCompleted } = await transition(animationOpts);\n    return this.transitionFinish(hasCompleted, enteringView, leavingView, opts);\n  }\n  transitionFinish(hasCompleted, enteringView, leavingView, opts) {\n    /**\n     * If the transition did not complete, the leavingView will still be the active\n     * view on the stack. Otherwise unmount all the views after the enteringView.\n     */\n    const activeView = hasCompleted ? enteringView : leavingView;\n    if (activeView) {\n      this.unmountInactiveViews(activeView);\n    }\n    return {\n      hasCompleted,\n      requiresTransition: true,\n      enteringView,\n      leavingView,\n      direction: opts.direction,\n    };\n  }\n  /**\n   * Inserts a view at the specified index.\n   *\n   * When the view already is in the stack it will be moved to the new position.\n   *\n   * @param view The view to insert.\n   * @param index The index where to insert the view.\n   */\n  insertViewAt(view, index) {\n    const views = this.views;\n    const existingIndex = views.indexOf(view);\n    if (existingIndex > -1) {\n      assert(view.nav === this, 'view is not part of the nav');\n      // The view already in the stack, removes it.\n      views.splice(existingIndex, 1);\n      // and add it back at the requested index.\n      views.splice(index, 0, view);\n    }\n    else {\n      assert(!view.nav, 'nav is used');\n      // this is a new view to add to the stack\n      // create the new entering view\n      view.nav = this;\n      views.splice(index, 0, view);\n    }\n  }\n  /**\n   * Removes a view from the stack.\n   *\n   * @param view The view to remove.\n   */\n  removeView(view) {\n    assert(view.state === VIEW_STATE_ATTACHED || view.state === VIEW_STATE_DESTROYED, 'view state should be loaded or destroyed');\n    const views = this.views;\n    const index = views.indexOf(view);\n    assert(index > -1, 'view must be part of the stack');\n    if (index >= 0) {\n      views.splice(index, 1);\n    }\n  }\n  destroyView(view) {\n    view._destroy();\n    this.removeView(view);\n  }\n  /**\n   * Unmounts all inactive views after the specified active view.\n   *\n   * DOM WRITE\n   *\n   * @param activeView The view that is actively visible in the stack. Used to calculate which views to unmount.\n   */\n  unmountInactiveViews(activeView) {\n    // ok, cleanup time!! Destroy all of the views that are\n    // INACTIVE and come after the active view\n    // only do this if the views exist, though\n    if (this.destroyed) {\n      return;\n    }\n    const views = this.views;\n    const activeViewIndex = views.indexOf(activeView);\n    for (let i = views.length - 1; i >= 0; i--) {\n      const view = views[i];\n      /**\n       * When inserting multiple views via insertPages\n       * the last page will be transitioned to, but the\n       * others will not be. As a result, a DOM element\n       * will only be created for the last page inserted.\n       * As a result, it is possible to have views in the\n       * stack that do not have `view.element` yet.\n       */\n      const element = view.element;\n      if (element) {\n        if (i > activeViewIndex) {\n          // this view comes after the active view\n          // let's unload it\n          lifecycle(element, LIFECYCLE_WILL_UNLOAD);\n          this.destroyView(view);\n        }\n        else if (i < activeViewIndex) {\n          // this view comes before the active view\n          // and it is not a portal then ensure it is hidden\n          setPageHidden(element, true);\n        }\n      }\n    }\n  }\n  canStart() {\n    return (!this.gestureOrAnimationInProgress &&\n      !!this.swipeGesture &&\n      !this.isTransitioning &&\n      this.transInstr.length === 0 &&\n      this.canGoBackSync());\n  }\n  onStart() {\n    this.gestureOrAnimationInProgress = true;\n    this.pop({ direction: 'back', progressAnimation: true });\n  }\n  onMove(stepValue) {\n    if (this.sbAni) {\n      this.sbAni.progressStep(stepValue);\n    }\n  }\n  onEnd(shouldComplete, stepValue, dur) {\n    if (this.sbAni) {\n      this.sbAni.onFinish(() => {\n        this.gestureOrAnimationInProgress = false;\n      }, { oneTimeCallback: true });\n      // Account for rounding errors in JS\n      let newStepValue = shouldComplete ? -0.001 : 0.001;\n      /**\n       * Animation will be reversed here, so need to\n       * reverse the easing curve as well\n       *\n       * Additionally, we need to account for the time relative\n       * to the new easing curve, as `stepValue` is going to be given\n       * in terms of a linear curve.\n       */\n      if (!shouldComplete) {\n        this.sbAni.easing('cubic-bezier(1, 0, 0.68, 0.28)');\n        newStepValue += getTimeGivenProgression([0, 0], [1, 0], [0.68, 0.28], [1, 1], stepValue)[0];\n      }\n      else {\n        newStepValue += getTimeGivenProgression([0, 0], [0.32, 0.72], [0, 1], [1, 1], stepValue)[0];\n      }\n      this.sbAni.progressEnd(shouldComplete ? 1 : 0, newStepValue, dur);\n    }\n    else {\n      this.gestureOrAnimationInProgress = false;\n    }\n  }\n  render() {\n    return h(\"slot\", null);\n  }\n  get el() { return getElement(this); }\n  static get watchers() { return {\n    \"swipeGesture\": [\"swipeGestureChanged\"],\n    \"root\": [\"rootChanged\"]\n  }; }\n};\nNav.style = navCss;\n\nconst navLink = (el, routerDirection, component, componentProps, routerAnimation) => {\n  const nav = el.closest('ion-nav');\n  if (nav) {\n    if (routerDirection === 'forward') {\n      if (component !== undefined) {\n        return nav.push(component, componentProps, { skipIfBusy: true, animationBuilder: routerAnimation });\n      }\n    }\n    else if (routerDirection === 'root') {\n      if (component !== undefined) {\n        return nav.setRoot(component, componentProps, { skipIfBusy: true, animationBuilder: routerAnimation });\n      }\n    }\n    else if (routerDirection === 'back') {\n      return nav.pop({ skipIfBusy: true, animationBuilder: routerAnimation });\n    }\n  }\n  return Promise.resolve(false);\n};\n\nconst NavLink = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.onClick = () => {\n      return navLink(this.el, this.routerDirection, this.component, this.componentProps, this.routerAnimation);\n    };\n    this.component = undefined;\n    this.componentProps = undefined;\n    this.routerDirection = 'forward';\n    this.routerAnimation = undefined;\n  }\n  render() {\n    return h(Host, { onClick: this.onClick });\n  }\n  get el() { return getElement(this); }\n};\n\nexport { Nav as ion_nav, NavLink as ion_nav_link };\n"], "names": ["r", "registerInstance", "d", "createEvent", "h", "f", "getElement", "H", "Host", "g", "getTimeGivenProgression", "o", "assert", "s", "shallowEqualStringMap", "l", "lifecycle", "t", "transition", "setPageHidden", "LIFECYCLE_WILL_UNLOAD", "b", "LIFECYCLE_WILL_LEAVE", "c", "LIFECYCLE_DID_LEAVE", "getIonMode", "config", "a", "attachComponent", "VIEW_STATE_NEW", "VIEW_STATE_ATTACHED", "VIEW_STATE_DESTROYED", "ViewController", "constructor", "component", "params", "state", "init", "container", "_this", "_asyncToGenerator", "element", "delegate", "_destroy", "removeViewFromDom", "parentElement", "remove", "nav", "undefined", "matches", "view", "id", "convertToView", "page", "convertToViews", "pages", "map", "componentProps", "filter", "v", "navCss", "Nav", "hostRef", "ionNavWillLoad", "ionNavWillChange", "ionNavDidChange", "transInstr", "gestureOrAnimationInProgress", "useRouter", "isTransitioning", "destroyed", "views", "didLoad", "swipeGesture", "animated", "animation", "rootParams", "root", "swipeGestureChanged", "gesture", "enable", "rootChanged", "setRoot", "componentWillLoad", "document", "querySelector", "el", "closest", "mode", "getBoolean", "emit", "componentDidLoad", "_this2", "createSwipeBackGesture", "canStart", "bind", "onStart", "onMove", "onEnd", "connectedCallback", "disconnectedCallback", "destroy", "length", "push", "opts", "done", "insert", "insertIndex", "insertPages", "insertComponents", "queueTrns", "insertStart", "insertViews", "pop", "removeIndex", "popTo", "indexOrViewCtrl", "ti", "removeStart", "removeCount", "<PERSON><PERSON><PERSON><PERSON>", "popToRoot", "startIndex", "setPages", "setRouteId", "direction", "active", "getActiveSync", "Promise", "resolve", "changed", "promise", "finish", "commonOpts", "updateURL", "viewIsReady", "enteringEl", "mark", "p", "markVisible", "_ref", "apply", "arguments", "viewController", "find", "Object", "assign", "animationBuilder", "getRouteId", "_this3", "tagName", "getActive", "_this4", "getByIndex", "index", "_this5", "canGoBack", "_this6", "canGoBackSync", "getPrevious", "_this7", "getPreviousSync", "<PERSON><PERSON><PERSON><PERSON>", "indexOf", "_this8", "_a", "_b", "skipIfBusy", "reject", "router", "canTransition", "nextTrns", "success", "result", "fireError", "hasCompleted", "requiresTransition", "enteringView", "leavingView", "navChanged", "failed", "rejectReason", "shift", "runTransition", "_this9", "prepareTI", "getEnteringView", "Error", "postViewInit", "enteringRequiresTransition", "leavingRequiresTransition", "isBackDirection", "_c", "viewsLength", "viewControllers", "removeEnd", "i", "destroyQueue", "finalNumViews", "console", "warn", "insertViewAt", "destroyView", "_this10", "progressCallback", "progressAnimation", "ani", "onFinish", "oneTimeCallback", "progressEnd", "sbAni", "leavingEl", "animationOpts", "showGoBack", "baseEl", "get", "transitionFinish", "activeView", "unmountInactiveViews", "existingIndex", "splice", "activeViewIndex", "<PERSON><PERSON><PERSON><PERSON>", "progressStep", "shouldComplete", "dur", "newStepValue", "easing", "render", "watchers", "style", "navLink", "routerDirection", "routerAnimation", "NavLink", "onClick", "ion_nav", "ion_nav_link"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}