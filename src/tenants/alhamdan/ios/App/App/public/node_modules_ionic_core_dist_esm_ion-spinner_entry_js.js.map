{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-spinner_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC0E;AACZ;AACY;AACZ;AAE9D,MAAMY,UAAU,GAAG,svIAAsvI;AAEzwI,MAAMC,OAAO,GAAG,MAAM;EACpBC,WAAWA,CAACC,OAAO,EAAE;IACnBd,qDAAgB,CAAC,IAAI,EAAEc,OAAO,CAAC;IAC/B,IAAI,CAACC,KAAK,GAAGC,SAAS;IACtB,IAAI,CAACC,QAAQ,GAAGD,SAAS;IACzB,IAAI,CAACE,IAAI,GAAGF,SAAS;IACrB,IAAI,CAACG,MAAM,GAAG,KAAK;EACrB;EACAC,OAAOA,CAAA,EAAG;IACR,MAAMC,WAAW,GAAG,IAAI,CAACH,IAAI,IAAIZ,wDAAM,CAACgB,GAAG,CAAC,SAAS,CAAC;IACtD,MAAMC,IAAI,GAAGf,4DAAU,CAAC,IAAI,CAAC;IAC7B,IAAIa,WAAW,EAAE;MACf,OAAOA,WAAW;IACpB;IACA,OAAOE,IAAI,KAAK,KAAK,GAAG,OAAO,GAAG,UAAU;EAC9C;EACAC,MAAMA,CAAA,EAAG;IACP,IAAIC,EAAE;IACN,MAAMC,IAAI,GAAG,IAAI;IACjB,MAAMH,IAAI,GAAGf,4DAAU,CAACkB,IAAI,CAAC;IAC7B,MAAML,WAAW,GAAGK,IAAI,CAACN,OAAO,CAAC,CAAC;IAClC,MAAMO,OAAO,GAAG,CAACF,EAAE,GAAGf,2DAAQ,CAACW,WAAW,CAAC,MAAM,IAAI,IAAII,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGf,2DAAQ,CAAC,OAAO,CAAC;IAC/F,MAAMO,QAAQ,GAAG,OAAOS,IAAI,CAACT,QAAQ,KAAK,QAAQ,IAAIS,IAAI,CAACT,QAAQ,GAAG,EAAE,GAAGS,IAAI,CAACT,QAAQ,GAAGU,OAAO,CAACC,GAAG;IACtG,MAAMC,IAAI,GAAG,EAAE,CAAC,CAAC;IACjB,IAAIF,OAAO,CAACG,OAAO,KAAKd,SAAS,EAAE;MACjC,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,OAAO,CAACG,OAAO,EAAEC,CAAC,EAAE,EAAE;QACxCF,IAAI,CAACG,IAAI,CAACC,WAAW,CAACN,OAAO,EAAEV,QAAQ,EAAEc,CAAC,EAAEJ,OAAO,CAACG,OAAO,CAAC,CAAC;MAC/D;IACF,CAAC,MACI,IAAIH,OAAO,CAACO,KAAK,KAAKlB,SAAS,EAAE;MACpC,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,OAAO,CAACO,KAAK,EAAEH,CAAC,EAAE,EAAE;QACtCF,IAAI,CAACG,IAAI,CAACG,SAAS,CAACR,OAAO,EAAEV,QAAQ,EAAEc,CAAC,EAAEJ,OAAO,CAACO,KAAK,CAAC,CAAC;MAC3D;IACF;IACA,OAAQjC,qDAAC,CAACE,iDAAI,EAAE;MAAEiC,KAAK,EAAE/B,qDAAkB,CAACqB,IAAI,CAACX,KAAK,EAAE;QACpD,CAACQ,IAAI,GAAG,IAAI;QACZ,CAAE,WAAUF,WAAY,EAAC,GAAG,IAAI;QAChC,gBAAgB,EAAEK,IAAI,CAACP,MAAM,IAAIb,wDAAM,CAAC+B,UAAU,CAAC,UAAU;MAC/D,CAAC,CAAC;MAAEC,IAAI,EAAE,aAAa;MAAEC,KAAK,EAAEZ,OAAO,CAACa,WAAW,GAAG;QAAEC,iBAAiB,EAAExB,QAAQ,GAAG;MAAK,CAAC,GAAG,CAAC;IAAE,CAAC,EAAEY,IAAI,CAAC;EAC9G;AACF,CAAC;AACD,MAAMI,WAAW,GAAGA,CAACN,OAAO,EAAEV,QAAQ,EAAEyB,KAAK,EAAEC,KAAK,KAAK;EACvD,MAAMC,IAAI,GAAGjB,OAAO,CAACkB,EAAE,CAAC5B,QAAQ,EAAEyB,KAAK,EAAEC,KAAK,CAAC;EAC/CC,IAAI,CAACL,KAAK,CAAC,oBAAoB,CAAC,GAAGtB,QAAQ,GAAG,IAAI;EAClD,OAAQhB,qDAAC,CAAC,KAAK,EAAE;IAAE6C,OAAO,EAAEF,IAAI,CAACE,OAAO,IAAI,WAAW;IAAEP,KAAK,EAAEK,IAAI,CAACL;EAAM,CAAC,EAAEtC,qDAAC,CAAC,QAAQ,EAAE;IAAE8C,SAAS,EAAEH,IAAI,CAACG,SAAS,IAAI,kBAAkB;IAAEC,EAAE,EAAEJ,IAAI,CAACI,EAAE;IAAEC,EAAE,EAAEL,IAAI,CAACK,EAAE;IAAElD,CAAC,EAAE6C,IAAI,CAAC7C,CAAC;IAAEwC,KAAK,EAAEZ,OAAO,CAACa,WAAW,GAAG;MAAEC,iBAAiB,EAAExB,QAAQ,GAAG;IAAK,CAAC,GAAG,CAAC;EAAE,CAAC,CAAC,CAAC;AAChQ,CAAC;AACD,MAAMkB,SAAS,GAAGA,CAACR,OAAO,EAAEV,QAAQ,EAAEyB,KAAK,EAAEC,KAAK,KAAK;EACrD,MAAMC,IAAI,GAAGjB,OAAO,CAACkB,EAAE,CAAC5B,QAAQ,EAAEyB,KAAK,EAAEC,KAAK,CAAC;EAC/CC,IAAI,CAACL,KAAK,CAAC,oBAAoB,CAAC,GAAGtB,QAAQ,GAAG,IAAI;EAClD,OAAQhB,qDAAC,CAAC,KAAK,EAAE;IAAE6C,OAAO,EAAEF,IAAI,CAACE,OAAO,IAAI,WAAW;IAAEP,KAAK,EAAEK,IAAI,CAACL;EAAM,CAAC,EAAEtC,qDAAC,CAAC,MAAM,EAAE;IAAE8C,SAAS,EAAE,kBAAkB;IAAEG,EAAE,EAAEN,IAAI,CAACM,EAAE;IAAEC,EAAE,EAAEP,IAAI,CAACO;EAAG,CAAC,CAAC,CAAC;AACvJ,CAAC;AACDvC,OAAO,CAAC2B,KAAK,GAAG5B,UAAU;;;;;;;;;;;;;;;;;;;;AC7D1B;AACA;AACA;AACA,MAAM0C,WAAW,GAAGA,CAACC,QAAQ,EAAEC,EAAE,KAAK;EACpC,OAAOA,EAAE,CAACC,OAAO,CAACF,QAAQ,CAAC,KAAK,IAAI;AACtC,CAAC;AACD;AACA;AACA;AACA,MAAMjD,kBAAkB,GAAGA,CAACU,KAAK,EAAE0C,WAAW,KAAK;EACjD,OAAO,OAAO1C,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAAC2C,MAAM,GAAG,CAAC,GAChDC,MAAM,CAACC,MAAM,CAAC;IAAE,WAAW,EAAE,IAAI;IAAE,CAAE,aAAY7C,KAAM,EAAC,GAAG;EAAK,CAAC,EAAE0C,WAAW,CAAC,GAAGA,WAAW;AACnG,CAAC;AACD,MAAMI,YAAY,GAAIC,OAAO,IAAK;EAChC,IAAIA,OAAO,KAAK9C,SAAS,EAAE;IACzB,MAAM+C,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACH,OAAO,CAAC,GAAGA,OAAO,GAAGA,OAAO,CAACI,KAAK,CAAC,GAAG,CAAC;IACnE,OAAOH,KAAK,CACTI,MAAM,CAAE/D,CAAC,IAAKA,CAAC,IAAI,IAAI,CAAC,CACxBgE,GAAG,CAAEhE,CAAC,IAAKA,CAAC,CAACiE,IAAI,CAAC,CAAC,CAAC,CACpBF,MAAM,CAAE/D,CAAC,IAAKA,CAAC,KAAK,EAAE,CAAC;EAC5B;EACA,OAAO,EAAE;AACX,CAAC;AACD,MAAMkE,WAAW,GAAIR,OAAO,IAAK;EAC/B,MAAMM,GAAG,GAAG,CAAC,CAAC;EACdP,YAAY,CAACC,OAAO,CAAC,CAACS,OAAO,CAAEnE,CAAC,IAAMgE,GAAG,CAAChE,CAAC,CAAC,GAAG,IAAK,CAAC;EACrD,OAAOgE,GAAG;AACZ,CAAC;AACD,MAAMI,MAAM,GAAG,sBAAsB;AACrC,MAAMC,OAAO;EAAA,IAAAC,IAAA,GAAAC,8KAAA,CAAG,WAAOC,GAAG,EAAEC,EAAE,EAAEC,SAAS,EAAEC,SAAS,EAAK;IACvD,IAAIH,GAAG,IAAI,IAAI,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAACJ,MAAM,CAACQ,IAAI,CAACJ,GAAG,CAAC,EAAE;MACtD,MAAMK,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,YAAY,CAAC;MACnD,IAAIF,MAAM,EAAE;QACV,IAAIJ,EAAE,IAAI,IAAI,EAAE;UACdA,EAAE,CAACO,cAAc,CAAC,CAAC;QACrB;QACA,OAAOH,MAAM,CAACjD,IAAI,CAAC4C,GAAG,EAAEE,SAAS,EAAEC,SAAS,CAAC;MAC/C;IACF;IACA,OAAO,KAAK;EACd,CAAC;EAAA,gBAXKN,OAAOA,CAAAY,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAd,IAAA,CAAAe,KAAA,OAAAC,SAAA;EAAA;AAAA,GAWZ", "sources": ["./node_modules/@ionic/core/dist/esm/ion-spinner.entry.js", "./node_modules/@ionic/core/dist/esm/theme-17531cdf.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, H as Host } from './index-2d388930.js';\nimport { c as createColorClasses } from './theme-17531cdf.js';\nimport { c as config, b as getIonMode } from './ionic-global-b3fc28dd.js';\nimport { S as SPINNERS } from './spinner-configs-d09fbbbb.js';\n\nconst spinnerCss = \":host{display:inline-block;position:relative;width:28px;height:28px;color:var(--color);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}:host(.ion-color){color:var(--ion-color-base)}svg{-webkit-transform-origin:center;transform-origin:center;position:absolute;top:0;left:0;width:100%;height:100%;-webkit-transform:translateZ(0);transform:translateZ(0)}:host-context([dir=rtl]) svg{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}[dir=rtl] svg{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}@supports selector(:dir(rtl)){svg:dir(rtl){-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}}:host(.spinner-lines) line,:host(.spinner-lines-small) line{stroke-width:7px}:host(.spinner-lines-sharp) line,:host(.spinner-lines-sharp-small) line{stroke-width:4px}:host(.spinner-lines) line,:host(.spinner-lines-small) line,:host(.spinner-lines-sharp) line,:host(.spinner-lines-sharp-small) line{stroke-linecap:round;stroke:currentColor}:host(.spinner-lines) svg,:host(.spinner-lines-small) svg,:host(.spinner-lines-sharp) svg,:host(.spinner-lines-sharp-small) svg{-webkit-animation:spinner-fade-out 1s linear infinite;animation:spinner-fade-out 1s linear infinite}:host(.spinner-bubbles) svg{-webkit-animation:spinner-scale-out 1s linear infinite;animation:spinner-scale-out 1s linear infinite;fill:currentColor}:host(.spinner-circles) svg{-webkit-animation:spinner-fade-out 1s linear infinite;animation:spinner-fade-out 1s linear infinite;fill:currentColor}:host(.spinner-crescent) circle{fill:transparent;stroke-width:4px;stroke-dasharray:128px;stroke-dashoffset:82px;stroke:currentColor}:host(.spinner-crescent) svg{-webkit-animation:spinner-rotate 1s linear infinite;animation:spinner-rotate 1s linear infinite}:host(.spinner-dots) circle{stroke-width:0;fill:currentColor}:host(.spinner-dots) svg{-webkit-animation:spinner-dots 1s linear infinite;animation:spinner-dots 1s linear infinite}:host(.spinner-circular) svg{-webkit-animation:spinner-circular linear infinite;animation:spinner-circular linear infinite}:host(.spinner-circular) circle{-webkit-animation:spinner-circular-inner ease-in-out infinite;animation:spinner-circular-inner ease-in-out infinite;stroke:currentColor;stroke-dasharray:80px, 200px;stroke-dashoffset:0px;stroke-width:5.6;fill:none}:host(.spinner-paused),:host(.spinner-paused) svg,:host(.spinner-paused) circle{-webkit-animation-play-state:paused;animation-play-state:paused}@-webkit-keyframes spinner-fade-out{0%{opacity:1}100%{opacity:0}}@keyframes spinner-fade-out{0%{opacity:1}100%{opacity:0}}@-webkit-keyframes spinner-scale-out{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1)}100%{-webkit-transform:scale(0, 0);transform:scale(0, 0)}}@keyframes spinner-scale-out{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1)}100%{-webkit-transform:scale(0, 0);transform:scale(0, 0)}}@-webkit-keyframes spinner-rotate{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spinner-rotate{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes spinner-dots{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}50%{-webkit-transform:scale(0.4, 0.4);transform:scale(0.4, 0.4);opacity:0.3}100%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}}@keyframes spinner-dots{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}50%{-webkit-transform:scale(0.4, 0.4);transform:scale(0.4, 0.4);opacity:0.3}100%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}}@-webkit-keyframes spinner-circular{100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spinner-circular{100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes spinner-circular-inner{0%{stroke-dasharray:1px, 200px;stroke-dashoffset:0px}50%{stroke-dasharray:100px, 200px;stroke-dashoffset:-15px}100%{stroke-dasharray:100px, 200px;stroke-dashoffset:-125px}}@keyframes spinner-circular-inner{0%{stroke-dasharray:1px, 200px;stroke-dashoffset:0px}50%{stroke-dasharray:100px, 200px;stroke-dashoffset:-15px}100%{stroke-dasharray:100px, 200px;stroke-dashoffset:-125px}}\";\n\nconst Spinner = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.color = undefined;\n    this.duration = undefined;\n    this.name = undefined;\n    this.paused = false;\n  }\n  getName() {\n    const spinnerName = this.name || config.get('spinner');\n    const mode = getIonMode(this);\n    if (spinnerName) {\n      return spinnerName;\n    }\n    return mode === 'ios' ? 'lines' : 'circular';\n  }\n  render() {\n    var _a;\n    const self = this;\n    const mode = getIonMode(self);\n    const spinnerName = self.getName();\n    const spinner = (_a = SPINNERS[spinnerName]) !== null && _a !== void 0 ? _a : SPINNERS['lines'];\n    const duration = typeof self.duration === 'number' && self.duration > 10 ? self.duration : spinner.dur;\n    const svgs = []; // TODO(FW-2832): type\n    if (spinner.circles !== undefined) {\n      for (let i = 0; i < spinner.circles; i++) {\n        svgs.push(buildCircle(spinner, duration, i, spinner.circles));\n      }\n    }\n    else if (spinner.lines !== undefined) {\n      for (let i = 0; i < spinner.lines; i++) {\n        svgs.push(buildLine(spinner, duration, i, spinner.lines));\n      }\n    }\n    return (h(Host, { class: createColorClasses(self.color, {\n        [mode]: true,\n        [`spinner-${spinnerName}`]: true,\n        'spinner-paused': self.paused || config.getBoolean('_testing'),\n      }), role: \"progressbar\", style: spinner.elmDuration ? { animationDuration: duration + 'ms' } : {} }, svgs));\n  }\n};\nconst buildCircle = (spinner, duration, index, total) => {\n  const data = spinner.fn(duration, index, total);\n  data.style['animation-duration'] = duration + 'ms';\n  return (h(\"svg\", { viewBox: data.viewBox || '0 0 64 64', style: data.style }, h(\"circle\", { transform: data.transform || 'translate(32,32)', cx: data.cx, cy: data.cy, r: data.r, style: spinner.elmDuration ? { animationDuration: duration + 'ms' } : {} })));\n};\nconst buildLine = (spinner, duration, index, total) => {\n  const data = spinner.fn(duration, index, total);\n  data.style['animation-duration'] = duration + 'ms';\n  return (h(\"svg\", { viewBox: data.viewBox || '0 0 64 64', style: data.style }, h(\"line\", { transform: \"translate(32,32)\", y1: data.y1, y2: data.y2 })));\n};\nSpinner.style = spinnerCss;\n\nexport { Spinner as ion_spinner };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst hostContext = (selector, el) => {\n  return el.closest(selector) !== null;\n};\n/**\n * Create the mode and color classes for the component based on the classes passed in\n */\nconst createColorClasses = (color, cssClassMap) => {\n  return typeof color === 'string' && color.length > 0\n    ? Object.assign({ 'ion-color': true, [`ion-color-${color}`]: true }, cssClassMap) : cssClassMap;\n};\nconst getClassList = (classes) => {\n  if (classes !== undefined) {\n    const array = Array.isArray(classes) ? classes : classes.split(' ');\n    return array\n      .filter((c) => c != null)\n      .map((c) => c.trim())\n      .filter((c) => c !== '');\n  }\n  return [];\n};\nconst getClassMap = (classes) => {\n  const map = {};\n  getClassList(classes).forEach((c) => (map[c] = true));\n  return map;\n};\nconst SCHEME = /^[a-z][a-z0-9+\\-.]*:/;\nconst openURL = async (url, ev, direction, animation) => {\n  if (url != null && url[0] !== '#' && !SCHEME.test(url)) {\n    const router = document.querySelector('ion-router');\n    if (router) {\n      if (ev != null) {\n        ev.preventDefault();\n      }\n      return router.push(url, direction, animation);\n    }\n  }\n  return false;\n};\n\nexport { createColorClasses as c, getClassMap as g, hostContext as h, openURL as o };\n"], "names": ["r", "registerInstance", "h", "H", "Host", "c", "createColorClasses", "config", "b", "getIonMode", "S", "SPINNERS", "spinnerCss", "Spinner", "constructor", "hostRef", "color", "undefined", "duration", "name", "paused", "getName", "spinnerName", "get", "mode", "render", "_a", "self", "spinner", "dur", "svgs", "circles", "i", "push", "buildCircle", "lines", "buildLine", "class", "getBoolean", "role", "style", "elmDuration", "animationDuration", "index", "total", "data", "fn", "viewBox", "transform", "cx", "cy", "y1", "y2", "ion_spinner", "hostContext", "selector", "el", "closest", "cssClassMap", "length", "Object", "assign", "getClassList", "classes", "array", "Array", "isArray", "split", "filter", "map", "trim", "getClassMap", "for<PERSON>ach", "SCHEME", "openURL", "_ref", "_asyncToGenerator", "url", "ev", "direction", "animation", "test", "router", "document", "querySelector", "preventDefault", "_x", "_x2", "_x3", "_x4", "apply", "arguments", "g", "o"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0, 1]}