{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-datetime-button_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC2F;AACN;AAC5B;AACK;AACD;AACsH;AAEnL,MAAM6B,oBAAoB,GAAG,06BAA06B;AAEv8B,MAAMC,mBAAmB,GAAG,06BAA06B;AAEt8B,MAAMC,cAAc,GAAG,MAAM;EAC3BC,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAC,KAAA;IACnBjC,qDAAgB,CAAC,IAAI,EAAEgC,OAAO,CAAC;IAC/B,IAAI,CAACE,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;AACJ;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,mBAAmB,GAAIC,KAAK,IAAK;MACpC,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;QACzC,OAAO,EAAE;MACX;MACA,IAAIE,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE;QACxB,OAAOA,KAAK;MACd;MACA,OAAO,CAACA,KAAK,CAAC;IAChB,CAAC;IACD;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACI,eAAe,GAAG,MAAM;MAC3B,MAAM;QAAEP,UAAU;QAAEQ;MAAqB,CAAC,GAAG,IAAI;MACjD,IAAI,CAACR,UAAU,EAAE;QACf;MACF;MACA,MAAM;QAAEG,KAAK;QAAEM,MAAM;QAAEC,SAAS;QAAEC,WAAW;QAAEC,QAAQ;QAAEC;MAA4B,CAAC,GAAGb,UAAU;MACnG,MAAMc,YAAY,GAAG,IAAI,CAACZ,mBAAmB,CAACC,KAAK,CAAC;MACpD;AACN;AACA;AACA;MACM,MAAMY,eAAe,GAAGlC,oDAAS,CAACiC,YAAY,CAACE,MAAM,GAAG,CAAC,GAAGF,YAAY,GAAG,CAAC/B,oDAAQ,CAAC,CAAC,CAAC,CAAC;MACxF,IAAI,CAACgC,eAAe,EAAE;QACpB;MACF;MACA;AACN;AACA;AACA;AACA;AACA;AACA;MACM,MAAME,mBAAmB,GAAGF,eAAe,CAAC,CAAC,CAAC;MAC9C,MAAMG,SAAS,GAAGjC,oDAAQ,CAACwB,MAAM,EAAEC,SAAS,CAAC;MAC7C,IAAI,CAACS,QAAQ,GAAG,IAAI,CAACC,QAAQ,GAAGhB,SAAS;MACzC,QAAQI,oBAAoB;QAC1B,KAAK,WAAW;QAChB,KAAK,WAAW;UACd,MAAMW,QAAQ,GAAG1B,oDAAkB,CAACgB,MAAM,EAAEQ,mBAAmB,CAAC;UAChE,MAAMG,QAAQ,GAAG7B,oDAAgB,CAACkB,MAAM,EAAEQ,mBAAmB,EAAEC,SAAS,CAAC;UACzE,IAAIP,WAAW,EAAE;YACf,IAAI,CAACQ,QAAQ,GAAI,GAAEA,QAAS,IAAGC,QAAS,EAAC;UAC3C,CAAC,MACI;YACH,IAAI,CAACD,QAAQ,GAAGA,QAAQ;YACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;UAC1B;UACA;QACF,KAAK,MAAM;UACT,IAAIR,QAAQ,IAAIE,YAAY,CAACE,MAAM,KAAK,CAAC,EAAE;YACzC,IAAIK,UAAU,GAAI,GAAEP,YAAY,CAACE,MAAO,OAAM,CAAC,CAAC;YAChD,IAAIH,2BAA2B,KAAKT,SAAS,EAAE;cAC7C,IAAI;gBACFiB,UAAU,GAAGR,2BAA2B,CAACC,YAAY,CAAC;cACxD,CAAC,CACD,OAAOQ,CAAC,EAAE;gBACR9C,qDAAa,CAAC,uDAAuD,EAAE8C,CAAC,CAAC;cAC3E;YACF;YACA,IAAI,CAACH,QAAQ,GAAGE,UAAU;UAC5B,CAAC,MACI;YACH,IAAI,CAACF,QAAQ,GAAG1B,oDAAkB,CAACgB,MAAM,EAAEQ,mBAAmB,CAAC;UACjE;UACA;QACF,KAAK,MAAM;UACT,IAAI,CAACG,QAAQ,GAAG7B,oDAAgB,CAACkB,MAAM,EAAEQ,mBAAmB,EAAEC,SAAS,CAAC;UACxE;QACF,KAAK,YAAY;UACf,IAAI,CAACC,QAAQ,GAAG9B,oDAAe,CAACoB,MAAM,EAAEQ,mBAAmB,CAAC;UAC5D;QACF,KAAK,OAAO;UACV,IAAI,CAACE,QAAQ,GAAGhC,oDAAoB,CAACsB,MAAM,EAAEQ,mBAAmB,EAAE;YAAEM,KAAK,EAAE;UAAO,CAAC,CAAC;UACpF;QACF,KAAK,MAAM;UACT,IAAI,CAACJ,QAAQ,GAAGhC,oDAAoB,CAACsB,MAAM,EAAEQ,mBAAmB,EAAE;YAAEO,IAAI,EAAE;UAAU,CAAC,CAAC;UACtF;MACJ;IACF,CAAC;IACD;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACC,sBAAsB,gBAAAC,8KAAA,CAAG,aAAY;MACxC,MAAM;QAAE1B;MAAW,CAAC,GAAGD,KAAI;MAC3B,IAAI,CAACC,UAAU,EAAE;QACf,OAAO2B,OAAO,CAACC,OAAO,CAAC,CAAC;MAC1B;MACA,OAAO,IAAID,OAAO,CAAEC,OAAO,IAAK;QAC9BvD,uDAAgB,CAAC2B,UAAU,EAAE,WAAW,EAAE4B,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAK,CAAC,CAAC;MACpE,CAAC,CAAC;IACJ,CAAC;IACD,IAAI,CAACC,eAAe;MAAA,IAAAC,KAAA,GAAAL,8KAAA,CAAG,WAAOM,EAAE,EAAK;QACnC,MAAM;UAAEhC,UAAU;UAAEQ;QAAqB,CAAC,GAAGT,KAAI;QACjD,IAAI,CAACC,UAAU,EAAE;UACf;QACF;QACA,IAAIiC,uBAAuB,GAAG,KAAK;QACnC;AACN;AACA;AACA;AACA;AACA;AACA;QACM,QAAQzB,oBAAoB;UAC1B,KAAK,WAAW;UAChB,KAAK,WAAW;YACd,MAAM0B,WAAW,GAAGlC,UAAU,CAACmC,YAAY,KAAK,MAAM;YACtD;AACV;AACA;AACA;AACA;AACA;YACU,IAAI,CAACnC,UAAU,CAACW,WAAW,IAAIuB,WAAW,EAAE;cAC1ClC,UAAU,CAACmC,YAAY,GAAG,MAAM;cAChCF,uBAAuB,GAAG,IAAI;YAChC;YACA;QACJ;QACA;AACN;AACA;AACA;AACA;AACA;AACA;QACMlC,KAAI,CAACqC,cAAc,GAAG,MAAM;QAC5BrC,KAAI,CAACsC,cAAc,CAACL,EAAE,EAAEC,uBAAuB,EAAElC,KAAI,CAACuC,YAAY,CAAC;MACrE,CAAC;MAAA,iBAAAC,EAAA;QAAA,OAAAR,KAAA,CAAAS,KAAA,OAAAC,SAAA;MAAA;IAAA;IACD,IAAI,CAACC,eAAe,GAAIV,EAAE,IAAK;MAC7B,MAAM;QAAEhC,UAAU;QAAEQ;MAAqB,CAAC,GAAG,IAAI;MACjD,IAAI,CAACR,UAAU,EAAE;QACf;MACF;MACA,IAAIiC,uBAAuB,GAAG,KAAK;MACnC;AACN;AACA;AACA;AACA;AACA;AACA;MACM,QAAQzB,oBAAoB;QAC1B,KAAK,WAAW;QAChB,KAAK,WAAW;UACd,MAAM0B,WAAW,GAAGlC,UAAU,CAACmC,YAAY,KAAK,MAAM;UACtD,IAAID,WAAW,EAAE;YACflC,UAAU,CAACmC,YAAY,GAAG,MAAM;YAChCF,uBAAuB,GAAG,IAAI;UAChC;UACA;MACJ;MACA;AACN;AACA;AACA;AACA;AACA;AACA;MACM,IAAI,CAACG,cAAc,GAAG,MAAM;MAC5B,IAAI,CAACC,cAAc,CAACL,EAAE,EAAEC,uBAAuB,EAAE,IAAI,CAACU,YAAY,CAAC;IACrE,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACN,cAAc;MAAA,IAAAO,KAAA,GAAAlB,8KAAA,CAAG,WAAOM,EAAE,EAAEC,uBAAuB,EAAEY,SAAS,EAAK;QACtE,MAAM;UAAE5C;QAAU,CAAC,GAAGF,KAAI;QAC1B,IAAI,CAACE,SAAS,EAAE;UACd;QACF;QACA,IAAIA,SAAS,CAAC6C,OAAO,KAAK,aAAa,EAAE;UACvC;AACR;AACA;AACA;AACA;AACA;AACA;UACQ,IAAIb,uBAAuB,EAAE;YAC3B,MAAMlC,KAAI,CAAC0B,sBAAsB,CAAC,CAAC;UACrC;UACA;AACR;AACA;AACA;AACA;UACQxB,SAAS,CAAC8C,OAAO,CAACC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEjB,EAAE,CAAC,EAAE;YAAEkB,MAAM,EAAE;cAC7DC,eAAe,EAAEN;YACnB;UAAE,CAAC,CAAC,CAAC;QACT,CAAC,MACI;UACH5C,SAAS,CAAC8C,OAAO,CAAC,CAAC;QACrB;MACF,CAAC;MAAA,iBAAAK,GAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAV,KAAA,CAAAJ,KAAA,OAAAC,SAAA;MAAA;IAAA;IACD,IAAI,CAACjC,oBAAoB,GAAG,WAAW;IACvC,IAAI,CAACW,QAAQ,GAAGf,SAAS;IACzB,IAAI,CAACgB,QAAQ,GAAGhB,SAAS;IACzB,IAAI,CAACmD,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACnB,cAAc,GAAGhC,SAAS;IAC/B,IAAI,CAACoD,KAAK,GAAG,SAAS;IACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAGtD,SAAS;EAC3B;EACMuD,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAlC,8KAAA;MACxB,MAAM;QAAEgC;MAAS,CAAC,GAAGE,MAAI;MACzB,IAAI,CAACF,QAAQ,EAAE;QACblF,qDAAa,CAAC,0GAA0G,EAAEoF,MAAI,CAACC,EAAE,CAAC;QAClI;MACF;MACA,MAAM7D,UAAU,GAAI4D,MAAI,CAAC5D,UAAU,GAAG8D,QAAQ,CAACC,cAAc,CAACL,QAAQ,CAAE;MACxE,IAAI,CAAC1D,UAAU,EAAE;QACfxB,qDAAa,CAAE,0CAAyCkF,QAAS,IAAG,EAAEE,MAAI,CAACC,EAAE,CAAC;QAC9E;MACF;MACA;AACJ;AACA;AACA;MACI,IAAI7D,UAAU,CAAC8C,OAAO,KAAK,cAAc,EAAE;QACzCtE,qDAAa,CAAE,6CAA4CkF,QAAS,mBAAkB1D,UAAU,CAAC8C,OAAO,CAACkB,WAAW,CAAC,CAAE,YAAW,EAAEhE,UAAU,CAAC;QAC/I;MACF;MACA;AACJ;AACA;AACA;AACA;AACA;MACI,MAAMiE,EAAE,GAAG,IAAIC,oBAAoB,CAAEC,OAAO,IAAK;QAC/C,MAAMnC,EAAE,GAAGmC,OAAO,CAAC,CAAC,CAAC;QACrBP,MAAI,CAACL,cAAc,GAAGvB,EAAE,CAACoC,cAAc;MACzC,CAAC,EAAE;QACDC,SAAS,EAAE;MACb,CAAC,CAAC;MACFJ,EAAE,CAACK,OAAO,CAACtE,UAAU,CAAC;MACtB;AACJ;AACA;AACA;AACA;MACI,MAAMC,SAAS,GAAI2D,MAAI,CAAC3D,SAAS,GAAGD,UAAU,CAACuE,OAAO,CAAC,wBAAwB,CAAE;MACjF;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACI,IAAItE,SAAS,EAAE;QACbA,SAAS,CAACuE,SAAS,CAACC,GAAG,CAAC,6BAA6B,CAAC;MACxD;MACAlG,uDAAgB,CAACyB,UAAU,EAAE,MAAM;QACjC,MAAMQ,oBAAoB,GAAIoD,MAAI,CAACpD,oBAAoB,GAAGR,UAAU,CAACmC,YAAY,IAAI,WAAY;QACjG;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACMyB,MAAI,CAACrD,eAAe,CAAC,CAAC;QACtBlC,uDAAgB,CAAC2B,UAAU,EAAE,gBAAgB,EAAE4D,MAAI,CAACrD,eAAe,CAAC;QACpE;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACM,QAAQC,oBAAoB;UAC1B,KAAK,WAAW;UAChB,KAAK,MAAM;UACX,KAAK,YAAY;UACjB,KAAK,OAAO;UACZ,KAAK,MAAM;YACToD,MAAI,CAACxB,cAAc,GAAG,MAAM;YAC5B;UACF,KAAK,WAAW;UAChB,KAAK,MAAM;YACTwB,MAAI,CAACxB,cAAc,GAAG,MAAM;YAC5B;QACJ;MACF,CAAC,CAAC;IAAC;EACL;EACAsC,MAAMA,CAAA,EAAG;IACP,MAAM;MAAElB,KAAK;MAAErC,QAAQ;MAAEC,QAAQ;MAAEgB,cAAc;MAAEmB,cAAc;MAAEE;IAAS,CAAC,GAAG,IAAI;IACpF,MAAMkB,IAAI,GAAGhG,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQZ,qDAAC,CAACE,iDAAI,EAAE;MAAE2G,KAAK,EAAEnG,qDAAkB,CAAC+E,KAAK,EAAE;QAC/C,CAACmB,IAAI,GAAG,IAAI;QACZ,CAAE,GAAEvC,cAAe,SAAQ,GAAGmB,cAAc;QAC5C,CAAC,0BAA0B,GAAGE;MAChC,CAAC;IAAE,CAAC,EAAEtC,QAAQ,IAAKpD,qDAAC,CAAC,QAAQ,EAAE;MAAE6G,KAAK,EAAE,iBAAiB;MAAEC,EAAE,EAAE,aAAa;MAAE,eAAe,EAAEtB,cAAc,GAAG,MAAM,GAAG,OAAO;MAAEuB,OAAO,EAAE,IAAI,CAAChD,eAAe;MAAE2B,QAAQ,EAAEA,QAAQ;MAAEsB,IAAI,EAAE,QAAQ;MAAEC,GAAG,EAAGnB,EAAE,IAAM,IAAI,CAACvB,YAAY,GAAGuB;IAAI,CAAC,EAAE9F,qDAAC,CAAC,MAAM,EAAE;MAAEkH,IAAI,EAAE;IAAc,CAAC,EAAE9D,QAAQ,CAAC,EAAEwD,IAAI,KAAK,IAAI,IAAI5G,qDAAC,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAE,EAAEqD,QAAQ,IAAKrD,qDAAC,CAAC,QAAQ,EAAE;MAAE6G,KAAK,EAAE,iBAAiB;MAAEC,EAAE,EAAE,aAAa;MAAE,eAAe,EAAEtB,cAAc,GAAG,MAAM,GAAG,OAAO;MAAEuB,OAAO,EAAE,IAAI,CAACpC,eAAe;MAAEe,QAAQ,EAAEA,QAAQ;MAAEsB,IAAI,EAAE,QAAQ;MAAEC,GAAG,EAAGnB,EAAE,IAAM,IAAI,CAAClB,YAAY,GAAGkB;IAAI,CAAC,EAAE9F,qDAAC,CAAC,MAAM,EAAE;MAAEkH,IAAI,EAAE;IAAc,CAAC,EAAE7D,QAAQ,CAAC,EAAEuD,IAAI,KAAK,IAAI,IAAI5G,qDAAC,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAE,CAAC;EACvpB;EACA,IAAI8F,EAAEA,CAAA,EAAG;IAAE,OAAO1F,qDAAU,CAAC,IAAI,CAAC;EAAE;AACtC,CAAC;AACDyB,cAAc,CAACsF,KAAK,GAAG;EACrBC,GAAG,EAAEzF,oBAAoB;EACzB0F,EAAE,EAAEzF;AACN,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-datetime-button.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, H as Host, f as getElement } from './index-2d388930.js';\nimport { a as addEventListener, c as componentOnReady } from './helpers-3379ba19.js';\nimport { a as printIonError } from './index-595d62c9.js';\nimport { c as createColorClasses } from './theme-17531cdf.js';\nimport { b as getIonMode } from './ionic-global-b3fc28dd.js';\nimport { q as parseDate, t as getToday, J as is24Hour, S as getLocalizedDateTime, G as getMonthAndYear, K as getLocalizedTime, T as getMonthDayAndYear } from './data-009dbf15.js';\n\nconst iosDatetimeButtonCss = \":host{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}:host button{border-radius:8px;-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;margin-top:0px;margin-bottom:0px;position:relative;-webkit-transition:150ms color ease-in-out;transition:150ms color ease-in-out;border:none;background:var(--ion-color-step-300, #edeef0);color:var(--ion-text-color, #000);font-family:inherit;font-size:inherit;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;overflow:hidden}:host(.time-active) #time-button,:host(.date-active) #date-button{color:var(--ion-color-base)}:host(.datetime-button-disabled){pointer-events:none}:host(.datetime-button-disabled) button{opacity:0.4}\";\n\nconst mdDatetimeButtonCss = \":host{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}:host button{border-radius:8px;-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;margin-top:0px;margin-bottom:0px;position:relative;-webkit-transition:150ms color ease-in-out;transition:150ms color ease-in-out;border:none;background:var(--ion-color-step-300, #edeef0);color:var(--ion-text-color, #000);font-family:inherit;font-size:inherit;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;overflow:hidden}:host(.time-active) #time-button,:host(.date-active) #date-button{color:var(--ion-color-base)}:host(.datetime-button-disabled){pointer-events:none}:host(.datetime-button-disabled) button{opacity:0.4}\";\n\nconst DatetimeButton = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.datetimeEl = null;\n    this.overlayEl = null;\n    /**\n     * Accepts one or more string values and converts\n     * them to DatetimeParts. This is done so datetime-button\n     * can work with an array internally and not need\n     * to keep checking if the datetime value is `string` or `string[]`.\n     */\n    this.getParsedDateValues = (value) => {\n      if (value === undefined || value === null) {\n        return [];\n      }\n      if (Array.isArray(value)) {\n        return value;\n      }\n      return [value];\n    };\n    /**\n     * Check the value property on the linked\n     * ion-datetime and then format it according\n     * to the locale specified on ion-datetime.\n     */\n    this.setDateTimeText = () => {\n      const { datetimeEl, datetimePresentation } = this;\n      if (!datetimeEl) {\n        return;\n      }\n      const { value, locale, hourCycle, preferWheel, multiple, titleSelectedDatesFormatter } = datetimeEl;\n      const parsedValues = this.getParsedDateValues(value);\n      /**\n       * Both ion-datetime and ion-datetime-button default\n       * to today's date and time if no value is set.\n       */\n      const parsedDatetimes = parseDate(parsedValues.length > 0 ? parsedValues : [getToday()]);\n      if (!parsedDatetimes) {\n        return;\n      }\n      /**\n       * If developers incorrectly use multiple=\"true\"\n       * with non \"date\" datetimes, then just select\n       * the first value so the interface does\n       * not appear broken. Datetime will provide a\n       * warning in the console.\n       */\n      const firstParsedDatetime = parsedDatetimes[0];\n      const use24Hour = is24Hour(locale, hourCycle);\n      this.dateText = this.timeText = undefined;\n      switch (datetimePresentation) {\n        case 'date-time':\n        case 'time-date':\n          const dateText = getMonthDayAndYear(locale, firstParsedDatetime);\n          const timeText = getLocalizedTime(locale, firstParsedDatetime, use24Hour);\n          if (preferWheel) {\n            this.dateText = `${dateText} ${timeText}`;\n          }\n          else {\n            this.dateText = dateText;\n            this.timeText = timeText;\n          }\n          break;\n        case 'date':\n          if (multiple && parsedValues.length !== 1) {\n            let headerText = `${parsedValues.length} days`; // default/fallback for multiple selection\n            if (titleSelectedDatesFormatter !== undefined) {\n              try {\n                headerText = titleSelectedDatesFormatter(parsedValues);\n              }\n              catch (e) {\n                printIonError('Exception in provided `titleSelectedDatesFormatter`: ', e);\n              }\n            }\n            this.dateText = headerText;\n          }\n          else {\n            this.dateText = getMonthDayAndYear(locale, firstParsedDatetime);\n          }\n          break;\n        case 'time':\n          this.timeText = getLocalizedTime(locale, firstParsedDatetime, use24Hour);\n          break;\n        case 'month-year':\n          this.dateText = getMonthAndYear(locale, firstParsedDatetime);\n          break;\n        case 'month':\n          this.dateText = getLocalizedDateTime(locale, firstParsedDatetime, { month: 'long' });\n          break;\n        case 'year':\n          this.dateText = getLocalizedDateTime(locale, firstParsedDatetime, { year: 'numeric' });\n          break;\n      }\n    };\n    /**\n     * Waits for the ion-datetime to re-render.\n     * This is needed in order to correctly position\n     * a popover relative to the trigger element.\n     */\n    this.waitForDatetimeChanges = async () => {\n      const { datetimeEl } = this;\n      if (!datetimeEl) {\n        return Promise.resolve();\n      }\n      return new Promise((resolve) => {\n        addEventListener(datetimeEl, 'ionRender', resolve, { once: true });\n      });\n    };\n    this.handleDateClick = async (ev) => {\n      const { datetimeEl, datetimePresentation } = this;\n      if (!datetimeEl) {\n        return;\n      }\n      let needsPresentationChange = false;\n      /**\n       * When clicking the date button,\n       * we need to make sure that only a date\n       * picker is displayed. For presentation styles\n       * that display content other than a date picker,\n       * we need to update the presentation style.\n       */\n      switch (datetimePresentation) {\n        case 'date-time':\n        case 'time-date':\n          const needsChange = datetimeEl.presentation !== 'date';\n          /**\n           * The date+time wheel picker\n           * shows date and time together,\n           * so do not adjust the presentation\n           * in that case.\n           */\n          if (!datetimeEl.preferWheel && needsChange) {\n            datetimeEl.presentation = 'date';\n            needsPresentationChange = true;\n          }\n          break;\n      }\n      /**\n       * Track which button was clicked\n       * so that it can have the correct\n       * activated styles applied when\n       * the modal/popover containing\n       * the datetime is opened.\n       */\n      this.selectedButton = 'date';\n      this.presentOverlay(ev, needsPresentationChange, this.dateTargetEl);\n    };\n    this.handleTimeClick = (ev) => {\n      const { datetimeEl, datetimePresentation } = this;\n      if (!datetimeEl) {\n        return;\n      }\n      let needsPresentationChange = false;\n      /**\n       * When clicking the time button,\n       * we need to make sure that only a time\n       * picker is displayed. For presentation styles\n       * that display content other than a time picker,\n       * we need to update the presentation style.\n       */\n      switch (datetimePresentation) {\n        case 'date-time':\n        case 'time-date':\n          const needsChange = datetimeEl.presentation !== 'time';\n          if (needsChange) {\n            datetimeEl.presentation = 'time';\n            needsPresentationChange = true;\n          }\n          break;\n      }\n      /**\n       * Track which button was clicked\n       * so that it can have the correct\n       * activated styles applied when\n       * the modal/popover containing\n       * the datetime is opened.\n       */\n      this.selectedButton = 'time';\n      this.presentOverlay(ev, needsPresentationChange, this.timeTargetEl);\n    };\n    /**\n     * If the datetime is presented in an\n     * overlay, the datetime and overlay\n     * should be appropriately sized.\n     * These classes provide default sizing values\n     * that developers can customize.\n     * The goal is to provide an overlay that is\n     * reasonably sized with a datetime that\n     * fills the entire container.\n     */\n    this.presentOverlay = async (ev, needsPresentationChange, triggerEl) => {\n      const { overlayEl } = this;\n      if (!overlayEl) {\n        return;\n      }\n      if (overlayEl.tagName === 'ION-POPOVER') {\n        /**\n         * When the presentation on datetime changes,\n         * we need to wait for the component to re-render\n         * otherwise the computed width/height of the\n         * popover content will be wrong, causing\n         * the popover to not align with the trigger element.\n         */\n        if (needsPresentationChange) {\n          await this.waitForDatetimeChanges();\n        }\n        /**\n         * We pass the trigger button element\n         * so that the popover aligns with the individual\n         * button that was clicked, not the component container.\n         */\n        overlayEl.present(Object.assign(Object.assign({}, ev), { detail: {\n            ionShadowTarget: triggerEl,\n          } }));\n      }\n      else {\n        overlayEl.present();\n      }\n    };\n    this.datetimePresentation = 'date-time';\n    this.dateText = undefined;\n    this.timeText = undefined;\n    this.datetimeActive = false;\n    this.selectedButton = undefined;\n    this.color = 'primary';\n    this.disabled = false;\n    this.datetime = undefined;\n  }\n  async componentWillLoad() {\n    const { datetime } = this;\n    if (!datetime) {\n      printIonError('An ID associated with an ion-datetime instance is required for ion-datetime-button to function properly.', this.el);\n      return;\n    }\n    const datetimeEl = (this.datetimeEl = document.getElementById(datetime));\n    if (!datetimeEl) {\n      printIonError(`No ion-datetime instance found for ID '${datetime}'.`, this.el);\n      return;\n    }\n    /**\n     * The element reference must be an ion-datetime. Print an error\n     * if a non-datetime element was provided.\n     */\n    if (datetimeEl.tagName !== 'ION-DATETIME') {\n      printIonError(`Expected an ion-datetime instance for ID '${datetime}' but received '${datetimeEl.tagName.toLowerCase()}' instead.`, datetimeEl);\n      return;\n    }\n    /**\n     * Since the datetime can be used in any context (overlays, accordion, etc)\n     * we track when it is visible to determine when it is active.\n     * This informs which button is highlighted as well as the\n     * aria-expanded state.\n     */\n    const io = new IntersectionObserver((entries) => {\n      const ev = entries[0];\n      this.datetimeActive = ev.isIntersecting;\n    }, {\n      threshold: 0.01,\n    });\n    io.observe(datetimeEl);\n    /**\n     * Get a reference to any modal/popover\n     * the datetime is being used in so we can\n     * correctly size it when it is presented.\n     */\n    const overlayEl = (this.overlayEl = datetimeEl.closest('ion-modal, ion-popover'));\n    /**\n     * The .ion-datetime-button-overlay class contains\n     * styles that allow any modal/popover to be\n     * sized according to the dimensions of the datetime.\n     * If developers want a smaller/larger overlay all they need\n     * to do is change the width/height of the datetime.\n     * Additionally, this lets us avoid having to set\n     * explicit widths on each variant of datetime.\n     */\n    if (overlayEl) {\n      overlayEl.classList.add('ion-datetime-button-overlay');\n    }\n    componentOnReady(datetimeEl, () => {\n      const datetimePresentation = (this.datetimePresentation = datetimeEl.presentation || 'date-time');\n      /**\n       * Set the initial display\n       * in the rendered buttons.\n       *\n       * From there, we need to listen\n       * for ionChange to be emitted\n       * from datetime so we know when\n       * to re-render the displayed\n       * text in the buttons.\n       */\n      this.setDateTimeText();\n      addEventListener(datetimeEl, 'ionValueChange', this.setDateTimeText);\n      /**\n       * Configure the initial selected button\n       * in the event that the datetime is displayed\n       * without clicking one of the datetime buttons.\n       * For example, a datetime could be expanded\n       * in an accordion. In this case users only\n       * need to click the accordion header to show\n       * the datetime.\n       */\n      switch (datetimePresentation) {\n        case 'date-time':\n        case 'date':\n        case 'month-year':\n        case 'month':\n        case 'year':\n          this.selectedButton = 'date';\n          break;\n        case 'time-date':\n        case 'time':\n          this.selectedButton = 'time';\n          break;\n      }\n    });\n  }\n  render() {\n    const { color, dateText, timeText, selectedButton, datetimeActive, disabled } = this;\n    const mode = getIonMode(this);\n    return (h(Host, { class: createColorClasses(color, {\n        [mode]: true,\n        [`${selectedButton}-active`]: datetimeActive,\n        ['datetime-button-disabled']: disabled,\n      }) }, dateText && (h(\"button\", { class: \"ion-activatable\", id: \"date-button\", \"aria-expanded\": datetimeActive ? 'true' : 'false', onClick: this.handleDateClick, disabled: disabled, part: \"native\", ref: (el) => (this.dateTargetEl = el) }, h(\"slot\", { name: \"date-target\" }, dateText), mode === 'md' && h(\"ion-ripple-effect\", null))), timeText && (h(\"button\", { class: \"ion-activatable\", id: \"time-button\", \"aria-expanded\": datetimeActive ? 'true' : 'false', onClick: this.handleTimeClick, disabled: disabled, part: \"native\", ref: (el) => (this.timeTargetEl = el) }, h(\"slot\", { name: \"time-target\" }, timeText), mode === 'md' && h(\"ion-ripple-effect\", null)))));\n  }\n  get el() { return getElement(this); }\n};\nDatetimeButton.style = {\n  ios: iosDatetimeButtonCss,\n  md: mdDatetimeButtonCss\n};\n\nexport { DatetimeButton as ion_datetime_button };\n"], "names": ["r", "registerInstance", "h", "H", "Host", "f", "getElement", "a", "addEventListener", "c", "componentOnReady", "printIonError", "createColorClasses", "b", "getIonMode", "q", "parseDate", "t", "get<PERSON><PERSON>y", "J", "is24Hour", "S", "getLocalizedDateTime", "G", "getMonthAndYear", "K", "getLocalizedTime", "T", "getMonthDayAndYear", "iosDatetimeButtonCss", "mdDatetimeButtonCss", "DatetimeButton", "constructor", "hostRef", "_this", "datetimeEl", "overlayEl", "getParsedDateValues", "value", "undefined", "Array", "isArray", "setDateTimeText", "datetimePresentation", "locale", "hourCycle", "preferWheel", "multiple", "titleSelectedDatesFormatter", "parsed<PERSON><PERSON>ues", "parsedDatetimes", "length", "firstParsedDatetime", "use24Hour", "dateText", "timeText", "headerText", "e", "month", "year", "waitForDatetimeChanges", "_asyncToGenerator", "Promise", "resolve", "once", "handleDateClick", "_ref2", "ev", "needsPresentationChange", "needsChange", "presentation", "<PERSON><PERSON><PERSON><PERSON>", "presentOverlay", "dateTargetEl", "_x", "apply", "arguments", "handleTimeClick", "timeTargetEl", "_ref3", "triggerEl", "tagName", "present", "Object", "assign", "detail", "ionShadowTarget", "_x2", "_x3", "_x4", "datetimeActive", "color", "disabled", "datetime", "componentWillLoad", "_this2", "el", "document", "getElementById", "toLowerCase", "io", "IntersectionObserver", "entries", "isIntersecting", "threshold", "observe", "closest", "classList", "add", "render", "mode", "class", "id", "onClick", "part", "ref", "name", "style", "ios", "md", "ion_datetime_button"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}