{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-tab-bar_2_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC6G;AAC3B;AACpB;AACY;AACX;AAClC;AACG;AACC;AAEjC,MAAMiB,YAAY,GAAG,y1EAAy1E;AAE92E,MAAMC,WAAW,GAAG,62DAA62D;AAEj4D,MAAMC,MAAM,GAAG,MAAM;EACnBC,WAAWA,CAACC,OAAO,EAAE;IACnBpB,qDAAgB,CAAC,IAAI,EAAEoB,OAAO,CAAC;IAC/B,IAAI,CAACC,gBAAgB,GAAGnB,qDAAW,CAAC,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAC;IAChE,IAAI,CAACoB,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,KAAK,GAAGC,SAAS;IACtB,IAAI,CAACC,WAAW,GAAGD,SAAS;IAC5B,IAAI,CAACE,WAAW,GAAG,KAAK;EAC1B;EACAC,kBAAkBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACF,WAAW,KAAKD,SAAS,EAAE;MAClC,IAAI,CAACJ,gBAAgB,CAACQ,IAAI,CAAC;QACzBC,GAAG,EAAE,IAAI,CAACJ;MACZ,CAAC,CAAC;IACJ;EACF;EACAK,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAACH,kBAAkB,CAAC,CAAC;EAC3B;EACMI,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,8KAAA;MACxBD,KAAI,CAACX,YAAY,SAASb,mEAAwB;QAAA,IAAA0B,IAAA,GAAAD,8KAAA,CAAC,WAAOE,YAAY,EAAEC,aAAa,EAAK;UACxF;AACN;AACA;AACA;AACA;UACM,IAAID,YAAY,KAAK,KAAK,IAAIC,aAAa,KAAKZ,SAAS,EAAE;YACzD,MAAMY,aAAa;UACrB;UACAJ,KAAI,CAACV,eAAe,GAAGa,YAAY,CAAC,CAAC;QACvC,CAAC;QAAA,iBAAAE,EAAA,EAAAC,GAAA;UAAA,OAAAJ,IAAA,CAAAK,KAAA,OAAAC,SAAA;QAAA;MAAA,IAAC;IAAC;EACL;EACAC,oBAAoBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACpB,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAACqB,OAAO,CAAC,CAAC;IAC7B;EACF;EACAC,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEpB,KAAK;MAAEG,WAAW;MAAEJ;IAAgB,CAAC,GAAG,IAAI;IACpD,MAAMsB,IAAI,GAAGjC,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAMkC,UAAU,GAAGvB,eAAe,IAAI,IAAI,CAACwB,EAAE,CAACC,YAAY,CAAC,MAAM,CAAC,KAAK,KAAK;IAC5E,OAAQ7C,qDAAC,CAACE,iDAAI,EAAE;MAAE4C,IAAI,EAAE,SAAS;MAAE,aAAa,EAAEH,UAAU,GAAG,MAAM,GAAG,IAAI;MAAEI,KAAK,EAAExC,qDAAkB,CAACc,KAAK,EAAE;QAC3G,CAACqB,IAAI,GAAG,IAAI;QACZ,qBAAqB,EAAElB,WAAW;QAClC,gBAAgB,EAAEmB;MACpB,CAAC;IAAE,CAAC,EAAE3C,qDAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;EAC1B;EACA,IAAI4C,EAAEA,CAAA,EAAG;IAAE,OAAOxC,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW4C,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC7B,aAAa,EAAE,CAAC,oBAAoB;IACtC,CAAC;EAAE;AACL,CAAC;AACDjC,MAAM,CAACkC,KAAK,GAAG;EACbC,GAAG,EAAErC,YAAY;EACjBsC,EAAE,EAAErC;AACN,CAAC;AAED,MAAMsC,eAAe,GAAG,+jOAA+jO;AAEvlO,MAAMC,cAAc,GAAG,mkPAAmkP;AAE1lP,MAAMC,SAAS,GAAG,MAAM;EACtBtC,WAAWA,CAACC,OAAO,EAAE;IACnBpB,qDAAgB,CAAC,IAAI,EAAEoB,OAAO,CAAC;IAC/B,IAAI,CAACsC,iBAAiB,GAAGxD,qDAAW,CAAC,IAAI,EAAE,mBAAmB,EAAE,CAAC,CAAC;IAClE,IAAI,CAACyD,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,OAAO,GAAIC,EAAE,IAAK;MACrB,IAAIA,EAAE,CAACC,GAAG,KAAK,OAAO,IAAID,EAAE,CAACC,GAAG,KAAK,GAAG,EAAE;QACxC,IAAI,CAACC,SAAS,CAACF,EAAE,CAAC;MACpB;IACF,CAAC;IACD,IAAI,CAACG,OAAO,GAAIH,EAAE,IAAK;MACrB,IAAI,CAACE,SAAS,CAACF,EAAE,CAAC;IACpB,CAAC;IACD,IAAI,CAACI,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAGzC,SAAS;IACzB,IAAI,CAAC0C,IAAI,GAAG1C,SAAS;IACrB,IAAI,CAAC2C,GAAG,GAAG3C,SAAS;IACpB,IAAI,CAAC4C,MAAM,GAAG5C,SAAS;IACvB,IAAI,CAAC6C,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACxC,GAAG,GAAGL,SAAS;IACpB,IAAI,CAAC8C,MAAM,GAAG9C,SAAS;EACzB;EACA+C,eAAeA,CAACX,EAAE,EAAE;IAClB,MAAMY,cAAc,GAAGZ,EAAE,CAACU,MAAM;IAChC,MAAMG,MAAM,GAAG,IAAI,CAAC3B,EAAE,CAAC4B,aAAa;IACpC,IAAId,EAAE,CAACe,YAAY,CAAC,CAAC,CAACC,QAAQ,CAACH,MAAM,CAAC,KAAKD,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACK,QAAQ,CAAC,IAAI,CAAC/B,EAAE,CAAC,CAAC,EAAE;MAC5I,IAAI,CAACuB,QAAQ,GAAG,IAAI,CAACxC,GAAG,KAAK+B,EAAE,CAACkB,MAAM,CAACjD,GAAG;IAC5C;EACF;EACAC,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAAC4B,mBAAmB,GAAGqB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAElE,uDAAiB,CAAC,IAAI,CAACgC,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC;IACxF,IAAI,IAAI,CAACsB,MAAM,KAAK5C,SAAS,EAAE;MAC7B,IAAI,CAAC4C,MAAM,GAAGxD,wDAAM,CAACqE,GAAG,CAAC,iBAAiB,EAAE,UAAU,CAAC;IACzD;EACF;EACAnB,SAASA,CAACF,EAAE,EAAE;IACZ,IAAI,IAAI,CAAC/B,GAAG,KAAKL,SAAS,EAAE;MAC1B,IAAI,CAAC,IAAI,CAACwC,QAAQ,EAAE;QAClB,IAAI,CAACP,iBAAiB,CAAC7B,IAAI,CAAC;UAC1BC,GAAG,EAAE,IAAI,CAACA,GAAG;UACbqC,IAAI,EAAE,IAAI,CAACA,IAAI;UACfG,QAAQ,EAAE,IAAI,CAACA;QACjB,CAAC,CAAC;MACJ;MACAT,EAAE,CAACsB,cAAc,CAAC,CAAC;IACrB;EACF;EACA,IAAIC,QAAQA,CAAA,EAAG;IACb,OAAO,CAAC,CAAC,IAAI,CAACrC,EAAE,CAACsC,aAAa,CAAC,WAAW,CAAC;EAC7C;EACA,IAAIC,OAAOA,CAAA,EAAG;IACZ,OAAO,CAAC,CAAC,IAAI,CAACvC,EAAE,CAACsC,aAAa,CAAC,UAAU,CAAC;EAC5C;EACAzC,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEqB,QAAQ;MAAEqB,OAAO;MAAEF,QAAQ;MAAEjB,IAAI;MAAEC,GAAG;MAAEG,MAAM;MAAEF,MAAM;MAAEC,QAAQ;MAAExC,GAAG;MAAE6B;IAAoB,CAAC,GAAG,IAAI;IAC3G,MAAMd,IAAI,GAAGjC,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM2E,KAAK,GAAG;MACZrB,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,IAAI;MACJC,GAAG;MACHG;IACF,CAAC;IACD,OAAQpE,qDAAC,CAACE,iDAAI,EAAE;MAAE2D,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEwB,OAAO,EAAE,IAAI,CAAC5B,OAAO;MAAE6B,EAAE,EAAE3D,GAAG,KAAKL,SAAS,GAAI,cAAaK,GAAI,EAAC,GAAG,IAAI;MAAEoB,KAAK,EAAE;QACvH,CAACL,IAAI,GAAG,IAAI;QACZ,cAAc,EAAEyB,QAAQ;QACxB,cAAc,EAAEL,QAAQ;QACxB,eAAe,EAAEmB,QAAQ;QACzB,cAAc,EAAEE,OAAO;QACvB,oBAAoB,EAAEF,QAAQ,IAAI,CAACE,OAAO;QAC1C,mBAAmB,EAAEA,OAAO,IAAI,CAACF,QAAQ;QACzC,CAAE,cAAaf,MAAO,EAAC,GAAG,IAAI;QAC9B,iBAAiB,EAAE,IAAI;QACvB,gBAAgB,EAAE,IAAI;QACtB,eAAe,EAAE;MACnB;IAAE,CAAC,EAAElE,qDAAC,CAAC,GAAG,EAAE6E,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEM,KAAK,EAAE;MAAErC,KAAK,EAAE,eAAe;MAAEwC,IAAI,EAAE,QAAQ;MAAEzC,IAAI,EAAE,KAAK;MAAE,eAAe,EAAEqB,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAE,eAAe,EAAEL,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAE0B,QAAQ,EAAE1B,QAAQ,GAAG,IAAI,GAAGxC;IAAU,CAAC,EAAEkC,mBAAmB,CAAC,EAAExD,qDAAC,CAAC,MAAM,EAAE;MAAE+C,KAAK,EAAE;IAAe,CAAC,EAAE/C,qDAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,EAAE0C,IAAI,KAAK,IAAI,IAAI1C,qDAAC,CAAC,mBAAmB,EAAE;MAAEyF,IAAI,EAAE;IAAY,CAAC,CAAC,CAAC,CAAC;EAC5W;EACA,IAAI7C,EAAEA,CAAA,EAAG;IAAE,OAAOxC,qDAAU,CAAC,IAAI,CAAC;EAAE;AACtC,CAAC;AACDkD,SAAS,CAACL,KAAK,GAAG;EAChBC,GAAG,EAAEE,eAAe;EACpBD,EAAE,EAAEE;AACN,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-tab-bar_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement } from './index-2d388930.js';\nimport { c as create<PERSON>eyboardController } from './keyboard-controller-0c2dce71.js';\nimport { c as createColorClasses } from './theme-17531cdf.js';\nimport { b as getIonMode, c as config } from './ionic-global-b3fc28dd.js';\nimport { k as inheritAttributes } from './helpers-3379ba19.js';\nimport './index-7a14ecec.js';\nimport './keyboard-b063f012.js';\nimport './capacitor-b4979570.js';\n\nconst tabBarIosCss = \":host{-webkit-padding-start:var(--ion-safe-area-left);padding-inline-start:var(--ion-safe-area-left);-webkit-padding-end:var(--ion-safe-area-right);padding-inline-end:var(--ion-safe-area-right);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:auto;padding-bottom:var(--ion-safe-area-bottom, 0);border-top:var(--border);background:var(--background);color:var(--color);text-align:center;contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:10;-webkit-box-sizing:content-box !important;box-sizing:content-box !important}:host(.ion-color) ::slotted(ion-tab-button){--background-focused:var(--ion-color-shade);--color-selected:var(--ion-color-contrast)}:host(.ion-color) ::slotted(.tab-selected){color:var(--ion-color-contrast)}:host(.ion-color),:host(.ion-color) ::slotted(ion-tab-button){color:rgba(var(--ion-color-contrast-rgb), 0.7)}:host(.ion-color),:host(.ion-color) ::slotted(ion-tab-button){background:var(--ion-color-base)}:host(.ion-color) ::slotted(ion-tab-button.ion-focused),:host(.tab-bar-translucent) ::slotted(ion-tab-button.ion-focused){background:var(--background-focused)}:host(.tab-bar-translucent) ::slotted(ion-tab-button){background:transparent}:host([slot=top]){padding-top:var(--ion-safe-area-top, 0);padding-bottom:0;border-top:0;border-bottom:var(--border)}:host(.tab-bar-hidden){display:none !important}:host{--background:var(--ion-tab-bar-background, var(--ion-color-step-50, #f7f7f7));--background-focused:var(--ion-tab-bar-background-focused, #e0e0e0);--border:0.55px solid var(--ion-tab-bar-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.2))));--color:var(--ion-tab-bar-color, var(--ion-color-step-600, #666666));--color-selected:var(--ion-tab-bar-color-selected, var(--ion-color-primary, #3880ff));height:50px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.tab-bar-translucent){--background:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(210%) blur(20px);backdrop-filter:saturate(210%) blur(20px)}:host(.ion-color.tab-bar-translucent){background:rgba(var(--ion-color-base-rgb), 0.8)}:host(.tab-bar-translucent) ::slotted(ion-tab-button.ion-focused){background:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.6)}}\";\n\nconst tabBarMdCss = \":host{-webkit-padding-start:var(--ion-safe-area-left);padding-inline-start:var(--ion-safe-area-left);-webkit-padding-end:var(--ion-safe-area-right);padding-inline-end:var(--ion-safe-area-right);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:auto;padding-bottom:var(--ion-safe-area-bottom, 0);border-top:var(--border);background:var(--background);color:var(--color);text-align:center;contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:10;-webkit-box-sizing:content-box !important;box-sizing:content-box !important}:host(.ion-color) ::slotted(ion-tab-button){--background-focused:var(--ion-color-shade);--color-selected:var(--ion-color-contrast)}:host(.ion-color) ::slotted(.tab-selected){color:var(--ion-color-contrast)}:host(.ion-color),:host(.ion-color) ::slotted(ion-tab-button){color:rgba(var(--ion-color-contrast-rgb), 0.7)}:host(.ion-color),:host(.ion-color) ::slotted(ion-tab-button){background:var(--ion-color-base)}:host(.ion-color) ::slotted(ion-tab-button.ion-focused),:host(.tab-bar-translucent) ::slotted(ion-tab-button.ion-focused){background:var(--background-focused)}:host(.tab-bar-translucent) ::slotted(ion-tab-button){background:transparent}:host([slot=top]){padding-top:var(--ion-safe-area-top, 0);padding-bottom:0;border-top:0;border-bottom:var(--border)}:host(.tab-bar-hidden){display:none !important}:host{--background:var(--ion-tab-bar-background, var(--ion-background-color, #fff));--background-focused:var(--ion-tab-bar-background-focused, #e0e0e0);--border:1px solid var(--ion-tab-bar-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.07))));--color:var(--ion-tab-bar-color, var(--ion-color-step-650, #595959));--color-selected:var(--ion-tab-bar-color-selected, var(--ion-color-primary, #3880ff));height:56px}\";\n\nconst TabBar = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionTabBarChanged = createEvent(this, \"ionTabBarChanged\", 7);\n    this.keyboardCtrl = null;\n    this.keyboardVisible = false;\n    this.color = undefined;\n    this.selectedTab = undefined;\n    this.translucent = false;\n  }\n  selectedTabChanged() {\n    if (this.selectedTab !== undefined) {\n      this.ionTabBarChanged.emit({\n        tab: this.selectedTab,\n      });\n    }\n  }\n  componentWillLoad() {\n    this.selectedTabChanged();\n  }\n  async connectedCallback() {\n    this.keyboardCtrl = await createKeyboardController(async (keyboardOpen, waitForResize) => {\n      /**\n       * If the keyboard is hiding, then we need to wait\n       * for the webview to resize. Otherwise, the tab bar\n       * will flicker before the webview resizes.\n       */\n      if (keyboardOpen === false && waitForResize !== undefined) {\n        await waitForResize;\n      }\n      this.keyboardVisible = keyboardOpen; // trigger re-render by updating state\n    });\n  }\n  disconnectedCallback() {\n    if (this.keyboardCtrl) {\n      this.keyboardCtrl.destroy();\n    }\n  }\n  render() {\n    const { color, translucent, keyboardVisible } = this;\n    const mode = getIonMode(this);\n    const shouldHide = keyboardVisible && this.el.getAttribute('slot') !== 'top';\n    return (h(Host, { role: \"tablist\", \"aria-hidden\": shouldHide ? 'true' : null, class: createColorClasses(color, {\n        [mode]: true,\n        'tab-bar-translucent': translucent,\n        'tab-bar-hidden': shouldHide,\n      }) }, h(\"slot\", null)));\n  }\n  get el() { return getElement(this); }\n  static get watchers() { return {\n    \"selectedTab\": [\"selectedTabChanged\"]\n  }; }\n};\nTabBar.style = {\n  ios: tabBarIosCss,\n  md: tabBarMdCss\n};\n\nconst tabButtonIosCss = \":host{--ripple-color:var(--color-selected);--background-focused-opacity:1;-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;height:100%;outline:none;background:var(--background);color:var(--color)}.button-native{border-radius:inherit;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;height:100%;border:0;outline:none;background:transparent;text-decoration:none;cursor:pointer;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-user-drag:none}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:inherit;flex-flow:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;height:100%;z-index:1}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}@media (any-hover: hover){a:hover{color:var(--color-selected)}}:host(.tab-selected){color:var(--color-selected)}:host(.tab-hidden){display:none !important}:host(.tab-disabled){pointer-events:none;opacity:0.4}::slotted(ion-label),::slotted(ion-icon){display:block;-ms-flex-item-align:center;align-self:center;max-width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box}::slotted(ion-label){-ms-flex-order:0;order:0}::slotted(ion-icon){-ms-flex-order:-1;order:-1;height:1em}:host(.tab-has-label-only) ::slotted(ion-label){white-space:normal}::slotted(ion-badge){-webkit-box-sizing:border-box;box-sizing:border-box;position:absolute;z-index:1}:host(.tab-layout-icon-start){-ms-flex-direction:row;flex-direction:row}:host(.tab-layout-icon-end){-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.tab-layout-icon-bottom){-ms-flex-direction:column-reverse;flex-direction:column-reverse}:host(.tab-layout-icon-hide) ::slotted(ion-icon){display:none}:host(.tab-layout-label-hide) ::slotted(ion-label){display:none}ion-ripple-effect{color:var(--ripple-color)}:host{--padding-top:0;--padding-end:2px;--padding-bottom:0;--padding-start:2px;max-width:240px;font-size:10px}::slotted(ion-badge){-webkit-padding-start:6px;padding-inline-start:6px;-webkit-padding-end:6px;padding-inline-end:6px;padding-top:1px;padding-bottom:1px;top:4px;height:auto;font-size:12px;line-height:16px}@supports (inset-inline-start: 0){::slotted(ion-badge){inset-inline-start:calc(50% + 6px)}}@supports not (inset-inline-start: 0){::slotted(ion-badge){left:calc(50% + 6px)}:host-context([dir=rtl]) ::slotted(ion-badge){left:unset;right:unset;right:calc(50% + 6px)}[dir=rtl] ::slotted(ion-badge){left:unset;right:unset;right:calc(50% + 6px)}@supports selector(:dir(rtl)){::slotted(ion-badge):dir(rtl){left:unset;right:unset;right:calc(50% + 6px)}}}::slotted(ion-icon){margin-top:2px;margin-bottom:2px;font-size:30px}::slotted(ion-icon::before){vertical-align:top}::slotted(ion-label){margin-top:0;margin-bottom:1px;min-height:11px;font-weight:500}:host(.tab-has-label-only) ::slotted(ion-label){margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px;font-size:12px;font-size:14px;line-height:1.1}:host(.tab-layout-icon-end) ::slotted(ion-label),:host(.tab-layout-icon-start) ::slotted(ion-label),:host(.tab-layout-icon-hide) ::slotted(ion-label){margin-top:2px;margin-bottom:2px;font-size:14px;line-height:1.1}:host(.tab-layout-icon-end) ::slotted(ion-icon),:host(.tab-layout-icon-start) ::slotted(ion-icon){min-width:24px;height:26px;margin-top:2px;margin-bottom:1px;font-size:24px}@supports (inset-inline-start: 0){:host(.tab-layout-icon-bottom) ::slotted(ion-badge){inset-inline-start:calc(50% + 12px)}}@supports not (inset-inline-start: 0){:host(.tab-layout-icon-bottom) ::slotted(ion-badge){left:calc(50% + 12px)}:host-context([dir=rtl]):host(.tab-layout-icon-bottom) ::slotted(ion-badge),:host-context([dir=rtl]).tab-layout-icon-bottom ::slotted(ion-badge){left:unset;right:unset;right:calc(50% + 12px)}@supports selector(:dir(rtl)){:host(.tab-layout-icon-bottom) ::slotted(ion-badge):dir(rtl){left:unset;right:unset;right:calc(50% + 12px)}}}:host(.tab-layout-icon-bottom) ::slotted(ion-icon){margin-top:0;margin-bottom:1px}:host(.tab-layout-icon-bottom) ::slotted(ion-label){margin-top:4px}:host(.tab-layout-icon-start) ::slotted(ion-badge),:host(.tab-layout-icon-end) ::slotted(ion-badge){top:10px}@supports (inset-inline-start: 0){:host(.tab-layout-icon-start) ::slotted(ion-badge),:host(.tab-layout-icon-end) ::slotted(ion-badge){inset-inline-start:calc(50% + 35px)}}@supports not (inset-inline-start: 0){:host(.tab-layout-icon-start) ::slotted(ion-badge),:host(.tab-layout-icon-end) ::slotted(ion-badge){left:calc(50% + 35px)}:host-context([dir=rtl]):host(.tab-layout-icon-start) ::slotted(ion-badge),:host-context([dir=rtl]).tab-layout-icon-start ::slotted(ion-badge),:host-context([dir=rtl]):host(.tab-layout-icon-end) ::slotted(ion-badge),:host-context([dir=rtl]).tab-layout-icon-end ::slotted(ion-badge){left:unset;right:unset;right:calc(50% + 35px)}@supports selector(:dir(rtl)){:host(.tab-layout-icon-start) ::slotted(ion-badge):dir(rtl),:host(.tab-layout-icon-end) ::slotted(ion-badge):dir(rtl){left:unset;right:unset;right:calc(50% + 35px)}}}:host(.tab-layout-icon-hide) ::slotted(ion-badge),:host(.tab-has-label-only) ::slotted(ion-badge){top:10px}@supports (inset-inline-start: 0){:host(.tab-layout-icon-hide) ::slotted(ion-badge),:host(.tab-has-label-only) ::slotted(ion-badge){inset-inline-start:calc(50% + 30px)}}@supports not (inset-inline-start: 0){:host(.tab-layout-icon-hide) ::slotted(ion-badge),:host(.tab-has-label-only) ::slotted(ion-badge){left:calc(50% + 30px)}:host-context([dir=rtl]):host(.tab-layout-icon-hide) ::slotted(ion-badge),:host-context([dir=rtl]).tab-layout-icon-hide ::slotted(ion-badge),:host-context([dir=rtl]):host(.tab-has-label-only) ::slotted(ion-badge),:host-context([dir=rtl]).tab-has-label-only ::slotted(ion-badge){left:unset;right:unset;right:calc(50% + 30px)}@supports selector(:dir(rtl)){:host(.tab-layout-icon-hide) ::slotted(ion-badge):dir(rtl),:host(.tab-has-label-only) ::slotted(ion-badge):dir(rtl){left:unset;right:unset;right:calc(50% + 30px)}}}:host(.tab-layout-label-hide) ::slotted(ion-badge),:host(.tab-has-icon-only) ::slotted(ion-badge){top:10px}:host(.tab-layout-label-hide) ::slotted(ion-icon){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}\";\n\nconst tabButtonMdCss = \":host{--ripple-color:var(--color-selected);--background-focused-opacity:1;-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;height:100%;outline:none;background:var(--background);color:var(--color)}.button-native{border-radius:inherit;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;height:100%;border:0;outline:none;background:transparent;text-decoration:none;cursor:pointer;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-user-drag:none}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:inherit;flex-flow:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;height:100%;z-index:1}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}@media (any-hover: hover){a:hover{color:var(--color-selected)}}:host(.tab-selected){color:var(--color-selected)}:host(.tab-hidden){display:none !important}:host(.tab-disabled){pointer-events:none;opacity:0.4}::slotted(ion-label),::slotted(ion-icon){display:block;-ms-flex-item-align:center;align-self:center;max-width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box}::slotted(ion-label){-ms-flex-order:0;order:0}::slotted(ion-icon){-ms-flex-order:-1;order:-1;height:1em}:host(.tab-has-label-only) ::slotted(ion-label){white-space:normal}::slotted(ion-badge){-webkit-box-sizing:border-box;box-sizing:border-box;position:absolute;z-index:1}:host(.tab-layout-icon-start){-ms-flex-direction:row;flex-direction:row}:host(.tab-layout-icon-end){-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.tab-layout-icon-bottom){-ms-flex-direction:column-reverse;flex-direction:column-reverse}:host(.tab-layout-icon-hide) ::slotted(ion-icon){display:none}:host(.tab-layout-label-hide) ::slotted(ion-label){display:none}ion-ripple-effect{color:var(--ripple-color)}:host{--padding-top:0;--padding-end:12px;--padding-bottom:0;--padding-start:12px;max-width:168px;font-size:12px;font-weight:normal;letter-spacing:0.03em}::slotted(ion-label){margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px;text-transform:none}::slotted(ion-icon){margin-left:0;margin-right:0;margin-top:16px;margin-bottom:16px;-webkit-transform-origin:center center;transform-origin:center center;font-size:22px}:host-context([dir=rtl]) ::slotted(ion-icon){-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}[dir=rtl] ::slotted(ion-icon){-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}@supports selector(:dir(rtl)){::slotted(ion-icon):dir(rtl){-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}}::slotted(ion-badge){border-radius:8px;-webkit-padding-start:2px;padding-inline-start:2px;-webkit-padding-end:2px;padding-inline-end:2px;padding-top:3px;padding-bottom:2px;top:8px;min-width:12px;font-size:8px;font-weight:normal}@supports (inset-inline-start: 0){::slotted(ion-badge){inset-inline-start:calc(50% + 6px)}}@supports not (inset-inline-start: 0){::slotted(ion-badge){left:calc(50% + 6px)}:host-context([dir=rtl]) ::slotted(ion-badge){left:unset;right:unset;right:calc(50% + 6px)}[dir=rtl] ::slotted(ion-badge){left:unset;right:unset;right:calc(50% + 6px)}@supports selector(:dir(rtl)){::slotted(ion-badge):dir(rtl){left:unset;right:unset;right:calc(50% + 6px)}}}::slotted(ion-badge:empty){display:block;min-width:8px;height:8px}:host(.tab-layout-icon-top) ::slotted(ion-icon){margin-top:6px;margin-bottom:2px}:host(.tab-layout-icon-top) ::slotted(ion-label){margin-top:0;margin-bottom:6px}:host(.tab-layout-icon-bottom) ::slotted(ion-badge){top:8px}@supports (inset-inline-start: 0){:host(.tab-layout-icon-bottom) ::slotted(ion-badge){inset-inline-start:70%}}@supports not (inset-inline-start: 0){:host(.tab-layout-icon-bottom) ::slotted(ion-badge){left:70%}:host-context([dir=rtl]):host(.tab-layout-icon-bottom) ::slotted(ion-badge),:host-context([dir=rtl]).tab-layout-icon-bottom ::slotted(ion-badge){left:unset;right:unset;right:70%}@supports selector(:dir(rtl)){:host(.tab-layout-icon-bottom) ::slotted(ion-badge):dir(rtl){left:unset;right:unset;right:70%}}}:host(.tab-layout-icon-bottom) ::slotted(ion-icon){margin-top:0;margin-bottom:6px}:host(.tab-layout-icon-bottom) ::slotted(ion-label){margin-top:6px;margin-bottom:0}:host(.tab-layout-icon-start) ::slotted(ion-badge),:host(.tab-layout-icon-end) ::slotted(ion-badge){top:16px}@supports (inset-inline-start: 0){:host(.tab-layout-icon-start) ::slotted(ion-badge),:host(.tab-layout-icon-end) ::slotted(ion-badge){inset-inline-start:80%}}@supports not (inset-inline-start: 0){:host(.tab-layout-icon-start) ::slotted(ion-badge),:host(.tab-layout-icon-end) ::slotted(ion-badge){left:80%}:host-context([dir=rtl]):host(.tab-layout-icon-start) ::slotted(ion-badge),:host-context([dir=rtl]).tab-layout-icon-start ::slotted(ion-badge),:host-context([dir=rtl]):host(.tab-layout-icon-end) ::slotted(ion-badge),:host-context([dir=rtl]).tab-layout-icon-end ::slotted(ion-badge){left:unset;right:unset;right:80%}@supports selector(:dir(rtl)){:host(.tab-layout-icon-start) ::slotted(ion-badge):dir(rtl),:host(.tab-layout-icon-end) ::slotted(ion-badge):dir(rtl){left:unset;right:unset;right:80%}}}:host(.tab-layout-icon-start) ::slotted(ion-icon){-webkit-margin-end:6px;margin-inline-end:6px}:host(.tab-layout-icon-end) ::slotted(ion-icon){-webkit-margin-start:6px;margin-inline-start:6px}:host(.tab-layout-icon-hide) ::slotted(ion-badge),:host(.tab-has-label-only) ::slotted(ion-badge){top:16px}@supports (inset-inline-start: 0){:host(.tab-layout-icon-hide) ::slotted(ion-badge),:host(.tab-has-label-only) ::slotted(ion-badge){inset-inline-start:70%}}@supports not (inset-inline-start: 0){:host(.tab-layout-icon-hide) ::slotted(ion-badge),:host(.tab-has-label-only) ::slotted(ion-badge){left:70%}:host-context([dir=rtl]):host(.tab-layout-icon-hide) ::slotted(ion-badge),:host-context([dir=rtl]).tab-layout-icon-hide ::slotted(ion-badge),:host-context([dir=rtl]):host(.tab-has-label-only) ::slotted(ion-badge),:host-context([dir=rtl]).tab-has-label-only ::slotted(ion-badge){left:unset;right:unset;right:70%}@supports selector(:dir(rtl)){:host(.tab-layout-icon-hide) ::slotted(ion-badge):dir(rtl),:host(.tab-has-label-only) ::slotted(ion-badge):dir(rtl){left:unset;right:unset;right:70%}}}:host(.tab-layout-icon-hide) ::slotted(ion-label),:host(.tab-has-label-only) ::slotted(ion-label){margin-top:0;margin-bottom:0}:host(.tab-layout-label-hide) ::slotted(ion-badge),:host(.tab-has-icon-only) ::slotted(ion-badge){top:16px}:host(.tab-layout-label-hide) ::slotted(ion-icon),:host(.tab-has-icon-only) ::slotted(ion-icon){margin-top:0;margin-bottom:0;font-size:24px}\";\n\nconst TabButton = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionTabButtonClick = createEvent(this, \"ionTabButtonClick\", 7);\n    this.inheritedAttributes = {};\n    this.onKeyUp = (ev) => {\n      if (ev.key === 'Enter' || ev.key === ' ') {\n        this.selectTab(ev);\n      }\n    };\n    this.onClick = (ev) => {\n      this.selectTab(ev);\n    };\n    this.disabled = false;\n    this.download = undefined;\n    this.href = undefined;\n    this.rel = undefined;\n    this.layout = undefined;\n    this.selected = false;\n    this.tab = undefined;\n    this.target = undefined;\n  }\n  onTabBarChanged(ev) {\n    const dispatchedFrom = ev.target;\n    const parent = this.el.parentElement;\n    if (ev.composedPath().includes(parent) || (dispatchedFrom === null || dispatchedFrom === void 0 ? void 0 : dispatchedFrom.contains(this.el))) {\n      this.selected = this.tab === ev.detail.tab;\n    }\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = Object.assign({}, inheritAttributes(this.el, ['aria-label']));\n    if (this.layout === undefined) {\n      this.layout = config.get('tabButtonLayout', 'icon-top');\n    }\n  }\n  selectTab(ev) {\n    if (this.tab !== undefined) {\n      if (!this.disabled) {\n        this.ionTabButtonClick.emit({\n          tab: this.tab,\n          href: this.href,\n          selected: this.selected,\n        });\n      }\n      ev.preventDefault();\n    }\n  }\n  get hasLabel() {\n    return !!this.el.querySelector('ion-label');\n  }\n  get hasIcon() {\n    return !!this.el.querySelector('ion-icon');\n  }\n  render() {\n    const { disabled, hasIcon, hasLabel, href, rel, target, layout, selected, tab, inheritedAttributes } = this;\n    const mode = getIonMode(this);\n    const attrs = {\n      download: this.download,\n      href,\n      rel,\n      target,\n    };\n    return (h(Host, { onClick: this.onClick, onKeyup: this.onKeyUp, id: tab !== undefined ? `tab-button-${tab}` : null, class: {\n        [mode]: true,\n        'tab-selected': selected,\n        'tab-disabled': disabled,\n        'tab-has-label': hasLabel,\n        'tab-has-icon': hasIcon,\n        'tab-has-label-only': hasLabel && !hasIcon,\n        'tab-has-icon-only': hasIcon && !hasLabel,\n        [`tab-layout-${layout}`]: true,\n        'ion-activatable': true,\n        'ion-selectable': true,\n        'ion-focusable': true,\n      } }, h(\"a\", Object.assign({}, attrs, { class: \"button-native\", part: \"native\", role: \"tab\", \"aria-selected\": selected ? 'true' : null, \"aria-disabled\": disabled ? 'true' : null, tabindex: disabled ? '-1' : undefined }, inheritedAttributes), h(\"span\", { class: \"button-inner\" }, h(\"slot\", null)), mode === 'md' && h(\"ion-ripple-effect\", { type: \"unbounded\" }))));\n  }\n  get el() { return getElement(this); }\n};\nTabButton.style = {\n  ios: tabButtonIosCss,\n  md: tabButtonMdCss\n};\n\nexport { TabBar as ion_tab_bar, TabButton as ion_tab_button };\n"], "names": ["r", "registerInstance", "d", "createEvent", "h", "H", "Host", "f", "getElement", "c", "createKeyboardController", "createColorClasses", "b", "getIonMode", "config", "k", "inheritAttributes", "tabBarIosCss", "tabBarMdCss", "TabBar", "constructor", "hostRef", "ionTabBarChanged", "keyboardCtrl", "keyboardVisible", "color", "undefined", "selectedTab", "translucent", "selected<PERSON><PERSON><PERSON><PERSON><PERSON>", "emit", "tab", "componentWillLoad", "connectedCallback", "_this", "_asyncToGenerator", "_ref", "keyboardOpen", "waitForResize", "_x", "_x2", "apply", "arguments", "disconnectedCallback", "destroy", "render", "mode", "shouldHide", "el", "getAttribute", "role", "class", "watchers", "style", "ios", "md", "tabButtonIosCss", "tabButtonMdCss", "TabButton", "ionTabButtonClick", "inheritedAttributes", "onKeyUp", "ev", "key", "selectTab", "onClick", "disabled", "download", "href", "rel", "layout", "selected", "target", "onTabBarChanged", "dispatchedFrom", "parent", "parentElement", "<PERSON><PERSON><PERSON>", "includes", "contains", "detail", "Object", "assign", "get", "preventDefault", "<PERSON><PERSON><PERSON><PERSON>", "querySelector", "hasIcon", "attrs", "onKeyup", "id", "part", "tabindex", "type", "ion_tab_bar", "ion_tab_button"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}