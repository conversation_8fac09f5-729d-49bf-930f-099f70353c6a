{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-avatar_3_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC0E;AACb;AACC;AAE9D,MAAMS,YAAY,GAAG,2PAA2P;AAEhR,MAAMC,WAAW,GAAG,2PAA2P;AAE/Q,MAAMC,MAAM,GAAG,MAAM;EACnBC,WAAWA,CAACC,OAAO,EAAE;IACnBZ,qDAAgB,CAAC,IAAI,EAAEY,OAAO,CAAC;EACjC;EACAC,MAAMA,CAAA,EAAG;IACP,OAAQZ,qDAAC,CAACE,iDAAI,EAAE;MAAEW,KAAK,EAAET,4DAAU,CAAC,IAAI;IAAE,CAAC,EAAEJ,qDAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;EAC/D;AACF,CAAC;AACDS,MAAM,CAACK,KAAK,GAAG;EACbC,GAAG,EAAER,YAAY;EACjBS,EAAE,EAAER;AACN,CAAC;AAED,MAAMS,WAAW,GAAG,i2BAAi2B;AAEr3B,MAAMC,UAAU,GAAG,66BAA66B;AAEh8B,MAAMC,KAAK,GAAG,MAAM;EAClBT,WAAWA,CAACC,OAAO,EAAE;IACnBZ,qDAAgB,CAAC,IAAI,EAAEY,OAAO,CAAC;IAC/B,IAAI,CAACS,KAAK,GAAGC,SAAS;EACxB;EACAT,MAAMA,CAAA,EAAG;IACP,MAAMU,IAAI,GAAGlB,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQJ,qDAAC,CAACE,iDAAI,EAAE;MAAEW,KAAK,EAAEP,qDAAkB,CAAC,IAAI,CAACc,KAAK,EAAE;QACpD,CAACE,IAAI,GAAG;MACV,CAAC;IAAE,CAAC,EAAEtB,qDAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;EAC1B;AACF,CAAC;AACDmB,KAAK,CAACL,KAAK,GAAG;EACZC,GAAG,EAAEE,WAAW;EAChBD,EAAE,EAAEE;AACN,CAAC;AAED,MAAMK,YAAY,GAAG,6QAA6Q;AAElS,MAAMC,SAAS,GAAG,MAAM;EACtBd,WAAWA,CAACC,OAAO,EAAE;IACnBZ,qDAAgB,CAAC,IAAI,EAAEY,OAAO,CAAC;EACjC;EACAC,MAAMA,CAAA,EAAG;IACP,OAAQZ,qDAAC,CAACE,iDAAI,EAAE;MAAEW,KAAK,EAAET,4DAAU,CAAC,IAAI;IAAE,CAAC,EAAEJ,qDAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;EAC/D;AACF,CAAC;AACDwB,SAAS,CAACV,KAAK,GAAGS,YAAY;;;;;;;;;;;;;;;;;;;;ACvD9B;AACA;AACA;AACA,MAAMK,WAAW,GAAGA,CAACC,QAAQ,EAAEC,EAAE,KAAK;EACpC,OAAOA,EAAE,CAACC,OAAO,CAACF,QAAQ,CAAC,KAAK,IAAI;AACtC,CAAC;AACD;AACA;AACA;AACA,MAAMvB,kBAAkB,GAAGA,CAACc,KAAK,EAAEY,WAAW,KAAK;EACjD,OAAO,OAAOZ,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACa,MAAM,GAAG,CAAC,GAChDC,MAAM,CAACC,MAAM,CAAC;IAAE,WAAW,EAAE,IAAI;IAAE,CAAE,aAAYf,KAAM,EAAC,GAAG;EAAK,CAAC,EAAEY,WAAW,CAAC,GAAGA,WAAW;AACnG,CAAC;AACD,MAAMI,YAAY,GAAIC,OAAO,IAAK;EAChC,IAAIA,OAAO,KAAKhB,SAAS,EAAE;IACzB,MAAMiB,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACH,OAAO,CAAC,GAAGA,OAAO,GAAGA,OAAO,CAACI,KAAK,CAAC,GAAG,CAAC;IACnE,OAAOH,KAAK,CACTI,MAAM,CAAErC,CAAC,IAAKA,CAAC,IAAI,IAAI,CAAC,CACxBsC,GAAG,CAAEtC,CAAC,IAAKA,CAAC,CAACuC,IAAI,CAAC,CAAC,CAAC,CACpBF,MAAM,CAAErC,CAAC,IAAKA,CAAC,KAAK,EAAE,CAAC;EAC5B;EACA,OAAO,EAAE;AACX,CAAC;AACD,MAAMwC,WAAW,GAAIR,OAAO,IAAK;EAC/B,MAAMM,GAAG,GAAG,CAAC,CAAC;EACdP,YAAY,CAACC,OAAO,CAAC,CAACS,OAAO,CAAEzC,CAAC,IAAMsC,GAAG,CAACtC,CAAC,CAAC,GAAG,IAAK,CAAC;EACrD,OAAOsC,GAAG;AACZ,CAAC;AACD,MAAMI,MAAM,GAAG,sBAAsB;AACrC,MAAMC,OAAO;EAAA,IAAAC,IAAA,GAAAC,8KAAA,CAAG,WAAOC,GAAG,EAAEC,EAAE,EAAEC,SAAS,EAAEC,SAAS,EAAK;IACvD,IAAIH,GAAG,IAAI,IAAI,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAACJ,MAAM,CAACQ,IAAI,CAACJ,GAAG,CAAC,EAAE;MACtD,MAAMK,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,YAAY,CAAC;MACnD,IAAIF,MAAM,EAAE;QACV,IAAIJ,EAAE,IAAI,IAAI,EAAE;UACdA,EAAE,CAACO,cAAc,CAAC,CAAC;QACrB;QACA,OAAOH,MAAM,CAACI,IAAI,CAACT,GAAG,EAAEE,SAAS,EAAEC,SAAS,CAAC;MAC/C;IACF;IACA,OAAO,KAAK;EACd,CAAC;EAAA,gBAXKN,OAAOA,CAAAa,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAf,IAAA,CAAAgB,KAAA,OAAAC,SAAA;EAAA;AAAA,GAWZ", "sources": ["./node_modules/@ionic/core/dist/esm/ion-avatar_3.entry.js", "./node_modules/@ionic/core/dist/esm/theme-17531cdf.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, H as Host } from './index-2d388930.js';\nimport { b as getIonMode } from './ionic-global-b3fc28dd.js';\nimport { c as createColorClasses } from './theme-17531cdf.js';\n\nconst avatarIosCss = \":host{border-radius:var(--border-radius);display:block}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}:host{--border-radius:50%;width:48px;height:48px}\";\n\nconst avatarMdCss = \":host{border-radius:var(--border-radius);display:block}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}:host{--border-radius:50%;width:64px;height:64px}\";\n\nconst Avatar = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n  }\n  render() {\n    return (h(Host, { class: getIonMode(this) }, h(\"slot\", null)));\n  }\n};\nAvatar.style = {\n  ios: avatarIosCss,\n  md: avatarMdCss\n};\n\nconst badgeIosCss = \":host{--background:var(--ion-color-primary, #3880ff);--color:var(--ion-color-primary-contrast, #fff);--padding-top:3px;--padding-end:8px;--padding-bottom:3px;--padding-start:8px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:inline-block;min-width:10px;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);font-size:13px;font-weight:bold;line-height:1;text-align:center;white-space:nowrap;contain:content;vertical-align:baseline}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(:empty){display:none}:host{border-radius:10px}\";\n\nconst badgeMdCss = \":host{--background:var(--ion-color-primary, #3880ff);--color:var(--ion-color-primary-contrast, #fff);--padding-top:3px;--padding-end:8px;--padding-bottom:3px;--padding-start:8px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:inline-block;min-width:10px;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);font-size:13px;font-weight:bold;line-height:1;text-align:center;white-space:nowrap;contain:content;vertical-align:baseline}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(:empty){display:none}:host{--padding-top:3px;--padding-end:4px;--padding-bottom:4px;--padding-start:4px;border-radius:4px}\";\n\nconst Badge = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.color = undefined;\n  }\n  render() {\n    const mode = getIonMode(this);\n    return (h(Host, { class: createColorClasses(this.color, {\n        [mode]: true,\n      }) }, h(\"slot\", null)));\n  }\n};\nBadge.style = {\n  ios: badgeIosCss,\n  md: badgeMdCss\n};\n\nconst thumbnailCss = \":host{--size:48px;--border-radius:0;border-radius:var(--border-radius);display:block;width:var(--size);height:var(--size)}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}\";\n\nconst Thumbnail = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n  }\n  render() {\n    return (h(Host, { class: getIonMode(this) }, h(\"slot\", null)));\n  }\n};\nThumbnail.style = thumbnailCss;\n\nexport { Avatar as ion_avatar, Badge as ion_badge, Thumbnail as ion_thumbnail };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst hostContext = (selector, el) => {\n  return el.closest(selector) !== null;\n};\n/**\n * Create the mode and color classes for the component based on the classes passed in\n */\nconst createColorClasses = (color, cssClassMap) => {\n  return typeof color === 'string' && color.length > 0\n    ? Object.assign({ 'ion-color': true, [`ion-color-${color}`]: true }, cssClassMap) : cssClassMap;\n};\nconst getClassList = (classes) => {\n  if (classes !== undefined) {\n    const array = Array.isArray(classes) ? classes : classes.split(' ');\n    return array\n      .filter((c) => c != null)\n      .map((c) => c.trim())\n      .filter((c) => c !== '');\n  }\n  return [];\n};\nconst getClassMap = (classes) => {\n  const map = {};\n  getClassList(classes).forEach((c) => (map[c] = true));\n  return map;\n};\nconst SCHEME = /^[a-z][a-z0-9+\\-.]*:/;\nconst openURL = async (url, ev, direction, animation) => {\n  if (url != null && url[0] !== '#' && !SCHEME.test(url)) {\n    const router = document.querySelector('ion-router');\n    if (router) {\n      if (ev != null) {\n        ev.preventDefault();\n      }\n      return router.push(url, direction, animation);\n    }\n  }\n  return false;\n};\n\nexport { createColorClasses as c, getClassMap as g, hostContext as h, openURL as o };\n"], "names": ["r", "registerInstance", "h", "H", "Host", "b", "getIonMode", "c", "createColorClasses", "avatarIosCss", "avatarMdCss", "Avatar", "constructor", "hostRef", "render", "class", "style", "ios", "md", "badgeIosCss", "badgeMdCss", "Badge", "color", "undefined", "mode", "thumbnailCss", "<PERSON><PERSON><PERSON><PERSON>", "ion_avatar", "ion_badge", "ion_thumbnail", "hostContext", "selector", "el", "closest", "cssClassMap", "length", "Object", "assign", "getClassList", "classes", "array", "Array", "isArray", "split", "filter", "map", "trim", "getClassMap", "for<PERSON>ach", "SCHEME", "openURL", "_ref", "_asyncToGenerator", "url", "ev", "direction", "animation", "test", "router", "document", "querySelector", "preventDefault", "push", "_x", "_x2", "_x3", "_x4", "apply", "arguments", "g", "o"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0, 1]}