{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-backdrop_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC4F;AACjB;AACd;AAE7D,MAAMW,cAAc,GAAG,wWAAwW;AAE/X,MAAMC,aAAa,GAAG,wWAAwW;AAE9X,MAAMC,QAAQ,GAAG,MAAM;EACrBC,WAAWA,CAACC,OAAO,EAAE;IACnBd,qDAAgB,CAAC,IAAI,EAAEc,OAAO,CAAC;IAC/B,IAAI,CAACC,cAAc,GAAGb,qDAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAACc,OAAO,GAAGT,8DAAkB,CAACU,aAAa,CAAC;MAC9CC,aAAa,EAAE;IACjB,CAAC,CAAC;IACF,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,eAAe,GAAG,IAAI;EAC7B;EACAC,iBAAiBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACD,eAAe,EAAE;MACxB,IAAI,CAACL,OAAO,CAACO,KAAK,CAAC,CAAC;IACtB;EACF;EACAC,oBAAoBA,CAAA,EAAG;IACrB,IAAI,CAACR,OAAO,CAACS,OAAO,CAAC,CAAC;EACxB;EACAC,WAAWA,CAACC,EAAE,EAAE;IACd,IAAI,CAACC,OAAO,CAACD,EAAE,CAAC;EAClB;EACAC,OAAOA,CAACD,EAAE,EAAE;IACV,IAAI,IAAI,CAACN,eAAe,EAAE;MACxBM,EAAE,CAACE,cAAc,CAAC,CAAC;MACnBF,EAAE,CAACN,eAAe,CAAC,CAAC;IACtB;IACA,IAAI,IAAI,CAACD,QAAQ,EAAE;MACjB,IAAI,CAACL,cAAc,CAACe,IAAI,CAAC,CAAC;IAC5B;EACF;EACAC,MAAMA,CAAA,EAAG;IACP,MAAMC,IAAI,GAAGvB,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQN,qDAAC,CAACE,iDAAI,EAAE;MAAE4B,QAAQ,EAAE,IAAI;MAAE,aAAa,EAAE,MAAM;MAAEC,KAAK,EAAE;QAC5D,CAACF,IAAI,GAAG,IAAI;QACZ,eAAe,EAAE,CAAC,IAAI,CAACb,OAAO;QAC9B,sBAAsB,EAAE,CAAC,IAAI,CAACC;MAChC;IAAE,CAAC,CAAC;EACR;AACF,CAAC;AACDR,QAAQ,CAACuB,KAAK,GAAG;EACfC,GAAG,EAAE1B,cAAc;EACnB2B,EAAE,EAAE1B;AACN,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-backdrop.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host } from './index-2d388930.js';\nimport { G as GESTURE_CONTROLLER } from './gesture-controller-0fa396c4.js';\nimport { b as getIonMode } from './ionic-global-b3fc28dd.js';\n\nconst backdropIosCss = \":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}\";\n\nconst backdropMdCss = \":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}\";\n\nconst Backdrop = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionBackdropTap = createEvent(this, \"ionBackdropTap\", 7);\n    this.blocker = GESTURE_CONTROLLER.createBlocker({\n      disableScroll: true,\n    });\n    this.visible = true;\n    this.tappable = true;\n    this.stopPropagation = true;\n  }\n  connectedCallback() {\n    if (this.stopPropagation) {\n      this.blocker.block();\n    }\n  }\n  disconnectedCallback() {\n    this.blocker.unblock();\n  }\n  onMouseDown(ev) {\n    this.emitTap(ev);\n  }\n  emitTap(ev) {\n    if (this.stopPropagation) {\n      ev.preventDefault();\n      ev.stopPropagation();\n    }\n    if (this.tappable) {\n      this.ionBackdropTap.emit();\n    }\n  }\n  render() {\n    const mode = getIonMode(this);\n    return (h(Host, { tabindex: \"-1\", \"aria-hidden\": \"true\", class: {\n        [mode]: true,\n        'backdrop-hide': !this.visible,\n        'backdrop-no-tappable': !this.tappable,\n      } }));\n  }\n};\nBackdrop.style = {\n  ios: backdropIosCss,\n  md: backdropMdCss\n};\n\nexport { Backdrop as ion_backdrop };\n"], "names": ["r", "registerInstance", "d", "createEvent", "h", "H", "Host", "G", "GESTURE_CONTROLLER", "b", "getIonMode", "backdropIosCss", "backdropMdCss", "Backdrop", "constructor", "hostRef", "ionBackdropTap", "blocker", "createBlocker", "disableScroll", "visible", "tappable", "stopPropagation", "connectedCallback", "block", "disconnectedCallback", "unblock", "onMouseDown", "ev", "emitTap", "preventDefault", "emit", "render", "mode", "tabindex", "class", "style", "ios", "md", "ion_backdrop"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}