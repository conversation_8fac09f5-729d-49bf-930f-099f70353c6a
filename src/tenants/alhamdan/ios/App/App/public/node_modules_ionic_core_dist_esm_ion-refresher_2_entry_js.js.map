{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-refresher_2_entry_js.js", "mappings": ";;;;;;;;;;;;;;AAAA;AACA;AACA;AAC+C;AAE/C,MAAME,YAAY,GAAGA,CAAA,KAAM;EACzB,IAAID,iDAAG,KAAKE,SAAS,EAAE;IACrB,OAAOF,iDAAG,CAACG,SAAS;EACtB;EACA,OAAOD,SAAS;AAClB,CAAC;;;;;;;;;;;;;;;;;;;;;ACVD;AACA;AACA;AAC4D;AAE5D,IAAIG,WAAW;AACf,CAAC,UAAUA,WAAW,EAAE;EACtB;AACF;AACA;AACA;AACA;EACEA,WAAW,CAAC,OAAO,CAAC,GAAG,OAAO;EAC9B;AACF;AACA;AACA;AACA;EACEA,WAAW,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAChC;AACF;AACA;AACA;AACA;EACEA,WAAW,CAAC,OAAO,CAAC,GAAG,OAAO;AAChC,CAAC,EAAEA,WAAW,KAAKA,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;AACrC,IAAIC,gBAAgB;AACpB,CAAC,UAAUA,gBAAgB,EAAE;EAC3B;AACF;AACA;AACA;AACA;EACEA,gBAAgB,CAAC,SAAS,CAAC,GAAG,SAAS;EACvC;AACF;AACA;AACA;AACA;EACEA,gBAAgB,CAAC,SAAS,CAAC,GAAG,SAAS;EACvC;AACF;AACA;AACA;AACA;EACEA,gBAAgB,CAAC,OAAO,CAAC,GAAG,OAAO;AACrC,CAAC,EAAEA,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C,MAAMC,YAAY,GAAG;EACnBC,SAASA,CAAA,EAAG;IACV,MAAMC,YAAY,GAAGC,MAAM,CAACC,YAAY;IACxC,IAAIF,YAAY,EAAE;MAChB;MACA;MACA,OAAOA,YAAY;IACrB;IACA,MAAMG,SAAS,GAAGX,yDAAY,CAAC,CAAC;IAChC,IAAIW,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACC,iBAAiB,CAAC,SAAS,CAAC,EAAE;MAChG;MACA,OAAOD,SAAS,CAACE,OAAO,CAACC,OAAO;IAClC;IACA,OAAOb,SAAS;EAClB,CAAC;EACDc,SAASA,CAAA,EAAG;IACV,MAAMC,MAAM,GAAG,IAAI,CAACT,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACS,MAAM,EAAE;MACX,OAAO,KAAK;IACd;IACA,MAAML,SAAS,GAAGX,yDAAY,CAAC,CAAC;IAChC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACW,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACM,WAAW,CAAC,CAAC,MAAM,KAAK,EAAE;MAC7F,OAAO,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACC,OAAO,KAAKlB,SAAS;IAC5E;IACA,OAAO,IAAI;EACb,CAAC;EACDmB,SAASA,CAAA,EAAG;IACV,OAAOX,MAAM,CAACC,YAAY,KAAKT,SAAS;EAC1C,CAAC;EACDoB,WAAWA,CAAA,EAAG;IACZ,OAAOrB,yDAAY,CAAC,CAAC,KAAKC,SAAS;EACrC,CAAC;EACDqB,MAAMA,CAACC,OAAO,EAAE;IACd,MAAMP,MAAM,GAAG,IAAI,CAACT,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACS,MAAM,EAAE;MACX;IACF;IACA;AACJ;AACA;AACA;AACA;AACA;IACI,MAAMQ,KAAK,GAAG,IAAI,CAACH,WAAW,CAAC,CAAC,GAAGE,OAAO,CAACC,KAAK,GAAGD,OAAO,CAACC,KAAK,CAACC,WAAW,CAAC,CAAC;IAC9ET,MAAM,CAACM,MAAM,CAAC;MAAEE;IAAM,CAAC,CAAC;EAC1B,CAAC;EACDE,YAAYA,CAACH,OAAO,EAAE;IACpB,MAAMP,MAAM,GAAG,IAAI,CAACT,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACS,MAAM,EAAE;MACX;IACF;IACA;AACJ;AACA;AACA;AACA;AACA;IACI,MAAMW,IAAI,GAAG,IAAI,CAACN,WAAW,CAAC,CAAC,GAAGE,OAAO,CAACI,IAAI,GAAGJ,OAAO,CAACI,IAAI,CAACF,WAAW,CAAC,CAAC;IAC3ET,MAAM,CAACU,YAAY,CAAC;MAAEC;IAAK,CAAC,CAAC;EAC/B,CAAC;EACDC,SAASA,CAAA,EAAG;IACV;AACJ;AACA;AACA;AACA;AACA;IACI,MAAMJ,KAAK,GAAG,IAAI,CAACH,WAAW,CAAC,CAAC,GAAGjB,WAAW,CAACyB,KAAK,GAAG,OAAO;IAC9D,IAAI,CAACP,MAAM,CAAC;MAAEE;IAAM,CAAC,CAAC;EACxB,CAAC;EACDM,cAAcA,CAAA,EAAG;IACf,MAAMd,MAAM,GAAG,IAAI,CAACT,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACS,MAAM,EAAE;MACX;IACF;IACA,IAAI,IAAI,CAACK,WAAW,CAAC,CAAC,EAAE;MACtBL,MAAM,CAACc,cAAc,CAAC,CAAC;IACzB,CAAC,MACI;MACHd,MAAM,CAACe,qBAAqB,CAAC,CAAC;IAChC;EACF,CAAC;EACDC,gBAAgBA,CAAA,EAAG;IACjB,MAAMhB,MAAM,GAAG,IAAI,CAACT,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACS,MAAM,EAAE;MACX;IACF;IACA,IAAI,IAAI,CAACK,WAAW,CAAC,CAAC,EAAE;MACtBL,MAAM,CAACgB,gBAAgB,CAAC,CAAC;IAC3B,CAAC,MACI;MACHhB,MAAM,CAACiB,uBAAuB,CAAC,CAAC;IAClC;EACF,CAAC;EACDC,YAAYA,CAAA,EAAG;IACb,MAAMlB,MAAM,GAAG,IAAI,CAACT,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACS,MAAM,EAAE;MACX;IACF;IACA,IAAI,IAAI,CAACK,WAAW,CAAC,CAAC,EAAE;MACtBL,MAAM,CAACkB,YAAY,CAAC,CAAC;IACvB,CAAC,MACI;MACHlB,MAAM,CAACmB,mBAAmB,CAAC,CAAC;IAC9B;EACF;AACF,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC5B,OAAO9B,YAAY,CAACS,SAAS,CAAC,CAAC;AACjC,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMsB,eAAe,GAAGA,CAAA,KAAM;EAC5BD,eAAe,CAAC,CAAC,IAAI9B,YAAY,CAACsB,SAAS,CAAC,CAAC;AAC/C,CAAC;AACD;AACA;AACA;AACA,MAAMU,oBAAoB,GAAGA,CAAA,KAAM;EACjCF,eAAe,CAAC,CAAC,IAAI9B,YAAY,CAACwB,cAAc,CAAC,CAAC;AACpD,CAAC;AACD;AACA;AACA;AACA,MAAMS,sBAAsB,GAAGA,CAAA,KAAM;EACnCH,eAAe,CAAC,CAAC,IAAI9B,YAAY,CAAC0B,gBAAgB,CAAC,CAAC;AACtD,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMQ,kBAAkB,GAAGA,CAAA,KAAM;EAC/BJ,eAAe,CAAC,CAAC,IAAI9B,YAAY,CAAC4B,YAAY,CAAC,CAAC;AAClD,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMO,YAAY,GAAIlB,OAAO,IAAK;EAChCa,eAAe,CAAC,CAAC,IAAI9B,YAAY,CAACgB,MAAM,CAACC,OAAO,CAAC;AACnD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1MD;AACA;AACA;AAC4I;AAClE;AACoF;AAC5B;AACvD;AACgB;AAC5B;AACiC;AACtB;AACZ;AACjC;AACI;AACJ;AAE7B,MAAM4D,yBAAyB,GAAIC,SAAS,IAAK;EAC/C,MAAMC,eAAe,GAAGD,SAAS,CAACE,sBAAsB;EACxD,MAAMC,SAAS,GAAGF,eAAe,KAAK,IAAI,IAAIA,eAAe,CAACG,OAAO,KAAK,YAAY;EACtF,OAAOD,SAAS,GAAG,WAAW,GAAG,OAAO;AAC1C,CAAC;AACD,MAAME,sBAAsB,GAAGA,CAAC9D,IAAI,EAAE+D,cAAc,EAAEC,WAAW,KAAK;EACpE,OAAOhE,IAAI,KAAK,OAAO,GACnBiE,oBAAoB,CAACF,cAAc,EAAEC,WAAW,CAAC,GACjDE,wBAAwB,CAACH,cAAc,EAAEC,WAAW,CAAC;AAC3D,CAAC;AACD,MAAMG,mBAAmB,GAAIC,oBAAoB,IAAK;EACpD;EACA,MAAMC,OAAO,GAAGD,oBAAoB,CAACE,aAAa,CAAC,aAAa,CAAC;EACjE,MAAMC,MAAM,GAAGF,OAAO,CAACG,UAAU,CAACF,aAAa,CAAC,QAAQ,CAAC;EACzD,MAAMG,qBAAqB,GAAGL,oBAAoB,CAACE,aAAa,CAAC,0BAA0B,CAAC;EAC5F,MAAMI,cAAc,GAAGN,oBAAoB,CAACE,aAAa,CAAC,kBAAkB,CAAC;EAC7E,MAAMK,KAAK,GAAGD,cAAc,GAAGA,cAAc,CAACJ,aAAa,CAAC,UAAU,CAAC,GAAG,IAAI;EAC9E,MAAMM,aAAa,GAAG7B,yDAAe,CAAC,CAAC,CAAC8B,QAAQ,CAAC,IAAI,CAAC,CAACC,MAAM,CAAC,UAAU,CAAC;EACzE,MAAMC,8BAA8B,GAAGhC,yDAAe,CAAC,CAAC,CACrDiC,UAAU,CAACP,qBAAqB,CAAC,CACjCQ,SAAS,CAAC,CACX;IAAEC,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE;EAAM,CAAC,EAC7B;IAAED,MAAM,EAAE,IAAI;IAAEC,OAAO,EAAE;EAAM,CAAC,EAChC;IAAED,MAAM,EAAE,IAAI;IAAEC,OAAO,EAAE;EAAI,CAAC,EAC9B;IAAED,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE;EAAI,CAAC,CAC5B,CAAC;EACF,MAAMC,oBAAoB,GAAGrC,yDAAe,CAAC,CAAC,CAC3CiC,UAAU,CAACT,MAAM,CAAC,CAClBU,SAAS,CAAC,CACX;IAAEC,MAAM,EAAE,CAAC;IAAEG,eAAe,EAAE;EAAa,CAAC,EAC5C;IAAEH,MAAM,EAAE,GAAG;IAAEG,eAAe,EAAE;EAAa,CAAC,EAC9C;IAAEH,MAAM,EAAE,IAAI;IAAEG,eAAe,EAAE;EAAe,CAAC,EACjD;IAAEH,MAAM,EAAE,CAAC;IAAEG,eAAe,EAAE;EAAe,CAAC,CAC/C,CAAC;EACF,MAAMC,oBAAoB,GAAGvC,yDAAe,CAAC,CAAC,CAC3CiC,UAAU,CAACX,OAAO,CAAC,CACnBY,SAAS,CAAC,CACX;IAAEC,MAAM,EAAE,CAAC;IAAEK,SAAS,EAAE;EAAiB,CAAC,EAC1C;IAAEL,MAAM,EAAE,CAAC;IAAEK,SAAS,EAAE;EAAiB,CAAC,CAC3C,CAAC;EACF;AACF;AACA;AACA;AACA;EACE,IAAIb,cAAc,IAAIC,KAAK,EAAE;IAC3B,MAAMa,uBAAuB,GAAGzC,yDAAe,CAAC,CAAC,CAC9CiC,UAAU,CAACN,cAAc,CAAC,CAC1BO,SAAS,CAAC,CACX;MAAEC,MAAM,EAAE,CAAC;MAAEK,SAAS,EAAE;IAAe,CAAC,EACxC;MAAEL,MAAM,EAAE,GAAG;MAAEK,SAAS,EAAE;IAAe,CAAC,EAC1C;MAAEL,MAAM,EAAE,IAAI;MAAEK,SAAS,EAAE;IAAiB,CAAC,EAC7C;MAAEL,MAAM,EAAE,CAAC;MAAEK,SAAS,EAAE;IAAiB,CAAC,CAC3C,CAAC;IACF,MAAME,cAAc,GAAG1C,yDAAe,CAAC,CAAC,CACrCiC,UAAU,CAACL,KAAK,CAAC,CACjBM,SAAS,CAAC,CACX;MAAEC,MAAM,EAAE,CAAC;MAAEK,SAAS,EAAE;IAA2B,CAAC,EACpD;MAAEL,MAAM,EAAE,GAAG;MAAEK,SAAS,EAAE;IAA2B,CAAC,EACtD;MAAEL,MAAM,EAAE,IAAI;MAAEK,SAAS,EAAE;IAA8B,CAAC,EAC1D;MAAEL,MAAM,EAAE,CAAC;MAAEK,SAAS,EAAE;IAA8B,CAAC,CACxD,CAAC;IACFX,aAAa,CAACc,YAAY,CAAC,CAACF,uBAAuB,EAAEC,cAAc,CAAC,CAAC;EACvE;EACA,OAAOb,aAAa,CAACc,YAAY,CAAC,CAACX,8BAA8B,EAAEK,oBAAoB,EAAEE,oBAAoB,CAAC,CAAC;AACjH,CAAC;AACD,MAAMrB,oBAAoB,GAAGA,CAACG,oBAAoB,EAAEJ,WAAW,KAAK;EAClE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAM2B,MAAM,GAAG3B,WAAW,CAAC4B,YAAY;EACvC,MAAMC,gBAAgB,GAAG9C,yDAAe,CAAC,CAAC,CACvCiC,UAAU,CAACZ,oBAAoB,CAAC,CAChCa,SAAS,CAAC,CACX;IAAEC,MAAM,EAAE,CAAC;IAAEK,SAAS,EAAG,wBAAuBI,MAAO;EAAK,CAAC,EAC7D;IAAET,MAAM,EAAE,CAAC;IAAEK,SAAS,EAAE;EAA6B,CAAC,CACvD,CAAC;EACF,OAAOpB,mBAAmB,CAACC,oBAAoB,CAAC,CAACsB,YAAY,CAAC,CAACG,gBAAgB,CAAC,CAAC;AACnF,CAAC;AACD,MAAM3B,wBAAwB,GAAGA,CAACE,oBAAoB,EAAEJ,WAAW,KAAK;EACtE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAM2B,MAAM,GAAG3B,WAAW,CAAC4B,YAAY;EACvC,MAAMC,gBAAgB,GAAG9C,yDAAe,CAAC,CAAC,CACvCiC,UAAU,CAACZ,oBAAoB,CAAC,CAChCa,SAAS,CAAC,CACX;IAAEC,MAAM,EAAE,CAAC;IAAEK,SAAS,EAAG,eAAcI,MAAO;EAAK,CAAC,EACpD;IAAET,MAAM,EAAE,CAAC;IAAEK,SAAS,EAAE;EAAoB,CAAC,CAC9C,CAAC;EACF,OAAOpB,mBAAmB,CAACC,oBAAoB,CAAC,CAACsB,YAAY,CAAC,CAACG,gBAAgB,CAAC,CAAC;AACnF,CAAC;AACD,MAAMC,uBAAuB,GAAI1B,oBAAoB,IAAK;EACxD,OAAOrB,yDAAe,CAAC,CAAC,CACrB8B,QAAQ,CAAC,GAAG,CAAC,CACbG,UAAU,CAACZ,oBAAoB,CAAC,CAChC2B,MAAM,CAAC,WAAW,EAAE,2DAA2D,EAAE,iBAAiB,CAAC;AACxG,CAAC;AACD;AACA;AACA,MAAMC,iBAAiB,GAAGA,CAAC3B,OAAO,EAAEc,OAAO,KAAK;EAC9Cd,OAAO,CAACxE,KAAK,CAACoG,WAAW,CAAC,SAAS,EAAEd,OAAO,CAACe,QAAQ,CAAC,CAAC,CAAC;AAC1D,CAAC;AACD,MAAMC,wBAAwB,GAAGA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,UAAU,KAAK;EAChE,MAAMC,GAAG,GAAG,CAAC;EACblF,qDAAS,CAAC,MAAM;IACd+E,KAAK,CAACI,OAAO,CAAC,CAACC,EAAE,EAAErD,CAAC,KAAK;MACvB;AACN;AACA;AACA;AACA;AACA;MACM,MAAMsD,GAAG,GAAGtD,CAAC,IAAImD,GAAG,GAAGF,QAAQ,CAAC;MAChC,MAAMM,KAAK,GAAGJ,GAAG,GAAGG,GAAG;MACvB,MAAME,KAAK,GAAGN,UAAU,GAAGI,GAAG;MAC9B,MAAMG,WAAW,GAAGpE,uDAAK,CAAC,CAAC,EAAEmE,KAAK,GAAGD,KAAK,EAAE,CAAC,CAAC;MAC9CF,EAAE,CAAC5G,KAAK,CAACoG,WAAW,CAAC,SAAS,EAAEY,WAAW,CAACX,QAAQ,CAAC,CAAC,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AACD,MAAMY,2BAA2B,GAAGA,CAACzC,OAAO,EAAE0C,aAAa,KAAK;EAC9D1F,qDAAS,CAAC,MAAM;IACd;IACAgD,OAAO,CAACxE,KAAK,CAACoG,WAAW,CAAC,gCAAgC,EAAEc,aAAa,IAAI,GAAG,GAAG,MAAM,GAAG,IAAI,CAAC;IACjG1C,OAAO,CAACxE,KAAK,CAACoG,WAAW,CAAC,SAAS,EAAE,GAAG,CAAC;EAC3C,CAAC,CAAC;AACJ,CAAC;AACD,MAAMe,gBAAgB,GAAGA,CAACP,EAAE,EAAEQ,KAAK,EAAEpC,QAAQ,GAAG,GAAG,KAAK;EACtD,IAAI,CAAC4B,EAAE,EAAE;IACP,OAAOS,OAAO,CAACC,OAAO,CAAC,CAAC;EAC1B;EACA,MAAMC,KAAK,GAAG9E,uDAAkB,CAACmE,EAAE,EAAE5B,QAAQ,CAAC;EAC9CxD,qDAAS,CAAC,MAAM;IACdoF,EAAE,CAAC5G,KAAK,CAACoG,WAAW,CAAC,YAAY,EAAG,GAAEpB,QAAS,iBAAgB,CAAC;IAChE,IAAIoC,KAAK,KAAK3I,SAAS,EAAE;MACvBmI,EAAE,CAAC5G,KAAK,CAACwH,cAAc,CAAC,WAAW,CAAC;IACtC,CAAC,MACI;MACHZ,EAAE,CAAC5G,KAAK,CAACoG,WAAW,CAAC,WAAW,EAAG,oBAAmBgB,KAAM,QAAO,CAAC;IACtE;EACF,CAAC,CAAC;EACF,OAAOG,KAAK;AACd,CAAC;AACD;AACA;AACA,MAAME,wBAAwB;EAAA,IAAAC,IAAA,GAAAC,8KAAA,CAAG,WAAOC,WAAW,EAAEC,IAAI,EAAK;IAC5D,MAAMC,gBAAgB,GAAGF,WAAW,CAACnD,aAAa,CAAC,uBAAuB,CAAC;IAC3E,IAAI,CAACqD,gBAAgB,EAAE;MACrB,OAAOT,OAAO,CAACC,OAAO,CAAC,KAAK,CAAC;IAC/B;IACA,MAAM,IAAID,OAAO,CAAEC,OAAO,IAAK5E,uDAAgB,CAACoF,gBAAgB,EAAER,OAAO,CAAC,CAAC;IAC3E,MAAMpD,cAAc,GAAG0D,WAAW,CAACnD,aAAa,CAAC,sDAAsD,CAAC;IACxG,MAAMsD,iBAAiB,GAAGH,WAAW,CAACnD,aAAa,CAAC,yDAAyD,CAAC;IAC9G,OAAQP,cAAc,KAAK,IAAI,IAC7B6D,iBAAiB,KAAK,IAAI,KACxBF,IAAI,KAAK,KAAK,IAAI9E,4DAAU,CAAC,QAAQ,CAAC,IAAI6E,WAAW,CAAC5H,KAAK,CAACgI,uBAAuB,KAAKvJ,SAAS,IACjGoJ,IAAI,KAAK,IAAI,CAAC;EACpB,CAAC;EAAA,gBAZKJ,wBAAwBA,CAAAQ,EAAA,EAAAC,GAAA;IAAA,OAAAR,IAAA,CAAAS,KAAA,OAAAC,SAAA;EAAA;AAAA,GAY7B;AAED,MAAMC,eAAe,GAAG,q1KAAq1K;AAE72K,MAAMC,cAAc,GAAG,09JAA09J;AAEj/J,MAAMC,SAAS,GAAG,MAAM;EACtBC,WAAWA,CAACC,OAAO,EAAE;IACnB/G,qDAAgB,CAAC,IAAI,EAAE+G,OAAO,CAAC;IAC/B,IAAI,CAACC,UAAU,GAAG/G,qDAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IACpD,IAAI,CAACgH,OAAO,GAAGhH,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAACiH,QAAQ,GAAGjH,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACkH,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAAChC,aAAa,GAAG,CAAC;IACtB,IAAI,CAACiC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,OAAO,GAAG,IAAI,CAACD,OAAO,GAAG,EAAE;IAChC,IAAI,CAACE,aAAa,GAAG,OAAO;IAC5B,IAAI,CAACC,gBAAgB,GAAG,OAAO;IAC/B,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,QAAQ,GAAG,KAAK;EACvB;EACAC,eAAeA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACC,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACC,MAAM,CAAC,CAAC,IAAI,CAACH,QAAQ,CAAC;IACrC;EACF;EACMI,oBAAoBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAArC,8KAAA;MAC3B,MAAMsC,kBAAkB,SAASxC,wBAAwB,CAACuC,KAAI,CAACpD,EAAE,EAAE5D,4DAAU,CAACgH,KAAI,CAAC,CAAC;MACpF,IAAIC,kBAAkB,IAAI,CAACD,KAAI,CAACZ,eAAe,EAAE;QAC/C,MAAMxF,SAAS,GAAGoG,KAAI,CAACpD,EAAE,CAACsD,OAAO,CAAC,aAAa,CAAC;QAChDF,KAAI,CAACG,oBAAoB,CAACvG,SAAS,CAAC;MACtC,CAAC,MACI,IAAI,CAACqG,kBAAkB,EAAE;QAC5BD,KAAI,CAACI,sBAAsB,CAAC,CAAC;MAC/B;IAAC;EACH;EACAA,sBAAsBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACC,QAAQ,IAAI,IAAI,CAACC,sBAAsB,EAAE;MAChD,IAAI,CAACD,QAAQ,CAACE,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACD,sBAAsB,CAAC;MACxE,IAAI,CAACA,sBAAsB,GAAG7L,SAAS;IACzC;IACA,IAAI,CAAC2K,eAAe,GAAG,KAAK;EAC9B;EACMoB,oBAAoBA,CAAC5D,EAAE,EAAEyC,KAAK,EAAE;IAAA,IAAAoB,MAAA;IAAA,OAAA9C,8KAAA;MACpC8C,MAAI,CAACpB,KAAK,GAAGA,KAAK;MAClB,IAAIrG,4DAAU,CAACyH,MAAI,CAAC,KAAK,KAAK,EAAE;QAC9B,MAAMtD,gBAAgB,CAACP,EAAE,EAAEnI,SAAS,EAAE,GAAG,CAAC;MAC5C,CAAC,MACI;QACH,MAAMgE,uDAAkB,CAACgI,MAAI,CAAC7D,EAAE,CAACnC,aAAa,CAAC,4BAA4B,CAAC,EAAE,GAAG,CAAC;MACpF;MACAgG,MAAI,CAACvB,UAAU,GAAG,KAAK;MACvBuB,MAAI,CAACxB,eAAe,GAAG,KAAK;MAC5BwB,MAAI,CAACzB,WAAW,GAAG,KAAK;MACxByB,MAAI,CAACtB,UAAU,CAACxC,OAAO,CAAE+D,GAAG,IAAKA,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC;MAC/CF,MAAI,CAACtB,UAAU,GAAG,EAAE;MACpBsB,MAAI,CAAC1B,QAAQ,GAAG,CAAC;MACjB0B,MAAI,CAACpB,KAAK,GAAG,CAAC,CAAC;IAA8B;EAC/C;;EACMuB,uBAAuBA,CAAC1G,cAAc,EAAE6D,iBAAiB,EAAE;IAAA,IAAA8C,MAAA;IAAA,OAAAlD,8KAAA;MAC/DkD,MAAI,CAACC,kBAAkB,GAAGD,MAAI,CAACR,QAAQ;MACvC,MAAM9D,KAAK,GAAGrC,cAAc,CAACS,UAAU,CAACoG,gBAAgB,CAAC,KAAK,CAAC;MAC/D,IAAIC,QAAQ,GAAGH,MAAI,CAACR,QAAQ,CAACtE,YAAY,GAAG,IAAI;MAChD,MAAMkF,SAAS,GAAG1E,KAAK,CAAC2E,MAAM;MAC9B1J,qDAAS,CAAC,MAAM+E,KAAK,CAACI,OAAO,CAAEC,EAAE,IAAKA,EAAE,CAAC5G,KAAK,CAACoG,WAAW,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC;MACjFyE,MAAI,CAACP,sBAAsB,GAAG,MAAM;QAClC;QACA,IAAI,CAACO,MAAI,CAAC7B,WAAW,IAAI6B,MAAI,CAACxB,KAAK,KAAK,CAAC,CAAC,+BAA+B;UACvE;QACF;QACAxH,qDAAQ,CAAC,MAAM;UACb;UACA,MAAMsJ,SAAS,GAAGN,MAAI,CAACR,QAAQ,CAACc,SAAS;UACzC,MAAMC,eAAe,GAAGP,MAAI,CAACjE,EAAE,CAACb,YAAY;UAC5C,IAAIoF,SAAS,GAAG,CAAC,EAAE;YACjB;AACV;AACA;AACA;YACU,IAAIN,MAAI,CAACxB,KAAK,KAAK,CAAC,CAAC,iCAAiC;cACpD,MAAMgC,KAAK,GAAGzI,uDAAK,CAAC,CAAC,EAAEuI,SAAS,IAAIC,eAAe,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;cAC9D5J,qDAAS,CAAC,MAAM2E,iBAAiB,CAAC4B,iBAAiB,EAAE,CAAC,GAAGsD,KAAK,CAAC,CAAC;cAChE;YACF;YACA;UACF;UACA,IAAIR,MAAI,CAAC7B,WAAW,EAAE;YACpB,IAAI,CAAC6B,MAAI,CAAC/B,QAAQ,EAAE;cAClB+B,MAAI,CAAC/B,QAAQ,GAAG,IAAI;cACpB+B,MAAI,CAACjC,QAAQ,CAAC0C,IAAI,CAAC,CAAC;YACtB;YACA;YACA,IAAIT,MAAI,CAAC7B,WAAW,EAAE;cACpB6B,MAAI,CAAClC,OAAO,CAAC2C,IAAI,CAAC,CAAC;YACrB;UACF;UACA;AACR;AACA;AACA;AACA;AACA;AACA;UACQ,MAAMjG,MAAM,GAAGwF,MAAI,CAAC/B,QAAQ,GAAG,EAAE,GAAG,CAAC;UACrC,MAAMrC,UAAU,GAAIoE,MAAI,CAAC9B,QAAQ,GAAGnG,uDAAK,CAAC,CAAC,EAAE,CAAC2I,IAAI,CAACC,GAAG,CAACL,SAAS,CAAC,GAAG9F,MAAM,IAAI2F,QAAQ,EAAE,CAAC,CAAE;UAC3F,MAAMS,2BAA2B,GAAGZ,MAAI,CAACxB,KAAK,KAAK,CAAC,CAAC,mCAAmC5C,UAAU,KAAK,CAAC;UACxG,IAAIgF,2BAA2B,EAAE;YAC/B,IAAIZ,MAAI,CAAC7B,WAAW,EAAE;cACpB/B,2BAA2B,CAACc,iBAAiB,EAAE8C,MAAI,CAAC3D,aAAa,CAAC;YACpE;YACA,IAAI,CAAC2D,MAAI,CAAC3B,UAAU,EAAE;cACpB2B,MAAI,CAACa,YAAY,CAAC,CAAC;cACnBb,MAAI,CAAC3B,UAAU,GAAG,IAAI;cACtBjI,sDAAY,CAAC;gBAAEjB,KAAK,EAAEpB,kDAAW,CAACyB;cAAM,CAAC,CAAC;cAC1C;AACZ;AACA;AACA;cACY,IAAI,CAACwK,MAAI,CAAC7B,WAAW,EAAE;gBACrB7B,gBAAgB,CAAC0D,MAAI,CAACC,kBAAkB,EAAG,GAAEM,eAAgB,IAAG,CAAC;cACnE;YACF;UACF,CAAC,MACI;YACHP,MAAI,CAACxB,KAAK,GAAG,CAAC,CAAC;YACf/C,wBAAwB,CAACC,KAAK,EAAE0E,SAAS,EAAExE,UAAU,CAAC;UACxD;QACF,CAAC,CAAC;MACJ,CAAC;MACDoE,MAAI,CAACR,QAAQ,CAACsB,gBAAgB,CAAC,QAAQ,EAAEd,MAAI,CAACP,sBAAsB,CAAC;MACrEO,MAAI,CAAChB,OAAO,GAAG,OAAO,sHAA6B,EAAE+B,aAAa,CAAC;QACjEhF,EAAE,EAAEiE,MAAI,CAACR,QAAQ;QACjBwB,WAAW,EAAE,WAAW;QACxBC,eAAe,EAAE,EAAE;QACnBC,SAAS,EAAE,GAAG;QACdC,SAAS,EAAE,CAAC;QACZC,OAAO,EAAEA,CAAA,KAAM;UACbpB,MAAI,CAAC7B,WAAW,GAAG,IAAI;UACvB,IAAI,CAAC6B,MAAI,CAAC3B,UAAU,EAAE;YACpB/B,gBAAgB,CAAC0D,MAAI,CAACC,kBAAkB,EAAE,KAAK,CAAC;UAClD;UACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;UACQ,IAAIE,QAAQ,KAAK,CAAC,EAAE;YAClBA,QAAQ,GAAGH,MAAI,CAACR,QAAQ,CAACtE,YAAY,GAAG,IAAI;UAC9C;QACF,CAAC;QACDmG,MAAM,EAAGC,EAAE,IAAK;UACdtB,MAAI,CAAC3D,aAAa,GAAGiF,EAAE,CAACC,SAAS;QACnC,CAAC;QACDC,KAAK,EAAEA,CAAA,KAAM;UACXxB,MAAI,CAAC7B,WAAW,GAAG,KAAK;UACxB6B,MAAI,CAAC/B,QAAQ,GAAG,KAAK;UACrB,IAAI+B,MAAI,CAAC5B,eAAe,EAAE;YACxB4B,MAAI,CAACL,oBAAoB,CAACK,MAAI,CAACC,kBAAkB,EAAE,EAAE,CAAC,+BAA+B,CAAC;YACtFD,MAAI,CAAC5B,eAAe,GAAG,KAAK;UAC9B,CAAC,MACI,IAAI4B,MAAI,CAAC3B,UAAU,EAAE;YACxBrH,qDAAQ,CAAC,MAAMsF,gBAAgB,CAAC0D,MAAI,CAACC,kBAAkB,EAAG,GAAED,MAAI,CAACjE,EAAE,CAACb,YAAa,IAAG,CAAC,CAAC;UACxF;QACF;MACF,CAAC,CAAC;MACF8E,MAAI,CAACjB,eAAe,CAAC,CAAC;IAAC;EACzB;EACM0C,sBAAsBA,CAAC1I,SAAS,EAAEM,cAAc,EAAE6D,iBAAiB,EAAE;IAAA,IAAAwE,MAAA;IAAA,OAAA5E,8KAAA;MACzE,MAAMjD,MAAM,GAAG7B,uDAAc,CAACqB,cAAc,CAAC,CAACO,aAAa,CAAC,QAAQ,CAAC;MACrE,MAAMF,oBAAoB,GAAGgI,MAAI,CAAC3F,EAAE,CAACnC,aAAa,CAAC,+CAA+C,CAAC;MACnG,MAAM+H,gBAAgB,GAAG3J,uDAAc,CAACkF,iBAAiB,CAAC,CAACtD,aAAa,CAAC,QAAQ,CAAC;MAClF,IAAIC,MAAM,KAAK,IAAI,IAAI8H,gBAAgB,KAAK,IAAI,EAAE;QAChDhL,qDAAS,CAAC,MAAM;UACdkD,MAAM,CAAC1E,KAAK,CAACoG,WAAW,CAAC,WAAW,EAAE,MAAM,CAAC;UAC7C;UACA2B,iBAAiB,CAAC/H,KAAK,CAACoG,WAAW,CAAC,iBAAiB,EAAE,QAAQ,CAAC;UAChEoG,gBAAgB,CAACxM,KAAK,CAACoG,WAAW,CAAC,iBAAiB,EAAE,QAAQ,CAAC;QACjE,CAAC,CAAC;MACJ;MACAmG,MAAI,CAAC1C,OAAO,GAAG,OAAO,sHAA6B,EAAE+B,aAAa,CAAC;QACjEhF,EAAE,EAAE2F,MAAI,CAAClC,QAAQ;QACjBwB,WAAW,EAAE,WAAW;QACxBC,eAAe,EAAE,EAAE;QACnBC,SAAS,EAAE,GAAG;QACdC,SAAS,EAAE,CAAC;QACZS,QAAQ,EAAEA,CAAA,KAAMF,MAAI,CAAClD,KAAK,KAAK,CAAC,CAAC,mCAC/BkD,MAAI,CAAClD,KAAK,KAAK,EAAE,CAAC,mCAClBkD,MAAI,CAAClC,QAAQ,CAACc,SAAS,KAAK,CAAC;QAC/Bc,OAAO,EAAGE,EAAE,IAAK;UACfI,MAAI,CAACxD,QAAQ,GAAG,CAAC;UACjBoD,EAAE,CAACO,IAAI,GAAG;YAAEC,SAAS,EAAElO,SAAS;YAAEqK,QAAQ,EAAE,KAAK;YAAE8D,SAAS,EAAE;UAAM,CAAC;QACvE,CAAC;QACDV,MAAM,EAAGC,EAAE,IAAK;UACd,IAAKA,EAAE,CAACC,SAAS,GAAG,CAAC,IAAIG,MAAI,CAACxD,QAAQ,KAAK,CAAC,IAAI,CAACoD,EAAE,CAACO,IAAI,CAAC5D,QAAQ,IAAKqD,EAAE,CAACO,IAAI,CAACE,SAAS,EAAE;YACvFT,EAAE,CAACO,IAAI,CAACE,SAAS,GAAG,IAAI;YACxB;UACF;UACA,IAAI,CAACT,EAAE,CAACO,IAAI,CAAC5D,QAAQ,EAAE;YACrBqD,EAAE,CAACO,IAAI,CAAC5D,QAAQ,GAAG,IAAI;YACvByD,MAAI,CAAClD,KAAK,GAAG,CAAC,CAAC;YACf;YACA,MAAM;cAAEgB;YAAS,CAAC,GAAGkC,MAAI;YACzB,MAAMM,gBAAgB,GAAGxC,QAAQ,CAACyC,OAAO,CAAC3K,iDAA0B,CAAC,GAAG,UAAU,GAAG,YAAY;YACjGX,qDAAS,CAAC,MAAM6I,QAAQ,CAACrK,KAAK,CAACoG,WAAW,CAACyG,gBAAgB,EAAE,QAAQ,CAAC,CAAC;YACvE,MAAME,aAAa,GAAGpJ,yBAAyB,CAACC,SAAS,CAAC;YAC1D,MAAM+I,SAAS,GAAG1I,sBAAsB,CAAC8I,aAAa,EAAExI,oBAAoB,EAAEgI,MAAI,CAAC3F,EAAE,CAAC;YACtFuF,EAAE,CAACO,IAAI,CAACC,SAAS,GAAGA,SAAS;YAC7BA,SAAS,CAACK,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC;YACjCT,MAAI,CAAC3D,QAAQ,CAAC0C,IAAI,CAAC,CAAC;YACpBiB,MAAI,CAACpD,UAAU,CAAC8D,IAAI,CAACN,SAAS,CAAC;YAC/B;UACF;UACA;UACAJ,MAAI,CAACxD,QAAQ,GAAGnG,uDAAK,CAAC,CAAC,EAAGuJ,EAAE,CAACe,MAAM,GAAG,GAAG,GAAI,GAAG,EAAE,CAAC,CAAC;UACpDf,EAAE,CAACO,IAAI,CAACC,SAAS,CAACQ,YAAY,CAACZ,MAAI,CAACxD,QAAQ,CAAC;UAC7CwD,MAAI,CAAC5D,OAAO,CAAC2C,IAAI,CAAC,CAAC;QACrB,CAAC;QACDe,KAAK,EAAGF,EAAE,IAAK;UACb,IAAI,CAACA,EAAE,CAACO,IAAI,CAAC5D,QAAQ,EAAE;YACrB;UACF;UACAyD,MAAI,CAAC1C,OAAO,CAACC,MAAM,CAAC,KAAK,CAAC;UAC1B,MAAM;YAAEO;UAAS,CAAC,GAAGkC,MAAI;UACzB,MAAMM,gBAAgB,GAAGxC,QAAQ,CAACyC,OAAO,CAAC3K,iDAA0B,CAAC,GAAG,UAAU,GAAG,YAAY;UACjGX,qDAAS,CAAC,MAAM6I,QAAQ,CAACrK,KAAK,CAACwH,cAAc,CAACqF,gBAAgB,CAAC,CAAC;UAChE,IAAIN,MAAI,CAACxD,QAAQ,IAAI,GAAG,EAAE;YACxBoD,EAAE,CAACO,IAAI,CAACC,SAAS,CAACS,WAAW,CAAC,CAAC,EAAEb,MAAI,CAACxD,QAAQ,EAAE,GAAG,CAAC,CAACsE,QAAQ,CAAC,MAAM;cAClEd,MAAI,CAACpD,UAAU,CAACxC,OAAO,CAAE+D,GAAG,IAAKA,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC;cAC/C4B,MAAI,CAACpD,UAAU,GAAG,EAAE;cACpBoD,MAAI,CAAC1C,OAAO,CAACC,MAAM,CAAC,IAAI,CAAC;cACzByC,MAAI,CAAClD,KAAK,GAAG,CAAC,CAAC;YACjB,CAAC,CAAC;;YACF;UACF;UACA,MAAMN,QAAQ,GAAG7G,4DAAuB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEqK,MAAI,CAACxD,QAAQ,CAAC,CAAC,CAAC,CAAC;UAC1F,MAAMuE,iBAAiB,GAAGrH,uBAAuB,CAAC1B,oBAAoB,CAAC;UACvEgI,MAAI,CAACpD,UAAU,CAAC8D,IAAI,CAACK,iBAAiB,CAAC;UACvC9L,qDAAS,eAAAmG,8KAAA,CAAC,aAAY;YACpBpD,oBAAoB,CAACvE,KAAK,CAACoG,WAAW,CAAC,mCAAmC,EAAG,GAAE2C,QAAQ,GAAG,GAAI,IAAG,CAAC;YAClGoD,EAAE,CAACO,IAAI,CAACC,SAAS,CAACS,WAAW,CAAC,CAAC;YAC/B,MAAME,iBAAiB,CAACC,IAAI,CAAC,CAAC;YAC9BhB,MAAI,CAACb,YAAY,CAAC,CAAC;YACnBS,EAAE,CAACO,IAAI,CAACC,SAAS,CAAChC,OAAO,CAAC,CAAC;YAC3B4B,MAAI,CAAC1C,OAAO,CAACC,MAAM,CAAC,IAAI,CAAC;UAC3B,CAAC,EAAC;QACJ;MACF,CAAC,CAAC;MACFyC,MAAI,CAAC3C,eAAe,CAAC,CAAC;IAAC;EACzB;EACMO,oBAAoBA,CAACvG,SAAS,EAAE;IAAA,IAAA4J,MAAA;IAAA,OAAA7F,8KAAA;MACpC,IAAI6F,MAAI,CAAClD,sBAAsB,IAAI,CAAC1G,SAAS,IAAI4J,MAAI,CAACpE,eAAe,IAAI,CAACoE,MAAI,CAACnD,QAAQ,EAAE;QACvF;MACF;MACA;AACJ;AACA;AACA;AACA;AACA;AACA;MACImD,MAAI,CAACC,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC;MAC7BD,MAAI,CAACpE,eAAe,GAAG,IAAI;MAC3B,MAAMlF,cAAc,GAAGsJ,MAAI,CAAC5G,EAAE,CAACnC,aAAa,CAAC,sDAAsD,CAAC;MACpG,MAAMsD,iBAAiB,GAAGyF,MAAI,CAAC5G,EAAE,CAACnC,aAAa,CAAC,yDAAyD,CAAC;MAC1G,IAAIzB,4DAAU,CAACwK,MAAI,CAAC,KAAK,KAAK,EAAE;QAC9BA,MAAI,CAAC5C,uBAAuB,CAAC1G,cAAc,EAAE6D,iBAAiB,CAAC;MACjE,CAAC,MACI;QACHyF,MAAI,CAAClB,sBAAsB,CAAC1I,SAAS,EAAEM,cAAc,EAAE6D,iBAAiB,CAAC;MAC3E;IAAC;EACH;EACA2F,kBAAkBA,CAAA,EAAG;IACnB,IAAI,CAAC3D,oBAAoB,CAAC,CAAC;EAC7B;EACM4D,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAjG,8KAAA;MACxB,IAAIiG,MAAI,CAAChH,EAAE,CAACiH,YAAY,CAAC,MAAM,CAAC,KAAK,OAAO,EAAE;QAC5CC,OAAO,CAACC,KAAK,CAAC,iDAAiD,CAAC;QAChE;MACF;MACA,MAAMnK,SAAS,GAAGgK,MAAI,CAAChH,EAAE,CAACsD,OAAO,CAAC9H,iDAA4B,CAAC;MAC/D,IAAI,CAACwB,SAAS,EAAE;QACdtB,qDAAuB,CAACsL,MAAI,CAAChH,EAAE,CAAC;QAChC;MACF;MACA;AACJ;AACA;AACA;MACIlE,uDAAgB,CAACkB,SAAS,eAAA+D,8KAAA,CAAE,aAAY;QACtC,MAAMqG,kBAAkB,GAAGpK,SAAS,CAACa,aAAa,CAACtC,iDAA0B,CAAC;QAC9E;AACN;AACA;AACA;AACA;AACA;QACMyL,MAAI,CAACvD,QAAQ,SAAS9H,qDAAgB,CAACyL,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAGpK,SAAS,CAAC;QACrI;AACN;AACA;QACMgK,MAAI,CAACK,mBAAmB,SAASrK,SAAS,CAACsK,oBAAoB,CAAC,CAAC;QACjE,UAAUzG,wBAAwB,CAACmG,MAAI,CAAChH,EAAE,EAAE5D,4DAAU,CAAC4K,MAAI,CAAC,CAAC,EAAE;UAC7DA,MAAI,CAACzD,oBAAoB,CAACvG,SAAS,CAAC;QACtC,CAAC,MACI;UACHgK,MAAI,CAAC/D,OAAO,GAAG,OAAO,sHAA6B,EAAE+B,aAAa,CAAC;YACjEhF,EAAE,EAAEhD,SAAS;YACbiI,WAAW,EAAE,WAAW;YACxBC,eAAe,EAAE,EAAE;YACnBC,SAAS,EAAE,GAAG;YACdC,SAAS,EAAE,EAAE;YACbmC,OAAO,EAAE,KAAK;YACd1B,QAAQ,EAAEA,CAAA,KAAMmB,MAAI,CAACnB,QAAQ,CAAC,CAAC;YAC/BR,OAAO,EAAEA,CAAA,KAAM2B,MAAI,CAAC3B,OAAO,CAAC,CAAC;YAC7BC,MAAM,EAAGC,EAAE,IAAKyB,MAAI,CAAC1B,MAAM,CAACC,EAAE,CAAC;YAC/BE,KAAK,EAAEA,CAAA,KAAMuB,MAAI,CAACvB,KAAK,CAAC;UAC1B,CAAC,CAAC;UACFuB,MAAI,CAAChE,eAAe,CAAC,CAAC;QACxB;MACF,CAAC,EAAC;IAAC;EACL;EACAwE,oBAAoBA,CAAA,EAAG;IACrB,IAAI,CAAChE,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAACC,QAAQ,GAAG5L,SAAS;IACzB,IAAI,IAAI,CAACoL,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACc,OAAO,CAAC,CAAC;MACtB,IAAI,CAACd,OAAO,GAAGpL,SAAS;IAC1B;EACF;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACQ4P,QAAQA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA3G,8KAAA;MACf,IAAI2G,MAAI,CAAClF,eAAe,EAAE;QACxBkF,MAAI,CAACrF,eAAe,GAAG,IAAI;QAC3B;QACA,IAAI,CAACqF,MAAI,CAACtF,WAAW,EAAE;UACrBlG,uDAAG,CAAC,MAAMA,uDAAG,CAAC,MAAMwL,MAAI,CAAC9D,oBAAoB,CAAC8D,MAAI,CAACxD,kBAAkB,EAAE,EAAE,CAAC,+BAA+B,CAAC,CAAC,CAAC;QAC9G;MACF,CAAC,MACI;QACHwD,MAAI,CAACC,KAAK,CAAC,EAAE,CAAC,iCAAiC,OAAO,CAAC;MACzD;IAAC;EACH;EACA;AACF;AACA;EACQC,MAAMA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA9G,8KAAA;MACb,IAAI8G,MAAI,CAACrF,eAAe,EAAE;QACxB;QACA,IAAI,CAACqF,MAAI,CAACzF,WAAW,EAAE;UACrBlG,uDAAG,CAAC,MAAMA,uDAAG,CAAC,MAAM2L,MAAI,CAACjE,oBAAoB,CAACiE,MAAI,CAAC3D,kBAAkB,EAAE,EAAE,CAAC,+BAA+B,CAAC,CAAC,CAAC;QAC9G;MACF,CAAC,MACI;QACH2D,MAAI,CAACF,KAAK,CAAC,EAAE,CAAC,iCAAiC,EAAE,CAAC;MACpD;IAAC;EACH;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEG,WAAWA,CAAA,EAAG;IACZ,OAAOrH,OAAO,CAACC,OAAO,CAAC,IAAI,CAACyB,QAAQ,CAAC;EACvC;EACA0D,QAAQA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACpC,QAAQ,EAAE;MAClB,OAAO,KAAK;IACd;IACA,IAAI,IAAI,CAAChB,KAAK,KAAK,CAAC,CAAC,+BAA+B;MAClD,OAAO,KAAK;IACd;IACA;IACA;IACA,IAAI,IAAI,CAACgB,QAAQ,CAACc,SAAS,GAAG,CAAC,EAAE;MAC/B,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb;EACAc,OAAOA,CAAA,EAAG;IACR,IAAI,CAAClD,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACM,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACsF,oBAAoB,CAAC,CAAC;EAC7B;EACAzC,MAAMA,CAAC0C,MAAM,EAAE;IACb,IAAI,CAAC,IAAI,CAACvE,QAAQ,EAAE;MAClB;IACF;IACA;IACA;IACA;IACA;IACA,MAAM8B,EAAE,GAAGyC,MAAM,CAACC,KAAK;IACvB,IAAI1C,EAAE,CAAC2C,OAAO,KAAKrQ,SAAS,IAAI0N,EAAE,CAAC2C,OAAO,CAAC5D,MAAM,GAAG,CAAC,EAAE;MACrD;IACF;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAC7B,KAAK,GAAG,EAAE,CAAC,iCAAiC,CAAC,EAAE;MACvD;IACF;IACA,MAAMK,UAAU,GAAGqF,MAAM,CAACC,KAAK,CAAC,IAAI,CAACtF,UAAU,CAAC,IAAI,IAAI,CAACA,UAAU,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAACA,UAAU;IAC7F,MAAMwD,MAAM,GAAG0B,MAAM,CAAC1B,MAAM,GAAGxD,UAAU;IACzC;IACA;IACA,IAAIwD,MAAM,IAAI,CAAC,EAAE;MACf;MACA;MACA,IAAI,CAACnE,QAAQ,GAAG,CAAC;MACjB,IAAI,CAACM,KAAK,GAAG,CAAC,CAAC;MACf,IAAI,IAAI,CAACR,aAAa,EAAE;QACtB;QACA,IAAI,CAAC4E,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC;QAC7B;MACF;MACA;IACF;IACA,IAAI,IAAI,CAACpE,KAAK,KAAK,CAAC,CAAC,+BAA+B;MAClD;MACA;MACA,MAAM4F,mBAAmB,GAAG,IAAI,CAAC5E,QAAQ,CAACc,SAAS;MACnD;MACA;MACA,IAAI8D,mBAAmB,GAAG,CAAC,EAAE;QAC3B,IAAI,CAAClG,QAAQ,GAAG,CAAC;QACjB;MACF;MACA;MACA,IAAI,CAACM,KAAK,GAAG,CAAC,CAAC;IACjB;IACA;IACA,IAAI8C,EAAE,CAAC+C,UAAU,EAAE;MACjB/C,EAAE,CAACgD,cAAc,CAAC,CAAC;IACrB;IACA;IACA;IACA,IAAI,CAAC1B,MAAM,CAACP,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC;IACpC,IAAIA,MAAM,KAAK,CAAC,EAAE;MAChB;MACA,IAAI,CAACnE,QAAQ,GAAG,CAAC;MACjB;IACF;IACA,MAAMO,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B;IACA,IAAI,CAACP,QAAQ,GAAGmE,MAAM,GAAG5D,OAAO;IAChC;IACA,IAAI,CAAC,IAAI,CAACR,QAAQ,EAAE;MAClB,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACF,QAAQ,CAAC0C,IAAI,CAAC,CAAC;IACtB;IACA;IACA,IAAI,CAAC3C,OAAO,CAAC2C,IAAI,CAAC,CAAC;IACnB;IACA,IAAI4B,MAAM,GAAG5D,OAAO,EAAE;MACpB;MACA,IAAI,CAACD,KAAK,GAAG,CAAC,CAAC;MACf;IACF;IACA,IAAI6D,MAAM,GAAG,IAAI,CAAC3D,OAAO,EAAE;MACzB;MACA,IAAI,CAACmC,YAAY,CAAC,CAAC;MACnB;IACF;IACA;IACA;IACA;IACA,IAAI,CAACrC,KAAK,GAAG,CAAC,CAAC;IACf;EACF;EACAgD,KAAKA,CAAA,EAAG;IACN;IACA,IAAI,IAAI,CAAChD,KAAK,KAAK,CAAC,CAAC,4BAA4B;MAC/C;MACA,IAAI,CAACqC,YAAY,CAAC,CAAC;IACrB,CAAC,MACI,IAAI,IAAI,CAACrC,KAAK,KAAK,CAAC,CAAC,8BAA8B;MACtD;MACA;MACA;MACA;MACA,IAAI,CAACmF,MAAM,CAAC,CAAC;IACf,CAAC,MACI,IAAI,IAAI,CAACnF,KAAK,KAAK,CAAC,CAAC,+BAA+B;MACvD;AACN;AACA;AACA;AACA;AACA;AACA;AACA;MACM,IAAI,CAAC+F,oBAAoB,CAAC,CAAC;IAC7B;EACF;EACA1D,YAAYA,CAAA,EAAG;IACb;IACA;IACA,IAAI,CAACrC,KAAK,GAAG,CAAC,CAAC;IACf;IACA,IAAI,CAACoE,MAAM,CAAC,IAAI,CAACnE,OAAO,EAAE,IAAI,CAACG,gBAAgB,EAAE,IAAI,EAAE,EAAE,CAAC;IAC1D;IACA;IACA,IAAI,CAACf,UAAU,CAAC4C,IAAI,CAAC;MACnB+C,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAACgB,IAAI,CAAC,IAAI;IACnC,CAAC,CAAC;EACJ;EACAd,KAAKA,CAAClF,KAAK,EAAEiG,KAAK,EAAE;IAClB;IACAC,UAAU,CAAC,MAAM;MACf,IAAI,CAAClG,KAAK,GAAG,CAAC,CAAC;MACf,IAAI,CAACN,QAAQ,GAAG,CAAC;MACjB,IAAI,CAACD,QAAQ,GAAG,KAAK;MACrB;AACN;AACA;AACA;MACM,IAAI,CAAC2E,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC;IACxC,CAAC,EAAE,GAAG,CAAC;IACP;IACA;IACA,IAAI,CAACpE,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACoE,MAAM,CAAC,CAAC,EAAE,IAAI,CAACjE,aAAa,EAAE,IAAI,EAAE8F,KAAK,CAAC;EACjD;EACA7B,MAAMA,CAAC+B,CAAC,EAAExK,QAAQ,EAAEyK,eAAe,EAAEH,KAAK,EAAEI,0BAA0B,GAAG,KAAK,EAAE;IAC9E,IAAI,IAAI,CAACtG,eAAe,EAAE;MACxB;IACF;IACA,IAAI,CAACP,aAAa,GAAG2G,CAAC,GAAG,CAAC;IAC1BhO,qDAAS,CAAC,MAAM;MACd,IAAI,IAAI,CAAC6I,QAAQ,IAAI,IAAI,CAAC4D,mBAAmB,EAAE;QAC7C,MAAM0B,WAAW,GAAG,IAAI,CAACtF,QAAQ,CAACrK,KAAK;QACvC,MAAM4P,eAAe,GAAG,IAAI,CAAC3B,mBAAmB,CAACjO,KAAK;QACtD2P,WAAW,CAACjK,SAAS,GAAGkK,eAAe,CAAClK,SAAS,GAAG8J,CAAC,GAAG,CAAC,GAAI,cAAaA,CAAE,qBAAoB,GAAG,EAAE;QACrGG,WAAW,CAACE,kBAAkB,GAAGD,eAAe,CAACC,kBAAkB,GAAG7K,QAAQ;QAC9E2K,WAAW,CAACG,eAAe,GAAGF,eAAe,CAACE,eAAe,GAAGR,KAAK;QACrEK,WAAW,CAACI,QAAQ,GAAGN,eAAe,GAAG,QAAQ,GAAG,EAAE;MACxD;MACA;AACN;AACA;AACA;AACA;AACA;AACA;MACM,IAAIC,0BAA0B,EAAE;QAC9B,IAAI,CAACN,oBAAoB,CAAC,CAAC;MAC7B;IACF,CAAC,CAAC;EACJ;EACAT,oBAAoBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACtE,QAAQ,EAAE;MACjB,MAAM;QAAE0F,QAAQ;QAAEC,SAAS;QAAEC;MAAU,CAAC,GAAG,IAAI,CAAC5F,QAAQ,CAACrK,KAAK;MAC9D,IAAI,CAACkQ,cAAc,GAAG;QACpBH,QAAQ,EAAEA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAG,EAAE;QAClEC,SAAS,EAAEA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAG,EAAE;QACtEC,SAAS,EAAEA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAG;MACtE,CAAC;IACH;EACF;EACAb,oBAAoBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACc,cAAc,KAAKzR,SAAS,IAAI,IAAI,CAAC4L,QAAQ,KAAK5L,SAAS,EAAE;MACpE,MAAM;QAAEsR,QAAQ;QAAEC,SAAS;QAAEC;MAAU,CAAC,GAAG,IAAI,CAACC,cAAc;MAC9D,IAAI,CAAC7F,QAAQ,CAACrK,KAAK,CAAC+P,QAAQ,GAAGA,QAAQ;MACvC,IAAI,CAAC1F,QAAQ,CAACrK,KAAK,CAACgQ,SAAS,GAAGA,SAAS;MACzC,IAAI,CAAC3F,QAAQ,CAACrK,KAAK,CAACiQ,SAAS,GAAGA,SAAS;MACzC,IAAI,CAACC,cAAc,GAAGzR,SAAS;IACjC;EACF;EACA0R,MAAMA,CAAA,EAAG;IACP,MAAMtI,IAAI,GAAG7E,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQzB,qDAAC,CAACU,iDAAI,EAAE;MAAEmO,IAAI,EAAE,OAAO;MAAEC,KAAK,EAAE;QACpC,CAACxI,IAAI,GAAG,IAAI;QACZ;QACA,CAAE,aAAYA,IAAK,EAAC,GAAG,IAAI;QAC3B,kBAAkB,EAAE,IAAI,CAACuB,eAAe;QACxC,kBAAkB,EAAE,IAAI,CAACC,KAAK,KAAK,CAAC,CAAC;QACrC,mBAAmB,EAAE,IAAI,CAACA,KAAK,KAAK,CAAC,CAAC;QACtC,iBAAiB,EAAE,IAAI,CAACA,KAAK,KAAK,CAAC,CAAC;QACpC,sBAAsB,EAAE,IAAI,CAACA,KAAK,KAAK,CAAC,CAAC;QACzC,sBAAsB,EAAE,IAAI,CAACA,KAAK,KAAK,EAAE,CAAC;QAC1C,sBAAsB,EAAE,IAAI,CAACA,KAAK,KAAK,EAAE,CAAC;MAC5C;IAAE,CAAC,CAAC;EACR;;EACA,IAAIzC,EAAEA,CAAA,EAAG;IAAE,OAAO7E,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWuO,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC7B,UAAU,EAAE,CAAC,iBAAiB;IAChC,CAAC;EAAE;AACL,CAAC;AACD/H,SAAS,CAACvI,KAAK,GAAG;EAChBuQ,GAAG,EAAElI,eAAe;EACpBmI,EAAE,EAAElI;AACN,CAAC;AAED,MAAMmI,gBAAgB,GAAG,MAAM;EAC7BjI,WAAWA,CAACC,OAAO,EAAE;IACnB/G,qDAAgB,CAAC,IAAI,EAAE+G,OAAO,CAAC;IAC/B,IAAI,CAACiI,iBAAiB,GAAGzN,wDAAM,CAAC0N,GAAG,CAAC,2BAA2B,EAAEvN,kDAA2B,CAAC;IAC7F,IAAI,CAACwN,WAAW,GAAGnS,SAAS;IAC5B,IAAI,CAACoS,WAAW,GAAGpS,SAAS;IAC5B,IAAI,CAACsJ,iBAAiB,GAAGtJ,SAAS;IAClC,IAAI,CAACqS,cAAc,GAAGrS,SAAS;EACjC;EACAsS,iBAAiBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACH,WAAW,KAAKnS,SAAS,EAAE;MAClC,MAAMoJ,IAAI,GAAG7E,4DAAU,CAAC,IAAI,CAAC;MAC7B,MAAMgO,iBAAiB,GAAG,IAAI,CAACpK,EAAE,CAAC5G,KAAK,CAACgI,uBAAuB,KAAKvJ,SAAS,GAAG,OAAO,GAAG+E,iDAAS;MACnG,IAAI,CAACoN,WAAW,GAAG3N,wDAAM,CAAC0N,GAAG,CAAC,gBAAgB,EAAE9I,IAAI,KAAK,KAAK,IAAI9E,4DAAU,CAAC,QAAQ,CAAC,GAAGE,wDAAM,CAAC0N,GAAG,CAAC,SAAS,EAAEK,iBAAiB,CAAC,GAAG,UAAU,CAAC;IACjJ;IACA,IAAI,IAAI,CAACjJ,iBAAiB,KAAKtJ,SAAS,EAAE;MACxC,MAAMoJ,IAAI,GAAG7E,4DAAU,CAAC,IAAI,CAAC;MAC7B,IAAI,CAAC+E,iBAAiB,GAAG9E,wDAAM,CAAC0N,GAAG,CAAC,mBAAmB,EAAE1N,wDAAM,CAAC0N,GAAG,CAAC,SAAS,EAAE9I,IAAI,KAAK,KAAK,GAAG,OAAO,GAAG,UAAU,CAAC,CAAC;IACxH;EACF;EACAoJ,iBAAiBA,CAAA,EAAG;IAClB,MAAM;MAAEP,iBAAiB;MAAEG;IAAY,CAAC,GAAG,IAAI;IAC/C,IAAIH,iBAAiB,EAAE;MACrB,OAAOnP,qDAAC,CAAC,KAAK,EAAE;QAAE8O,KAAK,EAAE,wBAAwB;QAAEa,SAAS,EAAE7N,sDAAiB,CAACwN,WAAW;MAAE,CAAC,CAAC;IACjG;IACA,OAAOtP,qDAAC,CAAC,KAAK,EAAE;MAAE8O,KAAK,EAAE;IAAyB,CAAC,EAAEQ,WAAW,CAAC;EACnE;EACAM,oBAAoBA,CAAA,EAAG;IACrB,MAAM;MAAET,iBAAiB;MAAEI;IAAe,CAAC,GAAG,IAAI;IAClD,IAAIJ,iBAAiB,EAAE;MACrB,OAAOnP,qDAAC,CAAC,KAAK,EAAE;QAAE8O,KAAK,EAAE,2BAA2B;QAAEa,SAAS,EAAE7N,sDAAiB,CAACyN,cAAc;MAAE,CAAC,CAAC;IACvG;IACA,OAAOvP,qDAAC,CAAC,KAAK,EAAE;MAAE8O,KAAK,EAAE;IAA4B,CAAC,EAAES,cAAc,CAAC;EACzE;EACAX,MAAMA,CAAA,EAAG;IACP,MAAMS,WAAW,GAAG,IAAI,CAACA,WAAW;IACpC,MAAMQ,UAAU,GAAGR,WAAW,IAAI,IAAI,IAAIlN,4DAAQ,CAACkN,WAAW,CAAC,KAAKnS,SAAS;IAC7E,MAAMoJ,IAAI,GAAG7E,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQzB,qDAAC,CAACU,iDAAI,EAAE;MAAEoO,KAAK,EAAExI;IAAK,CAAC,EAAEtG,qDAAC,CAAC,KAAK,EAAE;MAAE8O,KAAK,EAAE;IAAoB,CAAC,EAAE,IAAI,CAACO,WAAW,IAAIQ,UAAU,IAAK7P,qDAAC,CAAC,KAAK,EAAE;MAAE8O,KAAK,EAAE;IAAyB,CAAC,EAAE9O,qDAAC,CAAC,KAAK,EAAE;MAAE8O,KAAK,EAAE;IAA0B,CAAC,EAAE9O,qDAAC,CAAC,aAAa,EAAE;MAAE8P,IAAI,EAAE,IAAI,CAACT,WAAW;MAAEU,MAAM,EAAE;IAAK,CAAC,CAAC,EAAEzJ,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC+I,WAAW,KAAK,UAAU,IAAKrP,qDAAC,CAAC,KAAK,EAAE;MAAE8O,KAAK,EAAE;IAAkB,CAAC,EAAE9O,qDAAC,CAAC,UAAU,EAAE;MAAEgQ,IAAI,EAAEjO,iDAAc;MAAE,aAAa,EAAE;IAAO,CAAC,CAAC,CAAE,CAAC,CAAE,EAAE,IAAI,CAACsN,WAAW,IAAI,CAACQ,UAAU,IAAK7P,qDAAC,CAAC,KAAK,EAAE;MAAE8O,KAAK,EAAE;IAAyB,CAAC,EAAE9O,qDAAC,CAAC,UAAU,EAAE;MAAEgQ,IAAI,EAAE,IAAI,CAACX,WAAW;MAAEY,IAAI,EAAE,KAAK;MAAE,aAAa,EAAE;IAAO,CAAC,CAAC,CAAE,EAAE,IAAI,CAACX,WAAW,KAAKpS,SAAS,IAAI,IAAI,CAACwS,iBAAiB,CAAC,CAAC,CAAC,EAAE1P,qDAAC,CAAC,KAAK,EAAE;MAAE8O,KAAK,EAAE;IAAuB,CAAC,EAAE,IAAI,CAACtI,iBAAiB,IAAKxG,qDAAC,CAAC,KAAK,EAAE;MAAE8O,KAAK,EAAE;IAA4B,CAAC,EAAE9O,qDAAC,CAAC,aAAa,EAAE;MAAE8P,IAAI,EAAE,IAAI,CAACtJ;IAAkB,CAAC,CAAC,CAAE,EAAE,IAAI,CAAC+I,cAAc,KAAKrS,SAAS,IAAI,IAAI,CAAC0S,oBAAoB,CAAC,CAAC,CAAC,CAAC;EACz3B;EACA,IAAIvK,EAAEA,CAAA,EAAG;IAAE,OAAO7E,qDAAU,CAAC,IAAI,CAAC;EAAE;AACtC,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/capacitor-b4979570.js", "./node_modules/@ionic/core/dist/esm/haptic-6447af60.js", "./node_modules/@ionic/core/dist/esm/ion-refresher_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index-7a14ecec.js';\n\nconst getCapacitor = () => {\n  if (win !== undefined) {\n    return win.Capacitor;\n  }\n  return undefined;\n};\n\nexport { getCapacitor as g };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { g as getCapacitor } from './capacitor-b4979570.js';\n\nvar ImpactStyle;\n(function (ImpactStyle) {\n  /**\n   * A collision between large, heavy user interface elements\n   *\n   * @since 1.0.0\n   */\n  ImpactStyle[\"Heavy\"] = \"HEAVY\";\n  /**\n   * A collision between moderately sized user interface elements\n   *\n   * @since 1.0.0\n   */\n  ImpactStyle[\"Medium\"] = \"MEDIUM\";\n  /**\n   * A collision between small, light user interface elements\n   *\n   * @since 1.0.0\n   */\n  ImpactStyle[\"Light\"] = \"LIGHT\";\n})(ImpactStyle || (ImpactStyle = {}));\nvar NotificationType;\n(function (NotificationType) {\n  /**\n   * A notification feedback type indicating that a task has completed successfully\n   *\n   * @since 1.0.0\n   */\n  NotificationType[\"Success\"] = \"SUCCESS\";\n  /**\n   * A notification feedback type indicating that a task has produced a warning\n   *\n   * @since 1.0.0\n   */\n  NotificationType[\"Warning\"] = \"WARNING\";\n  /**\n   * A notification feedback type indicating that a task has failed\n   *\n   * @since 1.0.0\n   */\n  NotificationType[\"Error\"] = \"ERROR\";\n})(NotificationType || (NotificationType = {}));\nconst HapticEngine = {\n  getEngine() {\n    const tapticEngine = window.TapticEngine;\n    if (tapticEngine) {\n      // Cordova\n      // TODO FW-4707 - Remove this in Ionic 8\n      return tapticEngine;\n    }\n    const capacitor = getCapacitor();\n    if (capacitor === null || capacitor === void 0 ? void 0 : capacitor.isPluginAvailable('Haptics')) {\n      // Capacitor\n      return capacitor.Plugins.Haptics;\n    }\n    return undefined;\n  },\n  available() {\n    const engine = this.getEngine();\n    if (!engine) {\n      return false;\n    }\n    const capacitor = getCapacitor();\n    /**\n     * Developers can manually import the\n     * Haptics plugin in their app which will cause\n     * getEngine to return the Haptics engine. However,\n     * the Haptics engine will throw an error if\n     * used in a web browser that does not support\n     * the Vibrate API. This check avoids that error\n     * if the browser does not support the Vibrate API.\n     */\n    if ((capacitor === null || capacitor === void 0 ? void 0 : capacitor.getPlatform()) === 'web') {\n      return typeof navigator !== 'undefined' && navigator.vibrate !== undefined;\n    }\n    return true;\n  },\n  isCordova() {\n    return window.TapticEngine !== undefined;\n  },\n  isCapacitor() {\n    return getCapacitor() !== undefined;\n  },\n  impact(options) {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    /**\n     * To provide backwards compatibility with Cordova apps,\n     * we convert the style to lowercase.\n     *\n     * TODO: FW-4707 - Remove this in Ionic 8\n     */\n    const style = this.isCapacitor() ? options.style : options.style.toLowerCase();\n    engine.impact({ style });\n  },\n  notification(options) {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    /**\n     * To provide backwards compatibility with Cordova apps,\n     * we convert the style to lowercase.\n     *\n     * TODO: FW-4707 - Remove this in Ionic 8\n     */\n    const type = this.isCapacitor() ? options.type : options.type.toLowerCase();\n    engine.notification({ type });\n  },\n  selection() {\n    /**\n     * To provide backwards compatibility with Cordova apps,\n     * we convert the style to lowercase.\n     *\n     * TODO: FW-4707 - Remove this in Ionic 8\n     */\n    const style = this.isCapacitor() ? ImpactStyle.Light : 'light';\n    this.impact({ style });\n  },\n  selectionStart() {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    if (this.isCapacitor()) {\n      engine.selectionStart();\n    }\n    else {\n      engine.gestureSelectionStart();\n    }\n  },\n  selectionChanged() {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    if (this.isCapacitor()) {\n      engine.selectionChanged();\n    }\n    else {\n      engine.gestureSelectionChanged();\n    }\n  },\n  selectionEnd() {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    if (this.isCapacitor()) {\n      engine.selectionEnd();\n    }\n    else {\n      engine.gestureSelectionEnd();\n    }\n  },\n};\n/**\n * Check to see if the Haptic Plugin is available\n * @return Returns `true` or false if the plugin is available\n */\nconst hapticAvailable = () => {\n  return HapticEngine.available();\n};\n/**\n * Trigger a selection changed haptic event. Good for one-time events\n * (not for gestures)\n */\nconst hapticSelection = () => {\n  hapticAvailable() && HapticEngine.selection();\n};\n/**\n * Tell the haptic engine that a gesture for a selection change is starting.\n */\nconst hapticSelectionStart = () => {\n  hapticAvailable() && HapticEngine.selectionStart();\n};\n/**\n * Tell the haptic engine that a selection changed during a gesture.\n */\nconst hapticSelectionChanged = () => {\n  hapticAvailable() && HapticEngine.selectionChanged();\n};\n/**\n * Tell the haptic engine we are done with a gesture. This needs to be\n * called lest resources are not properly recycled.\n */\nconst hapticSelectionEnd = () => {\n  hapticAvailable() && HapticEngine.selectionEnd();\n};\n/**\n * Use this to indicate success/failure/warning to the user.\n * options should be of the type `{ style: ImpactStyle.LIGHT }` (or `MEDIUM`/`HEAVY`)\n */\nconst hapticImpact = (options) => {\n  hapticAvailable() && HapticEngine.impact(options);\n};\n\nexport { ImpactStyle as I, hapticSelectionStart as a, hapticSelectionChanged as b, hapticSelection as c, hapticImpact as d, hapticSelectionEnd as h };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as writeTask, r as registerInstance, d as createEvent, e as readTask, h, f as getElement, H as Host } from './index-2d388930.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-66542bc5.js';\nimport { I as ION_CONTENT_CLASS_SELECTOR, b as ION_CONTENT_ELEMENT_SELECTOR, p as printIonContentErrorMsg, g as getScrollElement } from './index-746a238e.js';\nimport { t as transitionEndAsync, c as componentOnReady, l as clamp, g as getElementRoot, r as raf } from './helpers-3379ba19.js';\nimport { d as hapticImpact, I as ImpactStyle } from './haptic-6447af60.js';\nimport { a as isPlatform, b as getIonMode, c as config } from './ionic-global-b3fc28dd.js';\nimport { c as createAnimation } from './animation-a1d9e088.js';\nimport { E as ENABLE_HTML_CONTENT_DEFAULT, a as sanitizeDOMString } from './config-96c9ace3.js';\nimport { h as caretBackSharp, i as arrowDown } from './index-ecfc2c9f.js';\nimport { S as SPINNERS } from './spinner-configs-d09fbbbb.js';\nimport './index-595d62c9.js';\nimport './capacitor-b4979570.js';\nimport './index-7a14ecec.js';\n\nconst getRefresherAnimationType = (contentEl) => {\n  const previousSibling = contentEl.previousElementSibling;\n  const hasHeader = previousSibling !== null && previousSibling.tagName === 'ION-HEADER';\n  return hasHeader ? 'translate' : 'scale';\n};\nconst createPullingAnimation = (type, pullingSpinner, refresherEl) => {\n  return type === 'scale'\n    ? createScaleAnimation(pullingSpinner, refresherEl)\n    : createTranslateAnimation(pullingSpinner, refresherEl);\n};\nconst createBaseAnimation = (pullingRefresherIcon) => {\n  // TODO(FW-2832): add types/re-evaluate asserting so many things\n  const spinner = pullingRefresherIcon.querySelector('ion-spinner');\n  const circle = spinner.shadowRoot.querySelector('circle');\n  const spinnerArrowContainer = pullingRefresherIcon.querySelector('.spinner-arrow-container');\n  const arrowContainer = pullingRefresherIcon.querySelector('.arrow-container');\n  const arrow = arrowContainer ? arrowContainer.querySelector('ion-icon') : null;\n  const baseAnimation = createAnimation().duration(1000).easing('ease-out');\n  const spinnerArrowContainerAnimation = createAnimation()\n    .addElement(spinnerArrowContainer)\n    .keyframes([\n    { offset: 0, opacity: '0.3' },\n    { offset: 0.45, opacity: '0.3' },\n    { offset: 0.55, opacity: '1' },\n    { offset: 1, opacity: '1' },\n  ]);\n  const circleInnerAnimation = createAnimation()\n    .addElement(circle)\n    .keyframes([\n    { offset: 0, strokeDasharray: '1px, 200px' },\n    { offset: 0.2, strokeDasharray: '1px, 200px' },\n    { offset: 0.55, strokeDasharray: '100px, 200px' },\n    { offset: 1, strokeDasharray: '100px, 200px' },\n  ]);\n  const circleOuterAnimation = createAnimation()\n    .addElement(spinner)\n    .keyframes([\n    { offset: 0, transform: 'rotate(-90deg)' },\n    { offset: 1, transform: 'rotate(210deg)' },\n  ]);\n  /**\n   * Only add arrow animation if present\n   * this allows users to customize the spinners\n   * without errors being thrown\n   */\n  if (arrowContainer && arrow) {\n    const arrowContainerAnimation = createAnimation()\n      .addElement(arrowContainer)\n      .keyframes([\n      { offset: 0, transform: 'rotate(0deg)' },\n      { offset: 0.3, transform: 'rotate(0deg)' },\n      { offset: 0.55, transform: 'rotate(280deg)' },\n      { offset: 1, transform: 'rotate(400deg)' },\n    ]);\n    const arrowAnimation = createAnimation()\n      .addElement(arrow)\n      .keyframes([\n      { offset: 0, transform: 'translateX(2px) scale(0)' },\n      { offset: 0.3, transform: 'translateX(2px) scale(0)' },\n      { offset: 0.55, transform: 'translateX(-1.5px) scale(1)' },\n      { offset: 1, transform: 'translateX(-1.5px) scale(1)' },\n    ]);\n    baseAnimation.addAnimation([arrowContainerAnimation, arrowAnimation]);\n  }\n  return baseAnimation.addAnimation([spinnerArrowContainerAnimation, circleInnerAnimation, circleOuterAnimation]);\n};\nconst createScaleAnimation = (pullingRefresherIcon, refresherEl) => {\n  /**\n   * Do not take the height of the refresher icon\n   * because at this point the DOM has not updated,\n   * so the refresher icon is still hidden with\n   * display: none.\n   * The `ion-refresher` container height\n   * is roughly the amount we need to offset\n   * the icon by when pulling down.\n   */\n  const height = refresherEl.clientHeight;\n  const spinnerAnimation = createAnimation()\n    .addElement(pullingRefresherIcon)\n    .keyframes([\n    { offset: 0, transform: `scale(0) translateY(-${height}px)` },\n    { offset: 1, transform: 'scale(1) translateY(100px)' },\n  ]);\n  return createBaseAnimation(pullingRefresherIcon).addAnimation([spinnerAnimation]);\n};\nconst createTranslateAnimation = (pullingRefresherIcon, refresherEl) => {\n  /**\n   * Do not take the height of the refresher icon\n   * because at this point the DOM has not updated,\n   * so the refresher icon is still hidden with\n   * display: none.\n   * The `ion-refresher` container height\n   * is roughly the amount we need to offset\n   * the icon by when pulling down.\n   */\n  const height = refresherEl.clientHeight;\n  const spinnerAnimation = createAnimation()\n    .addElement(pullingRefresherIcon)\n    .keyframes([\n    { offset: 0, transform: `translateY(-${height}px)` },\n    { offset: 1, transform: 'translateY(100px)' },\n  ]);\n  return createBaseAnimation(pullingRefresherIcon).addAnimation([spinnerAnimation]);\n};\nconst createSnapBackAnimation = (pullingRefresherIcon) => {\n  return createAnimation()\n    .duration(125)\n    .addElement(pullingRefresherIcon)\n    .fromTo('transform', 'translateY(var(--ion-pulling-refresher-translate, 100px))', 'translateY(0px)');\n};\n// iOS Native Refresher\n// -----------------------------\nconst setSpinnerOpacity = (spinner, opacity) => {\n  spinner.style.setProperty('opacity', opacity.toString());\n};\nconst handleScrollWhilePulling = (ticks, numTicks, pullAmount) => {\n  const max = 1;\n  writeTask(() => {\n    ticks.forEach((el, i) => {\n      /**\n       * Compute the opacity of each tick\n       * mark as a percentage of the pullAmount\n       * offset by max / numTicks so\n       * the tick marks are shown staggered.\n       */\n      const min = i * (max / numTicks);\n      const range = max - min;\n      const start = pullAmount - min;\n      const progression = clamp(0, start / range, 1);\n      el.style.setProperty('opacity', progression.toString());\n    });\n  });\n};\nconst handleScrollWhileRefreshing = (spinner, lastVelocityY) => {\n  writeTask(() => {\n    // If user pulls down quickly, the spinner should spin faster\n    spinner.style.setProperty('--refreshing-rotation-duration', lastVelocityY >= 1.0 ? '0.5s' : '2s');\n    spinner.style.setProperty('opacity', '1');\n  });\n};\nconst translateElement = (el, value, duration = 200) => {\n  if (!el) {\n    return Promise.resolve();\n  }\n  const trans = transitionEndAsync(el, duration);\n  writeTask(() => {\n    el.style.setProperty('transition', `${duration}ms all ease-out`);\n    if (value === undefined) {\n      el.style.removeProperty('transform');\n    }\n    else {\n      el.style.setProperty('transform', `translate3d(0px, ${value}, 0px)`);\n    }\n  });\n  return trans;\n};\n// Utils\n// -----------------------------\nconst shouldUseNativeRefresher = async (referenceEl, mode) => {\n  const refresherContent = referenceEl.querySelector('ion-refresher-content');\n  if (!refresherContent) {\n    return Promise.resolve(false);\n  }\n  await new Promise((resolve) => componentOnReady(refresherContent, resolve));\n  const pullingSpinner = referenceEl.querySelector('ion-refresher-content .refresher-pulling ion-spinner');\n  const refreshingSpinner = referenceEl.querySelector('ion-refresher-content .refresher-refreshing ion-spinner');\n  return (pullingSpinner !== null &&\n    refreshingSpinner !== null &&\n    ((mode === 'ios' && isPlatform('mobile') && referenceEl.style.webkitOverflowScrolling !== undefined) ||\n      mode === 'md'));\n};\n\nconst refresherIosCss = \"ion-refresher{top:0;display:none;position:absolute;width:100%;height:60px;pointer-events:none;z-index:-1}@supports (inset-inline-start: 0){ion-refresher{inset-inline-start:0}}@supports not (inset-inline-start: 0){ion-refresher{left:0}:host-context([dir=rtl]) ion-refresher{left:unset;right:unset;right:0}[dir=rtl] ion-refresher{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){ion-refresher:dir(rtl){left:unset;right:unset;right:0}}}ion-refresher.refresher-active{display:block}ion-refresher-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;height:100%}.refresher-pulling,.refresher-refreshing{display:none;width:100%}.refresher-pulling-icon,.refresher-refreshing-icon{-webkit-transform-origin:center;transform-origin:center;-webkit-transition:200ms;transition:200ms;font-size:30px;text-align:center}:host-context([dir=rtl]) .refresher-pulling-icon,:host-context([dir=rtl]) .refresher-refreshing-icon{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}[dir=rtl] .refresher-pulling-icon,[dir=rtl] .refresher-refreshing-icon{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}@supports selector(:dir(rtl)){.refresher-pulling-icon:dir(rtl),.refresher-refreshing-icon:dir(rtl){-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}}.refresher-pulling-text,.refresher-refreshing-text{font-size:16px;text-align:center}ion-refresher-content .arrow-container{display:none}.refresher-pulling ion-refresher-content .refresher-pulling{display:block}.refresher-ready ion-refresher-content .refresher-pulling{display:block}.refresher-ready ion-refresher-content .refresher-pulling-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.refresher-refreshing ion-refresher-content .refresher-refreshing{display:block}.refresher-cancelling ion-refresher-content .refresher-pulling{display:block}.refresher-cancelling ion-refresher-content .refresher-pulling-icon{-webkit-transform:scale(0);transform:scale(0)}.refresher-completing ion-refresher-content .refresher-refreshing{display:block}.refresher-completing ion-refresher-content .refresher-refreshing-icon{-webkit-transform:scale(0);transform:scale(0)}.refresher-native .refresher-pulling-text,.refresher-native .refresher-refreshing-text{display:none}.refresher-ios .refresher-pulling-icon,.refresher-ios .refresher-refreshing-icon{color:var(--ion-text-color, #000)}.refresher-ios .refresher-pulling-text,.refresher-ios .refresher-refreshing-text{color:var(--ion-text-color, #000)}.refresher-ios .refresher-refreshing .spinner-lines-ios line,.refresher-ios .refresher-refreshing .spinner-lines-small-ios line,.refresher-ios .refresher-refreshing .spinner-crescent circle{stroke:var(--ion-text-color, #000)}.refresher-ios .refresher-refreshing .spinner-bubbles circle,.refresher-ios .refresher-refreshing .spinner-circles circle,.refresher-ios .refresher-refreshing .spinner-dots circle{fill:var(--ion-text-color, #000)}ion-refresher.refresher-native{display:block;z-index:1}ion-refresher.refresher-native ion-spinner{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0}.refresher-native .refresher-refreshing ion-spinner{--refreshing-rotation-duration:2s;display:none;-webkit-animation:var(--refreshing-rotation-duration) ease-out refresher-rotate forwards;animation:var(--refreshing-rotation-duration) ease-out refresher-rotate forwards}.refresher-native .refresher-refreshing{display:none;-webkit-animation:250ms linear refresher-pop forwards;animation:250ms linear refresher-pop forwards}.refresher-native ion-spinner{width:32px;height:32px;color:var(--ion-color-step-450, #747577)}.refresher-native.refresher-refreshing .refresher-pulling ion-spinner,.refresher-native.refresher-completing .refresher-pulling ion-spinner{display:none}.refresher-native.refresher-refreshing .refresher-refreshing ion-spinner,.refresher-native.refresher-completing .refresher-refreshing ion-spinner{display:block}.refresher-native.refresher-pulling .refresher-pulling ion-spinner{display:block}.refresher-native.refresher-pulling .refresher-refreshing ion-spinner{display:none}.refresher-native.refresher-completing ion-refresher-content .refresher-refreshing-icon{-webkit-transform:scale(0) rotate(180deg);transform:scale(0) rotate(180deg);-webkit-transition:300ms;transition:300ms}@-webkit-keyframes refresher-pop{0%{-webkit-transform:scale(1);transform:scale(1);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}50%{-webkit-transform:scale(1.2);transform:scale(1.2);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}100%{-webkit-transform:scale(1);transform:scale(1)}}@keyframes refresher-pop{0%{-webkit-transform:scale(1);transform:scale(1);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}50%{-webkit-transform:scale(1.2);transform:scale(1.2);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}100%{-webkit-transform:scale(1);transform:scale(1)}}@-webkit-keyframes refresher-rotate{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(180deg);transform:rotate(180deg)}}@keyframes refresher-rotate{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(180deg);transform:rotate(180deg)}}\";\n\nconst refresherMdCss = \"ion-refresher{top:0;display:none;position:absolute;width:100%;height:60px;pointer-events:none;z-index:-1}@supports (inset-inline-start: 0){ion-refresher{inset-inline-start:0}}@supports not (inset-inline-start: 0){ion-refresher{left:0}:host-context([dir=rtl]) ion-refresher{left:unset;right:unset;right:0}[dir=rtl] ion-refresher{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){ion-refresher:dir(rtl){left:unset;right:unset;right:0}}}ion-refresher.refresher-active{display:block}ion-refresher-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;height:100%}.refresher-pulling,.refresher-refreshing{display:none;width:100%}.refresher-pulling-icon,.refresher-refreshing-icon{-webkit-transform-origin:center;transform-origin:center;-webkit-transition:200ms;transition:200ms;font-size:30px;text-align:center}:host-context([dir=rtl]) .refresher-pulling-icon,:host-context([dir=rtl]) .refresher-refreshing-icon{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}[dir=rtl] .refresher-pulling-icon,[dir=rtl] .refresher-refreshing-icon{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}@supports selector(:dir(rtl)){.refresher-pulling-icon:dir(rtl),.refresher-refreshing-icon:dir(rtl){-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}}.refresher-pulling-text,.refresher-refreshing-text{font-size:16px;text-align:center}ion-refresher-content .arrow-container{display:none}.refresher-pulling ion-refresher-content .refresher-pulling{display:block}.refresher-ready ion-refresher-content .refresher-pulling{display:block}.refresher-ready ion-refresher-content .refresher-pulling-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.refresher-refreshing ion-refresher-content .refresher-refreshing{display:block}.refresher-cancelling ion-refresher-content .refresher-pulling{display:block}.refresher-cancelling ion-refresher-content .refresher-pulling-icon{-webkit-transform:scale(0);transform:scale(0)}.refresher-completing ion-refresher-content .refresher-refreshing{display:block}.refresher-completing ion-refresher-content .refresher-refreshing-icon{-webkit-transform:scale(0);transform:scale(0)}.refresher-native .refresher-pulling-text,.refresher-native .refresher-refreshing-text{display:none}.refresher-md .refresher-pulling-icon,.refresher-md .refresher-refreshing-icon{color:var(--ion-text-color, #000)}.refresher-md .refresher-pulling-text,.refresher-md .refresher-refreshing-text{color:var(--ion-text-color, #000)}.refresher-md .refresher-refreshing .spinner-lines-md line,.refresher-md .refresher-refreshing .spinner-lines-small-md line,.refresher-md .refresher-refreshing .spinner-crescent circle{stroke:var(--ion-text-color, #000)}.refresher-md .refresher-refreshing .spinner-bubbles circle,.refresher-md .refresher-refreshing .spinner-circles circle,.refresher-md .refresher-refreshing .spinner-dots circle{fill:var(--ion-text-color, #000)}ion-refresher.refresher-native{display:block;z-index:1}ion-refresher.refresher-native ion-spinner{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;width:24px;height:24px;color:var(--ion-color-primary, #3880ff)}ion-refresher.refresher-native .spinner-arrow-container{display:inherit}ion-refresher.refresher-native .arrow-container{display:block;position:absolute;width:24px;height:24px}ion-refresher.refresher-native .arrow-container ion-icon{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;left:0;right:0;bottom:-4px;position:absolute;color:var(--ion-color-primary, #3880ff);font-size:12px}ion-refresher.refresher-native.refresher-pulling ion-refresher-content .refresher-pulling,ion-refresher.refresher-native.refresher-ready ion-refresher-content .refresher-pulling{display:-ms-flexbox;display:flex}ion-refresher.refresher-native.refresher-refreshing ion-refresher-content .refresher-refreshing,ion-refresher.refresher-native.refresher-completing ion-refresher-content .refresher-refreshing,ion-refresher.refresher-native.refresher-cancelling ion-refresher-content .refresher-refreshing{display:-ms-flexbox;display:flex}ion-refresher.refresher-native .refresher-pulling-icon{-webkit-transform:translateY(calc(-100% - 10px));transform:translateY(calc(-100% - 10px))}ion-refresher.refresher-native .refresher-pulling-icon,ion-refresher.refresher-native .refresher-refreshing-icon{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;border-radius:100%;-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;display:-ms-flexbox;display:flex;border:1px solid var(--ion-color-step-200, #ececec);background:var(--ion-color-step-250, #ffffff);-webkit-box-shadow:0px 1px 6px rgba(0, 0, 0, 0.1);box-shadow:0px 1px 6px rgba(0, 0, 0, 0.1)}\";\n\nconst Refresher = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionRefresh = createEvent(this, \"ionRefresh\", 7);\n    this.ionPull = createEvent(this, \"ionPull\", 7);\n    this.ionStart = createEvent(this, \"ionStart\", 7);\n    this.appliedStyles = false;\n    this.didStart = false;\n    this.progress = 0;\n    this.pointerDown = false;\n    this.needsCompletion = false;\n    this.didRefresh = false;\n    this.lastVelocityY = 0;\n    this.animations = [];\n    this.nativeRefresher = false;\n    this.state = 1 /* RefresherState.Inactive */;\n    this.pullMin = 60;\n    this.pullMax = this.pullMin + 60;\n    this.closeDuration = '280ms';\n    this.snapbackDuration = '280ms';\n    this.pullFactor = 1;\n    this.disabled = false;\n  }\n  disabledChanged() {\n    if (this.gesture) {\n      this.gesture.enable(!this.disabled);\n    }\n  }\n  async checkNativeRefresher() {\n    const useNativeRefresher = await shouldUseNativeRefresher(this.el, getIonMode(this));\n    if (useNativeRefresher && !this.nativeRefresher) {\n      const contentEl = this.el.closest('ion-content');\n      this.setupNativeRefresher(contentEl);\n    }\n    else if (!useNativeRefresher) {\n      this.destroyNativeRefresher();\n    }\n  }\n  destroyNativeRefresher() {\n    if (this.scrollEl && this.scrollListenerCallback) {\n      this.scrollEl.removeEventListener('scroll', this.scrollListenerCallback);\n      this.scrollListenerCallback = undefined;\n    }\n    this.nativeRefresher = false;\n  }\n  async resetNativeRefresher(el, state) {\n    this.state = state;\n    if (getIonMode(this) === 'ios') {\n      await translateElement(el, undefined, 300);\n    }\n    else {\n      await transitionEndAsync(this.el.querySelector('.refresher-refreshing-icon'), 200);\n    }\n    this.didRefresh = false;\n    this.needsCompletion = false;\n    this.pointerDown = false;\n    this.animations.forEach((ani) => ani.destroy());\n    this.animations = [];\n    this.progress = 0;\n    this.state = 1 /* RefresherState.Inactive */;\n  }\n  async setupiOSNativeRefresher(pullingSpinner, refreshingSpinner) {\n    this.elementToTransform = this.scrollEl;\n    const ticks = pullingSpinner.shadowRoot.querySelectorAll('svg');\n    let MAX_PULL = this.scrollEl.clientHeight * 0.16;\n    const NUM_TICKS = ticks.length;\n    writeTask(() => ticks.forEach((el) => el.style.setProperty('animation', 'none')));\n    this.scrollListenerCallback = () => {\n      // If pointer is not on screen or refresher is not active, ignore scroll\n      if (!this.pointerDown && this.state === 1 /* RefresherState.Inactive */) {\n        return;\n      }\n      readTask(() => {\n        // PTR should only be active when overflow scrolling at the top\n        const scrollTop = this.scrollEl.scrollTop;\n        const refresherHeight = this.el.clientHeight;\n        if (scrollTop > 0) {\n          /**\n           * If refresher is refreshing and user tries to scroll\n           * progressively fade refresher out/in\n           */\n          if (this.state === 8 /* RefresherState.Refreshing */) {\n            const ratio = clamp(0, scrollTop / (refresherHeight * 0.5), 1);\n            writeTask(() => setSpinnerOpacity(refreshingSpinner, 1 - ratio));\n            return;\n          }\n          return;\n        }\n        if (this.pointerDown) {\n          if (!this.didStart) {\n            this.didStart = true;\n            this.ionStart.emit();\n          }\n          // emit \"pulling\" on every move\n          if (this.pointerDown) {\n            this.ionPull.emit();\n          }\n        }\n        /**\n         * We want to delay the start of this gesture by ~30px\n         * when initially pulling down so the refresher does not\n         * overlap with the content. But when letting go of the\n         * gesture before the refresher completes, we want the\n         * refresher tick marks to quickly fade out.\n         */\n        const offset = this.didStart ? 30 : 0;\n        const pullAmount = (this.progress = clamp(0, (Math.abs(scrollTop) - offset) / MAX_PULL, 1));\n        const shouldShowRefreshingSpinner = this.state === 8 /* RefresherState.Refreshing */ || pullAmount === 1;\n        if (shouldShowRefreshingSpinner) {\n          if (this.pointerDown) {\n            handleScrollWhileRefreshing(refreshingSpinner, this.lastVelocityY);\n          }\n          if (!this.didRefresh) {\n            this.beginRefresh();\n            this.didRefresh = true;\n            hapticImpact({ style: ImpactStyle.Light });\n            /**\n             * Translate the content element otherwise when pointer is removed\n             * from screen the scroll content will bounce back over the refresher\n             */\n            if (!this.pointerDown) {\n              translateElement(this.elementToTransform, `${refresherHeight}px`);\n            }\n          }\n        }\n        else {\n          this.state = 2 /* RefresherState.Pulling */;\n          handleScrollWhilePulling(ticks, NUM_TICKS, pullAmount);\n        }\n      });\n    };\n    this.scrollEl.addEventListener('scroll', this.scrollListenerCallback);\n    this.gesture = (await import('./index-ff313b19.js')).createGesture({\n      el: this.scrollEl,\n      gestureName: 'refresher',\n      gesturePriority: 31,\n      direction: 'y',\n      threshold: 5,\n      onStart: () => {\n        this.pointerDown = true;\n        if (!this.didRefresh) {\n          translateElement(this.elementToTransform, '0px');\n        }\n        /**\n         * If the content had `display: none` when\n         * the refresher was initialized, its clientHeight\n         * will be 0. When the gesture starts, the content\n         * will be visible, so try to get the correct\n         * client height again. This is most common when\n         * using the refresher in an ion-menu.\n         */\n        if (MAX_PULL === 0) {\n          MAX_PULL = this.scrollEl.clientHeight * 0.16;\n        }\n      },\n      onMove: (ev) => {\n        this.lastVelocityY = ev.velocityY;\n      },\n      onEnd: () => {\n        this.pointerDown = false;\n        this.didStart = false;\n        if (this.needsCompletion) {\n          this.resetNativeRefresher(this.elementToTransform, 32 /* RefresherState.Completing */);\n          this.needsCompletion = false;\n        }\n        else if (this.didRefresh) {\n          readTask(() => translateElement(this.elementToTransform, `${this.el.clientHeight}px`));\n        }\n      },\n    });\n    this.disabledChanged();\n  }\n  async setupMDNativeRefresher(contentEl, pullingSpinner, refreshingSpinner) {\n    const circle = getElementRoot(pullingSpinner).querySelector('circle');\n    const pullingRefresherIcon = this.el.querySelector('ion-refresher-content .refresher-pulling-icon');\n    const refreshingCircle = getElementRoot(refreshingSpinner).querySelector('circle');\n    if (circle !== null && refreshingCircle !== null) {\n      writeTask(() => {\n        circle.style.setProperty('animation', 'none');\n        // This lines up the animation on the refreshing spinner with the pulling spinner\n        refreshingSpinner.style.setProperty('animation-delay', '-655ms');\n        refreshingCircle.style.setProperty('animation-delay', '-655ms');\n      });\n    }\n    this.gesture = (await import('./index-ff313b19.js')).createGesture({\n      el: this.scrollEl,\n      gestureName: 'refresher',\n      gesturePriority: 31,\n      direction: 'y',\n      threshold: 5,\n      canStart: () => this.state !== 8 /* RefresherState.Refreshing */ &&\n        this.state !== 32 /* RefresherState.Completing */ &&\n        this.scrollEl.scrollTop === 0,\n      onStart: (ev) => {\n        this.progress = 0;\n        ev.data = { animation: undefined, didStart: false, cancelled: false };\n      },\n      onMove: (ev) => {\n        if ((ev.velocityY < 0 && this.progress === 0 && !ev.data.didStart) || ev.data.cancelled) {\n          ev.data.cancelled = true;\n          return;\n        }\n        if (!ev.data.didStart) {\n          ev.data.didStart = true;\n          this.state = 2 /* RefresherState.Pulling */;\n          // When ion-refresher is being used with a custom scroll target, the overflow styles need to be applied directly instead of via a css variable\n          const { scrollEl } = this;\n          const overflowProperty = scrollEl.matches(ION_CONTENT_CLASS_SELECTOR) ? 'overflow' : '--overflow';\n          writeTask(() => scrollEl.style.setProperty(overflowProperty, 'hidden'));\n          const animationType = getRefresherAnimationType(contentEl);\n          const animation = createPullingAnimation(animationType, pullingRefresherIcon, this.el);\n          ev.data.animation = animation;\n          animation.progressStart(false, 0);\n          this.ionStart.emit();\n          this.animations.push(animation);\n          return;\n        }\n        // Since we are using an easing curve, slow the gesture tracking down a bit\n        this.progress = clamp(0, (ev.deltaY / 180) * 0.5, 1);\n        ev.data.animation.progressStep(this.progress);\n        this.ionPull.emit();\n      },\n      onEnd: (ev) => {\n        if (!ev.data.didStart) {\n          return;\n        }\n        this.gesture.enable(false);\n        const { scrollEl } = this;\n        const overflowProperty = scrollEl.matches(ION_CONTENT_CLASS_SELECTOR) ? 'overflow' : '--overflow';\n        writeTask(() => scrollEl.style.removeProperty(overflowProperty));\n        if (this.progress <= 0.4) {\n          ev.data.animation.progressEnd(0, this.progress, 500).onFinish(() => {\n            this.animations.forEach((ani) => ani.destroy());\n            this.animations = [];\n            this.gesture.enable(true);\n            this.state = 1 /* RefresherState.Inactive */;\n          });\n          return;\n        }\n        const progress = getTimeGivenProgression([0, 0], [0, 0], [1, 1], [1, 1], this.progress)[0];\n        const snapBackAnimation = createSnapBackAnimation(pullingRefresherIcon);\n        this.animations.push(snapBackAnimation);\n        writeTask(async () => {\n          pullingRefresherIcon.style.setProperty('--ion-pulling-refresher-translate', `${progress * 100}px`);\n          ev.data.animation.progressEnd();\n          await snapBackAnimation.play();\n          this.beginRefresh();\n          ev.data.animation.destroy();\n          this.gesture.enable(true);\n        });\n      },\n    });\n    this.disabledChanged();\n  }\n  async setupNativeRefresher(contentEl) {\n    if (this.scrollListenerCallback || !contentEl || this.nativeRefresher || !this.scrollEl) {\n      return;\n    }\n    /**\n     * If using non-native refresher before make sure\n     * we clean up any old CSS. This can happen when\n     * a user manually calls the refresh method in a\n     * component create callback before the native\n     * refresher is setup.\n     */\n    this.setCss(0, '', false, '');\n    this.nativeRefresher = true;\n    const pullingSpinner = this.el.querySelector('ion-refresher-content .refresher-pulling ion-spinner');\n    const refreshingSpinner = this.el.querySelector('ion-refresher-content .refresher-refreshing ion-spinner');\n    if (getIonMode(this) === 'ios') {\n      this.setupiOSNativeRefresher(pullingSpinner, refreshingSpinner);\n    }\n    else {\n      this.setupMDNativeRefresher(contentEl, pullingSpinner, refreshingSpinner);\n    }\n  }\n  componentDidUpdate() {\n    this.checkNativeRefresher();\n  }\n  async connectedCallback() {\n    if (this.el.getAttribute('slot') !== 'fixed') {\n      console.error('Make sure you use: <ion-refresher slot=\"fixed\">');\n      return;\n    }\n    const contentEl = this.el.closest(ION_CONTENT_ELEMENT_SELECTOR);\n    if (!contentEl) {\n      printIonContentErrorMsg(this.el);\n      return;\n    }\n    /**\n     * Waits for the content to be ready before querying the scroll\n     * or the background content element.\n     */\n    componentOnReady(contentEl, async () => {\n      const customScrollTarget = contentEl.querySelector(ION_CONTENT_CLASS_SELECTOR);\n      /**\n       * Query the custom scroll target (if available), first. In refresher implementations,\n       * the ion-refresher element will always be a direct child of ion-content (slot=\"fixed\"). By\n       * querying the custom scroll target first and falling back to the ion-content element,\n       * the correct scroll element will be returned by the implementation.\n       */\n      this.scrollEl = await getScrollElement(customScrollTarget !== null && customScrollTarget !== void 0 ? customScrollTarget : contentEl);\n      /**\n       * Query the background content element from the host ion-content element directly.\n       */\n      this.backgroundContentEl = await contentEl.getBackgroundElement();\n      if (await shouldUseNativeRefresher(this.el, getIonMode(this))) {\n        this.setupNativeRefresher(contentEl);\n      }\n      else {\n        this.gesture = (await import('./index-ff313b19.js')).createGesture({\n          el: contentEl,\n          gestureName: 'refresher',\n          gesturePriority: 31,\n          direction: 'y',\n          threshold: 20,\n          passive: false,\n          canStart: () => this.canStart(),\n          onStart: () => this.onStart(),\n          onMove: (ev) => this.onMove(ev),\n          onEnd: () => this.onEnd(),\n        });\n        this.disabledChanged();\n      }\n    });\n  }\n  disconnectedCallback() {\n    this.destroyNativeRefresher();\n    this.scrollEl = undefined;\n    if (this.gesture) {\n      this.gesture.destroy();\n      this.gesture = undefined;\n    }\n  }\n  /**\n   * Call `complete()` when your async operation has completed.\n   * For example, the `refreshing` state is while the app is performing\n   * an asynchronous operation, such as receiving more data from an\n   * AJAX request. Once the data has been received, you then call this\n   * method to signify that the refreshing has completed and to close\n   * the refresher. This method also changes the refresher's state from\n   * `refreshing` to `completing`.\n   */\n  async complete() {\n    if (this.nativeRefresher) {\n      this.needsCompletion = true;\n      // Do not reset scroll el until user removes pointer from screen\n      if (!this.pointerDown) {\n        raf(() => raf(() => this.resetNativeRefresher(this.elementToTransform, 32 /* RefresherState.Completing */)));\n      }\n    }\n    else {\n      this.close(32 /* RefresherState.Completing */, '120ms');\n    }\n  }\n  /**\n   * Changes the refresher's state from `refreshing` to `cancelling`.\n   */\n  async cancel() {\n    if (this.nativeRefresher) {\n      // Do not reset scroll el until user removes pointer from screen\n      if (!this.pointerDown) {\n        raf(() => raf(() => this.resetNativeRefresher(this.elementToTransform, 16 /* RefresherState.Cancelling */)));\n      }\n    }\n    else {\n      this.close(16 /* RefresherState.Cancelling */, '');\n    }\n  }\n  /**\n   * A number representing how far down the user has pulled.\n   * The number `0` represents the user hasn't pulled down at all. The\n   * number `1`, and anything greater than `1`, represents that the user\n   * has pulled far enough down that when they let go then the refresh will\n   * happen. If they let go and the number is less than `1`, then the\n   * refresh will not happen, and the content will return to it's original\n   * position.\n   */\n  getProgress() {\n    return Promise.resolve(this.progress);\n  }\n  canStart() {\n    if (!this.scrollEl) {\n      return false;\n    }\n    if (this.state !== 1 /* RefresherState.Inactive */) {\n      return false;\n    }\n    // if the scrollTop is greater than zero then it's\n    // not possible to pull the content down yet\n    if (this.scrollEl.scrollTop > 0) {\n      return false;\n    }\n    return true;\n  }\n  onStart() {\n    this.progress = 0;\n    this.state = 1 /* RefresherState.Inactive */;\n    this.memoizeOverflowStyle();\n  }\n  onMove(detail) {\n    if (!this.scrollEl) {\n      return;\n    }\n    // this method can get called like a bazillion times per second,\n    // so it's built to be as efficient as possible, and does its\n    // best to do any DOM read/writes only when absolutely necessary\n    // if multi-touch then get out immediately\n    const ev = detail.event;\n    if (ev.touches !== undefined && ev.touches.length > 1) {\n      return;\n    }\n    // do nothing if it's actively refreshing\n    // or it's in the way of closing\n    // or this was never a startY\n    if ((this.state & 56 /* RefresherState._BUSY_ */) !== 0) {\n      return;\n    }\n    const pullFactor = Number.isNaN(this.pullFactor) || this.pullFactor < 0 ? 1 : this.pullFactor;\n    const deltaY = detail.deltaY * pullFactor;\n    // don't bother if they're scrolling up\n    // and have not already started dragging\n    if (deltaY <= 0) {\n      // the current Y is higher than the starting Y\n      // so they scrolled up enough to be ignored\n      this.progress = 0;\n      this.state = 1 /* RefresherState.Inactive */;\n      if (this.appliedStyles) {\n        // reset the styles only if they were applied\n        this.setCss(0, '', false, '');\n        return;\n      }\n      return;\n    }\n    if (this.state === 1 /* RefresherState.Inactive */) {\n      // this refresh is not already actively pulling down\n      // get the content's scrollTop\n      const scrollHostScrollTop = this.scrollEl.scrollTop;\n      // if the scrollTop is greater than zero then it's\n      // not possible to pull the content down yet\n      if (scrollHostScrollTop > 0) {\n        this.progress = 0;\n        return;\n      }\n      // content scrolled all the way to the top, and dragging down\n      this.state = 2 /* RefresherState.Pulling */;\n    }\n    // prevent native scroll events\n    if (ev.cancelable) {\n      ev.preventDefault();\n    }\n    // the refresher is actively pulling at this point\n    // move the scroll element within the content element\n    this.setCss(deltaY, '0ms', true, '');\n    if (deltaY === 0) {\n      // don't continue if there's no delta yet\n      this.progress = 0;\n      return;\n    }\n    const pullMin = this.pullMin;\n    // set pull progress\n    this.progress = deltaY / pullMin;\n    // emit \"start\" if it hasn't started yet\n    if (!this.didStart) {\n      this.didStart = true;\n      this.ionStart.emit();\n    }\n    // emit \"pulling\" on every move\n    this.ionPull.emit();\n    // do nothing if the delta is less than the pull threshold\n    if (deltaY < pullMin) {\n      // ensure it stays in the pulling state, cuz its not ready yet\n      this.state = 2 /* RefresherState.Pulling */;\n      return;\n    }\n    if (deltaY > this.pullMax) {\n      // they pulled farther than the max, so kick off the refresh\n      this.beginRefresh();\n      return;\n    }\n    // pulled farther than the pull min!!\n    // it is now in the `ready` state!!\n    // if they let go then it'll refresh, kerpow!!\n    this.state = 4 /* RefresherState.Ready */;\n    return;\n  }\n  onEnd() {\n    // only run in a zone when absolutely necessary\n    if (this.state === 4 /* RefresherState.Ready */) {\n      // they pulled down far enough, so it's ready to refresh\n      this.beginRefresh();\n    }\n    else if (this.state === 2 /* RefresherState.Pulling */) {\n      // they were pulling down, but didn't pull down far enough\n      // set the content back to it's original location\n      // and close the refresher\n      // set that the refresh is actively cancelling\n      this.cancel();\n    }\n    else if (this.state === 1 /* RefresherState.Inactive */) {\n      /**\n       * The pull to refresh gesture was aborted\n       * so we should immediately restore any overflow styles\n       * that have been modified. Do not call this.cancel\n       * because the styles will only be reset after a timeout.\n       * If the gesture is aborted then scrolling should be\n       * available right away.\n       */\n      this.restoreOverflowStyle();\n    }\n  }\n  beginRefresh() {\n    // assumes we're already back in a zone\n    // they pulled down far enough, so it's ready to refresh\n    this.state = 8 /* RefresherState.Refreshing */;\n    // place the content in a hangout position while it thinks\n    this.setCss(this.pullMin, this.snapbackDuration, true, '');\n    // emit \"refresh\" because it was pulled down far enough\n    // and they let go to begin refreshing\n    this.ionRefresh.emit({\n      complete: this.complete.bind(this),\n    });\n  }\n  close(state, delay) {\n    // create fallback timer incase something goes wrong with transitionEnd event\n    setTimeout(() => {\n      this.state = 1 /* RefresherState.Inactive */;\n      this.progress = 0;\n      this.didStart = false;\n      /**\n       * Reset any overflow styles so the\n       * user can scroll again.\n       */\n      this.setCss(0, '0ms', false, '', true);\n    }, 600);\n    // reset the styles on the scroll element\n    // set that the refresh is actively cancelling/completing\n    this.state = state;\n    this.setCss(0, this.closeDuration, true, delay);\n  }\n  setCss(y, duration, overflowVisible, delay, shouldRestoreOverflowStyle = false) {\n    if (this.nativeRefresher) {\n      return;\n    }\n    this.appliedStyles = y > 0;\n    writeTask(() => {\n      if (this.scrollEl && this.backgroundContentEl) {\n        const scrollStyle = this.scrollEl.style;\n        const backgroundStyle = this.backgroundContentEl.style;\n        scrollStyle.transform = backgroundStyle.transform = y > 0 ? `translateY(${y}px) translateZ(0px)` : '';\n        scrollStyle.transitionDuration = backgroundStyle.transitionDuration = duration;\n        scrollStyle.transitionDelay = backgroundStyle.transitionDelay = delay;\n        scrollStyle.overflow = overflowVisible ? 'hidden' : '';\n      }\n      /**\n       * Reset the overflow styles only once\n       * the pull to refresh effect has been closed.\n       * This ensures that the gesture is done\n       * and the refresh operation has either\n       * been aborted or has completed.\n       */\n      if (shouldRestoreOverflowStyle) {\n        this.restoreOverflowStyle();\n      }\n    });\n  }\n  memoizeOverflowStyle() {\n    if (this.scrollEl) {\n      const { overflow, overflowX, overflowY } = this.scrollEl.style;\n      this.overflowStyles = {\n        overflow: overflow !== null && overflow !== void 0 ? overflow : '',\n        overflowX: overflowX !== null && overflowX !== void 0 ? overflowX : '',\n        overflowY: overflowY !== null && overflowY !== void 0 ? overflowY : '',\n      };\n    }\n  }\n  restoreOverflowStyle() {\n    if (this.overflowStyles !== undefined && this.scrollEl !== undefined) {\n      const { overflow, overflowX, overflowY } = this.overflowStyles;\n      this.scrollEl.style.overflow = overflow;\n      this.scrollEl.style.overflowX = overflowX;\n      this.scrollEl.style.overflowY = overflowY;\n      this.overflowStyles = undefined;\n    }\n  }\n  render() {\n    const mode = getIonMode(this);\n    return (h(Host, { slot: \"fixed\", class: {\n        [mode]: true,\n        // Used internally for styling\n        [`refresher-${mode}`]: true,\n        'refresher-native': this.nativeRefresher,\n        'refresher-active': this.state !== 1 /* RefresherState.Inactive */,\n        'refresher-pulling': this.state === 2 /* RefresherState.Pulling */,\n        'refresher-ready': this.state === 4 /* RefresherState.Ready */,\n        'refresher-refreshing': this.state === 8 /* RefresherState.Refreshing */,\n        'refresher-cancelling': this.state === 16 /* RefresherState.Cancelling */,\n        'refresher-completing': this.state === 32 /* RefresherState.Completing */,\n      } }));\n  }\n  get el() { return getElement(this); }\n  static get watchers() { return {\n    \"disabled\": [\"disabledChanged\"]\n  }; }\n};\nRefresher.style = {\n  ios: refresherIosCss,\n  md: refresherMdCss\n};\n\nconst RefresherContent = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.customHTMLEnabled = config.get('innerHTMLTemplatesEnabled', ENABLE_HTML_CONTENT_DEFAULT);\n    this.pullingIcon = undefined;\n    this.pullingText = undefined;\n    this.refreshingSpinner = undefined;\n    this.refreshingText = undefined;\n  }\n  componentWillLoad() {\n    if (this.pullingIcon === undefined) {\n      const mode = getIonMode(this);\n      const overflowRefresher = this.el.style.webkitOverflowScrolling !== undefined ? 'lines' : arrowDown;\n      this.pullingIcon = config.get('refreshingIcon', mode === 'ios' && isPlatform('mobile') ? config.get('spinner', overflowRefresher) : 'circular');\n    }\n    if (this.refreshingSpinner === undefined) {\n      const mode = getIonMode(this);\n      this.refreshingSpinner = config.get('refreshingSpinner', config.get('spinner', mode === 'ios' ? 'lines' : 'circular'));\n    }\n  }\n  renderPullingText() {\n    const { customHTMLEnabled, pullingText } = this;\n    if (customHTMLEnabled) {\n      return h(\"div\", { class: \"refresher-pulling-text\", innerHTML: sanitizeDOMString(pullingText) });\n    }\n    return h(\"div\", { class: \"refresher-pulling-text\" }, pullingText);\n  }\n  renderRefreshingText() {\n    const { customHTMLEnabled, refreshingText } = this;\n    if (customHTMLEnabled) {\n      return h(\"div\", { class: \"refresher-refreshing-text\", innerHTML: sanitizeDOMString(refreshingText) });\n    }\n    return h(\"div\", { class: \"refresher-refreshing-text\" }, refreshingText);\n  }\n  render() {\n    const pullingIcon = this.pullingIcon;\n    const hasSpinner = pullingIcon != null && SPINNERS[pullingIcon] !== undefined;\n    const mode = getIonMode(this);\n    return (h(Host, { class: mode }, h(\"div\", { class: \"refresher-pulling\" }, this.pullingIcon && hasSpinner && (h(\"div\", { class: \"refresher-pulling-icon\" }, h(\"div\", { class: \"spinner-arrow-container\" }, h(\"ion-spinner\", { name: this.pullingIcon, paused: true }), mode === 'md' && this.pullingIcon === 'circular' && (h(\"div\", { class: \"arrow-container\" }, h(\"ion-icon\", { icon: caretBackSharp, \"aria-hidden\": \"true\" })))))), this.pullingIcon && !hasSpinner && (h(\"div\", { class: \"refresher-pulling-icon\" }, h(\"ion-icon\", { icon: this.pullingIcon, lazy: false, \"aria-hidden\": \"true\" }))), this.pullingText !== undefined && this.renderPullingText()), h(\"div\", { class: \"refresher-refreshing\" }, this.refreshingSpinner && (h(\"div\", { class: \"refresher-refreshing-icon\" }, h(\"ion-spinner\", { name: this.refreshingSpinner }))), this.refreshingText !== undefined && this.renderRefreshingText())));\n  }\n  get el() { return getElement(this); }\n};\n\nexport { Refresher as ion_refresher, RefresherContent as ion_refresher_content };\n"], "names": ["w", "win", "getCapacitor", "undefined", "Capacitor", "g", "ImpactStyle", "NotificationType", "HapticEngine", "getEngine", "tapticEngine", "window", "TapticEngine", "capacitor", "isPluginAvailable", "Plugins", "Haptics", "available", "engine", "getPlatform", "navigator", "vibrate", "<PERSON><PERSON><PERSON><PERSON>", "isCapacitor", "impact", "options", "style", "toLowerCase", "notification", "type", "selection", "Light", "selectionStart", "gestureSelectionStart", "selectionChanged", "gestureSelectionChanged", "selectionEnd", "gestureSelectionEnd", "hapticAvailable", "hapticSelection", "hapticSelectionStart", "hapticSelectionChanged", "hapticSelectionEnd", "hapticImpact", "I", "a", "b", "c", "d", "h", "writeTask", "r", "registerInstance", "createEvent", "e", "readTask", "f", "getElement", "H", "Host", "getTimeGivenProgression", "ION_CONTENT_CLASS_SELECTOR", "ION_CONTENT_ELEMENT_SELECTOR", "p", "printIonContentErrorMsg", "getScrollElement", "t", "transitionEndAsync", "componentOnReady", "l", "clamp", "getElementRoot", "raf", "isPlatform", "getIonMode", "config", "createAnimation", "E", "ENABLE_HTML_CONTENT_DEFAULT", "sanitizeDOMString", "caretBackSharp", "i", "arrowDown", "S", "SPINNERS", "getRefresherAnimationType", "contentEl", "previousSibling", "previousElementSibling", "<PERSON><PERSON><PERSON><PERSON>", "tagName", "createPullingAnimation", "pullingSpinner", "refresherEl", "createScaleAnimation", "createTranslateAnimation", "createBaseAnimation", "pullingRefresherIcon", "spinner", "querySelector", "circle", "shadowRoot", "spinnerArrowContainer", "arrowContainer", "arrow", "baseAnimation", "duration", "easing", "spinnerArrowContainerAnimation", "addElement", "keyframes", "offset", "opacity", "circleInnerAnimation", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "circleOuterAnimation", "transform", "arrowContainerAnimation", "arrowAnimation", "addAnimation", "height", "clientHeight", "spinnerAnimation", "createSnapBackAnimation", "fromTo", "setSpinnerOpacity", "setProperty", "toString", "handleScrollWhilePulling", "ticks", "numTicks", "pullAmount", "max", "for<PERSON>ach", "el", "min", "range", "start", "progression", "handleScrollWhileRefreshing", "lastVelocityY", "translateElement", "value", "Promise", "resolve", "trans", "removeProperty", "shouldUseNative<PERSON><PERSON><PERSON>er", "_ref", "_asyncToGenerator", "referenceEl", "mode", "refresher<PERSON>ontent", "refreshingSpinner", "webkitOverflowScrolling", "_x", "_x2", "apply", "arguments", "refresherIosCss", "refresherMdCss", "Refresher", "constructor", "hostRef", "ionRefresh", "ionPull", "ionStart", "appliedStyles", "didStart", "progress", "pointerDown", "needsCompletion", "did<PERSON><PERSON>resh", "animations", "nativeRefresher", "state", "pullMin", "pullMax", "closeDuration", "snapbackDuration", "pullFactor", "disabled", "disabled<PERSON><PERSON>ed", "gesture", "enable", "checkNativeRefresher", "_this", "useNativeRefresher", "closest", "setupNativeRefresher", "destroyNativeRefresher", "scrollEl", "scrollListenerCallback", "removeEventListener", "resetNativeRef<PERSON>er", "_this2", "ani", "destroy", "setupiOSNativeRefresher", "_this3", "elementToTransform", "querySelectorAll", "MAX_PULL", "NUM_TICKS", "length", "scrollTop", "refresherHeight", "ratio", "emit", "Math", "abs", "shouldShowRefreshingSpinner", "beginRefresh", "addEventListener", "createGesture", "<PERSON><PERSON><PERSON>", "gesturePriority", "direction", "threshold", "onStart", "onMove", "ev", "velocityY", "onEnd", "setupMDNativeRefresher", "_this4", "refreshingCircle", "canStart", "data", "animation", "cancelled", "overflowProperty", "matches", "animationType", "progressStart", "push", "deltaY", "progressStep", "progressEnd", "onFinish", "snapBackAnimation", "play", "_this5", "setCss", "componentDidUpdate", "connectedCallback", "_this6", "getAttribute", "console", "error", "customScrollTarget", "backgroundContentEl", "getBackgroundElement", "passive", "disconnectedCallback", "complete", "_this7", "close", "cancel", "_this8", "getProgress", "memoizeOverflowStyle", "detail", "event", "touches", "Number", "isNaN", "scrollHostScrollTop", "cancelable", "preventDefault", "restoreOverflowStyle", "bind", "delay", "setTimeout", "y", "overflowVisible", "shouldRestoreOverflowStyle", "scrollStyle", "backgroundStyle", "transitionDuration", "transitionDelay", "overflow", "overflowX", "overflowY", "overflowStyles", "render", "slot", "class", "watchers", "ios", "md", "Ref<PERSON><PERSON><PERSON><PERSON><PERSON>", "customHTMLEnabled", "get", "pullingIcon", "pullingText", "refreshingText", "componentWillLoad", "overflowRefresher", "renderPullingText", "innerHTML", "renderRefreshingText", "<PERSON><PERSON><PERSON><PERSON>", "name", "paused", "icon", "lazy", "ion_refresher", "ion_refresher_content"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0, 1, 2]}