{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-action-sheet_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC4H;AAC/C;AAC5B;AACyB;AAC8J;AACjL;AACM;AACE;AACjC;AACG;AACJ;AACA;AACa;AACA;AACE;AACf;;AAE7B;AACA;AACA;AACA,MAAMmC,iBAAiB,GAAIC,MAAM,IAAK;EACpC,MAAMC,aAAa,GAAGH,yDAAe,CAAC,CAAC;EACvC,MAAMI,iBAAiB,GAAGJ,yDAAe,CAAC,CAAC;EAC3C,MAAMK,gBAAgB,GAAGL,yDAAe,CAAC,CAAC;EAC1CI,iBAAiB,CACdE,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,cAAc,CAAC,CAAC,CAChDC,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,yBAAyB,CAAC,CAClDC,YAAY,CAAC;IACd,gBAAgB,EAAE;EACpB,CAAC,CAAC,CACCC,gBAAgB,CAAC,CAAC,gBAAgB,CAAC,CAAC;EACvCL,gBAAgB,CACbC,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,uBAAuB,CAAC,CAAC,CACzDC,MAAM,CAAC,WAAW,EAAE,kBAAkB,EAAE,gBAAgB,CAAC;EAC5D,OAAOL,aAAa,CACjBG,UAAU,CAACJ,MAAM,CAAC,CAClBS,MAAM,CAAC,6BAA6B,CAAC,CACrCC,QAAQ,CAAC,GAAG,CAAC,CACbC,YAAY,CAAC,CAACT,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AACxD,CAAC;;AAED;AACA;AACA;AACA,MAAMS,iBAAiB,GAAIZ,MAAM,IAAK;EACpC,MAAMC,aAAa,GAAGH,yDAAe,CAAC,CAAC;EACvC,MAAMI,iBAAiB,GAAGJ,yDAAe,CAAC,CAAC;EAC3C,MAAMK,gBAAgB,GAAGL,yDAAe,CAAC,CAAC;EAC1CI,iBAAiB,CAACE,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,cAAc,CAAC,CAAC,CAACC,MAAM,CAAC,SAAS,EAAE,yBAAyB,EAAE,CAAC,CAAC;EAClHH,gBAAgB,CACbC,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,uBAAuB,CAAC,CAAC,CACzDC,MAAM,CAAC,WAAW,EAAE,gBAAgB,EAAE,kBAAkB,CAAC;EAC5D,OAAOL,aAAa,CACjBG,UAAU,CAACJ,MAAM,CAAC,CAClBS,MAAM,CAAC,6BAA6B,CAAC,CACrCC,QAAQ,CAAC,GAAG,CAAC,CACbC,YAAY,CAAC,CAACT,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AACxD,CAAC;;AAED;AACA;AACA;AACA,MAAMU,gBAAgB,GAAIb,MAAM,IAAK;EACnC,MAAMC,aAAa,GAAGH,yDAAe,CAAC,CAAC;EACvC,MAAMI,iBAAiB,GAAGJ,yDAAe,CAAC,CAAC;EAC3C,MAAMK,gBAAgB,GAAGL,yDAAe,CAAC,CAAC;EAC1CI,iBAAiB,CACdE,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,cAAc,CAAC,CAAC,CAChDC,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,yBAAyB,CAAC,CAClDC,YAAY,CAAC;IACd,gBAAgB,EAAE;EACpB,CAAC,CAAC,CACCC,gBAAgB,CAAC,CAAC,gBAAgB,CAAC,CAAC;EACvCL,gBAAgB,CACbC,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,uBAAuB,CAAC,CAAC,CACzDC,MAAM,CAAC,WAAW,EAAE,kBAAkB,EAAE,gBAAgB,CAAC;EAC5D,OAAOL,aAAa,CACjBG,UAAU,CAACJ,MAAM,CAAC,CAClBS,MAAM,CAAC,6BAA6B,CAAC,CACrCC,QAAQ,CAAC,GAAG,CAAC,CACbC,YAAY,CAAC,CAACT,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AACxD,CAAC;;AAED;AACA;AACA;AACA,MAAMW,gBAAgB,GAAId,MAAM,IAAK;EACnC,MAAMC,aAAa,GAAGH,yDAAe,CAAC,CAAC;EACvC,MAAMI,iBAAiB,GAAGJ,yDAAe,CAAC,CAAC;EAC3C,MAAMK,gBAAgB,GAAGL,yDAAe,CAAC,CAAC;EAC1CI,iBAAiB,CAACE,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,cAAc,CAAC,CAAC,CAACC,MAAM,CAAC,SAAS,EAAE,yBAAyB,EAAE,CAAC,CAAC;EAClHH,gBAAgB,CACbC,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,uBAAuB,CAAC,CAAC,CACzDC,MAAM,CAAC,WAAW,EAAE,gBAAgB,EAAE,kBAAkB,CAAC;EAC5D,OAAOL,aAAa,CACjBG,UAAU,CAACJ,MAAM,CAAC,CAClBS,MAAM,CAAC,6BAA6B,CAAC,CACrCC,QAAQ,CAAC,GAAG,CAAC,CACbC,YAAY,CAAC,CAACT,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AACxD,CAAC;AAED,MAAMY,iBAAiB,GAAG,klRAAklR;AAE5mR,MAAMC,gBAAgB,GAAG,gvKAAgvK;AAEzwK,MAAMC,WAAW,GAAG,MAAM;EACxBC,WAAWA,CAACC,OAAO,EAAE;IACnBtD,qDAAgB,CAAC,IAAI,EAAEsD,OAAO,CAAC;IAC/B,IAAI,CAACC,UAAU,GAAGrD,qDAAW,CAAC,IAAI,EAAE,0BAA0B,EAAE,CAAC,CAAC;IAClE,IAAI,CAACsD,WAAW,GAAGtD,qDAAW,CAAC,IAAI,EAAE,2BAA2B,EAAE,CAAC,CAAC;IACpE,IAAI,CAACuD,WAAW,GAAGvD,qDAAW,CAAC,IAAI,EAAE,2BAA2B,EAAE,CAAC,CAAC;IACpE,IAAI,CAACwD,UAAU,GAAGxD,qDAAW,CAAC,IAAI,EAAE,0BAA0B,EAAE,CAAC,CAAC;IAClE,IAAI,CAACyD,mBAAmB,GAAGzD,qDAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IAC7D,IAAI,CAAC0D,oBAAoB,GAAG1D,qDAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IAC/D,IAAI,CAAC2D,oBAAoB,GAAG3D,qDAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IAC/D,IAAI,CAAC4D,mBAAmB,GAAG5D,qDAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IAC7D,IAAI,CAAC6D,kBAAkB,GAAGjD,wDAAwB,CAAC,IAAI,CAAC;IACxD,IAAI,CAACkD,cAAc,GAAGnD,+DAAoB,CAAC,CAAC;IAC5C,IAAI,CAACoD,iBAAiB,GAAGlD,wDAAuB,CAAC,CAAC;IAClD,IAAI,CAACmD,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,aAAa,GAAG,MAAM;MACzB,IAAI,CAAC7C,OAAO,CAAC8C,SAAS,EAAEnD,oDAAQ,CAAC;IACnC,CAAC;IACD,IAAI,CAACoD,qBAAqB,GAAIC,EAAE,IAAK;MACnC,MAAMC,IAAI,GAAGD,EAAE,CAACE,MAAM,CAACD,IAAI;MAC3B,IAAIpD,wDAAQ,CAACoD,IAAI,CAAC,EAAE;QAClB,MAAME,YAAY,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC,CAACC,IAAI,CAAE5C,CAAC,IAAKA,CAAC,CAACwC,IAAI,KAAK,QAAQ,CAAC;QACvE,IAAI,CAACK,iBAAiB,CAACH,YAAY,CAAC;MACtC;IACF,CAAC;IACD,IAAI,CAACI,YAAY,GAAGT,SAAS;IAC7B,IAAI,CAACU,QAAQ,GAAGV,SAAS;IACzB,IAAI,CAACW,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,cAAc,GAAGb,SAAS;IAC/B,IAAI,CAACc,cAAc,GAAGd,SAAS;IAC/B,IAAI,CAACe,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,QAAQ,GAAGhB,SAAS;IACzB,IAAI,CAACiB,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,MAAM,GAAGlB,SAAS;IACvB,IAAI,CAACmB,SAAS,GAAGnB,SAAS;IAC1B,IAAI,CAACoB,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,cAAc,GAAGtB,SAAS;IAC/B,IAAI,CAACuB,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,OAAO,GAAGxB,SAAS;EAC1B;EACAyB,cAAcA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;IACjC,IAAID,QAAQ,KAAK,IAAI,IAAIC,QAAQ,KAAK,KAAK,EAAE;MAC3C,IAAI,CAAC3E,OAAO,CAAC,CAAC;IAChB,CAAC,MACI,IAAI0E,QAAQ,KAAK,KAAK,IAAIC,QAAQ,KAAK,IAAI,EAAE;MAChD,IAAI,CAACzE,OAAO,CAAC,CAAC;IAChB;EACF;EACA0E,cAAcA,CAAA,EAAG;IACf,MAAM;MAAEJ,OAAO;MAAEK,EAAE;MAAEhC;IAAkB,CAAC,GAAG,IAAI;IAC/C,IAAI2B,OAAO,EAAE;MACX3B,iBAAiB,CAACiC,gBAAgB,CAACD,EAAE,EAAEL,OAAO,CAAC;IACjD;EACF;EACA;AACF;AACA;EACQxE,OAAOA,CAAA,EAAG;IAAA,IAAA+E,KAAA;IAAA,OAAAC,8KAAA;MACd,MAAMC,MAAM,SAASF,KAAI,CAACnC,cAAc,CAACsC,IAAI,CAAC,CAAC;MAC/C,MAAMH,KAAI,CAACpC,kBAAkB,CAACwC,eAAe,CAAC,CAAC;MAC/C,MAAMnF,wDAAO,CAAC+E,KAAI,EAAE,kBAAkB,EAAEjE,iBAAiB,EAAEc,gBAAgB,CAAC;MAC5EqD,MAAM,CAAC,CAAC;IAAC;EACX;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACQ/E,OAAOA,CAACkF,IAAI,EAAEjC,IAAI,EAAE;IAAA,IAAAkC,MAAA;IAAA,OAAAL,8KAAA;MACxB,MAAMC,MAAM,SAASI,MAAI,CAACzC,cAAc,CAACsC,IAAI,CAAC,CAAC;MAC/C,MAAMI,SAAS,SAASpF,wDAAO,CAACmF,MAAI,EAAED,IAAI,EAAEjC,IAAI,EAAE,kBAAkB,EAAExB,iBAAiB,EAAEE,gBAAgB,CAAC;MAC1G,IAAIyD,SAAS,EAAE;QACbD,MAAI,CAAC1C,kBAAkB,CAAC4C,iBAAiB,CAAC,CAAC;MAC7C;MACAN,MAAM,CAAC,CAAC;MACR,OAAOK,SAAS;IAAC;EACnB;EACA;AACF;AACA;EACEE,YAAYA,CAAA,EAAG;IACb,OAAOrF,wDAAW,CAAC,IAAI,CAAC0E,EAAE,EAAE,0BAA0B,CAAC;EACzD;EACA;AACF;AACA;AACA;EACEY,aAAaA,CAAA,EAAG;IACd,OAAOtF,wDAAW,CAAC,IAAI,CAAC0E,EAAE,EAAE,2BAA2B,CAAC;EAC1D;EACMa,WAAWA,CAACC,MAAM,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAZ,8KAAA;MACxB,MAAM7B,IAAI,GAAGwC,MAAM,CAACxC,IAAI;MACxB,IAAIpD,wDAAQ,CAACoD,IAAI,CAAC,EAAE;QAClB,OAAOyC,MAAI,CAAC1F,OAAO,CAACyF,MAAM,CAACP,IAAI,EAAEjC,IAAI,CAAC;MACxC;MACA,MAAM0C,aAAa,SAASD,MAAI,CAACpC,iBAAiB,CAACmC,MAAM,CAAC;MAC1D,IAAIE,aAAa,EAAE;QACjB,OAAOD,MAAI,CAAC1F,OAAO,CAACyF,MAAM,CAACP,IAAI,EAAEO,MAAM,CAACxC,IAAI,CAAC;MAC/C;MACA,OAAO2C,OAAO,CAACC,OAAO,CAAC,CAAC;IAAC;EAC3B;EACMvC,iBAAiBA,CAACmC,MAAM,EAAE;IAAA,OAAAX,8KAAA;MAC9B,IAAIW,MAAM,EAAE;QACV;QACA;QACA,MAAMK,GAAG,SAAS3F,wDAAQ,CAACsF,MAAM,CAACM,OAAO,CAAC;QAC1C,IAAID,GAAG,KAAK,KAAK,EAAE;UACjB;UACA,OAAO,KAAK;QACd;MACF;MACA,OAAO,IAAI;IAAC;EACd;EACA1C,UAAUA,CAAA,EAAG;IACX,OAAO,IAAI,CAACS,OAAO,CAACmC,GAAG,CAAEvF,CAAC,IAAK;MAC7B,OAAO,OAAOA,CAAC,KAAK,QAAQ,GAAG;QAAEwF,IAAI,EAAExF;MAAE,CAAC,GAAGA,CAAC;IAChD,CAAC,CAAC;EACJ;EACAyF,iBAAiBA,CAAA,EAAG;IAClB7F,wDAAc,CAAC,IAAI,CAACsE,EAAE,CAAC;IACvB,IAAI,CAACD,cAAc,CAAC,CAAC;EACvB;EACAyB,oBAAoBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACC,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACC,OAAO,CAAC,CAAC;MACtB,IAAI,CAACD,OAAO,GAAGtD,SAAS;IAC1B;IACA,IAAI,CAACH,iBAAiB,CAAC2D,mBAAmB,CAAC,CAAC;EAC9C;EACAC,iBAAiBA,CAAA,EAAG;IAClBhG,wDAAY,CAAC,IAAI,CAACoE,EAAE,CAAC;EACvB;EACA6B,gBAAgBA,CAAA,EAAG;IACjB;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,MAAM;MAAEC,OAAO;MAAEC;IAAU,CAAC,GAAG,IAAI;IACnC,IAAI,CAAC,IAAI,CAACN,OAAO,IAAI1F,4DAAU,CAAC,IAAI,CAAC,KAAK,KAAK,IAAIgG,SAAS,IAAID,OAAO,EAAE;MACvE3H,qDAAQ,CAAC,MAAM;QACb,MAAM6H,YAAY,GAAGF,OAAO,CAACG,YAAY,GAAGH,OAAO,CAACI,YAAY;QAChE,IAAI,CAACF,YAAY,EAAE;UACjB,IAAI,CAACP,OAAO,GAAG/G,6DAAyB,CAACqH,SAAS,EAAGI,KAAK,IAAKA,KAAK,CAACC,SAAS,CAACC,QAAQ,CAAC,qBAAqB,CAAC,CAAC;UAC/G,IAAI,CAACZ,OAAO,CAACa,MAAM,CAAC,IAAI,CAAC;QAC3B;MACF,CAAC,CAAC;IACJ;IACA;AACJ;AACA;AACA;IACI,IAAI,IAAI,CAAC5C,MAAM,KAAK,IAAI,EAAE;MACxB/E,uDAAG,CAAC,MAAM,IAAI,CAACQ,OAAO,CAAC,CAAC,CAAC;IAC3B;EACF;EACAoH,MAAMA,CAAA,EAAG;IACP,MAAM;MAAElD,MAAM;MAAEI,cAAc;MAAEb;IAAa,CAAC,GAAG,IAAI;IACrD,MAAM4D,IAAI,GAAGzG,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM0G,UAAU,GAAG,IAAI,CAAChE,UAAU,CAAC,CAAC;IACpC,MAAMD,YAAY,GAAGiE,UAAU,CAAC/D,IAAI,CAAE5C,CAAC,IAAKA,CAAC,CAACwC,IAAI,KAAK,QAAQ,CAAC;IAChE,MAAMY,OAAO,GAAGuD,UAAU,CAACC,MAAM,CAAE5G,CAAC,IAAKA,CAAC,CAACwC,IAAI,KAAK,QAAQ,CAAC;IAC7D,MAAMqE,QAAQ,GAAI,gBAAe/D,YAAa,SAAQ;IACtD,OAAQxE,qDAAC,CAACE,iDAAI,EAAEsI,MAAM,CAACC,MAAM,CAAC;MAAEvE,IAAI,EAAE,QAAQ;MAAE,YAAY,EAAE,MAAM;MAAE,iBAAiB,EAAEe,MAAM,KAAKlB,SAAS,GAAGwE,QAAQ,GAAG,IAAI;MAAEG,QAAQ,EAAE;IAAK,CAAC,EAAErD,cAAc,EAAE;MAAEsD,KAAK,EAAE;QACxKC,MAAM,EAAG,GAAE,KAAK,GAAG,IAAI,CAACpE,YAAa;MACvC,CAAC;MAAEqE,KAAK,EAAEL,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAE,CAACL,IAAI,GAAG;MAAK,CAAC,EAAE3G,qDAAW,CAAC,IAAI,CAACsD,QAAQ,CAAC,CAAC,EAAE;QAAE,gBAAgB,EAAE,IAAI;QAAE,0BAA0B,EAAE,IAAI,CAACI;MAAY,CAAC,CAAC;MAAE2D,2BAA2B,EAAE,IAAI,CAAC9E,qBAAqB;MAAE+E,gBAAgB,EAAE,IAAI,CAACjF;IAAc,CAAC,CAAC,EAAE9D,qDAAC,CAAC,cAAc,EAAE;MAAEgJ,QAAQ,EAAE,IAAI,CAAChE;IAAgB,CAAC,CAAC,EAAEhF,qDAAC,CAAC,KAAK,EAAE;MAAE0I,QAAQ,EAAE;IAAI,CAAC,CAAC,EAAE1I,qDAAC,CAAC,KAAK,EAAE;MAAE6I,KAAK,EAAE,0CAA0C;MAAEI,GAAG,EAAGrD,EAAE,IAAM,IAAI,CAAC+B,SAAS,GAAG/B;IAAI,CAAC,EAAE5F,qDAAC,CAAC,KAAK,EAAE;MAAE6I,KAAK,EAAE;IAAyB,CAAC,EAAE7I,qDAAC,CAAC,KAAK,EAAE;MAAE6I,KAAK,EAAE,oBAAoB;MAAEI,GAAG,EAAGrD,EAAE,IAAM,IAAI,CAAC8B,OAAO,GAAG9B;IAAI,CAAC,EAAEX,MAAM,KAAKlB,SAAS,IAAK/D,qDAAC,CAAC,KAAK,EAAE;MAAEkJ,EAAE,EAAEX,QAAQ;MAAEM,KAAK,EAAE;QAC3mB,oBAAoB,EAAE,IAAI;QAC1B,4BAA4B,EAAE,IAAI,CAAC3D,SAAS,KAAKnB;MACnD;IAAE,CAAC,EAAEkB,MAAM,EAAE,IAAI,CAACC,SAAS,IAAIlF,qDAAC,CAAC,KAAK,EAAE;MAAE6I,KAAK,EAAE;IAAyB,CAAC,EAAE,IAAI,CAAC3D,SAAS,CAAC,CAAE,EAAEJ,OAAO,CAACmC,GAAG,CAAEvF,CAAC,IAAM1B,qDAAC,CAAC,QAAQ,EAAEwI,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE/G,CAAC,CAAC2D,cAAc,EAAE;MAAE8D,IAAI,EAAE,QAAQ;MAAED,EAAE,EAAExH,CAAC,CAACwH,EAAE;MAAEL,KAAK,EAAEO,WAAW,CAAC1H,CAAC,CAAC;MAAE2H,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC5C,WAAW,CAAC/E,CAAC;IAAE,CAAC,CAAC,EAAE1B,qDAAC,CAAC,MAAM,EAAE;MAAE6I,KAAK,EAAE;IAA4B,CAAC,EAAEnH,CAAC,CAAC4H,IAAI,IAAItJ,qDAAC,CAAC,UAAU,EAAE;MAAEsJ,IAAI,EAAE5H,CAAC,CAAC4H,IAAI;MAAE,aAAa,EAAE,MAAM;MAAEC,IAAI,EAAE,KAAK;MAAEV,KAAK,EAAE;IAAoB,CAAC,CAAC,EAAEnH,CAAC,CAACwF,IAAI,CAAC,EAAEkB,IAAI,KAAK,IAAI,IAAIpI,qDAAC,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAE,CAAC,CAAC,EAAEoE,YAAY,IAAKpE,qDAAC,CAAC,KAAK,EAAE;MAAE6I,KAAK,EAAE;IAA+C,CAAC,EAAE7I,qDAAC,CAAC,QAAQ,EAAEwI,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAErE,YAAY,CAACiB,cAAc,EAAE;MAAE8D,IAAI,EAAE,QAAQ;MAAEN,KAAK,EAAEO,WAAW,CAAChF,YAAY,CAAC;MAAEiF,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC5C,WAAW,CAACrC,YAAY;IAAE,CAAC,CAAC,EAAEpE,qDAAC,CAAC,MAAM,EAAE;MAAE6I,KAAK,EAAE;IAA4B,CAAC,EAAEzE,YAAY,CAACkF,IAAI,IAAKtJ,qDAAC,CAAC,UAAU,EAAE;MAAEsJ,IAAI,EAAElF,YAAY,CAACkF,IAAI;MAAE,aAAa,EAAE,MAAM;MAAEC,IAAI,EAAE,KAAK;MAAEV,KAAK,EAAE;IAAoB,CAAC,CAAE,EAAEzE,YAAY,CAAC8C,IAAI,CAAC,EAAEkB,IAAI,KAAK,IAAI,IAAIpI,qDAAC,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC,CAAE,CAAC,CAAC,EAAEA,qDAAC,CAAC,KAAK,EAAE;MAAE0I,QAAQ,EAAE;IAAI,CAAC,CAAC,CAAC;EAC1+B;EACA,IAAI9C,EAAEA,CAAA,EAAG;IAAE,OAAOxF,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWoJ,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC7B,QAAQ,EAAE,CAAC,gBAAgB,CAAC;MAC5B,SAAS,EAAE,CAAC,gBAAgB;IAC9B,CAAC;EAAE;AACL,CAAC;AACD,MAAMJ,WAAW,GAAI1C,MAAM,IAAK;EAC9B,OAAO8B,MAAM,CAACC,MAAM,CAAC;IAAE,qBAAqB,EAAE,IAAI;IAAE,iBAAiB,EAAE,IAAI;IAAE,eAAe,EAAE,IAAI;IAAE,CAAE,gBAAe/B,MAAM,CAACxC,IAAK,EAAC,GAAGwC,MAAM,CAACxC,IAAI,KAAKH;EAAU,CAAC,EAAEtC,qDAAW,CAACiF,MAAM,CAAC3B,QAAQ,CAAC,CAAC;AACjM,CAAC;AACDhC,WAAW,CAAC4F,KAAK,GAAG;EAClBc,GAAG,EAAE5G,iBAAiB;EACtB6G,EAAE,EAAE5G;AACN,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-action-sheet.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, e as readTask, h, H as Host, f as getElement } from './index-2d388930.js';\nimport { c as createButtonActiveGesture } from './button-active-513e9fff.js';\nimport { r as raf } from './helpers-3379ba19.js';\nimport { c as createLockController } from './lock-controller-e8c6c051.js';\nimport { d as createDelegateController, e as createTriggerController, B as BACKDROP, i as isCancel, f as present, g as dismiss, h as eventMethod, s as safeCall, j as prepareOverlay, k as setOverlayId } from './overlays-8fc6656c.js';\nimport { g as getClassMap } from './theme-17531cdf.js';\nimport { b as getIonMode } from './ionic-global-b3fc28dd.js';\nimport { c as createAnimation } from './animation-a1d9e088.js';\nimport './haptic-6447af60.js';\nimport './capacitor-b4979570.js';\nimport './index-7a14ecec.js';\nimport './index-ff313b19.js';\nimport './gesture-controller-0fa396c4.js';\nimport './framework-delegate-aa433dea.js';\nimport './hardware-back-button-39299f84.js';\nimport './index-595d62c9.js';\n\n/**\n * iOS Action Sheet Enter Animation\n */\nconst iosEnterAnimation = (baseEl) => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation\n    .addElement(baseEl.querySelector('ion-backdrop'))\n    .fromTo('opacity', 0.01, 'var(--backdrop-opacity)')\n    .beforeStyles({\n    'pointer-events': 'none',\n  })\n    .afterClearStyles(['pointer-events']);\n  wrapperAnimation\n    .addElement(baseEl.querySelector('.action-sheet-wrapper'))\n    .fromTo('transform', 'translateY(100%)', 'translateY(0%)');\n  return baseAnimation\n    .addElement(baseEl)\n    .easing('cubic-bezier(.36,.66,.04,1)')\n    .duration(400)\n    .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * iOS Action Sheet Leave Animation\n */\nconst iosLeaveAnimation = (baseEl) => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n  wrapperAnimation\n    .addElement(baseEl.querySelector('.action-sheet-wrapper'))\n    .fromTo('transform', 'translateY(0%)', 'translateY(100%)');\n  return baseAnimation\n    .addElement(baseEl)\n    .easing('cubic-bezier(.36,.66,.04,1)')\n    .duration(450)\n    .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * MD Action Sheet Enter Animation\n */\nconst mdEnterAnimation = (baseEl) => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation\n    .addElement(baseEl.querySelector('ion-backdrop'))\n    .fromTo('opacity', 0.01, 'var(--backdrop-opacity)')\n    .beforeStyles({\n    'pointer-events': 'none',\n  })\n    .afterClearStyles(['pointer-events']);\n  wrapperAnimation\n    .addElement(baseEl.querySelector('.action-sheet-wrapper'))\n    .fromTo('transform', 'translateY(100%)', 'translateY(0%)');\n  return baseAnimation\n    .addElement(baseEl)\n    .easing('cubic-bezier(.36,.66,.04,1)')\n    .duration(400)\n    .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * MD Action Sheet Leave Animation\n */\nconst mdLeaveAnimation = (baseEl) => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n  wrapperAnimation\n    .addElement(baseEl.querySelector('.action-sheet-wrapper'))\n    .fromTo('transform', 'translateY(0%)', 'translateY(100%)');\n  return baseAnimation\n    .addElement(baseEl)\n    .easing('cubic-bezier(.36,.66,.04,1)')\n    .duration(450)\n    .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\nconst actionSheetIosCss = \".sc-ion-action-sheet-ios-h{--color:initial;--button-color-activated:var(--button-color);--button-color-focused:var(--button-color);--button-color-hover:var(--button-color);--button-color-selected:var(--button-color);--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--height:auto;--max-height:calc(100% - (var(--ion-safe-area-top) + var(--ion-safe-area-bottom)));-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:block;position:fixed;outline:none;font-family:var(--ion-font-family, inherit);-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-action-sheet-ios-h{display:none}.action-sheet-wrapper.sc-ion-action-sheet-ios{left:0;right:0;bottom:0;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:block;position:absolute;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);z-index:10;pointer-events:none}.action-sheet-button.sc-ion-action-sheet-ios{display:block;position:relative;width:100%;border:0;outline:none;background:var(--button-background);color:var(--button-color);font-family:inherit;overflow:hidden}.action-sheet-button-inner.sc-ion-action-sheet-ios{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;pointer-events:none;width:100%;height:100%;z-index:1}.action-sheet-container.sc-ion-action-sheet-ios{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;-ms-flex-pack:end;justify-content:flex-end;height:100%;max-height:100vh}.action-sheet-group.sc-ion-action-sheet-ios{-ms-flex-negative:2;flex-shrink:2;overscroll-behavior-y:contain;overflow-y:auto;-webkit-overflow-scrolling:touch;pointer-events:all;background:var(--background)}.action-sheet-group.sc-ion-action-sheet-ios::-webkit-scrollbar{display:none}.action-sheet-group-cancel.sc-ion-action-sheet-ios{-ms-flex-negative:0;flex-shrink:0;overflow:hidden}.action-sheet-button.sc-ion-action-sheet-ios::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}.action-sheet-selected.sc-ion-action-sheet-ios{color:var(--button-color-selected)}.action-sheet-selected.sc-ion-action-sheet-ios::after{background:var(--button-background-selected);opacity:var(--button-background-selected-opacity)}.action-sheet-button.ion-activated.sc-ion-action-sheet-ios{color:var(--button-color-activated)}.action-sheet-button.ion-activated.sc-ion-action-sheet-ios::after{background:var(--button-background-activated);opacity:var(--button-background-activated-opacity)}.action-sheet-button.ion-focused.sc-ion-action-sheet-ios{color:var(--button-color-focused)}.action-sheet-button.ion-focused.sc-ion-action-sheet-ios::after{background:var(--button-background-focused);opacity:var(--button-background-focused-opacity)}@media (any-hover: hover){.action-sheet-button.sc-ion-action-sheet-ios:hover{color:var(--button-color-hover)}.action-sheet-button.sc-ion-action-sheet-ios:hover::after{background:var(--button-background-hover);opacity:var(--button-background-hover-opacity)}}.sc-ion-action-sheet-ios-h{--background:var(--ion-overlay-background-color, var(--ion-color-step-100, #f9f9f9));--backdrop-opacity:var(--ion-backdrop-opacity, 0.4);--button-background:linear-gradient(0deg, rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08), rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08) 50%, transparent 50%) bottom/100% 1px no-repeat transparent;--button-background-activated:var(--ion-text-color, #000);--button-background-activated-opacity:.08;--button-background-hover:currentColor;--button-background-hover-opacity:.04;--button-background-focused:currentColor;--button-background-focused-opacity:.12;--button-background-selected:var(--ion-color-step-150, var(--ion-background-color, #fff));--button-background-selected-opacity:1;--button-color:var(--ion-color-primary, #3880ff);--color:var(--ion-color-step-400, #999999);text-align:center}.action-sheet-wrapper.sc-ion-action-sheet-ios{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:var(--ion-safe-area-top, 0);margin-bottom:var(--ion-safe-area-bottom, 0)}.action-sheet-container.sc-ion-action-sheet-ios{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0}.action-sheet-group.sc-ion-action-sheet-ios{border-radius:13px;margin-bottom:8px}.action-sheet-group.sc-ion-action-sheet-ios:first-child{margin-top:10px}.action-sheet-group.sc-ion-action-sheet-ios:last-child{margin-bottom:10px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.action-sheet-translucent.sc-ion-action-sheet-ios-h .action-sheet-group.sc-ion-action-sheet-ios{background-color:transparent;-webkit-backdrop-filter:saturate(280%) blur(20px);backdrop-filter:saturate(280%) blur(20px)}.action-sheet-translucent.sc-ion-action-sheet-ios-h .action-sheet-title.sc-ion-action-sheet-ios,.action-sheet-translucent.sc-ion-action-sheet-ios-h .action-sheet-button.sc-ion-action-sheet-ios{background-color:transparent;background-image:-webkit-gradient(linear, left bottom, left top, from(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)), to(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8))), -webkit-gradient(linear, left bottom, left top, from(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.4)), color-stop(50%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.4)), color-stop(50%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)));background-image:linear-gradient(0deg, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8), rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 100%), linear-gradient(0deg, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.4), rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.4) 50%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 50%);background-repeat:no-repeat;background-position:top, bottom;background-size:100% calc(100% - 1px), 100% 1px;-webkit-backdrop-filter:saturate(120%);backdrop-filter:saturate(120%)}.action-sheet-translucent.sc-ion-action-sheet-ios-h .action-sheet-button.ion-activated.sc-ion-action-sheet-ios{background-color:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.7);background-image:none}.action-sheet-translucent.sc-ion-action-sheet-ios-h .action-sheet-cancel.sc-ion-action-sheet-ios{background:var(--button-background-selected)}}.action-sheet-title.sc-ion-action-sheet-ios{background:-webkit-gradient(linear, left bottom, left top, from(rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08)), color-stop(50%, rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08)), color-stop(50%, transparent)) bottom/100% 1px no-repeat transparent;background:linear-gradient(0deg, rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08), rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08) 50%, transparent 50%) bottom/100% 1px no-repeat transparent}.action-sheet-title.sc-ion-action-sheet-ios{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:14px;padding-bottom:13px;color:var(--color, var(--ion-color-step-400, #999999));font-size:13px;font-weight:400;text-align:center}.action-sheet-title.action-sheet-has-sub-title.sc-ion-action-sheet-ios{font-weight:600}.action-sheet-sub-title.sc-ion-action-sheet-ios{padding-left:0;padding-right:0;padding-top:6px;padding-bottom:0;font-size:13px;font-weight:400}.action-sheet-button.sc-ion-action-sheet-ios{-webkit-padding-start:18px;padding-inline-start:18px;-webkit-padding-end:18px;padding-inline-end:18px;padding-top:18px;padding-bottom:18px;height:56px;font-size:20px;contain:strict}.action-sheet-button.sc-ion-action-sheet-ios .action-sheet-icon.sc-ion-action-sheet-ios{-webkit-margin-end:0.3em;margin-inline-end:0.3em;font-size:28px;pointer-events:none}.action-sheet-button.sc-ion-action-sheet-ios:last-child{background-image:none}.action-sheet-selected.sc-ion-action-sheet-ios{font-weight:bold}.action-sheet-cancel.sc-ion-action-sheet-ios{font-weight:600}.action-sheet-cancel.sc-ion-action-sheet-ios::after{background:var(--button-background-selected);opacity:var(--button-background-selected-opacity)}.action-sheet-destructive.sc-ion-action-sheet-ios,.action-sheet-destructive.ion-activated.sc-ion-action-sheet-ios,.action-sheet-destructive.ion-focused.sc-ion-action-sheet-ios{color:var(--ion-color-danger, #eb445a)}@media (any-hover: hover){.action-sheet-destructive.sc-ion-action-sheet-ios:hover{color:var(--ion-color-danger, #eb445a)}}\";\n\nconst actionSheetMdCss = \".sc-ion-action-sheet-md-h{--color:initial;--button-color-activated:var(--button-color);--button-color-focused:var(--button-color);--button-color-hover:var(--button-color);--button-color-selected:var(--button-color);--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--height:auto;--max-height:calc(100% - (var(--ion-safe-area-top) + var(--ion-safe-area-bottom)));-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:block;position:fixed;outline:none;font-family:var(--ion-font-family, inherit);-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-action-sheet-md-h{display:none}.action-sheet-wrapper.sc-ion-action-sheet-md{left:0;right:0;bottom:0;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:block;position:absolute;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);z-index:10;pointer-events:none}.action-sheet-button.sc-ion-action-sheet-md{display:block;position:relative;width:100%;border:0;outline:none;background:var(--button-background);color:var(--button-color);font-family:inherit;overflow:hidden}.action-sheet-button-inner.sc-ion-action-sheet-md{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;pointer-events:none;width:100%;height:100%;z-index:1}.action-sheet-container.sc-ion-action-sheet-md{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;-ms-flex-pack:end;justify-content:flex-end;height:100%;max-height:100vh}.action-sheet-group.sc-ion-action-sheet-md{-ms-flex-negative:2;flex-shrink:2;overscroll-behavior-y:contain;overflow-y:auto;-webkit-overflow-scrolling:touch;pointer-events:all;background:var(--background)}.action-sheet-group.sc-ion-action-sheet-md::-webkit-scrollbar{display:none}.action-sheet-group-cancel.sc-ion-action-sheet-md{-ms-flex-negative:0;flex-shrink:0;overflow:hidden}.action-sheet-button.sc-ion-action-sheet-md::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}.action-sheet-selected.sc-ion-action-sheet-md{color:var(--button-color-selected)}.action-sheet-selected.sc-ion-action-sheet-md::after{background:var(--button-background-selected);opacity:var(--button-background-selected-opacity)}.action-sheet-button.ion-activated.sc-ion-action-sheet-md{color:var(--button-color-activated)}.action-sheet-button.ion-activated.sc-ion-action-sheet-md::after{background:var(--button-background-activated);opacity:var(--button-background-activated-opacity)}.action-sheet-button.ion-focused.sc-ion-action-sheet-md{color:var(--button-color-focused)}.action-sheet-button.ion-focused.sc-ion-action-sheet-md::after{background:var(--button-background-focused);opacity:var(--button-background-focused-opacity)}@media (any-hover: hover){.action-sheet-button.sc-ion-action-sheet-md:hover{color:var(--button-color-hover)}.action-sheet-button.sc-ion-action-sheet-md:hover::after{background:var(--button-background-hover);opacity:var(--button-background-hover-opacity)}}.sc-ion-action-sheet-md-h{--background:var(--ion-overlay-background-color, var(--ion-background-color, #fff));--backdrop-opacity:var(--ion-backdrop-opacity, 0.32);--button-background:transparent;--button-background-selected:currentColor;--button-background-selected-opacity:0;--button-background-activated:transparent;--button-background-activated-opacity:0;--button-background-hover:currentColor;--button-background-hover-opacity:.04;--button-background-focused:currentColor;--button-background-focused-opacity:.12;--button-color:var(--ion-color-step-850, #262626);--color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.54)}.action-sheet-wrapper.sc-ion-action-sheet-md{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:var(--ion-safe-area-top, 0);margin-bottom:0}.action-sheet-title.sc-ion-action-sheet-md{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:20px;padding-bottom:17px;min-height:60px;color:var(--color, rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.54));font-size:16px;text-align:start}.action-sheet-sub-title.sc-ion-action-sheet-md{padding-left:0;padding-right:0;padding-top:16px;padding-bottom:0;font-size:14px}.action-sheet-group.sc-ion-action-sheet-md:first-child{padding-top:0}.action-sheet-group.sc-ion-action-sheet-md:last-child{padding-bottom:var(--ion-safe-area-bottom)}.action-sheet-button.sc-ion-action-sheet-md{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0;padding-bottom:0;position:relative;height:52px;font-size:16px;text-align:start;contain:strict;overflow:hidden}.action-sheet-icon.sc-ion-action-sheet-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:32px;margin-inline-end:32px;margin-top:0;margin-bottom:0;color:var(--color);font-size:24px}.action-sheet-button-inner.sc-ion-action-sheet-md{-ms-flex-pack:start;justify-content:flex-start}.action-sheet-selected.sc-ion-action-sheet-md{font-weight:bold}\";\n\nconst ActionSheet = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.didPresent = createEvent(this, \"ionActionSheetDidPresent\", 7);\n    this.willPresent = createEvent(this, \"ionActionSheetWillPresent\", 7);\n    this.willDismiss = createEvent(this, \"ionActionSheetWillDismiss\", 7);\n    this.didDismiss = createEvent(this, \"ionActionSheetDidDismiss\", 7);\n    this.didPresentShorthand = createEvent(this, \"didPresent\", 7);\n    this.willPresentShorthand = createEvent(this, \"willPresent\", 7);\n    this.willDismissShorthand = createEvent(this, \"willDismiss\", 7);\n    this.didDismissShorthand = createEvent(this, \"didDismiss\", 7);\n    this.delegateController = createDelegateController(this);\n    this.lockController = createLockController();\n    this.triggerController = createTriggerController();\n    this.presented = false;\n    this.onBackdropTap = () => {\n      this.dismiss(undefined, BACKDROP);\n    };\n    this.dispatchCancelHandler = (ev) => {\n      const role = ev.detail.role;\n      if (isCancel(role)) {\n        const cancelButton = this.getButtons().find((b) => b.role === 'cancel');\n        this.callButtonHandler(cancelButton);\n      }\n    };\n    this.overlayIndex = undefined;\n    this.delegate = undefined;\n    this.hasController = false;\n    this.keyboardClose = true;\n    this.enterAnimation = undefined;\n    this.leaveAnimation = undefined;\n    this.buttons = [];\n    this.cssClass = undefined;\n    this.backdropDismiss = true;\n    this.header = undefined;\n    this.subHeader = undefined;\n    this.translucent = false;\n    this.animated = true;\n    this.htmlAttributes = undefined;\n    this.isOpen = false;\n    this.trigger = undefined;\n  }\n  onIsOpenChange(newValue, oldValue) {\n    if (newValue === true && oldValue === false) {\n      this.present();\n    }\n    else if (newValue === false && oldValue === true) {\n      this.dismiss();\n    }\n  }\n  triggerChanged() {\n    const { trigger, el, triggerController } = this;\n    if (trigger) {\n      triggerController.addClickListener(el, trigger);\n    }\n  }\n  /**\n   * Present the action sheet overlay after it has been created.\n   */\n  async present() {\n    const unlock = await this.lockController.lock();\n    await this.delegateController.attachViewToDom();\n    await present(this, 'actionSheetEnter', iosEnterAnimation, mdEnterAnimation);\n    unlock();\n  }\n  /**\n   * Dismiss the action sheet overlay after it has been presented.\n   *\n   * @param data Any data to emit in the dismiss events.\n   * @param role The role of the element that is dismissing the action sheet.\n   * This can be useful in a button handler for determining which button was\n   * clicked to dismiss the action sheet.\n   * Some examples include: ``\"cancel\"`, `\"destructive\"`, \"selected\"`, and `\"backdrop\"`.\n   */\n  async dismiss(data, role) {\n    const unlock = await this.lockController.lock();\n    const dismissed = await dismiss(this, data, role, 'actionSheetLeave', iosLeaveAnimation, mdLeaveAnimation);\n    if (dismissed) {\n      this.delegateController.removeViewFromDom();\n    }\n    unlock();\n    return dismissed;\n  }\n  /**\n   * Returns a promise that resolves when the action sheet did dismiss.\n   */\n  onDidDismiss() {\n    return eventMethod(this.el, 'ionActionSheetDidDismiss');\n  }\n  /**\n   * Returns a promise that resolves when the action sheet will dismiss.\n   *\n   */\n  onWillDismiss() {\n    return eventMethod(this.el, 'ionActionSheetWillDismiss');\n  }\n  async buttonClick(button) {\n    const role = button.role;\n    if (isCancel(role)) {\n      return this.dismiss(button.data, role);\n    }\n    const shouldDismiss = await this.callButtonHandler(button);\n    if (shouldDismiss) {\n      return this.dismiss(button.data, button.role);\n    }\n    return Promise.resolve();\n  }\n  async callButtonHandler(button) {\n    if (button) {\n      // a handler has been provided, execute it\n      // pass the handler the values from the inputs\n      const rtn = await safeCall(button.handler);\n      if (rtn === false) {\n        // if the return value of the handler is false then do not dismiss\n        return false;\n      }\n    }\n    return true;\n  }\n  getButtons() {\n    return this.buttons.map((b) => {\n      return typeof b === 'string' ? { text: b } : b;\n    });\n  }\n  connectedCallback() {\n    prepareOverlay(this.el);\n    this.triggerChanged();\n  }\n  disconnectedCallback() {\n    if (this.gesture) {\n      this.gesture.destroy();\n      this.gesture = undefined;\n    }\n    this.triggerController.removeClickListener();\n  }\n  componentWillLoad() {\n    setOverlayId(this.el);\n  }\n  componentDidLoad() {\n    /**\n     * Only create gesture if:\n     * 1. A gesture does not already exist\n     * 2. App is running in iOS mode\n     * 3. A wrapper ref exists\n     * 4. A group ref exists\n     */\n    const { groupEl, wrapperEl } = this;\n    if (!this.gesture && getIonMode(this) === 'ios' && wrapperEl && groupEl) {\n      readTask(() => {\n        const isScrollable = groupEl.scrollHeight > groupEl.clientHeight;\n        if (!isScrollable) {\n          this.gesture = createButtonActiveGesture(wrapperEl, (refEl) => refEl.classList.contains('action-sheet-button'));\n          this.gesture.enable(true);\n        }\n      });\n    }\n    /**\n     * If action sheet was rendered with isOpen=\"true\"\n     * then we should open action sheet immediately.\n     */\n    if (this.isOpen === true) {\n      raf(() => this.present());\n    }\n  }\n  render() {\n    const { header, htmlAttributes, overlayIndex } = this;\n    const mode = getIonMode(this);\n    const allButtons = this.getButtons();\n    const cancelButton = allButtons.find((b) => b.role === 'cancel');\n    const buttons = allButtons.filter((b) => b.role !== 'cancel');\n    const headerID = `action-sheet-${overlayIndex}-header`;\n    return (h(Host, Object.assign({ role: \"dialog\", \"aria-modal\": \"true\", \"aria-labelledby\": header !== undefined ? headerID : null, tabindex: \"-1\" }, htmlAttributes, { style: {\n        zIndex: `${20000 + this.overlayIndex}`,\n      }, class: Object.assign(Object.assign({ [mode]: true }, getClassMap(this.cssClass)), { 'overlay-hidden': true, 'action-sheet-translucent': this.translucent }), onIonActionSheetWillDismiss: this.dispatchCancelHandler, onIonBackdropTap: this.onBackdropTap }), h(\"ion-backdrop\", { tappable: this.backdropDismiss }), h(\"div\", { tabindex: \"0\" }), h(\"div\", { class: \"action-sheet-wrapper ion-overlay-wrapper\", ref: (el) => (this.wrapperEl = el) }, h(\"div\", { class: \"action-sheet-container\" }, h(\"div\", { class: \"action-sheet-group\", ref: (el) => (this.groupEl = el) }, header !== undefined && (h(\"div\", { id: headerID, class: {\n        'action-sheet-title': true,\n        'action-sheet-has-sub-title': this.subHeader !== undefined,\n      } }, header, this.subHeader && h(\"div\", { class: \"action-sheet-sub-title\" }, this.subHeader))), buttons.map((b) => (h(\"button\", Object.assign({}, b.htmlAttributes, { type: \"button\", id: b.id, class: buttonClass(b), onClick: () => this.buttonClick(b) }), h(\"span\", { class: \"action-sheet-button-inner\" }, b.icon && h(\"ion-icon\", { icon: b.icon, \"aria-hidden\": \"true\", lazy: false, class: \"action-sheet-icon\" }), b.text), mode === 'md' && h(\"ion-ripple-effect\", null))))), cancelButton && (h(\"div\", { class: \"action-sheet-group action-sheet-group-cancel\" }, h(\"button\", Object.assign({}, cancelButton.htmlAttributes, { type: \"button\", class: buttonClass(cancelButton), onClick: () => this.buttonClick(cancelButton) }), h(\"span\", { class: \"action-sheet-button-inner\" }, cancelButton.icon && (h(\"ion-icon\", { icon: cancelButton.icon, \"aria-hidden\": \"true\", lazy: false, class: \"action-sheet-icon\" })), cancelButton.text), mode === 'md' && h(\"ion-ripple-effect\", null)))))), h(\"div\", { tabindex: \"0\" })));\n  }\n  get el() { return getElement(this); }\n  static get watchers() { return {\n    \"isOpen\": [\"onIsOpenChange\"],\n    \"trigger\": [\"triggerChanged\"]\n  }; }\n};\nconst buttonClass = (button) => {\n  return Object.assign({ 'action-sheet-button': true, 'ion-activatable': true, 'ion-focusable': true, [`action-sheet-${button.role}`]: button.role !== undefined }, getClassMap(button.cssClass));\n};\nActionSheet.style = {\n  ios: actionSheetIosCss,\n  md: actionSheetMdCss\n};\n\nexport { ActionSheet as ion_action_sheet };\n"], "names": ["r", "registerInstance", "d", "createEvent", "e", "readTask", "h", "H", "Host", "f", "getElement", "c", "createButtonActiveGesture", "raf", "createLockController", "createDelegateController", "createTriggerController", "B", "BACKDROP", "i", "isCancel", "present", "g", "dismiss", "eventMethod", "s", "safeCall", "j", "prepareOverlay", "k", "setOverlayId", "getClassMap", "b", "getIonMode", "createAnimation", "iosEnterAnimation", "baseEl", "baseAnimation", "backdropAnimation", "wrapperAnimation", "addElement", "querySelector", "fromTo", "beforeStyles", "afterClearStyles", "easing", "duration", "addAnimation", "iosLeaveAnimation", "mdEnterAnimation", "mdLeaveAnimation", "actionSheetIosCss", "actionSheetMdCss", "ActionSheet", "constructor", "hostRef", "didPresent", "willPresent", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "didPresentShorthand", "willPresentShorthand", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "delegate<PERSON><PERSON>roller", "lockController", "triggerController", "presented", "onBackdropTap", "undefined", "dispatchCancelHandler", "ev", "role", "detail", "cancelButton", "getButtons", "find", "callButtonHandler", "overlayIndex", "delegate", "hasController", "keyboardClose", "enterAnimation", "leaveAnimation", "buttons", "cssClass", "<PERSON><PERSON><PERSON><PERSON>", "header", "subHeader", "translucent", "animated", "htmlAttributes", "isOpen", "trigger", "onIsOpenChange", "newValue", "oldValue", "triggerChanged", "el", "addClickListener", "_this", "_asyncToGenerator", "unlock", "lock", "attachViewToDom", "data", "_this2", "dismissed", "removeViewFromDom", "onDid<PERSON><PERSON><PERSON>", "on<PERSON>ill<PERSON><PERSON>iss", "buttonClick", "button", "_this3", "<PERSON><PERSON><PERSON><PERSON>", "Promise", "resolve", "rtn", "handler", "map", "text", "connectedCallback", "disconnectedCallback", "gesture", "destroy", "removeClickListener", "componentWillLoad", "componentDidLoad", "groupEl", "wrapperEl", "isScrollable", "scrollHeight", "clientHeight", "refEl", "classList", "contains", "enable", "render", "mode", "allButtons", "filter", "headerID", "Object", "assign", "tabindex", "style", "zIndex", "class", "onIonActionSheetWillDismiss", "onIonBackdropTap", "tappable", "ref", "id", "type", "buttonClass", "onClick", "icon", "lazy", "watchers", "ios", "md", "ion_action_sheet"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}