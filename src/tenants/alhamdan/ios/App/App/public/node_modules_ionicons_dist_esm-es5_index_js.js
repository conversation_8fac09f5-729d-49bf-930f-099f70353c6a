"use strict";
(self["webpackChunkapp"] = self["webpackChunkapp"] || []).push([["node_modules_ionicons_dist_esm-es5_index_js"],{

/***/ 514:
/*!*****************************************************!*\
  !*** ./node_modules/ionicons/dist/esm-es5/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   addIcons: () => (/* reexport safe */ _utils_ccb924b9_js__WEBPACK_IMPORTED_MODULE_0__.a)
/* harmony export */ });
/* harmony import */ var _utils_ccb924b9_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils-ccb924b9.js */ 78709);



/***/ })

}]);
//# sourceMappingURL=node_modules_ionicons_dist_esm-es5_index_js.js.map