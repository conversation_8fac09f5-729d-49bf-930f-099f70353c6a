"use strict";
(self["webpackChunkapp"] = self["webpackChunkapp"] || []).push([["node_modules_ionicons_dist_esm-es5_loader_js"],{

/***/ 10311:
/*!******************************************************!*\
  !*** ./node_modules/ionicons/dist/esm-es5/loader.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   defineCustomElements: () => (/* binding */ defineCustomElements)
/* harmony export */ });
/* harmony import */ var _index_5514a13d_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index-5514a13d.js */ 58160);

var patchEsm = function () {
  return (0,_index_5514a13d_js__WEBPACK_IMPORTED_MODULE_0__.p)();
};
var defineCustomElements = function (e, o) {
  if (typeof window === "undefined") return Promise.resolve();
  return patchEsm().then(function () {
    return (0,_index_5514a13d_js__WEBPACK_IMPORTED_MODULE_0__.b)([["ion-icon", [[1, "ion-icon", {
      mode: [1025],
      color: [1],
      ios: [1],
      md: [1],
      flipRtl: [4, "flip-rtl"],
      name: [513],
      src: [1],
      icon: [8],
      size: [1],
      lazy: [4],
      sanitize: [4],
      svgContent: [32],
      isVisible: [32],
      ariaLabel: [32]
    }]]]], o);
  });
};


/***/ })

}]);
//# sourceMappingURL=node_modules_ionicons_dist_esm-es5_loader_js.js.map