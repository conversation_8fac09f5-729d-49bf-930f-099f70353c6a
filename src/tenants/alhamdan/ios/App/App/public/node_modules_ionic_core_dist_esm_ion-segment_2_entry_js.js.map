{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-segment_2_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC+I;AAChG;AACiC;AACnB;AACmD;AAEhH,MAAMwB,aAAa,GAAG,4sCAA4sC;AAEluC,MAAMC,YAAY,GAAG,o3BAAo3B;AAEz4B,MAAMC,OAAO,GAAG,MAAM;EACpBC,WAAWA,CAACC,OAAO,EAAE;IACnB3B,qDAAgB,CAAC,IAAI,EAAE2B,OAAO,CAAC;IAC/B,IAAI,CAACC,SAAS,GAAG1B,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAAC2B,SAAS,GAAG3B,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAAC4B,QAAQ,GAAG5B,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC6B,OAAO,GAAIC,EAAE,IAAK;MACrB,MAAMC,OAAO,GAAGD,EAAE,CAACE,MAAM;MACzB,MAAMC,QAAQ,GAAG,IAAI,CAACC,OAAO;MAC7B;MACA;MACA;MACA;MACA,IAAIH,OAAO,CAACI,OAAO,KAAK,aAAa,EAAE;QACrC;MACF;MACA,IAAI,CAACC,KAAK,GAAGL,OAAO,CAACK,KAAK;MAC1B,IAAIL,OAAO,KAAKE,QAAQ,EAAE;QACxB,IAAI,CAACI,eAAe,CAAC,CAAC;MACxB;MACA,IAAI,IAAI,CAACC,UAAU,IAAI,CAAC,IAAI,CAACC,YAAY,EAAE;QACzC,IAAIN,QAAQ,EAAE;UACZ,IAAI,CAACO,WAAW,CAACP,QAAQ,EAAEF,OAAO,CAAC;QACrC,CAAC,MACI;UACH,IAAI,CAACU,iBAAiB,CAAC,CAAC;QAC1B;MACF;IACF,CAAC;IACD,IAAI,CAACC,gBAAgB,GAAIC,QAAQ,IAAK;MACpC,IAAIC,EAAE,EAAEC,EAAE;MACV,MAAMC,OAAO,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC,CAACC,MAAM,CAAEC,MAAM,IAAK,CAACA,MAAM,CAACC,QAAQ,CAAC;MACtE,MAAMC,SAAS,GAAGL,OAAO,CAACM,SAAS,CAAEH,MAAM,IAAKA,MAAM,KAAKI,QAAQ,CAACC,aAAa,CAAC;MAClF,QAAQX,QAAQ;QACd,KAAK,OAAO;UACV,OAAOG,OAAO,CAAC,CAAC,CAAC;QACnB,KAAK,MAAM;UACT,OAAOA,OAAO,CAACA,OAAO,CAACS,MAAM,GAAG,CAAC,CAAC;QACpC,KAAK,MAAM;UACT,OAAO,CAACX,EAAE,GAAGE,OAAO,CAACK,SAAS,GAAG,CAAC,CAAC,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGE,OAAO,CAAC,CAAC,CAAC;QAClF,KAAK,UAAU;UACb,OAAO,CAACD,EAAE,GAAGC,OAAO,CAACK,SAAS,GAAG,CAAC,CAAC,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGC,OAAO,CAACA,OAAO,CAACS,MAAM,GAAG,CAAC,CAAC;QACnG;UACE,OAAO,IAAI;MACf;IACF,CAAC;IACD,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,KAAK,GAAGC,SAAS;IACtB,IAAI,CAACR,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACZ,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACH,KAAK,GAAGsB,SAAS;IACtB,IAAI,CAACC,aAAa,GAAG,KAAK;EAC5B;EACAC,YAAYA,CAACxB,KAAK,EAAEyB,QAAQ,EAAE;IAC5B;AACJ;AACA;AACA;AACA;AACA;IACI,IAAKA,QAAQ,KAAKH,SAAS,IAAItB,KAAK,KAAKsB,SAAS,IAAMG,QAAQ,KAAKH,SAAS,IAAItB,KAAK,KAAKsB,SAAU,EAAE;MACtG,IAAI,CAACI,SAAS,CAAC,CAAC;IAClB;EACF;EACAC,mBAAmBA,CAAA,EAAG;IACpB,IAAI,CAACC,cAAc,CAAC,CAAC;EACvB;EACAC,YAAYA,CAAC7B,KAAK,EAAE;IAClB;AACJ;AACA;AACA;IACI,IAAI,CAACT,SAAS,CAACuC,IAAI,CAAC;MAAE9B;IAAM,CAAC,CAAC;IAC9B,IAAI,IAAI,CAACE,UAAU,EAAE;MACnB,MAAMQ,OAAO,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;MACjC,MAAMoB,YAAY,GAAGrB,OAAO,CAACsB,IAAI,CAAEnB,MAAM,IAAKA,MAAM,CAACb,KAAK,KAAKA,KAAK,CAAC;MACrE,IAAI+B,YAAY,KAAKT,SAAS,EAAE;QAC9B;AACR;AACA;AACA;AACA;QACQS,YAAY,CAACE,cAAc,CAAC;UAC1BC,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE,QAAQ;UAChB;AACV;AACA;AACA;AACA;AACA;AACA;UACUC,KAAK,EAAE;QACT,CAAC,CAAC;MACJ;IACF;EACF;EACAC,eAAeA,CAAA,EAAG;IAChB,IAAI,CAACT,cAAc,CAAC,CAAC;IACrB,MAAMlB,OAAO,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IACjC,KAAK,MAAME,MAAM,IAAIH,OAAO,EAAE;MAC5BG,MAAM,CAACC,QAAQ,GAAG,IAAI,CAACA,QAAQ;IACjC;EACF;EACAc,cAAcA,CAAA,EAAG;IACf,IAAI,IAAI,CAACU,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACC,MAAM,CAAC,CAAC,IAAI,CAACrC,UAAU,IAAI,CAAC,IAAI,CAACY,QAAQ,IAAI,IAAI,CAACX,YAAY,CAAC;IAC9E;EACF;EACAqC,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAACd,SAAS,CAAC,CAAC;EAClB;EACAe,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAACf,SAAS,CAAC,CAAC;EAClB;EACMgB,gBAAgBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,8KAAA;MACvBD,KAAI,CAACtC,iBAAiB,CAAC,CAAC;MACxBsC,KAAI,CAACL,OAAO,GAAG,OAAO,sHAA6B,EAAEO,aAAa,CAAC;QACjEC,EAAE,EAAEH,KAAI,CAACG,EAAE;QACXC,WAAW,EAAE,SAAS;QACtBC,eAAe,EAAE,GAAG;QACpBC,SAAS,EAAE,CAAC;QACZC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAGzD,EAAE,IAAKiD,KAAI,CAACQ,OAAO,CAACzD,EAAE,CAAC;QACjC0D,MAAM,EAAG1D,EAAE,IAAKiD,KAAI,CAACS,MAAM,CAAC1D,EAAE,CAAC;QAC/B2D,KAAK,EAAG3D,EAAE,IAAKiD,KAAI,CAACU,KAAK,CAAC3D,EAAE;MAC9B,CAAC,CAAC;MACFiD,KAAI,CAACf,cAAc,CAAC,CAAC;MACrB,IAAIe,KAAI,CAAC7B,QAAQ,EAAE;QACjB6B,KAAI,CAACN,eAAe,CAAC,CAAC;MACxB;IAAC;EACH;EACAc,OAAOA,CAACG,MAAM,EAAE;IACd,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACvD,KAAK;IACpC,IAAI,CAACwD,QAAQ,CAACF,MAAM,CAAC;EACvB;EACAF,MAAMA,CAACE,MAAM,EAAE;IACb,IAAI,CAACG,YAAY,CAACH,MAAM,CAAC;EAC3B;EACAD,KAAKA,CAACC,MAAM,EAAE;IACZ,IAAI,CAACI,YAAY,CAAC,KAAK,CAAC;IACxB,IAAI,CAACD,YAAY,CAACH,MAAM,EAAE,IAAI,CAAC;IAC/BA,MAAM,CAACK,KAAK,CAACC,wBAAwB,CAAC,CAAC;IACvC,MAAM5D,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAIA,KAAK,KAAKsB,SAAS,EAAE;MACvB,IAAI,IAAI,CAACiC,kBAAkB,KAAKvD,KAAK,EAAE;QACrC,IAAI,CAACC,eAAe,CAAC,CAAC;MACxB;IACF;IACA,IAAI,CAACsD,kBAAkB,GAAGjC,SAAS;EACrC;EACA;AACF;AACA;AACA;AACA;AACA;EACErB,eAAeA,CAAA,EAAG;IAChB,MAAM;MAAED;IAAM,CAAC,GAAG,IAAI;IACtB,IAAI,CAACV,SAAS,CAACwC,IAAI,CAAC;MAAE9B;IAAM,CAAC,CAAC;EAChC;EACAW,UAAUA,CAAA,EAAG;IACX,OAAOkD,KAAK,CAACC,IAAI,CAAC,IAAI,CAAChB,EAAE,CAACiB,gBAAgB,CAAC,oBAAoB,CAAC,CAAC;EACnE;EACA,IAAIjE,OAAOA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACa,UAAU,CAAC,CAAC,CAACqB,IAAI,CAAEnB,MAAM,IAAKA,MAAM,CAACb,KAAK,KAAK,IAAI,CAACA,KAAK,CAAC;EACxE;EACA;AACF;AACA;AACA;EACE0D,YAAYA,CAACtC,SAAS,EAAE;IACtB,MAAMV,OAAO,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IACjCD,OAAO,CAACsD,OAAO,CAAEnD,MAAM,IAAK;MAC1B,IAAIO,SAAS,EAAE;QACbP,MAAM,CAACoD,SAAS,CAACC,GAAG,CAAC,0BAA0B,CAAC;MAClD,CAAC,MACI;QACHrD,MAAM,CAACoD,SAAS,CAACE,MAAM,CAAC,0BAA0B,CAAC;MACrD;IACF,CAAC,CAAC;IACF,IAAI,CAAC/C,SAAS,GAAGA,SAAS;EAC5B;EACAoC,QAAQA,CAACF,MAAM,EAAE;IACf,MAAMc,OAAO,GAAGd,MAAM,CAACK,KAAK,CAAC/D,MAAM;IACnC,MAAMc,OAAO,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IACjC,MAAMb,OAAO,GAAGY,OAAO,CAACsB,IAAI,CAAEnB,MAAM,IAAKA,MAAM,CAACb,KAAK,KAAK,IAAI,CAACA,KAAK,CAAC;IACrE;IACA;IACA,IAAIoE,OAAO,CAACrE,OAAO,KAAK,oBAAoB,EAAE;MAC5C;IACF;IACA;IACA,IAAI,CAACD,OAAO,EAAE;MACZ,IAAI,CAACE,KAAK,GAAGoE,OAAO,CAACpE,KAAK;MAC1B,IAAI,CAACK,iBAAiB,CAAC,CAAC;IAC1B;IACA;IACA;IACA,IAAI,IAAI,CAACL,KAAK,KAAKoE,OAAO,CAACpE,KAAK,EAAE;MAChC,IAAI,CAAC0D,YAAY,CAAC,IAAI,CAAC;IACzB;EACF;EACAW,YAAYA,CAACxD,MAAM,EAAE;IACnB,MAAMyD,IAAI,GAAGzD,MAAM,CAAC0D,UAAU,IAAI1D,MAAM;IACxC,OAAOyD,IAAI,CAACE,aAAa,CAAC,2BAA2B,CAAC;EACxD;EACApE,WAAWA,CAACP,QAAQ,EAAEF,OAAO,EAAE;IAC7B,MAAM8E,iBAAiB,GAAG,IAAI,CAACJ,YAAY,CAACxE,QAAQ,CAAC;IACrD,MAAM6E,gBAAgB,GAAG,IAAI,CAACL,YAAY,CAAC1E,OAAO,CAAC;IACnD,IAAI8E,iBAAiB,KAAK,IAAI,IAAIC,gBAAgB,KAAK,IAAI,EAAE;MAC3D;IACF;IACA,MAAMC,kBAAkB,GAAGF,iBAAiB,CAACG,qBAAqB,CAAC,CAAC;IACpE,MAAMC,iBAAiB,GAAGH,gBAAgB,CAACE,qBAAqB,CAAC,CAAC;IAClE,MAAME,UAAU,GAAGH,kBAAkB,CAACI,KAAK,GAAGF,iBAAiB,CAACE,KAAK;IACrE,MAAMC,SAAS,GAAGL,kBAAkB,CAACM,IAAI,GAAGJ,iBAAiB,CAACI,IAAI;IAClE;IACA;IACA,MAAMC,SAAS,GAAI,eAAcF,SAAU,oBAAmBF,UAAW,GAAE;IAC3EhH,qDAAS,CAAC,MAAM;MACd;MACA4G,gBAAgB,CAACT,SAAS,CAACE,MAAM,CAAC,mCAAmC,CAAC;MACtEO,gBAAgB,CAACS,KAAK,CAACC,WAAW,CAAC,WAAW,EAAEF,SAAS,CAAC;MAC1D;MACAR,gBAAgB,CAACE,qBAAqB,CAAC,CAAC;MACxC;MACAF,gBAAgB,CAACT,SAAS,CAACC,GAAG,CAAC,mCAAmC,CAAC;MACnE;MACAQ,gBAAgB,CAACS,KAAK,CAACC,WAAW,CAAC,WAAW,EAAE,EAAE,CAAC;IACrD,CAAC,CAAC;IACF,IAAI,CAACpF,KAAK,GAAGL,OAAO,CAACK,KAAK;IAC1B,IAAI,CAACK,iBAAiB,CAAC,CAAC;EAC1B;EACAA,iBAAiBA,CAAA,EAAG;IAClB,MAAMK,OAAO,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IACjC,MAAM0E,KAAK,GAAG3E,OAAO,CAACM,SAAS,CAAEH,MAAM,IAAKA,MAAM,CAACb,KAAK,KAAK,IAAI,CAACA,KAAK,CAAC;IACxE,MAAMsF,IAAI,GAAGD,KAAK,GAAG,CAAC;IACtB,KAAK,MAAMxE,MAAM,IAAIH,OAAO,EAAE;MAC5BG,MAAM,CAACoD,SAAS,CAACE,MAAM,CAAC,8BAA8B,CAAC;IACzD;IACA,IAAImB,IAAI,GAAG5E,OAAO,CAACS,MAAM,EAAE;MACzBT,OAAO,CAAC4E,IAAI,CAAC,CAACrB,SAAS,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC7D;EACF;EACAT,YAAYA,CAACH,MAAM,EAAEiC,KAAK,GAAG,KAAK,EAAE;IAClC,MAAMC,GAAG,GAAGlH,mDAAK,CAAC,IAAI,CAACwE,EAAE,CAAC;IAC1B,MAAM1B,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,MAAMV,OAAO,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IACjC,MAAM0E,KAAK,GAAG3E,OAAO,CAACM,SAAS,CAAEH,MAAM,IAAKA,MAAM,CAACb,KAAK,KAAK,IAAI,CAACA,KAAK,CAAC;IACxE,MAAMH,QAAQ,GAAGa,OAAO,CAAC2E,KAAK,CAAC;IAC/B,IAAI1F,OAAO;IACX,IAAI8F,SAAS;IACb,IAAIJ,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB;IACF;IACA;IACA;IACA,MAAMK,IAAI,GAAG7F,QAAQ,CAAC+E,qBAAqB,CAAC,CAAC;IAC7C,MAAMK,IAAI,GAAGS,IAAI,CAACT,IAAI;IACtB,MAAMF,KAAK,GAAGW,IAAI,CAACX,KAAK;IACxB;IACA;IACA;IACA,MAAMY,QAAQ,GAAGrC,MAAM,CAACqC,QAAQ;IAChC,MAAMC,SAAS,GAAGF,IAAI,CAACG,GAAG,GAAGH,IAAI,CAACI,MAAM,GAAG,CAAC;IAC5C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,MAAMxB,IAAI,GAAG,IAAI,CAACxB,EAAE,CAACiD,WAAW,CAAC,CAAC;IAClC,MAAMC,MAAM,GAAG1B,IAAI,CAAC2B,gBAAgB,CAACN,QAAQ,EAAEC,SAAS,CAAC;IACzD,MAAMM,aAAa,GAAGV,GAAG,GAAGG,QAAQ,GAAGV,IAAI,GAAGF,KAAK,GAAGY,QAAQ,GAAGV,IAAI;IACrE,MAAMkB,aAAa,GAAGX,GAAG,GAAGG,QAAQ,GAAGV,IAAI,GAAGU,QAAQ,GAAGV,IAAI,GAAGF,KAAK;IACrE;IACA;IACA;IACA,IAAI3D,SAAS,IAAI,CAACmE,KAAK,EAAE;MACvB;MACA,IAAIW,aAAa,EAAE;QACjB,MAAME,QAAQ,GAAGf,KAAK,GAAG,CAAC;QAC1B,IAAIe,QAAQ,IAAI,CAAC,EAAE;UACjBX,SAAS,GAAGW,QAAQ;QACtB;QACA;MACF,CAAC,MACI,IAAID,aAAa,EAAE;QACtB,IAAI/E,SAAS,IAAI,CAACmE,KAAK,EAAE;UACvB,MAAMa,QAAQ,GAAGf,KAAK,GAAG,CAAC;UAC1B,IAAIe,QAAQ,GAAG1F,OAAO,CAACS,MAAM,EAAE;YAC7BsE,SAAS,GAAGW,QAAQ;UACtB;QACF;MACF;MACA,IAAIX,SAAS,KAAKnE,SAAS,IAAI,CAACZ,OAAO,CAAC+E,SAAS,CAAC,CAAC3E,QAAQ,EAAE;QAC3DnB,OAAO,GAAGe,OAAO,CAAC+E,SAAS,CAAC;MAC9B;IACF;IACA;IACA;IACA,IAAI,CAACrE,SAAS,IAAImE,KAAK,EAAE;MACvB5F,OAAO,GAAGqG,MAAM;IAClB;IACA,IAAIrG,OAAO,IAAI,IAAI,EAAE;MACnB;AACN;AACA;AACA;AACA;MACM,IAAIA,OAAO,CAACI,OAAO,KAAK,aAAa,EAAE;QACrC,OAAO,KAAK;MACd;MACA,IAAIF,QAAQ,KAAKF,OAAO,EAAE;QACxB,IAAI,CAACS,WAAW,CAACP,QAAQ,EAAEF,OAAO,CAAC;MACrC;IACF;IACA,OAAO,IAAI;EACb;EACA+B,SAASA,CAAA,EAAG;IACV,IAAI,CAAClC,QAAQ,CAACsC,IAAI,CAAC;MACjBuE,OAAO,EAAE;IACX,CAAC,CAAC;EACJ;EACAC,SAASA,CAAC5G,EAAE,EAAE;IACZ,MAAM8F,GAAG,GAAGlH,mDAAK,CAAC,IAAI,CAACwE,EAAE,CAAC;IAC1B,IAAIyD,oBAAoB,GAAG,IAAI,CAAChF,aAAa;IAC7C,IAAI5B,OAAO;IACX,QAAQD,EAAE,CAAC8G,GAAG;MACZ,KAAK,YAAY;QACf9G,EAAE,CAAC+G,cAAc,CAAC,CAAC;QACnB9G,OAAO,GAAG6F,GAAG,GAAG,IAAI,CAAClF,gBAAgB,CAAC,UAAU,CAAC,GAAG,IAAI,CAACA,gBAAgB,CAAC,MAAM,CAAC;QACjF;MACF,KAAK,WAAW;QACdZ,EAAE,CAAC+G,cAAc,CAAC,CAAC;QACnB9G,OAAO,GAAG6F,GAAG,GAAG,IAAI,CAAClF,gBAAgB,CAAC,MAAM,CAAC,GAAG,IAAI,CAACA,gBAAgB,CAAC,UAAU,CAAC;QACjF;MACF,KAAK,MAAM;QACTZ,EAAE,CAAC+G,cAAc,CAAC,CAAC;QACnB9G,OAAO,GAAG,IAAI,CAACW,gBAAgB,CAAC,OAAO,CAAC;QACxC;MACF,KAAK,KAAK;QACRZ,EAAE,CAAC+G,cAAc,CAAC,CAAC;QACnB9G,OAAO,GAAG,IAAI,CAACW,gBAAgB,CAAC,MAAM,CAAC;QACvC;MACF,KAAK,GAAG;MACR,KAAK,OAAO;QACVZ,EAAE,CAAC+G,cAAc,CAAC,CAAC;QACnB9G,OAAO,GAAGsB,QAAQ,CAACC,aAAa;QAChCqF,oBAAoB,GAAG,IAAI;IAC/B;IACA,IAAI,CAAC5G,OAAO,EAAE;MACZ;IACF;IACA,IAAI4G,oBAAoB,EAAE;MACxB,MAAM1G,QAAQ,GAAG,IAAI,CAACC,OAAO;MAC7B,IAAI,CAACM,WAAW,CAACP,QAAQ,IAAIF,OAAO,EAAEA,OAAO,CAAC;MAC9C,IAAIA,OAAO,KAAKE,QAAQ,EAAE;QACxB,IAAI,CAACI,eAAe,CAAC,CAAC;MACxB;IACF;IACAN,OAAO,CAAC+G,QAAQ,CAAC,CAAC;EACpB;EACAC,MAAMA,CAAA,EAAG;IACP,MAAMC,IAAI,GAAGjI,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQZ,qDAAC,CAACE,iDAAI,EAAE;MAAE4I,IAAI,EAAE,SAAS;MAAEpH,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEqH,KAAK,EAAEtI,qDAAkB,CAAC,IAAI,CAAC6C,KAAK,EAAE;QAC5F,CAACuF,IAAI,GAAG,IAAI;QACZ,YAAY,EAAEnI,qDAAW,CAAC,aAAa,EAAE,IAAI,CAACqE,EAAE,CAAC;QACjD,kBAAkB,EAAErE,qDAAW,CAAC,oBAAoB,EAAE,IAAI,CAACqE,EAAE,CAAC;QAC9D,mBAAmB,EAAE,IAAI,CAAC1B,SAAS;QACnC,kBAAkB,EAAE,IAAI,CAACN,QAAQ;QACjC,oBAAoB,EAAE,IAAI,CAACZ;MAC7B,CAAC;IAAE,CAAC,EAAEnC,qDAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;EAC1B;EACA,IAAI+E,EAAEA,CAAA,EAAG;IAAE,OAAO3E,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW4I,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC7B,OAAO,EAAE,CAAC,cAAc,CAAC;MACzB,cAAc,EAAE,CAAC,qBAAqB,CAAC;MACvC,OAAO,EAAE,CAAC,cAAc,CAAC;MACzB,UAAU,EAAE,CAAC,iBAAiB;IAChC,CAAC;EAAE;AACL,CAAC;AACD5H,OAAO,CAACgG,KAAK,GAAG;EACd6B,GAAG,EAAE/H,aAAa;EAClBgI,EAAE,EAAE/H;AACN,CAAC;AAED,MAAMgI,mBAAmB,GAAG,gqQAAgqQ;AAE5rQ,MAAMC,kBAAkB,GAAG,8pQAA8pQ;AAEzrQ,IAAIC,GAAG,GAAG,CAAC;AACX,MAAMC,aAAa,GAAG,MAAM;EAC1BjI,WAAWA,CAACC,OAAO,EAAE;IACnB3B,qDAAgB,CAAC,IAAI,EAAE2B,OAAO,CAAC;IAC/B,IAAI,CAACiI,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,WAAW,GAAG,MAAM;MACvBnJ,qDAAW,CAAC,IAAI,CAAC;IACnB,CAAC;IACD,IAAI,CAACoJ,WAAW,GAAG,MAAM;MACvB,MAAM;QAAEH;MAAU,CAAC,GAAG,IAAI;MAC1B,IAAIA,SAAS,EAAE;QACb,IAAI,CAACxH,OAAO,GAAGwH,SAAS,CAACtH,KAAK,KAAK,IAAI,CAACA,KAAK;QAC7C,IAAIsH,SAAS,CAACxG,QAAQ,EAAE;UACtB,IAAI,CAACA,QAAQ,GAAG,IAAI;QACtB;MACF;IACF,CAAC;IACD,IAAI,CAAChB,OAAO,GAAG,KAAK;IACpB,IAAI,CAACgB,QAAQ,GAAG,KAAK;IACrB,IAAI,CAAC4G,MAAM,GAAG,UAAU;IACxB,IAAI,CAACC,IAAI,GAAG,QAAQ;IACpB,IAAI,CAAC3H,KAAK,GAAG,SAAS,GAAGoH,GAAG,EAAE;EAChC;EACAvF,YAAYA,CAAA,EAAG;IACb,IAAI,CAAC4F,WAAW,CAAC,CAAC;EACpB;EACAjF,iBAAiBA,CAAA,EAAG;IAClB,MAAM8E,SAAS,GAAI,IAAI,CAACA,SAAS,GAAG,IAAI,CAACxE,EAAE,CAAC8E,OAAO,CAAC,aAAa,CAAE;IACnE,IAAIN,SAAS,EAAE;MACb,IAAI,CAACG,WAAW,CAAC,CAAC;MAClB5I,uDAAgB,CAACyI,SAAS,EAAE,WAAW,EAAE,IAAI,CAACG,WAAW,CAAC;MAC1D5I,uDAAgB,CAACyI,SAAS,EAAE,UAAU,EAAE,IAAI,CAACE,WAAW,CAAC;IAC3D;EACF;EACAK,oBAAoBA,CAAA,EAAG;IACrB,MAAMP,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,IAAIA,SAAS,EAAE;MACbxI,uDAAmB,CAACwI,SAAS,EAAE,WAAW,EAAE,IAAI,CAACG,WAAW,CAAC;MAC7D3I,uDAAmB,CAACwI,SAAS,EAAE,UAAU,EAAE,IAAI,CAACE,WAAW,CAAC;MAC5D,IAAI,CAACF,SAAS,GAAG,IAAI;IACvB;EACF;EACA7E,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAAC8E,mBAAmB,GAAGO,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE/I,uDAAiB,CAAC,IAAI,CAAC8D,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC;EAC1F;EACA,IAAIkF,QAAQA,CAAA,EAAG;IACb,OAAO,CAAC,CAAC,IAAI,CAAClF,EAAE,CAAC0B,aAAa,CAAC,WAAW,CAAC;EAC7C;EACA,IAAIyD,OAAOA,CAAA,EAAG;IACZ,OAAO,CAAC,CAAC,IAAI,CAACnF,EAAE,CAAC0B,aAAa,CAAC,UAAU,CAAC;EAC5C;EACA;AACF;AACA;AACA;AACA;EACQkC,QAAQA,CAAA,EAAG;IAAA,IAAAwB,MAAA;IAAA,OAAAtF,8KAAA;MACf,MAAM;QAAEuF;MAAS,CAAC,GAAGD,MAAI;MACzB,IAAIC,QAAQ,KAAK7G,SAAS,EAAE;QAC1B6G,QAAQ,CAACC,KAAK,CAAC,CAAC;MAClB;IAAC;EACH;EACAzB,MAAMA,CAAA,EAAG;IACP,MAAM;MAAE7G,OAAO;MAAE6H,IAAI;MAAE7G,QAAQ;MAAEmH,OAAO;MAAED,QAAQ;MAAEN,MAAM;MAAEJ;IAAU,CAAC,GAAG,IAAI;IAC9E,MAAMV,IAAI,GAAGjI,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM0J,eAAe,GAAGA,CAAA,KAAM,CAACf,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACjG,KAAK,MAAMC,SAAS;IACnH,OAAQvD,qDAAC,CAACE,iDAAI,EAAE;MAAE6I,KAAK,EAAE;QACrB,CAACF,IAAI,GAAG,IAAI;QACZ,YAAY,EAAEnI,qDAAW,CAAC,aAAa,EAAE,IAAI,CAACqE,EAAE,CAAC;QACjD,kBAAkB,EAAErE,qDAAW,CAAC,oBAAoB,EAAE,IAAI,CAACqE,EAAE,CAAC;QAC9D,YAAY,EAAErE,qDAAW,CAAC,aAAa,EAAE,IAAI,CAACqE,EAAE,CAAC;QACjD,kBAAkB,EAAEuF,eAAe,CAAC,CAAC;QACrC,0BAA0B,EAAEL,QAAQ;QACpC,yBAAyB,EAAEC,OAAO;QAClC,+BAA+B,EAAED,QAAQ,IAAI,CAACC,OAAO;QACrD,8BAA8B,EAAEA,OAAO,IAAI,CAACD,QAAQ;QACpD,yBAAyB,EAAElH,QAAQ;QACnC,wBAAwB,EAAEhB,OAAO;QACjC,CAAE,yBAAwB4H,MAAO,EAAC,GAAG,IAAI;QACzC,iBAAiB,EAAE,IAAI;QACvB,yBAAyB,EAAE,IAAI;QAC/B,eAAe,EAAE;MACnB;IAAE,CAAC,EAAE3J,qDAAC,CAAC,QAAQ,EAAE+J,MAAM,CAACC,MAAM,CAAC;MAAE,eAAe,EAAEjI,OAAO,GAAG,MAAM,GAAG,OAAO;MAAE+G,IAAI,EAAE,KAAK;MAAEyB,GAAG,EAAGxF,EAAE,IAAM,IAAI,CAACqF,QAAQ,GAAGrF,EAAG;MAAE6E,IAAI,EAAEA,IAAI;MAAEb,KAAK,EAAE,eAAe;MAAEyB,IAAI,EAAE,QAAQ;MAAEzH,QAAQ,EAAEA;IAAS,CAAC,EAAE,IAAI,CAACyG,mBAAmB,CAAC,EAAExJ,qDAAC,CAAC,MAAM,EAAE;MAAE+I,KAAK,EAAE;IAAe,CAAC,EAAE/I,qDAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,EAAE6I,IAAI,KAAK,IAAI,IAAI7I,qDAAC,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC,EAAEA,qDAAC,CAAC,KAAK,EAAE;MAAEwK,IAAI,EAAE,WAAW;MAAEzB,KAAK,EAAE;QAC7W,0BAA0B,EAAE,IAAI;QAChC,mCAAmC,EAAE;MACvC;IAAE,CAAC,EAAE/I,qDAAC,CAAC,KAAK,EAAE;MAAEwK,IAAI,EAAE,sBAAsB;MAAEzB,KAAK,EAAE;IAAsC,CAAC,CAAC,CAAC,CAAC;EACnG;EACA,IAAIhE,EAAEA,CAAA,EAAG;IAAE,OAAO3E,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW4I,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC7B,OAAO,EAAE,CAAC,cAAc;IAC1B,CAAC;EAAE;AACL,CAAC;AACDM,aAAa,CAAClC,KAAK,GAAG;EACpB6B,GAAG,EAAEE,mBAAmB;EACxBD,EAAE,EAAEE;AACN,CAAC;;;;;;;;;;;;;;;;;;;;ACxfD;AACA;AACA;AACA,MAAM1I,WAAW,GAAGA,CAAC8B,QAAQ,EAAEuC,EAAE,KAAK;EACpC,OAAOA,EAAE,CAAC8E,OAAO,CAACrH,QAAQ,CAAC,KAAK,IAAI;AACtC,CAAC;AACD;AACA;AACA;AACA,MAAM/B,kBAAkB,GAAGA,CAAC6C,KAAK,EAAEqH,WAAW,KAAK;EACjD,OAAO,OAAOrH,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACF,MAAM,GAAG,CAAC,GAChD2G,MAAM,CAACC,MAAM,CAAC;IAAE,WAAW,EAAE,IAAI;IAAE,CAAE,aAAY1G,KAAM,EAAC,GAAG;EAAK,CAAC,EAAEqH,WAAW,CAAC,GAAGA,WAAW;AACnG,CAAC;AACD,MAAMC,YAAY,GAAIC,OAAO,IAAK;EAChC,IAAIA,OAAO,KAAKtH,SAAS,EAAE;IACzB,MAAMuH,KAAK,GAAGhF,KAAK,CAACiF,OAAO,CAACF,OAAO,CAAC,GAAGA,OAAO,GAAGA,OAAO,CAACG,KAAK,CAAC,GAAG,CAAC;IACnE,OAAOF,KAAK,CACTjI,MAAM,CAAErC,CAAC,IAAKA,CAAC,IAAI,IAAI,CAAC,CACxByK,GAAG,CAAEzK,CAAC,IAAKA,CAAC,CAAC0K,IAAI,CAAC,CAAC,CAAC,CACpBrI,MAAM,CAAErC,CAAC,IAAKA,CAAC,KAAK,EAAE,CAAC;EAC5B;EACA,OAAO,EAAE;AACX,CAAC;AACD,MAAM2K,WAAW,GAAIN,OAAO,IAAK;EAC/B,MAAMI,GAAG,GAAG,CAAC,CAAC;EACdL,YAAY,CAACC,OAAO,CAAC,CAAC5E,OAAO,CAAEzF,CAAC,IAAMyK,GAAG,CAACzK,CAAC,CAAC,GAAG,IAAK,CAAC;EACrD,OAAOyK,GAAG;AACZ,CAAC;AACD,MAAMG,MAAM,GAAG,sBAAsB;AACrC,MAAMC,OAAO;EAAA,IAAAC,IAAA,GAAAzG,8KAAA,CAAG,WAAO0G,GAAG,EAAE5J,EAAE,EAAE6J,SAAS,EAAEC,SAAS,EAAK;IACvD,IAAIF,GAAG,IAAI,IAAI,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAACH,MAAM,CAACM,IAAI,CAACH,GAAG,CAAC,EAAE;MACtD,MAAMI,MAAM,GAAGzI,QAAQ,CAACuD,aAAa,CAAC,YAAY,CAAC;MACnD,IAAIkF,MAAM,EAAE;QACV,IAAIhK,EAAE,IAAI,IAAI,EAAE;UACdA,EAAE,CAAC+G,cAAc,CAAC,CAAC;QACrB;QACA,OAAOiD,MAAM,CAACC,IAAI,CAACL,GAAG,EAAEC,SAAS,EAAEC,SAAS,CAAC;MAC/C;IACF;IACA,OAAO,KAAK;EACd,CAAC;EAAA,gBAXKJ,OAAOA,CAAAQ,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAV,IAAA,CAAAW,KAAA,OAAAC,SAAA;EAAA;AAAA,GAWZ", "sources": ["./node_modules/@ionic/core/dist/esm/ion-segment_2.entry.js", "./node_modules/@ionic/core/dist/esm/theme-17531cdf.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, w as writeTask, h, H as Host, f as getElement, i as forceUpdate } from './index-2d388930.js';\nimport { i as isRTL } from './dir-912e3e13.js';\nimport { c as createColorClasses, h as hostContext } from './theme-17531cdf.js';\nimport { b as getIonMode } from './ionic-global-b3fc28dd.js';\nimport { a as addEventListener, b as removeEventListener, k as inheritAttributes } from './helpers-3379ba19.js';\n\nconst segmentIosCss = \":host{--ripple-color:currentColor;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:grid;grid-auto-columns:1fr;position:relative;-ms-flex-align:stretch;align-items:stretch;-ms-flex-pack:center;justify-content:center;width:100%;background:var(--background);font-family:var(--ion-font-family, inherit);text-align:center;contain:paint;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}:host(.segment-scrollable){-ms-flex-pack:start;justify-content:start;width:auto;overflow-x:auto;grid-auto-columns:minmax(-webkit-min-content, 1fr);grid-auto-columns:minmax(min-content, 1fr)}:host(.segment-scrollable::-webkit-scrollbar){display:none}:host{--background:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.065);border-radius:8px;overflow:hidden;z-index:0}:host(.ion-color){background:rgba(var(--ion-color-base-rgb), 0.065)}:host(.in-toolbar){-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;width:auto}:host(.in-toolbar:not(.ion-color)){background:var(--ion-toolbar-segment-background, var(--background))}:host(.in-toolbar-color:not(.ion-color)){background:rgba(var(--ion-color-contrast-rgb), 0.11)}\";\n\nconst segmentMdCss = \":host{--ripple-color:currentColor;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:grid;grid-auto-columns:1fr;position:relative;-ms-flex-align:stretch;align-items:stretch;-ms-flex-pack:center;justify-content:center;width:100%;background:var(--background);font-family:var(--ion-font-family, inherit);text-align:center;contain:paint;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}:host(.segment-scrollable){-ms-flex-pack:start;justify-content:start;width:auto;overflow-x:auto;grid-auto-columns:minmax(-webkit-min-content, 1fr);grid-auto-columns:minmax(min-content, 1fr)}:host(.segment-scrollable::-webkit-scrollbar){display:none}:host{--background:transparent;grid-auto-columns:minmax(auto, 360px)}:host(.in-toolbar){min-height:var(--min-height)}:host(.segment-scrollable) ::slotted(ion-segment-button){min-width:auto}\";\n\nconst Segment = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionSelect = createEvent(this, \"ionSelect\", 7);\n    this.ionStyle = createEvent(this, \"ionStyle\", 7);\n    this.onClick = (ev) => {\n      const current = ev.target;\n      const previous = this.checked;\n      // If the current element is a segment then that means\n      // the user tried to swipe to a segment button and\n      // click a segment button at the same time so we should\n      // not update the checked segment button\n      if (current.tagName === 'ION-SEGMENT') {\n        return;\n      }\n      this.value = current.value;\n      if (current !== previous) {\n        this.emitValueChange();\n      }\n      if (this.scrollable || !this.swipeGesture) {\n        if (previous) {\n          this.checkButton(previous, current);\n        }\n        else {\n          this.setCheckedClasses();\n        }\n      }\n    };\n    this.getSegmentButton = (selector) => {\n      var _a, _b;\n      const buttons = this.getButtons().filter((button) => !button.disabled);\n      const currIndex = buttons.findIndex((button) => button === document.activeElement);\n      switch (selector) {\n        case 'first':\n          return buttons[0];\n        case 'last':\n          return buttons[buttons.length - 1];\n        case 'next':\n          return (_a = buttons[currIndex + 1]) !== null && _a !== void 0 ? _a : buttons[0];\n        case 'previous':\n          return (_b = buttons[currIndex - 1]) !== null && _b !== void 0 ? _b : buttons[buttons.length - 1];\n        default:\n          return null;\n      }\n    };\n    this.activated = false;\n    this.color = undefined;\n    this.disabled = false;\n    this.scrollable = false;\n    this.swipeGesture = true;\n    this.value = undefined;\n    this.selectOnFocus = false;\n  }\n  colorChanged(value, oldValue) {\n    /**\n     * If color is set after not having\n     * previously been set (or vice versa),\n     * we need to emit style so the segment-buttons\n     * can apply their color classes properly.\n     */\n    if ((oldValue === undefined && value !== undefined) || (oldValue !== undefined && value === undefined)) {\n      this.emitStyle();\n    }\n  }\n  swipeGestureChanged() {\n    this.gestureChanged();\n  }\n  valueChanged(value) {\n    /**\n     * `ionSelect` is emitted every time the value changes (internal or external changes).\n     * Used by `ion-segment-button` to determine if the button should be checked.\n     */\n    this.ionSelect.emit({ value });\n    if (this.scrollable) {\n      const buttons = this.getButtons();\n      const activeButton = buttons.find((button) => button.value === value);\n      if (activeButton !== undefined) {\n        /**\n         * Scrollable segment buttons should be\n         * centered within the view including\n         * buttons that are partially offscreen.\n         */\n        activeButton.scrollIntoView({\n          behavior: 'smooth',\n          inline: 'center',\n          /**\n           * Segment should scroll on the\n           * horizontal axis. `block: 'nearest'`\n           * ensures that the vertical axis\n           * does not scroll if the segment\n           * as a whole is already in view.\n           */\n          block: 'nearest',\n        });\n      }\n    }\n  }\n  disabledChanged() {\n    this.gestureChanged();\n    const buttons = this.getButtons();\n    for (const button of buttons) {\n      button.disabled = this.disabled;\n    }\n  }\n  gestureChanged() {\n    if (this.gesture) {\n      this.gesture.enable(!this.scrollable && !this.disabled && this.swipeGesture);\n    }\n  }\n  connectedCallback() {\n    this.emitStyle();\n  }\n  componentWillLoad() {\n    this.emitStyle();\n  }\n  async componentDidLoad() {\n    this.setCheckedClasses();\n    this.gesture = (await import('./index-ff313b19.js')).createGesture({\n      el: this.el,\n      gestureName: 'segment',\n      gesturePriority: 100,\n      threshold: 0,\n      passive: false,\n      onStart: (ev) => this.onStart(ev),\n      onMove: (ev) => this.onMove(ev),\n      onEnd: (ev) => this.onEnd(ev),\n    });\n    this.gestureChanged();\n    if (this.disabled) {\n      this.disabledChanged();\n    }\n  }\n  onStart(detail) {\n    this.valueBeforeGesture = this.value;\n    this.activate(detail);\n  }\n  onMove(detail) {\n    this.setNextIndex(detail);\n  }\n  onEnd(detail) {\n    this.setActivated(false);\n    this.setNextIndex(detail, true);\n    detail.event.stopImmediatePropagation();\n    const value = this.value;\n    if (value !== undefined) {\n      if (this.valueBeforeGesture !== value) {\n        this.emitValueChange();\n      }\n    }\n    this.valueBeforeGesture = undefined;\n  }\n  /**\n   * Emits an `ionChange` event.\n   *\n   * This API should be called for user committed changes.\n   * This API should not be used for external value changes.\n   */\n  emitValueChange() {\n    const { value } = this;\n    this.ionChange.emit({ value });\n  }\n  getButtons() {\n    return Array.from(this.el.querySelectorAll('ion-segment-button'));\n  }\n  get checked() {\n    return this.getButtons().find((button) => button.value === this.value);\n  }\n  /*\n   * Activate both the segment and the buttons\n   * due to a bug with ::slotted in Safari\n   */\n  setActivated(activated) {\n    const buttons = this.getButtons();\n    buttons.forEach((button) => {\n      if (activated) {\n        button.classList.add('segment-button-activated');\n      }\n      else {\n        button.classList.remove('segment-button-activated');\n      }\n    });\n    this.activated = activated;\n  }\n  activate(detail) {\n    const clicked = detail.event.target;\n    const buttons = this.getButtons();\n    const checked = buttons.find((button) => button.value === this.value);\n    // Make sure we are only checking for activation on a segment button\n    // since disabled buttons will get the click on the segment\n    if (clicked.tagName !== 'ION-SEGMENT-BUTTON') {\n      return;\n    }\n    // If there are no checked buttons, set the current button to checked\n    if (!checked) {\n      this.value = clicked.value;\n      this.setCheckedClasses();\n    }\n    // If the gesture began on the clicked button with the indicator\n    // then we should activate the indicator\n    if (this.value === clicked.value) {\n      this.setActivated(true);\n    }\n  }\n  getIndicator(button) {\n    const root = button.shadowRoot || button;\n    return root.querySelector('.segment-button-indicator');\n  }\n  checkButton(previous, current) {\n    const previousIndicator = this.getIndicator(previous);\n    const currentIndicator = this.getIndicator(current);\n    if (previousIndicator === null || currentIndicator === null) {\n      return;\n    }\n    const previousClientRect = previousIndicator.getBoundingClientRect();\n    const currentClientRect = currentIndicator.getBoundingClientRect();\n    const widthDelta = previousClientRect.width / currentClientRect.width;\n    const xPosition = previousClientRect.left - currentClientRect.left;\n    // Scale the indicator width to match the previous indicator width\n    // and translate it on top of the previous indicator\n    const transform = `translate3d(${xPosition}px, 0, 0) scaleX(${widthDelta})`;\n    writeTask(() => {\n      // Remove the transition before positioning on top of the previous indicator\n      currentIndicator.classList.remove('segment-button-indicator-animated');\n      currentIndicator.style.setProperty('transform', transform);\n      // Force a repaint to ensure the transform happens\n      currentIndicator.getBoundingClientRect();\n      // Add the transition to move the indicator into place\n      currentIndicator.classList.add('segment-button-indicator-animated');\n      // Remove the transform to slide the indicator back to the button clicked\n      currentIndicator.style.setProperty('transform', '');\n    });\n    this.value = current.value;\n    this.setCheckedClasses();\n  }\n  setCheckedClasses() {\n    const buttons = this.getButtons();\n    const index = buttons.findIndex((button) => button.value === this.value);\n    const next = index + 1;\n    for (const button of buttons) {\n      button.classList.remove('segment-button-after-checked');\n    }\n    if (next < buttons.length) {\n      buttons[next].classList.add('segment-button-after-checked');\n    }\n  }\n  setNextIndex(detail, isEnd = false) {\n    const rtl = isRTL(this.el);\n    const activated = this.activated;\n    const buttons = this.getButtons();\n    const index = buttons.findIndex((button) => button.value === this.value);\n    const previous = buttons[index];\n    let current;\n    let nextIndex;\n    if (index === -1) {\n      return;\n    }\n    // Get the element that the touch event started on in case\n    // it was the checked button, then we will move the indicator\n    const rect = previous.getBoundingClientRect();\n    const left = rect.left;\n    const width = rect.width;\n    // Get the element that the gesture is on top of based on the currentX of the\n    // gesture event and the Y coordinate of the starting element, since the gesture\n    // can move up and down off of the segment\n    const currentX = detail.currentX;\n    const previousY = rect.top + rect.height / 2;\n    /**\n     * Segment can be used inside the shadow dom\n     * so doing document.elementFromPoint would never\n     * return a segment button in that instance.\n     * We use getRootNode to which will return the parent\n     * shadow root if used inside a shadow component and\n     * returns document otherwise.\n     */\n    const root = this.el.getRootNode();\n    const nextEl = root.elementFromPoint(currentX, previousY);\n    const decreaseIndex = rtl ? currentX > left + width : currentX < left;\n    const increaseIndex = rtl ? currentX < left : currentX > left + width;\n    // If the indicator is currently activated then we have started the gesture\n    // on top of the checked button so we need to slide the indicator\n    // by checking the button next to it as we move\n    if (activated && !isEnd) {\n      // Decrease index, move left in LTR & right in RTL\n      if (decreaseIndex) {\n        const newIndex = index - 1;\n        if (newIndex >= 0) {\n          nextIndex = newIndex;\n        }\n        // Increase index, moves right in LTR & left in RTL\n      }\n      else if (increaseIndex) {\n        if (activated && !isEnd) {\n          const newIndex = index + 1;\n          if (newIndex < buttons.length) {\n            nextIndex = newIndex;\n          }\n        }\n      }\n      if (nextIndex !== undefined && !buttons[nextIndex].disabled) {\n        current = buttons[nextIndex];\n      }\n    }\n    // If the indicator is not activated then we will just set the indicator\n    // to the element where the gesture ended\n    if (!activated && isEnd) {\n      current = nextEl;\n    }\n    if (current != null) {\n      /**\n       * If current element is ion-segment then that means\n       * user tried to select a disabled ion-segment-button,\n       * and we should not update the ripple.\n       */\n      if (current.tagName === 'ION-SEGMENT') {\n        return false;\n      }\n      if (previous !== current) {\n        this.checkButton(previous, current);\n      }\n    }\n    return true;\n  }\n  emitStyle() {\n    this.ionStyle.emit({\n      segment: true,\n    });\n  }\n  onKeyDown(ev) {\n    const rtl = isRTL(this.el);\n    let keyDownSelectsButton = this.selectOnFocus;\n    let current;\n    switch (ev.key) {\n      case 'ArrowRight':\n        ev.preventDefault();\n        current = rtl ? this.getSegmentButton('previous') : this.getSegmentButton('next');\n        break;\n      case 'ArrowLeft':\n        ev.preventDefault();\n        current = rtl ? this.getSegmentButton('next') : this.getSegmentButton('previous');\n        break;\n      case 'Home':\n        ev.preventDefault();\n        current = this.getSegmentButton('first');\n        break;\n      case 'End':\n        ev.preventDefault();\n        current = this.getSegmentButton('last');\n        break;\n      case ' ':\n      case 'Enter':\n        ev.preventDefault();\n        current = document.activeElement;\n        keyDownSelectsButton = true;\n    }\n    if (!current) {\n      return;\n    }\n    if (keyDownSelectsButton) {\n      const previous = this.checked;\n      this.checkButton(previous || current, current);\n      if (current !== previous) {\n        this.emitValueChange();\n      }\n    }\n    current.setFocus();\n  }\n  render() {\n    const mode = getIonMode(this);\n    return (h(Host, { role: \"tablist\", onClick: this.onClick, class: createColorClasses(this.color, {\n        [mode]: true,\n        'in-toolbar': hostContext('ion-toolbar', this.el),\n        'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n        'segment-activated': this.activated,\n        'segment-disabled': this.disabled,\n        'segment-scrollable': this.scrollable,\n      }) }, h(\"slot\", null)));\n  }\n  get el() { return getElement(this); }\n  static get watchers() { return {\n    \"color\": [\"colorChanged\"],\n    \"swipeGesture\": [\"swipeGestureChanged\"],\n    \"value\": [\"valueChanged\"],\n    \"disabled\": [\"disabledChanged\"]\n  }; }\n};\nSegment.style = {\n  ios: segmentIosCss,\n  md: segmentMdCss\n};\n\nconst segmentButtonIosCss = \":host{--color:initial;--color-hover:var(--color);--color-checked:var(--color);--color-disabled:var(--color);--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:column;flex-direction:column;height:auto;background:var(--background);color:var(--color);text-decoration:none;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;grid-row:1;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;min-width:inherit;max-width:inherit;height:auto;min-height:inherit;max-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border:none;outline:none;background:transparent;contain:content;pointer-events:none;overflow:hidden;z-index:2}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:inherit;flex-flow:inherit;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}:host(.segment-button-checked){background:var(--background-checked);color:var(--color-checked)}:host(.segment-button-disabled){cursor:default;pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(:focus){outline:none}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}:host(.segment-button-checked:hover) .button-native{color:var(--color-checked)}}::slotted(ion-icon){-ms-flex-negative:0;flex-shrink:0;-ms-flex-order:-1;order:-1;pointer-events:none}::slotted(ion-label){display:block;-ms-flex-item-align:center;align-self:center;max-width:100%;line-height:22px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host(.segment-button-layout-icon-top) .button-native{-ms-flex-direction:column;flex-direction:column}:host(.segment-button-layout-icon-start) .button-native{-ms-flex-direction:row;flex-direction:row}:host(.segment-button-layout-icon-end) .button-native{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.segment-button-layout-icon-bottom) .button-native{-ms-flex-direction:column-reverse;flex-direction:column-reverse}:host(.segment-button-layout-icon-hide) ::slotted(ion-icon){display:none}:host(.segment-button-layout-label-hide) ::slotted(ion-label){display:none}ion-ripple-effect{color:var(--ripple-color, var(--color-checked))}.segment-button-indicator{-webkit-transform-origin:left;transform-origin:left;position:absolute;opacity:0;-webkit-box-sizing:border-box;box-sizing:border-box;will-change:transform, opacity;pointer-events:none}.segment-button-indicator-background{width:100%;height:var(--indicator-height);-webkit-transform:var(--indicator-transform);transform:var(--indicator-transform);-webkit-box-shadow:var(--indicator-box-shadow);box-shadow:var(--indicator-box-shadow);pointer-events:none}.segment-button-indicator-animated{-webkit-transition:var(--indicator-transition);transition:var(--indicator-transition)}:host(.segment-button-checked) .segment-button-indicator{opacity:1}@media (prefers-reduced-motion: reduce){.segment-button-indicator-background{-webkit-transform:none;transform:none}.segment-button-indicator-animated{-webkit-transition:none;transition:none}}:host{--background:none;--background-checked:none;--background-hover:none;--background-hover-opacity:0;--background-focused:none;--background-focused-opacity:0;--border-radius:7px;--border-width:1px;--border-color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.12);--border-style:solid;--indicator-box-shadow:0 0 5px rgba(0, 0, 0, 0.16);--indicator-color:var(--ion-color-step-350, var(--ion-background-color, #fff));--indicator-height:100%;--indicator-transition:transform 260ms cubic-bezier(0.4, 0, 0.2, 1);--indicator-transform:none;--transition:100ms all linear;--padding-top:0;--padding-end:13px;--padding-bottom:0;--padding-start:13px;margin-top:2px;margin-bottom:2px;position:relative;-ms-flex-direction:row;flex-direction:row;min-width:70px;min-height:28px;-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0);font-size:13px;font-weight:450;line-height:37px}:host::before{margin-left:0;margin-right:0;margin-top:5px;margin-bottom:5px;-webkit-transition:160ms opacity ease-in-out;transition:160ms opacity ease-in-out;-webkit-transition-delay:100ms;transition-delay:100ms;border-left:var(--border-width) var(--border-style) var(--border-color);content:\\\"\\\";opacity:1;will-change:opacity}:host(:first-of-type)::before{border-left-color:transparent}:host(.segment-button-disabled){opacity:0.3}::slotted(ion-icon){font-size:24px}:host(.segment-button-layout-icon-start) ::slotted(ion-label){-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:0;margin-inline-end:0}:host(.segment-button-layout-icon-end) ::slotted(ion-label){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:2px;margin-inline-end:2px}.segment-button-indicator{-webkit-padding-start:2px;padding-inline-start:2px;-webkit-padding-end:2px;padding-inline-end:2px;left:0;right:0;top:0;bottom:0}.segment-button-indicator-background{border-radius:var(--border-radius);background:var(--indicator-color)}.segment-button-indicator-background{-webkit-transition:var(--indicator-transition);transition:var(--indicator-transition)}:host(.segment-button-checked)::before,:host(.segment-button-after-checked)::before{opacity:0}:host(.segment-button-checked){z-index:-1}:host(.segment-button-activated){--indicator-transform:scale(0.95)}:host(.ion-focused) .button-native{opacity:0.7}@media (any-hover: hover){:host(:hover) .button-native{opacity:0.5}:host(.segment-button-checked:hover) .button-native{opacity:1}}:host(.in-segment-color){background:none;color:var(--ion-text-color, #000)}:host(.in-segment-color) .segment-button-indicator-background{background:var(--ion-color-step-350, var(--ion-background-color, #fff))}@media (any-hover: hover){:host(.in-segment-color:hover) .button-native,:host(.in-segment-color.segment-button-checked:hover) .button-native{color:var(--ion-text-color, #000)}}:host(.in-toolbar:not(.in-segment-color)){--background-checked:var(--ion-toolbar-segment-background-checked, none);--color:var(--ion-toolbar-segment-color, var(--ion-toolbar-color), initial);--color-checked:var(--ion-toolbar-segment-color-checked, var(--ion-toolbar-color), initial);--indicator-color:var(--ion-toolbar-segment-indicator-color, var(--ion-color-step-350, var(--ion-background-color, #fff)))}:host(.in-toolbar-color) .segment-button-indicator-background{background:var(--ion-color-contrast)}:host(.in-toolbar-color:not(.in-segment-color)) .button-native{color:var(--ion-color-contrast)}:host(.in-toolbar-color.segment-button-checked:not(.in-segment-color)) .button-native{color:var(--ion-color-base)}@media (any-hover: hover){:host(.in-toolbar-color:not(.in-segment-color):hover) .button-native{color:var(--ion-color-contrast)}:host(.in-toolbar-color.segment-button-checked:not(.in-segment-color):hover) .button-native{color:var(--ion-color-base)}}\";\n\nconst segmentButtonMdCss = \":host{--color:initial;--color-hover:var(--color);--color-checked:var(--color);--color-disabled:var(--color);--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:column;flex-direction:column;height:auto;background:var(--background);color:var(--color);text-decoration:none;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;grid-row:1;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;min-width:inherit;max-width:inherit;height:auto;min-height:inherit;max-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border:none;outline:none;background:transparent;contain:content;pointer-events:none;overflow:hidden;z-index:2}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:inherit;flex-flow:inherit;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}:host(.segment-button-checked){background:var(--background-checked);color:var(--color-checked)}:host(.segment-button-disabled){cursor:default;pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(:focus){outline:none}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}:host(.segment-button-checked:hover) .button-native{color:var(--color-checked)}}::slotted(ion-icon){-ms-flex-negative:0;flex-shrink:0;-ms-flex-order:-1;order:-1;pointer-events:none}::slotted(ion-label){display:block;-ms-flex-item-align:center;align-self:center;max-width:100%;line-height:22px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host(.segment-button-layout-icon-top) .button-native{-ms-flex-direction:column;flex-direction:column}:host(.segment-button-layout-icon-start) .button-native{-ms-flex-direction:row;flex-direction:row}:host(.segment-button-layout-icon-end) .button-native{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.segment-button-layout-icon-bottom) .button-native{-ms-flex-direction:column-reverse;flex-direction:column-reverse}:host(.segment-button-layout-icon-hide) ::slotted(ion-icon){display:none}:host(.segment-button-layout-label-hide) ::slotted(ion-label){display:none}ion-ripple-effect{color:var(--ripple-color, var(--color-checked))}.segment-button-indicator{-webkit-transform-origin:left;transform-origin:left;position:absolute;opacity:0;-webkit-box-sizing:border-box;box-sizing:border-box;will-change:transform, opacity;pointer-events:none}.segment-button-indicator-background{width:100%;height:var(--indicator-height);-webkit-transform:var(--indicator-transform);transform:var(--indicator-transform);-webkit-box-shadow:var(--indicator-box-shadow);box-shadow:var(--indicator-box-shadow);pointer-events:none}.segment-button-indicator-animated{-webkit-transition:var(--indicator-transition);transition:var(--indicator-transition)}:host(.segment-button-checked) .segment-button-indicator{opacity:1}@media (prefers-reduced-motion: reduce){.segment-button-indicator-background{-webkit-transform:none;transform:none}.segment-button-indicator-animated{-webkit-transition:none;transition:none}}:host{--background:none;--background-checked:none;--background-hover:var(--color-checked);--background-focused:var(--color-checked);--background-activated-opacity:0;--background-focused-opacity:.12;--background-hover-opacity:.04;--color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.6);--color-checked:var(--ion-color-primary, #3880ff);--indicator-box-shadow:none;--indicator-color:var(--color-checked);--indicator-height:2px;--indicator-transition:transform 250ms cubic-bezier(0.4, 0, 0.2, 1);--indicator-transform:none;--padding-top:0;--padding-end:16px;--padding-bottom:0;--padding-start:16px;--transition:color 0.15s linear 0s, opacity 0.15s linear 0s;min-width:90px;min-height:48px;border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);font-size:14px;font-weight:500;letter-spacing:0.06em;line-height:40px;text-transform:uppercase}:host(.segment-button-disabled){opacity:0.3}:host(.in-segment-color){background:none;color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.6)}:host(.in-segment-color) ion-ripple-effect{color:var(--ion-color-base)}:host(.in-segment-color) .segment-button-indicator-background{background:var(--ion-color-base)}:host(.in-segment-color.segment-button-checked) .button-native{color:var(--ion-color-base)}:host(.in-segment-color.ion-focused) .button-native::after{background:var(--ion-color-base)}@media (any-hover: hover){:host(.in-segment-color:hover) .button-native{color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.6)}:host(.in-segment-color:hover) .button-native::after{background:var(--ion-color-base)}:host(.in-segment-color.segment-button-checked:hover) .button-native{color:var(--ion-color-base)}}:host(.in-toolbar:not(.in-segment-color)){--background:var(--ion-toolbar-segment-background, none);--background-checked:var(--ion-toolbar-segment-background-checked, none);--color:var(--ion-toolbar-segment-color, rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.6));--color-checked:var(--ion-toolbar-segment-color-checked, var(--ion-color-primary, #3880ff));--indicator-color:var(--ion-toolbar-segment-color-checked, var(--color-checked))}:host(.in-toolbar-color:not(.in-segment-color)) .button-native{color:rgba(var(--ion-color-contrast-rgb), 0.6)}:host(.in-toolbar-color.segment-button-checked:not(.in-segment-color)) .button-native{color:var(--ion-color-contrast)}@media (any-hover: hover){:host(.in-toolbar-color:not(.in-segment-color)) .button-native::after{background:var(--ion-color-contrast)}}::slotted(ion-icon){margin-top:12px;margin-bottom:12px;font-size:24px}::slotted(ion-label){margin-top:12px;margin-bottom:12px}:host(.segment-button-layout-icon-top) ::slotted(ion-label),:host(.segment-button-layout-icon-bottom) ::slotted(ion-icon){margin-top:0}:host(.segment-button-layout-icon-top) ::slotted(ion-icon),:host(.segment-button-layout-icon-bottom) ::slotted(ion-label){margin-bottom:0}:host(.segment-button-layout-icon-start) ::slotted(ion-label){-webkit-margin-start:8px;margin-inline-start:8px;-webkit-margin-end:0;margin-inline-end:0}:host(.segment-button-layout-icon-end) ::slotted(ion-label){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:8px;margin-inline-end:8px}:host(.segment-button-has-icon-only) ::slotted(ion-icon){margin-top:12px;margin-bottom:12px}:host(.segment-button-has-label-only) ::slotted(ion-label){margin-top:12px;margin-bottom:12px}.segment-button-indicator{left:0;right:0;bottom:0}.segment-button-indicator-background{background:var(--indicator-color)}:host(.in-toolbar:not(.in-segment-color)) .segment-button-indicator-background{background:var(--ion-toolbar-segment-indicator-color, var(--indicator-color))}:host(.in-toolbar-color:not(.in-segment-color)) .segment-button-indicator-background{background:var(--ion-color-contrast)}\";\n\nlet ids = 0;\nconst SegmentButton = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.segmentEl = null;\n    this.inheritedAttributes = {};\n    this.updateStyle = () => {\n      forceUpdate(this);\n    };\n    this.updateState = () => {\n      const { segmentEl } = this;\n      if (segmentEl) {\n        this.checked = segmentEl.value === this.value;\n        if (segmentEl.disabled) {\n          this.disabled = true;\n        }\n      }\n    };\n    this.checked = false;\n    this.disabled = false;\n    this.layout = 'icon-top';\n    this.type = 'button';\n    this.value = 'ion-sb-' + ids++;\n  }\n  valueChanged() {\n    this.updateState();\n  }\n  connectedCallback() {\n    const segmentEl = (this.segmentEl = this.el.closest('ion-segment'));\n    if (segmentEl) {\n      this.updateState();\n      addEventListener(segmentEl, 'ionSelect', this.updateState);\n      addEventListener(segmentEl, 'ionStyle', this.updateStyle);\n    }\n  }\n  disconnectedCallback() {\n    const segmentEl = this.segmentEl;\n    if (segmentEl) {\n      removeEventListener(segmentEl, 'ionSelect', this.updateState);\n      removeEventListener(segmentEl, 'ionStyle', this.updateStyle);\n      this.segmentEl = null;\n    }\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = Object.assign({}, inheritAttributes(this.el, ['aria-label']));\n  }\n  get hasLabel() {\n    return !!this.el.querySelector('ion-label');\n  }\n  get hasIcon() {\n    return !!this.el.querySelector('ion-icon');\n  }\n  /**\n   * @internal\n   * Focuses the native <button> element\n   * inside of ion-segment-button.\n   */\n  async setFocus() {\n    const { nativeEl } = this;\n    if (nativeEl !== undefined) {\n      nativeEl.focus();\n    }\n  }\n  render() {\n    const { checked, type, disabled, hasIcon, hasLabel, layout, segmentEl } = this;\n    const mode = getIonMode(this);\n    const hasSegmentColor = () => (segmentEl === null || segmentEl === void 0 ? void 0 : segmentEl.color) !== undefined;\n    return (h(Host, { class: {\n        [mode]: true,\n        'in-toolbar': hostContext('ion-toolbar', this.el),\n        'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n        'in-segment': hostContext('ion-segment', this.el),\n        'in-segment-color': hasSegmentColor(),\n        'segment-button-has-label': hasLabel,\n        'segment-button-has-icon': hasIcon,\n        'segment-button-has-label-only': hasLabel && !hasIcon,\n        'segment-button-has-icon-only': hasIcon && !hasLabel,\n        'segment-button-disabled': disabled,\n        'segment-button-checked': checked,\n        [`segment-button-layout-${layout}`]: true,\n        'ion-activatable': true,\n        'ion-activatable-instant': true,\n        'ion-focusable': true,\n      } }, h(\"button\", Object.assign({ \"aria-selected\": checked ? 'true' : 'false', role: \"tab\", ref: (el) => (this.nativeEl = el), type: type, class: \"button-native\", part: \"native\", disabled: disabled }, this.inheritedAttributes), h(\"span\", { class: \"button-inner\" }, h(\"slot\", null)), mode === 'md' && h(\"ion-ripple-effect\", null)), h(\"div\", { part: \"indicator\", class: {\n        'segment-button-indicator': true,\n        'segment-button-indicator-animated': true,\n      } }, h(\"div\", { part: \"indicator-background\", class: \"segment-button-indicator-background\" }))));\n  }\n  get el() { return getElement(this); }\n  static get watchers() { return {\n    \"value\": [\"valueChanged\"]\n  }; }\n};\nSegmentButton.style = {\n  ios: segmentButtonIosCss,\n  md: segmentButtonMdCss\n};\n\nexport { Segment as ion_segment, SegmentButton as ion_segment_button };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst hostContext = (selector, el) => {\n  return el.closest(selector) !== null;\n};\n/**\n * Create the mode and color classes for the component based on the classes passed in\n */\nconst createColorClasses = (color, cssClassMap) => {\n  return typeof color === 'string' && color.length > 0\n    ? Object.assign({ 'ion-color': true, [`ion-color-${color}`]: true }, cssClassMap) : cssClassMap;\n};\nconst getClassList = (classes) => {\n  if (classes !== undefined) {\n    const array = Array.isArray(classes) ? classes : classes.split(' ');\n    return array\n      .filter((c) => c != null)\n      .map((c) => c.trim())\n      .filter((c) => c !== '');\n  }\n  return [];\n};\nconst getClassMap = (classes) => {\n  const map = {};\n  getClassList(classes).forEach((c) => (map[c] = true));\n  return map;\n};\nconst SCHEME = /^[a-z][a-z0-9+\\-.]*:/;\nconst openURL = async (url, ev, direction, animation) => {\n  if (url != null && url[0] !== '#' && !SCHEME.test(url)) {\n    const router = document.querySelector('ion-router');\n    if (router) {\n      if (ev != null) {\n        ev.preventDefault();\n      }\n      return router.push(url, direction, animation);\n    }\n  }\n  return false;\n};\n\nexport { createColorClasses as c, getClassMap as g, hostContext as h, openURL as o };\n"], "names": ["r", "registerInstance", "d", "createEvent", "w", "writeTask", "h", "H", "Host", "f", "getElement", "i", "forceUpdate", "isRTL", "c", "createColorClasses", "hostContext", "b", "getIonMode", "a", "addEventListener", "removeEventListener", "k", "inheritAttributes", "segmentIosCss", "segmentMdCss", "Segment", "constructor", "hostRef", "ionChange", "ionSelect", "ionStyle", "onClick", "ev", "current", "target", "previous", "checked", "tagName", "value", "emitValueChange", "scrollable", "swipeGesture", "checkButton", "setCheckedClasses", "getSegmentButton", "selector", "_a", "_b", "buttons", "getButtons", "filter", "button", "disabled", "currIndex", "findIndex", "document", "activeElement", "length", "activated", "color", "undefined", "selectOnFocus", "colorChanged", "oldValue", "emitStyle", "swipeGestureChanged", "gestureChanged", "valueChanged", "emit", "activeButton", "find", "scrollIntoView", "behavior", "inline", "block", "disabled<PERSON><PERSON>ed", "gesture", "enable", "connectedCallback", "componentWillLoad", "componentDidLoad", "_this", "_asyncToGenerator", "createGesture", "el", "<PERSON><PERSON><PERSON>", "gesturePriority", "threshold", "passive", "onStart", "onMove", "onEnd", "detail", "valueBeforeGesture", "activate", "setNextIndex", "setActivated", "event", "stopImmediatePropagation", "Array", "from", "querySelectorAll", "for<PERSON>ach", "classList", "add", "remove", "clicked", "getIndicator", "root", "shadowRoot", "querySelector", "previousIndicator", "currentIndicator", "previousClientRect", "getBoundingClientRect", "currentClientRect", "<PERSON><PERSON><PERSON><PERSON>", "width", "xPosition", "left", "transform", "style", "setProperty", "index", "next", "isEnd", "rtl", "nextIndex", "rect", "currentX", "previousY", "top", "height", "getRootNode", "nextEl", "elementFromPoint", "decreaseIndex", "increaseIndex", "newIndex", "segment", "onKeyDown", "keyDownSelectsButton", "key", "preventDefault", "setFocus", "render", "mode", "role", "class", "watchers", "ios", "md", "segmentButtonIosCss", "segmentButtonMdCss", "ids", "SegmentButton", "segmentEl", "inheritedAttributes", "updateStyle", "updateState", "layout", "type", "closest", "disconnectedCallback", "Object", "assign", "<PERSON><PERSON><PERSON><PERSON>", "hasIcon", "_this2", "nativeEl", "focus", "hasSegmentColor", "ref", "part", "ion_segment", "ion_segment_button", "cssClassMap", "getClassList", "classes", "array", "isArray", "split", "map", "trim", "getClassMap", "SCHEME", "openURL", "_ref", "url", "direction", "animation", "test", "router", "push", "_x", "_x2", "_x3", "_x4", "apply", "arguments", "g", "o"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0, 1]}