"use strict";
(self["webpackChunkapp"] = self["webpackChunkapp"] || []).push([["node_modules_ionicons_dist_esm-es5_ionicons_js"],{

/***/ 88186:
/*!********************************************************!*\
  !*** ./node_modules/ionicons/dist/esm-es5/ionicons.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _index_5514a13d_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index-5514a13d.js */ 58160);

var patchBrowser = function () {
  var r = "file:///D:/apps/altwijry/family-social/node_modules/ionicons/dist/esm-es5/ionicons.js";
  var o = {};
  if (r !== "") {
    o.resourcesUrl = new URL(".", r).href;
  }
  return (0,_index_5514a13d_js__WEBPACK_IMPORTED_MODULE_0__.p)(o);
};
patchBrowser().then(function (r) {
  return (0,_index_5514a13d_js__WEBPACK_IMPORTED_MODULE_0__.b)([["ion-icon", [[1, "ion-icon", {
    mode: [1025],
    color: [1],
    ios: [1],
    md: [1],
    flipRtl: [4, "flip-rtl"],
    name: [513],
    src: [1],
    icon: [8],
    size: [1],
    lazy: [4],
    sanitize: [4],
    svgContent: [32],
    isVisible: [32],
    ariaLabel: [32]
  }]]]], r);
});

/***/ })

}]);
//# sourceMappingURL=node_modules_ionicons_dist_esm-es5_ionicons_js.js.map