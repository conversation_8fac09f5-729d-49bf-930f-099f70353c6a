{"version": 3, "file": "node_modules_ionicons_dist_esm-es5_ionicons_js.js", "mappings": ";;;;;;;;;;;AAAwE;AAAA,IAAII,YAAY,GAAC,SAAAA,CAAA,EAAU;EAAC,IAAIC,CAAC,GAACC,uFAAe;EAAC,IAAIG,CAAC,GAAC,CAAC,CAAC;EAAC,IAAGJ,CAAC,KAAG,EAAE,EAAC;IAACI,CAAC,CAACC,YAAY,GAAC,IAAIC,GAAG,CAAC,GAAG,EAACN,CAAC,CAAC,CAACO,IAAI;EAAA;EAAC,OAAOX,qDAAc,CAACQ,CAAC,CAAC;AAAA,CAAC;AAACL,YAAY,CAAC,CAAC,CAACS,IAAI,CAAE,UAASR,CAAC,EAAC;EAAC,OAAOF,qDAAa,CAAC,CAAC,CAAC,UAAU,EAAC,CAAC,CAAC,CAAC,EAAC,UAAU,EAAC;IAACW,IAAI,EAAC,CAAC,IAAI,CAAC;IAACC,KAAK,EAAC,CAAC,CAAC,CAAC;IAACC,GAAG,EAAC,CAAC,CAAC,CAAC;IAACC,EAAE,EAAC,CAAC,CAAC,CAAC;IAACC,OAAO,EAAC,CAAC,CAAC,EAAC,UAAU,CAAC;IAACC,IAAI,EAAC,CAAC,GAAG,CAAC;IAACC,GAAG,EAAC,CAAC,CAAC,CAAC;IAACC,IAAI,EAAC,CAAC,CAAC,CAAC;IAACC,IAAI,EAAC,CAAC,CAAC,CAAC;IAACC,IAAI,EAAC,CAAC,CAAC,CAAC;IAACC,QAAQ,EAAC,CAAC,CAAC,CAAC;IAACC,UAAU,EAAC,CAAC,EAAE,CAAC;IAACC,SAAS,EAAC,CAAC,EAAE,CAAC;IAACC,SAAS,EAAC,CAAC,EAAE;EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAACtB,CAAC,CAAC;AAAA,CAAE,CAAC", "sources": ["./node_modules/ionicons/dist/esm-es5/ionicons.js"], "sourcesContent": ["import{p as promiseResolve,b as bootstrapLazy}from\"./index-5514a13d.js\";var patchBrowser=function(){var r=import.meta.url;var o={};if(r!==\"\"){o.resourcesUrl=new URL(\".\",r).href}return promiseResolve(o)};patchBrowser().then((function(r){return bootstrapLazy([[\"ion-icon\",[[1,\"ion-icon\",{mode:[1025],color:[1],ios:[1],md:[1],flipRtl:[4,\"flip-rtl\"],name:[513],src:[1],icon:[8],size:[1],lazy:[4],sanitize:[4],svgContent:[32],isVisible:[32],ariaLabel:[32]}]]]],r)}));"], "names": ["p", "promiseResolve", "b", "bootstrapLazy", "patchBrowser", "r", "import", "meta", "url", "o", "resourcesUrl", "URL", "href", "then", "mode", "color", "ios", "md", "flipRtl", "name", "src", "icon", "size", "lazy", "sanitize", "svgContent", "isVisible", "aria<PERSON><PERSON><PERSON>"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}