{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-reorder_2_entry_js.js", "mappings": ";;;;;;;;;;;;;;AAAA;AACA;AACA;AAC+C;AAE/C,MAAME,YAAY,GAAGA,CAAA,KAAM;EACzB,IAAID,iDAAG,KAAKE,SAAS,EAAE;IACrB,OAAOF,iDAAG,CAACG,SAAS;EACtB;EACA,OAAOD,SAAS;AAClB,CAAC;;;;;;;;;;;;;;;;;;;;;ACVD;AACA;AACA;AAC4D;AAE5D,IAAIG,WAAW;AACf,CAAC,UAAUA,WAAW,EAAE;EACtB;AACF;AACA;AACA;AACA;EACEA,WAAW,CAAC,OAAO,CAAC,GAAG,OAAO;EAC9B;AACF;AACA;AACA;AACA;EACEA,WAAW,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAChC;AACF;AACA;AACA;AACA;EACEA,WAAW,CAAC,OAAO,CAAC,GAAG,OAAO;AAChC,CAAC,EAAEA,WAAW,KAAKA,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;AACrC,IAAIC,gBAAgB;AACpB,CAAC,UAAUA,gBAAgB,EAAE;EAC3B;AACF;AACA;AACA;AACA;EACEA,gBAAgB,CAAC,SAAS,CAAC,GAAG,SAAS;EACvC;AACF;AACA;AACA;AACA;EACEA,gBAAgB,CAAC,SAAS,CAAC,GAAG,SAAS;EACvC;AACF;AACA;AACA;AACA;EACEA,gBAAgB,CAAC,OAAO,CAAC,GAAG,OAAO;AACrC,CAAC,EAAEA,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C,MAAMC,YAAY,GAAG;EACnBC,SAASA,CAAA,EAAG;IACV,MAAMC,YAAY,GAAGC,MAAM,CAACC,YAAY;IACxC,IAAIF,YAAY,EAAE;MAChB;MACA;MACA,OAAOA,YAAY;IACrB;IACA,MAAMG,SAAS,GAAGX,yDAAY,CAAC,CAAC;IAChC,IAAIW,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACC,iBAAiB,CAAC,SAAS,CAAC,EAAE;MAChG;MACA,OAAOD,SAAS,CAACE,OAAO,CAACC,OAAO;IAClC;IACA,OAAOb,SAAS;EAClB,CAAC;EACDc,SAASA,CAAA,EAAG;IACV,MAAMC,MAAM,GAAG,IAAI,CAACT,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACS,MAAM,EAAE;MACX,OAAO,KAAK;IACd;IACA,MAAML,SAAS,GAAGX,yDAAY,CAAC,CAAC;IAChC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACW,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACM,WAAW,CAAC,CAAC,MAAM,KAAK,EAAE;MAC7F,OAAO,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACC,OAAO,KAAKlB,SAAS;IAC5E;IACA,OAAO,IAAI;EACb,CAAC;EACDmB,SAASA,CAAA,EAAG;IACV,OAAOX,MAAM,CAACC,YAAY,KAAKT,SAAS;EAC1C,CAAC;EACDoB,WAAWA,CAAA,EAAG;IACZ,OAAOrB,yDAAY,CAAC,CAAC,KAAKC,SAAS;EACrC,CAAC;EACDqB,MAAMA,CAACC,OAAO,EAAE;IACd,MAAMP,MAAM,GAAG,IAAI,CAACT,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACS,MAAM,EAAE;MACX;IACF;IACA;AACJ;AACA;AACA;AACA;AACA;IACI,MAAMQ,KAAK,GAAG,IAAI,CAACH,WAAW,CAAC,CAAC,GAAGE,OAAO,CAACC,KAAK,GAAGD,OAAO,CAACC,KAAK,CAACC,WAAW,CAAC,CAAC;IAC9ET,MAAM,CAACM,MAAM,CAAC;MAAEE;IAAM,CAAC,CAAC;EAC1B,CAAC;EACDE,YAAYA,CAACH,OAAO,EAAE;IACpB,MAAMP,MAAM,GAAG,IAAI,CAACT,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACS,MAAM,EAAE;MACX;IACF;IACA;AACJ;AACA;AACA;AACA;AACA;IACI,MAAMW,IAAI,GAAG,IAAI,CAACN,WAAW,CAAC,CAAC,GAAGE,OAAO,CAACI,IAAI,GAAGJ,OAAO,CAACI,IAAI,CAACF,WAAW,CAAC,CAAC;IAC3ET,MAAM,CAACU,YAAY,CAAC;MAAEC;IAAK,CAAC,CAAC;EAC/B,CAAC;EACDC,SAASA,CAAA,EAAG;IACV;AACJ;AACA;AACA;AACA;AACA;IACI,MAAMJ,KAAK,GAAG,IAAI,CAACH,WAAW,CAAC,CAAC,GAAGjB,WAAW,CAACyB,KAAK,GAAG,OAAO;IAC9D,IAAI,CAACP,MAAM,CAAC;MAAEE;IAAM,CAAC,CAAC;EACxB,CAAC;EACDM,cAAcA,CAAA,EAAG;IACf,MAAMd,MAAM,GAAG,IAAI,CAACT,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACS,MAAM,EAAE;MACX;IACF;IACA,IAAI,IAAI,CAACK,WAAW,CAAC,CAAC,EAAE;MACtBL,MAAM,CAACc,cAAc,CAAC,CAAC;IACzB,CAAC,MACI;MACHd,MAAM,CAACe,qBAAqB,CAAC,CAAC;IAChC;EACF,CAAC;EACDC,gBAAgBA,CAAA,EAAG;IACjB,MAAMhB,MAAM,GAAG,IAAI,CAACT,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACS,MAAM,EAAE;MACX;IACF;IACA,IAAI,IAAI,CAACK,WAAW,CAAC,CAAC,EAAE;MACtBL,MAAM,CAACgB,gBAAgB,CAAC,CAAC;IAC3B,CAAC,MACI;MACHhB,MAAM,CAACiB,uBAAuB,CAAC,CAAC;IAClC;EACF,CAAC;EACDC,YAAYA,CAAA,EAAG;IACb,MAAMlB,MAAM,GAAG,IAAI,CAACT,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACS,MAAM,EAAE;MACX;IACF;IACA,IAAI,IAAI,CAACK,WAAW,CAAC,CAAC,EAAE;MACtBL,MAAM,CAACkB,YAAY,CAAC,CAAC;IACvB,CAAC,MACI;MACHlB,MAAM,CAACmB,mBAAmB,CAAC,CAAC;IAC9B;EACF;AACF,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC5B,OAAO9B,YAAY,CAACS,SAAS,CAAC,CAAC;AACjC,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMsB,eAAe,GAAGA,CAAA,KAAM;EAC5BD,eAAe,CAAC,CAAC,IAAI9B,YAAY,CAACsB,SAAS,CAAC,CAAC;AAC/C,CAAC;AACD;AACA;AACA;AACA,MAAMU,oBAAoB,GAAGA,CAAA,KAAM;EACjCF,eAAe,CAAC,CAAC,IAAI9B,YAAY,CAACwB,cAAc,CAAC,CAAC;AACpD,CAAC;AACD;AACA;AACA;AACA,MAAMS,sBAAsB,GAAGA,CAAA,KAAM;EACnCH,eAAe,CAAC,CAAC,IAAI9B,YAAY,CAAC0B,gBAAgB,CAAC,CAAC;AACtD,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMQ,kBAAkB,GAAGA,CAAA,KAAM;EAC/BJ,eAAe,CAAC,CAAC,IAAI9B,YAAY,CAAC4B,YAAY,CAAC,CAAC;AAClD,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMO,YAAY,GAAIlB,OAAO,IAAK;EAChCa,eAAe,CAAC,CAAC,IAAI9B,YAAY,CAACgB,MAAM,CAACC,OAAO,CAAC;AACnD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1MD;AACA;AACA;AAC6G;AACxB;AACxB;AAC2B;AACvC;AACsE;AAC1F;AACI;AACJ;AAE7B,MAAMwC,aAAa,GAAG,2IAA2I;AAEjK,MAAMC,YAAY,GAAG,2IAA2I;AAEhK,MAAMC,OAAO,GAAG,MAAM;EACpBC,WAAWA,CAACC,OAAO,EAAE;IACnBlB,qDAAgB,CAAC,IAAI,EAAEkB,OAAO,CAAC;EACjC;EACAC,OAAOA,CAACC,EAAE,EAAE;IACV,MAAMC,YAAY,GAAG,IAAI,CAACC,EAAE,CAACC,OAAO,CAAC,mBAAmB,CAAC;IACzDH,EAAE,CAACI,cAAc,CAAC,CAAC;IACnB;IACA;IACA,IAAI,CAACH,YAAY,IAAI,CAACA,YAAY,CAACI,QAAQ,EAAE;MAC3CL,EAAE,CAACM,wBAAwB,CAAC,CAAC;IAC/B;EACF;EACAC,MAAMA,CAAA,EAAG;IACP,MAAMC,IAAI,GAAGlB,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAMmB,WAAW,GAAGD,IAAI,KAAK,KAAK,GAAGrB,iDAAmB,GAAGE,iDAAe;IAC1E,OAAQX,qDAAC,CAACI,iDAAI,EAAE;MAAE4B,KAAK,EAAEF;IAAK,CAAC,EAAE9B,qDAAC,CAAC,MAAM,EAAE,IAAI,EAAEA,qDAAC,CAAC,UAAU,EAAE;MAAEiC,IAAI,EAAEF,WAAW;MAAEG,IAAI,EAAE,KAAK;MAAEF,KAAK,EAAE,cAAc;MAAEG,IAAI,EAAE,MAAM;MAAE,aAAa,EAAE;IAAO,CAAC,CAAC,CAAC,CAAC;EAClK;EACA,IAAIX,EAAEA,CAAA,EAAG;IAAE,OAAOlB,qDAAU,CAAC,IAAI,CAAC;EAAE;AACtC,CAAC;AACDY,OAAO,CAACzC,KAAK,GAAG;EACd2D,GAAG,EAAEpB,aAAa;EAClBqB,EAAE,EAAEpB;AACN,CAAC;AAED,MAAMqB,eAAe,GAAG,m2BAAm2B;AAE33B,MAAMC,YAAY,GAAG,MAAM;EACzBpB,WAAWA,CAACC,OAAO,EAAE;IACnBlB,qDAAgB,CAAC,IAAI,EAAEkB,OAAO,CAAC;IAC/B,IAAI,CAACoB,cAAc,GAAGjC,qDAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAACkC,WAAW,GAAG,CAAC,CAAC;IACrB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACrB,QAAQ,GAAG,IAAI;EACtB;EACAsB,eAAeA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACC,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACC,MAAM,CAAC,CAAC,IAAI,CAACxB,QAAQ,CAAC;IACrC;EACF;EACMyB,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,8KAAA;MACxB,MAAMC,SAAS,GAAG1C,qDAAqB,CAACwC,KAAI,CAAC7B,EAAE,CAAC;MAChD,IAAI+B,SAAS,EAAE;QACbF,KAAI,CAACG,QAAQ,SAAS1C,qDAAgB,CAACyC,SAAS,CAAC;MACnD;MACAF,KAAI,CAACH,OAAO,GAAG,OAAO,sHAA6B,EAAEO,aAAa,CAAC;QACjEjC,EAAE,EAAE6B,KAAI,CAAC7B,EAAE;QACXkC,WAAW,EAAE,SAAS;QACtBC,eAAe,EAAE,GAAG;QACpBC,SAAS,EAAE,CAAC;QACZC,SAAS,EAAE,GAAG;QACdC,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAGC,MAAM,IAAKX,KAAI,CAACU,QAAQ,CAACC,MAAM,CAAC;QAC3CC,OAAO,EAAG3C,EAAE,IAAK+B,KAAI,CAACY,OAAO,CAAC3C,EAAE,CAAC;QACjC4C,MAAM,EAAG5C,EAAE,IAAK+B,KAAI,CAACa,MAAM,CAAC5C,EAAE,CAAC;QAC/B6C,KAAK,EAAEA,CAAA,KAAMd,KAAI,CAACc,KAAK,CAAC;MAC1B,CAAC,CAAC;MACFd,KAAI,CAACJ,eAAe,CAAC,CAAC;IAAC;EACzB;EACAmB,oBAAoBA,CAAA,EAAG;IACrB,IAAI,CAACD,KAAK,CAAC,CAAC;IACZ,IAAI,IAAI,CAACjB,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACmB,OAAO,CAAC,CAAC;MACtB,IAAI,CAACnB,OAAO,GAAGhG,SAAS;IAC1B;EACF;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEoH,QAAQA,CAACC,aAAa,EAAE;IACtB,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAI,CAACC,eAAe,CAACH,aAAa,CAAC,CAAC;EAC7D;EACAR,QAAQA,CAACzC,EAAE,EAAE;IACX,IAAI,IAAI,CAACqD,cAAc,IAAI,IAAI,CAAC3B,KAAK,KAAK,CAAC,CAAC,8BAA8B;MACxE,OAAO,KAAK;IACd;IACA,MAAM4B,MAAM,GAAGtD,EAAE,CAACuD,KAAK,CAACD,MAAM;IAC9B,MAAME,SAAS,GAAGF,MAAM,CAACnD,OAAO,CAAC,aAAa,CAAC;IAC/C,IAAI,CAACqD,SAAS,EAAE;MACd,OAAO,KAAK;IACd;IACA,MAAMC,IAAI,GAAGC,eAAe,CAACF,SAAS,EAAE,IAAI,CAACtD,EAAE,CAAC;IAChD,IAAI,CAACuD,IAAI,EAAE;MACT,OAAO,KAAK;IACd;IACAzD,EAAE,CAAC2D,IAAI,GAAGF,IAAI;IACd,OAAO,IAAI;EACb;EACAd,OAAOA,CAAC3C,EAAE,EAAE;IACVA,EAAE,CAACuD,KAAK,CAACnD,cAAc,CAAC,CAAC;IACzB,MAAMqD,IAAI,GAAI,IAAI,CAACJ,cAAc,GAAGrD,EAAE,CAAC2D,IAAK;IAC5C,MAAMC,OAAO,GAAG,IAAI,CAACxC,aAAa;IAClCwC,OAAO,CAACC,MAAM,GAAG,CAAC;IAClB,MAAM3D,EAAE,GAAG,IAAI,CAACA,EAAE;IAClB,MAAM4D,QAAQ,GAAG5D,EAAE,CAAC4D,QAAQ;IAC5B,IAAI,CAACA,QAAQ,IAAIA,QAAQ,CAACD,MAAM,KAAK,CAAC,EAAE;MACtC;IACF;IACA,IAAIE,GAAG,GAAG,CAAC;IACX,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAACD,MAAM,EAAEG,CAAC,EAAE,EAAE;MACxC,MAAMC,KAAK,GAAGH,QAAQ,CAACE,CAAC,CAAC;MACzBD,GAAG,IAAIE,KAAK,CAACC,YAAY;MACzBN,OAAO,CAACO,IAAI,CAACJ,GAAG,CAAC;MACjBE,KAAK,CAACG,SAAS,GAAGJ,CAAC;IACrB;IACA,MAAMK,GAAG,GAAGnE,EAAE,CAACoE,qBAAqB,CAAC,CAAC;IACtC,IAAI,CAAC9C,YAAY,GAAG6C,GAAG,CAACE,GAAG;IAC3B,IAAI,CAAC9C,eAAe,GAAG4C,GAAG,CAACG,MAAM;IACjC,IAAI,IAAI,CAACtC,QAAQ,EAAE;MACjB,MAAMuC,SAAS,GAAG,IAAI,CAACvC,QAAQ,CAACoC,qBAAqB,CAAC,CAAC;MACvD,IAAI,CAAC/C,eAAe,GAAG,IAAI,CAACW,QAAQ,CAACwC,SAAS;MAC9C,IAAI,CAACrD,WAAW,GAAGoD,SAAS,CAACF,GAAG,GAAGI,kBAAkB;MACrD,IAAI,CAACrD,cAAc,GAAGmD,SAAS,CAACD,MAAM,GAAGG,kBAAkB;IAC7D,CAAC,MACI;MACH,IAAI,CAACpD,eAAe,GAAG,CAAC;MACxB,IAAI,CAACF,WAAW,GAAG,CAAC;MACpB,IAAI,CAACC,cAAc,GAAG,CAAC;IACzB;IACA,IAAI,CAACH,WAAW,GAAGyD,YAAY,CAACnB,IAAI,CAAC;IACrC,IAAI,CAACoB,kBAAkB,GAAGpB,IAAI,CAACS,YAAY;IAC3C,IAAI,CAACxC,KAAK,GAAG,CAAC,CAAC;IACf+B,IAAI,CAACqB,SAAS,CAACC,GAAG,CAACC,qBAAqB,CAAC;IACzC/G,sDAAoB,CAAC,CAAC;EACxB;EACA2E,MAAMA,CAAC5C,EAAE,EAAE;IACT,MAAMiF,YAAY,GAAG,IAAI,CAAC5B,cAAc;IACxC,IAAI,CAAC4B,YAAY,EAAE;MACjB;IACF;IACA;IACA,MAAMC,MAAM,GAAG,IAAI,CAACC,UAAU,CAACnF,EAAE,CAACoF,QAAQ,CAAC;IAC3C;IACA,MAAMb,GAAG,GAAG,IAAI,CAAC/C,YAAY,GAAG0D,MAAM;IACtC,MAAMV,MAAM,GAAG,IAAI,CAAC/C,eAAe,GAAGyD,MAAM;IAC5C,MAAME,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACf,GAAG,EAAEc,IAAI,CAACE,GAAG,CAACvF,EAAE,CAACoF,QAAQ,EAAEZ,MAAM,CAAC,CAAC;IAC7D,MAAMgB,MAAM,GAAGN,MAAM,GAAGE,QAAQ,GAAGpF,EAAE,CAACyF,MAAM;IAC5C,MAAMC,WAAW,GAAGN,QAAQ,GAAGb,GAAG;IAClC,MAAMoB,OAAO,GAAG,IAAI,CAACC,eAAe,CAACF,WAAW,CAAC;IACjD,IAAIC,OAAO,KAAK,IAAI,CAACxE,WAAW,EAAE;MAChC,MAAM0E,SAAS,GAAGjB,YAAY,CAACK,YAAY,CAAC;MAC5C,IAAI,CAAC9D,WAAW,GAAGwE,OAAO;MAC1BzH,sDAAsB,CAAC,CAAC;MACxB,IAAI,CAAC4H,WAAW,CAACD,SAAS,EAAEF,OAAO,CAAC;IACtC;IACA;IACAV,YAAY,CAAC9H,KAAK,CAAC4I,SAAS,GAAI,cAAaP,MAAO,KAAI;EAC1D;EACA3C,KAAKA,CAAA,EAAG;IACN,MAAMQ,cAAc,GAAG,IAAI,CAACA,cAAc;IAC1C,IAAI,CAAC3B,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAAC2B,cAAc,EAAE;MACnB,IAAI,CAAC3B,KAAK,GAAG,CAAC,CAAC;MACf;IACF;IACA,MAAMiE,OAAO,GAAG,IAAI,CAACxE,WAAW;IAChC,MAAM0E,SAAS,GAAGjB,YAAY,CAACvB,cAAc,CAAC;IAC9C,IAAIsC,OAAO,KAAKE,SAAS,EAAE;MACzB,IAAI,CAACzC,eAAe,CAAC,CAAC;IACxB,CAAC,MACI;MACH,IAAI,CAAClC,cAAc,CAAC8E,IAAI,CAAC;QACvBC,IAAI,EAAEJ,SAAS;QACfK,EAAE,EAAEP,OAAO;QACX3C,QAAQ,EAAE,IAAI,CAACI,eAAe,CAAC+C,IAAI,CAAC,IAAI;MAC1C,CAAC,CAAC;IACJ;IACAhI,sDAAkB,CAAC,CAAC;EACtB;EACAiF,eAAeA,CAACH,aAAa,EAAE;IAC7B,MAAMI,cAAc,GAAG,IAAI,CAACA,cAAc;IAC1C,IAAIA,cAAc,IAAI,IAAI,CAAC3B,KAAK,KAAK,CAAC,CAAC,kCAAkC;MACvE,MAAMoC,QAAQ,GAAG,IAAI,CAAC5D,EAAE,CAAC4D,QAAQ;MACjC,MAAMsC,GAAG,GAAGtC,QAAQ,CAACD,MAAM;MAC3B,MAAM8B,OAAO,GAAG,IAAI,CAACxE,WAAW;MAChC,MAAM0E,SAAS,GAAGjB,YAAY,CAACvB,cAAc,CAAC;MAC9C;AACN;AACA;AACA;AACA;AACA;AACA;MACM5D,uDAAG,CAAC,MAAM;QACR,IAAIkG,OAAO,KAAKE,SAAS,KAAK5C,aAAa,KAAKrH,SAAS,IAAIqH,aAAa,KAAK,IAAI,CAAC,EAAE;UACpF,MAAMoD,GAAG,GAAGR,SAAS,GAAGF,OAAO,GAAG7B,QAAQ,CAAC6B,OAAO,GAAG,CAAC,CAAC,GAAG7B,QAAQ,CAAC6B,OAAO,CAAC;UAC3E,IAAI,CAACzF,EAAE,CAACoG,YAAY,CAACjD,cAAc,EAAEgD,GAAG,CAAC;QAC3C;QACA,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoC,GAAG,EAAEpC,CAAC,EAAE,EAAE;UAC5BF,QAAQ,CAACE,CAAC,CAAC,CAAC7G,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE;QACrC;MACF,CAAC,CAAC;MACF,IAAIoJ,KAAK,CAACC,OAAO,CAACvD,aAAa,CAAC,EAAE;QAChCA,aAAa,GAAGwD,YAAY,CAACxD,aAAa,EAAE4C,SAAS,EAAEF,OAAO,CAAC;MACjE;MACAtC,cAAc,CAAClG,KAAK,CAACuJ,UAAU,GAAG,EAAE;MACpCrD,cAAc,CAACyB,SAAS,CAAC6B,MAAM,CAAC3B,qBAAqB,CAAC;MACtD,IAAI,CAAC3B,cAAc,GAAGzH,SAAS;MAC/B,IAAI,CAAC8F,KAAK,GAAG,CAAC,CAAC;IACjB;;IACA,OAAOuB,aAAa;EACtB;EACA2C,eAAeA,CAACJ,MAAM,EAAE;IACtB,MAAM5B,OAAO,GAAG,IAAI,CAACxC,aAAa;IAClC,KAAK,IAAI4C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,OAAO,CAACC,MAAM,EAAEG,CAAC,EAAE,EAAE;MACvC,IAAIJ,OAAO,CAACI,CAAC,CAAC,GAAGwB,MAAM,EAAE;QACvB,OAAOxB,CAAC;MACV;IACF;IACA,OAAOJ,OAAO,CAACC,MAAM,GAAG,CAAC;EAC3B;EACA;EACAiC,WAAWA,CAACD,SAAS,EAAEF,OAAO,EAAE;IAC9B,MAAMiB,UAAU,GAAG,IAAI,CAAC/B,kBAAkB;IAC1C,MAAMf,QAAQ,GAAG,IAAI,CAAC5D,EAAE,CAAC4D,QAAQ;IACjC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAACD,MAAM,EAAEG,CAAC,EAAE,EAAE;MACxC,MAAM7G,KAAK,GAAG2G,QAAQ,CAACE,CAAC,CAAC,CAAC7G,KAAK;MAC/B,IAAI0J,KAAK,GAAG,EAAE;MACd,IAAI7C,CAAC,GAAG6B,SAAS,IAAI7B,CAAC,IAAI2B,OAAO,EAAE;QACjCkB,KAAK,GAAI,cAAa,CAACD,UAAW,KAAI;MACxC,CAAC,MACI,IAAI5C,CAAC,GAAG6B,SAAS,IAAI7B,CAAC,IAAI2B,OAAO,EAAE;QACtCkB,KAAK,GAAI,cAAaD,UAAW,KAAI;MACvC;MACAzJ,KAAK,CAAC,WAAW,CAAC,GAAG0J,KAAK;IAC5B;EACF;EACA1B,UAAUA,CAAC2B,IAAI,EAAE;IACf,IAAI,CAAC,IAAI,CAAC5E,QAAQ,EAAE;MAClB,OAAO,CAAC;IACV;IACA,IAAI6E,MAAM,GAAG,CAAC;IACd,IAAID,IAAI,GAAG,IAAI,CAACzF,WAAW,EAAE;MAC3B0F,MAAM,GAAG,CAACC,WAAW;IACvB,CAAC,MACI,IAAIF,IAAI,GAAG,IAAI,CAACxF,cAAc,EAAE;MACnCyF,MAAM,GAAGC,WAAW;IACtB;IACA,IAAID,MAAM,KAAK,CAAC,EAAE;MAChB,IAAI,CAAC7E,QAAQ,CAAC+E,QAAQ,CAAC,CAAC,EAAEF,MAAM,CAAC;IACnC;IACA,OAAO,IAAI,CAAC7E,QAAQ,CAACwC,SAAS,GAAG,IAAI,CAACnD,eAAe;EACvD;EACAhB,MAAMA,CAAA,EAAG;IACP,MAAMC,IAAI,GAAGlB,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQZ,qDAAC,CAACI,iDAAI,EAAE;MAAE4B,KAAK,EAAE;QACrB,CAACF,IAAI,GAAG,IAAI;QACZ,iBAAiB,EAAE,CAAC,IAAI,CAACH,QAAQ;QACjC,qBAAqB,EAAE,IAAI,CAACqB,KAAK,KAAK,CAAC,CAAC;MAC1C;IAAE,CAAC,CAAC;EACR;;EACA,IAAIxB,EAAEA,CAAA,EAAG;IAAE,OAAOlB,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWkI,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC7B,UAAU,EAAE,CAAC,iBAAiB;IAChC,CAAC;EAAE;AACL,CAAC;AACD,MAAMtC,YAAY,GAAIuC,OAAO,IAAK;EAChC,OAAOA,OAAO,CAAC,WAAW,CAAC;AAC7B,CAAC;AACD,MAAMzD,eAAe,GAAGA,CAAC0D,IAAI,EAAEC,SAAS,KAAK;EAC3C,IAAIC,MAAM;EACV,OAAOF,IAAI,EAAE;IACXE,MAAM,GAAGF,IAAI,CAACG,aAAa;IAC3B,IAAID,MAAM,KAAKD,SAAS,EAAE;MACxB,OAAOD,IAAI;IACb;IACAA,IAAI,GAAGE,MAAM;EACf;EACA,OAAO1L,SAAS;AAClB,CAAC;AACD,MAAM+I,kBAAkB,GAAG,EAAE;AAC7B,MAAMqC,WAAW,GAAG,EAAE;AACtB,MAAMhC,qBAAqB,GAAG,kBAAkB;AAChD,MAAMyB,YAAY,GAAGA,CAACe,KAAK,EAAEvB,IAAI,EAAEC,EAAE,KAAK;EACxC,MAAMiB,OAAO,GAAGK,KAAK,CAACvB,IAAI,CAAC;EAC3BuB,KAAK,CAACC,MAAM,CAACxB,IAAI,EAAE,CAAC,CAAC;EACrBuB,KAAK,CAACC,MAAM,CAACvB,EAAE,EAAE,CAAC,EAAEiB,OAAO,CAAC;EAC5B,OAAOK,KAAK,CAACE,KAAK,CAAC,CAAC;AACtB,CAAC;AACDzG,YAAY,CAAC9D,KAAK,GAAG6D,eAAe", "sources": ["./node_modules/@ionic/core/dist/esm/capacitor-b4979570.js", "./node_modules/@ionic/core/dist/esm/haptic-6447af60.js", "./node_modules/@ionic/core/dist/esm/ion-reorder_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index-7a14ecec.js';\n\nconst getCapacitor = () => {\n  if (win !== undefined) {\n    return win.Capacitor;\n  }\n  return undefined;\n};\n\nexport { getCapacitor as g };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { g as getCapacitor } from './capacitor-b4979570.js';\n\nvar ImpactStyle;\n(function (ImpactStyle) {\n  /**\n   * A collision between large, heavy user interface elements\n   *\n   * @since 1.0.0\n   */\n  ImpactStyle[\"Heavy\"] = \"HEAVY\";\n  /**\n   * A collision between moderately sized user interface elements\n   *\n   * @since 1.0.0\n   */\n  ImpactStyle[\"Medium\"] = \"MEDIUM\";\n  /**\n   * A collision between small, light user interface elements\n   *\n   * @since 1.0.0\n   */\n  ImpactStyle[\"Light\"] = \"LIGHT\";\n})(ImpactStyle || (ImpactStyle = {}));\nvar NotificationType;\n(function (NotificationType) {\n  /**\n   * A notification feedback type indicating that a task has completed successfully\n   *\n   * @since 1.0.0\n   */\n  NotificationType[\"Success\"] = \"SUCCESS\";\n  /**\n   * A notification feedback type indicating that a task has produced a warning\n   *\n   * @since 1.0.0\n   */\n  NotificationType[\"Warning\"] = \"WARNING\";\n  /**\n   * A notification feedback type indicating that a task has failed\n   *\n   * @since 1.0.0\n   */\n  NotificationType[\"Error\"] = \"ERROR\";\n})(NotificationType || (NotificationType = {}));\nconst HapticEngine = {\n  getEngine() {\n    const tapticEngine = window.TapticEngine;\n    if (tapticEngine) {\n      // Cordova\n      // TODO FW-4707 - Remove this in Ionic 8\n      return tapticEngine;\n    }\n    const capacitor = getCapacitor();\n    if (capacitor === null || capacitor === void 0 ? void 0 : capacitor.isPluginAvailable('Haptics')) {\n      // Capacitor\n      return capacitor.Plugins.Haptics;\n    }\n    return undefined;\n  },\n  available() {\n    const engine = this.getEngine();\n    if (!engine) {\n      return false;\n    }\n    const capacitor = getCapacitor();\n    /**\n     * Developers can manually import the\n     * Haptics plugin in their app which will cause\n     * getEngine to return the Haptics engine. However,\n     * the Haptics engine will throw an error if\n     * used in a web browser that does not support\n     * the Vibrate API. This check avoids that error\n     * if the browser does not support the Vibrate API.\n     */\n    if ((capacitor === null || capacitor === void 0 ? void 0 : capacitor.getPlatform()) === 'web') {\n      return typeof navigator !== 'undefined' && navigator.vibrate !== undefined;\n    }\n    return true;\n  },\n  isCordova() {\n    return window.TapticEngine !== undefined;\n  },\n  isCapacitor() {\n    return getCapacitor() !== undefined;\n  },\n  impact(options) {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    /**\n     * To provide backwards compatibility with Cordova apps,\n     * we convert the style to lowercase.\n     *\n     * TODO: FW-4707 - Remove this in Ionic 8\n     */\n    const style = this.isCapacitor() ? options.style : options.style.toLowerCase();\n    engine.impact({ style });\n  },\n  notification(options) {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    /**\n     * To provide backwards compatibility with Cordova apps,\n     * we convert the style to lowercase.\n     *\n     * TODO: FW-4707 - Remove this in Ionic 8\n     */\n    const type = this.isCapacitor() ? options.type : options.type.toLowerCase();\n    engine.notification({ type });\n  },\n  selection() {\n    /**\n     * To provide backwards compatibility with Cordova apps,\n     * we convert the style to lowercase.\n     *\n     * TODO: FW-4707 - Remove this in Ionic 8\n     */\n    const style = this.isCapacitor() ? ImpactStyle.Light : 'light';\n    this.impact({ style });\n  },\n  selectionStart() {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    if (this.isCapacitor()) {\n      engine.selectionStart();\n    }\n    else {\n      engine.gestureSelectionStart();\n    }\n  },\n  selectionChanged() {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    if (this.isCapacitor()) {\n      engine.selectionChanged();\n    }\n    else {\n      engine.gestureSelectionChanged();\n    }\n  },\n  selectionEnd() {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    if (this.isCapacitor()) {\n      engine.selectionEnd();\n    }\n    else {\n      engine.gestureSelectionEnd();\n    }\n  },\n};\n/**\n * Check to see if the Haptic Plugin is available\n * @return Returns `true` or false if the plugin is available\n */\nconst hapticAvailable = () => {\n  return HapticEngine.available();\n};\n/**\n * Trigger a selection changed haptic event. Good for one-time events\n * (not for gestures)\n */\nconst hapticSelection = () => {\n  hapticAvailable() && HapticEngine.selection();\n};\n/**\n * Tell the haptic engine that a gesture for a selection change is starting.\n */\nconst hapticSelectionStart = () => {\n  hapticAvailable() && HapticEngine.selectionStart();\n};\n/**\n * Tell the haptic engine that a selection changed during a gesture.\n */\nconst hapticSelectionChanged = () => {\n  hapticAvailable() && HapticEngine.selectionChanged();\n};\n/**\n * Tell the haptic engine we are done with a gesture. This needs to be\n * called lest resources are not properly recycled.\n */\nconst hapticSelectionEnd = () => {\n  hapticAvailable() && HapticEngine.selectionEnd();\n};\n/**\n * Use this to indicate success/failure/warning to the user.\n * options should be of the type `{ style: ImpactStyle.LIGHT }` (or `MEDIUM`/`HEAVY`)\n */\nconst hapticImpact = (options) => {\n  hapticAvailable() && HapticEngine.impact(options);\n};\n\nexport { ImpactStyle as I, hapticSelectionStart as a, hapticSelectionChanged as b, hapticSelection as c, hapticImpact as d, hapticSelectionEnd as h };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, H as Host, f as getElement, d as createEvent } from './index-2d388930.js';\nimport { j as reorderThreeOutline, k as reorderTwoSharp } from './index-ecfc2c9f.js';\nimport { b as getIonMode } from './ionic-global-b3fc28dd.js';\nimport { f as findClosestIonContent, g as getScrollElement } from './index-746a238e.js';\nimport { r as raf } from './helpers-3379ba19.js';\nimport { a as hapticSelectionStart, b as hapticSelectionChanged, h as hapticSelectionEnd } from './haptic-6447af60.js';\nimport './index-595d62c9.js';\nimport './capacitor-b4979570.js';\nimport './index-7a14ecec.js';\n\nconst reorderIosCss = \":host([slot]){display:none;line-height:0;z-index:100}.reorder-icon{display:block;font-size:22px}.reorder-icon{font-size:34px;opacity:0.4}\";\n\nconst reorderMdCss = \":host([slot]){display:none;line-height:0;z-index:100}.reorder-icon{display:block;font-size:22px}.reorder-icon{font-size:31px;opacity:0.3}\";\n\nconst Reorder = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n  }\n  onClick(ev) {\n    const reorderGroup = this.el.closest('ion-reorder-group');\n    ev.preventDefault();\n    // Only stop event propagation if the reorder is inside of an enabled\n    // reorder group. This allows interaction with clickable children components.\n    if (!reorderGroup || !reorderGroup.disabled) {\n      ev.stopImmediatePropagation();\n    }\n  }\n  render() {\n    const mode = getIonMode(this);\n    const reorderIcon = mode === 'ios' ? reorderThreeOutline : reorderTwoSharp;\n    return (h(Host, { class: mode }, h(\"slot\", null, h(\"ion-icon\", { icon: reorderIcon, lazy: false, class: \"reorder-icon\", part: \"icon\", \"aria-hidden\": \"true\" }))));\n  }\n  get el() { return getElement(this); }\n};\nReorder.style = {\n  ios: reorderIosCss,\n  md: reorderMdCss\n};\n\nconst reorderGroupCss = \".reorder-list-active>*{display:block;-webkit-transition:-webkit-transform 300ms;transition:-webkit-transform 300ms;transition:transform 300ms;transition:transform 300ms, -webkit-transform 300ms;will-change:transform}.reorder-enabled{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.reorder-enabled ion-reorder{display:block;cursor:-webkit-grab;cursor:grab;pointer-events:all;-ms-touch-action:none;touch-action:none}.reorder-selected,.reorder-selected ion-reorder{cursor:-webkit-grabbing;cursor:grabbing}.reorder-selected{position:relative;-webkit-transition:none !important;transition:none !important;-webkit-box-shadow:0 0 10px rgba(0, 0, 0, 0.4);box-shadow:0 0 10px rgba(0, 0, 0, 0.4);opacity:0.8;z-index:100}.reorder-visible ion-reorder .reorder-icon{-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0)}\";\n\nconst ReorderGroup = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionItemReorder = createEvent(this, \"ionItemReorder\", 7);\n    this.lastToIndex = -1;\n    this.cachedHeights = [];\n    this.scrollElTop = 0;\n    this.scrollElBottom = 0;\n    this.scrollElInitial = 0;\n    this.containerTop = 0;\n    this.containerBottom = 0;\n    this.state = 0 /* ReorderGroupState.Idle */;\n    this.disabled = true;\n  }\n  disabledChanged() {\n    if (this.gesture) {\n      this.gesture.enable(!this.disabled);\n    }\n  }\n  async connectedCallback() {\n    const contentEl = findClosestIonContent(this.el);\n    if (contentEl) {\n      this.scrollEl = await getScrollElement(contentEl);\n    }\n    this.gesture = (await import('./index-ff313b19.js')).createGesture({\n      el: this.el,\n      gestureName: 'reorder',\n      gesturePriority: 110,\n      threshold: 0,\n      direction: 'y',\n      passive: false,\n      canStart: (detail) => this.canStart(detail),\n      onStart: (ev) => this.onStart(ev),\n      onMove: (ev) => this.onMove(ev),\n      onEnd: () => this.onEnd(),\n    });\n    this.disabledChanged();\n  }\n  disconnectedCallback() {\n    this.onEnd();\n    if (this.gesture) {\n      this.gesture.destroy();\n      this.gesture = undefined;\n    }\n  }\n  /**\n   * Completes the reorder operation. Must be called by the `ionItemReorder` event.\n   *\n   * If a list of items is passed, the list will be reordered and returned in the\n   * proper order.\n   *\n   * If no parameters are passed or if `true` is passed in, the reorder will complete\n   * and the item will remain in the position it was dragged to. If `false` is passed,\n   * the reorder will complete and the item will bounce back to its original position.\n   *\n   * @param listOrReorder A list of items to be sorted and returned in the new order or a\n   * boolean of whether or not the reorder should reposition the item.\n   */\n  complete(listOrReorder) {\n    return Promise.resolve(this.completeReorder(listOrReorder));\n  }\n  canStart(ev) {\n    if (this.selectedItemEl || this.state !== 0 /* ReorderGroupState.Idle */) {\n      return false;\n    }\n    const target = ev.event.target;\n    const reorderEl = target.closest('ion-reorder');\n    if (!reorderEl) {\n      return false;\n    }\n    const item = findReorderItem(reorderEl, this.el);\n    if (!item) {\n      return false;\n    }\n    ev.data = item;\n    return true;\n  }\n  onStart(ev) {\n    ev.event.preventDefault();\n    const item = (this.selectedItemEl = ev.data);\n    const heights = this.cachedHeights;\n    heights.length = 0;\n    const el = this.el;\n    const children = el.children;\n    if (!children || children.length === 0) {\n      return;\n    }\n    let sum = 0;\n    for (let i = 0; i < children.length; i++) {\n      const child = children[i];\n      sum += child.offsetHeight;\n      heights.push(sum);\n      child.$ionIndex = i;\n    }\n    const box = el.getBoundingClientRect();\n    this.containerTop = box.top;\n    this.containerBottom = box.bottom;\n    if (this.scrollEl) {\n      const scrollBox = this.scrollEl.getBoundingClientRect();\n      this.scrollElInitial = this.scrollEl.scrollTop;\n      this.scrollElTop = scrollBox.top + AUTO_SCROLL_MARGIN;\n      this.scrollElBottom = scrollBox.bottom - AUTO_SCROLL_MARGIN;\n    }\n    else {\n      this.scrollElInitial = 0;\n      this.scrollElTop = 0;\n      this.scrollElBottom = 0;\n    }\n    this.lastToIndex = indexForItem(item);\n    this.selectedItemHeight = item.offsetHeight;\n    this.state = 1 /* ReorderGroupState.Active */;\n    item.classList.add(ITEM_REORDER_SELECTED);\n    hapticSelectionStart();\n  }\n  onMove(ev) {\n    const selectedItem = this.selectedItemEl;\n    if (!selectedItem) {\n      return;\n    }\n    // Scroll if we reach the scroll margins\n    const scroll = this.autoscroll(ev.currentY);\n    // // Get coordinate\n    const top = this.containerTop - scroll;\n    const bottom = this.containerBottom - scroll;\n    const currentY = Math.max(top, Math.min(ev.currentY, bottom));\n    const deltaY = scroll + currentY - ev.startY;\n    const normalizedY = currentY - top;\n    const toIndex = this.itemIndexForTop(normalizedY);\n    if (toIndex !== this.lastToIndex) {\n      const fromIndex = indexForItem(selectedItem);\n      this.lastToIndex = toIndex;\n      hapticSelectionChanged();\n      this.reorderMove(fromIndex, toIndex);\n    }\n    // Update selected item position\n    selectedItem.style.transform = `translateY(${deltaY}px)`;\n  }\n  onEnd() {\n    const selectedItemEl = this.selectedItemEl;\n    this.state = 2 /* ReorderGroupState.Complete */;\n    if (!selectedItemEl) {\n      this.state = 0 /* ReorderGroupState.Idle */;\n      return;\n    }\n    const toIndex = this.lastToIndex;\n    const fromIndex = indexForItem(selectedItemEl);\n    if (toIndex === fromIndex) {\n      this.completeReorder();\n    }\n    else {\n      this.ionItemReorder.emit({\n        from: fromIndex,\n        to: toIndex,\n        complete: this.completeReorder.bind(this),\n      });\n    }\n    hapticSelectionEnd();\n  }\n  completeReorder(listOrReorder) {\n    const selectedItemEl = this.selectedItemEl;\n    if (selectedItemEl && this.state === 2 /* ReorderGroupState.Complete */) {\n      const children = this.el.children;\n      const len = children.length;\n      const toIndex = this.lastToIndex;\n      const fromIndex = indexForItem(selectedItemEl);\n      /**\n       * insertBefore and setting the transform\n       * needs to happen in the same frame otherwise\n       * there will be a duplicate transition. This primarily\n       * impacts Firefox where insertBefore and transform operations\n       * are happening in two separate frames.\n       */\n      raf(() => {\n        if (toIndex !== fromIndex && (listOrReorder === undefined || listOrReorder === true)) {\n          const ref = fromIndex < toIndex ? children[toIndex + 1] : children[toIndex];\n          this.el.insertBefore(selectedItemEl, ref);\n        }\n        for (let i = 0; i < len; i++) {\n          children[i].style['transform'] = '';\n        }\n      });\n      if (Array.isArray(listOrReorder)) {\n        listOrReorder = reorderArray(listOrReorder, fromIndex, toIndex);\n      }\n      selectedItemEl.style.transition = '';\n      selectedItemEl.classList.remove(ITEM_REORDER_SELECTED);\n      this.selectedItemEl = undefined;\n      this.state = 0 /* ReorderGroupState.Idle */;\n    }\n    return listOrReorder;\n  }\n  itemIndexForTop(deltaY) {\n    const heights = this.cachedHeights;\n    for (let i = 0; i < heights.length; i++) {\n      if (heights[i] > deltaY) {\n        return i;\n      }\n    }\n    return heights.length - 1;\n  }\n  /********* DOM WRITE ********* */\n  reorderMove(fromIndex, toIndex) {\n    const itemHeight = this.selectedItemHeight;\n    const children = this.el.children;\n    for (let i = 0; i < children.length; i++) {\n      const style = children[i].style;\n      let value = '';\n      if (i > fromIndex && i <= toIndex) {\n        value = `translateY(${-itemHeight}px)`;\n      }\n      else if (i < fromIndex && i >= toIndex) {\n        value = `translateY(${itemHeight}px)`;\n      }\n      style['transform'] = value;\n    }\n  }\n  autoscroll(posY) {\n    if (!this.scrollEl) {\n      return 0;\n    }\n    let amount = 0;\n    if (posY < this.scrollElTop) {\n      amount = -SCROLL_JUMP;\n    }\n    else if (posY > this.scrollElBottom) {\n      amount = SCROLL_JUMP;\n    }\n    if (amount !== 0) {\n      this.scrollEl.scrollBy(0, amount);\n    }\n    return this.scrollEl.scrollTop - this.scrollElInitial;\n  }\n  render() {\n    const mode = getIonMode(this);\n    return (h(Host, { class: {\n        [mode]: true,\n        'reorder-enabled': !this.disabled,\n        'reorder-list-active': this.state !== 0 /* ReorderGroupState.Idle */,\n      } }));\n  }\n  get el() { return getElement(this); }\n  static get watchers() { return {\n    \"disabled\": [\"disabledChanged\"]\n  }; }\n};\nconst indexForItem = (element) => {\n  return element['$ionIndex'];\n};\nconst findReorderItem = (node, container) => {\n  let parent;\n  while (node) {\n    parent = node.parentElement;\n    if (parent === container) {\n      return node;\n    }\n    node = parent;\n  }\n  return undefined;\n};\nconst AUTO_SCROLL_MARGIN = 60;\nconst SCROLL_JUMP = 10;\nconst ITEM_REORDER_SELECTED = 'reorder-selected';\nconst reorderArray = (array, from, to) => {\n  const element = array[from];\n  array.splice(from, 1);\n  array.splice(to, 0, element);\n  return array.slice();\n};\nReorderGroup.style = reorderGroupCss;\n\nexport { Reorder as ion_reorder, ReorderGroup as ion_reorder_group };\n"], "names": ["w", "win", "getCapacitor", "undefined", "Capacitor", "g", "ImpactStyle", "NotificationType", "HapticEngine", "getEngine", "tapticEngine", "window", "TapticEngine", "capacitor", "isPluginAvailable", "Plugins", "Haptics", "available", "engine", "getPlatform", "navigator", "vibrate", "<PERSON><PERSON><PERSON><PERSON>", "isCapacitor", "impact", "options", "style", "toLowerCase", "notification", "type", "selection", "Light", "selectionStart", "gestureSelectionStart", "selectionChanged", "gestureSelectionChanged", "selectionEnd", "gestureSelectionEnd", "hapticAvailable", "hapticSelection", "hapticSelectionStart", "hapticSelectionChanged", "hapticSelectionEnd", "hapticImpact", "I", "a", "b", "c", "d", "h", "r", "registerInstance", "H", "Host", "f", "getElement", "createEvent", "j", "reorderThreeOutline", "k", "reorderTwoSharp", "getIonMode", "findClosestIonContent", "getScrollElement", "raf", "reorderIosCss", "reorderMdCss", "Reorder", "constructor", "hostRef", "onClick", "ev", "reorderGroup", "el", "closest", "preventDefault", "disabled", "stopImmediatePropagation", "render", "mode", "reorderIcon", "class", "icon", "lazy", "part", "ios", "md", "reorderGroupCss", "ReorderGroup", "ionItemReorder", "lastToIndex", "cachedHeights", "scrollElTop", "scrollElBottom", "scrollElInitial", "containerTop", "containerBottom", "state", "disabled<PERSON><PERSON>ed", "gesture", "enable", "connectedCallback", "_this", "_asyncToGenerator", "contentEl", "scrollEl", "createGesture", "<PERSON><PERSON><PERSON>", "gesturePriority", "threshold", "direction", "passive", "canStart", "detail", "onStart", "onMove", "onEnd", "disconnectedCallback", "destroy", "complete", "listOr<PERSON>eorder", "Promise", "resolve", "completeReorder", "selectedItemEl", "target", "event", "reorderEl", "item", "findReorderItem", "data", "heights", "length", "children", "sum", "i", "child", "offsetHeight", "push", "$ionIndex", "box", "getBoundingClientRect", "top", "bottom", "scrollBox", "scrollTop", "AUTO_SCROLL_MARGIN", "indexForItem", "selectedItemHeight", "classList", "add", "ITEM_REORDER_SELECTED", "selectedItem", "scroll", "autoscroll", "currentY", "Math", "max", "min", "deltaY", "startY", "normalizedY", "toIndex", "itemIndexForTop", "fromIndex", "reorderMove", "transform", "emit", "from", "to", "bind", "len", "ref", "insertBefore", "Array", "isArray", "reorderArray", "transition", "remove", "itemHeight", "value", "posY", "amount", "SCROLL_JUMP", "scrollBy", "watchers", "element", "node", "container", "parent", "parentElement", "array", "splice", "slice", "ion_reorder", "ion_reorder_group"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0, 1, 2]}