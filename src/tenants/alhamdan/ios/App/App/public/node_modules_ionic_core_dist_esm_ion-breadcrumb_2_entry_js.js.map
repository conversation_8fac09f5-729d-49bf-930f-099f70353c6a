{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-breadcrumb_2_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC6G;AAC1C;AAC2B;AACJ;AAC7B;AAE7D,MAAMsB,gBAAgB,GAAG,wvHAAwvH;AAEjxH,MAAMC,eAAe,GAAG,gnHAAgnH;AAExoH,MAAMC,UAAU,GAAG,MAAM;EACvBC,WAAWA,CAACC,OAAO,EAAE;IACnBzB,qDAAgB,CAAC,IAAI,EAAEyB,OAAO,CAAC;IAC/B,IAAI,CAACC,QAAQ,GAAGxB,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACyB,OAAO,GAAGzB,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAAC0B,cAAc,GAAG1B,qDAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAAC2B,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,OAAO,GAAG,MAAM;MACnB,IAAI,CAACJ,QAAQ,CAACK,IAAI,CAAC,CAAC;IACtB,CAAC;IACD,IAAI,CAACC,MAAM,GAAG,MAAM;MAClB,IAAI,CAACL,OAAO,CAACI,IAAI,CAAC,CAAC;IACrB,CAAC;IACD,IAAI,CAACE,uBAAuB,GAAG,MAAM;MACnC,IAAI,CAACL,cAAc,CAACG,IAAI,CAAC;QAAEG,eAAe,EAAE,IAAI,CAACC;MAAa,CAAC,CAAC;IAClE,CAAC;IACD,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,IAAI,GAAGC,SAAS;IACrB,IAAI,CAACC,sBAAsB,GAAGD,SAAS;IACvC,IAAI,CAACE,KAAK,GAAGF,SAAS;IACtB,IAAI,CAACG,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAGL,SAAS;IACzB,IAAI,CAACM,IAAI,GAAGN,SAAS;IACrB,IAAI,CAACO,GAAG,GAAGP,SAAS;IACpB,IAAI,CAACQ,SAAS,GAAGR,SAAS;IAC1B,IAAI,CAACS,MAAM,GAAGT,SAAS;IACvB,IAAI,CAACU,eAAe,GAAG,SAAS;IAChC,IAAI,CAACC,eAAe,GAAGX,SAAS;EAClC;EACAY,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAACrB,mBAAmB,GAAGpB,uDAAqB,CAAC,IAAI,CAAC0C,EAAE,CAAC;EAC3D;EACAC,WAAWA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACR,IAAI,KAAKN,SAAS;EAChC;EACAe,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEb,KAAK;MAAEC,MAAM;MAAEL,SAAS;MAAEM,QAAQ;MAAEC,QAAQ;MAAEQ,EAAE;MAAEtB,mBAAmB;MAAEQ,IAAI;MAAEY,eAAe;MAAED,eAAe;MAAEF,SAAS;MAAEP,sBAAsB;MAAEQ;IAAQ,CAAC,GAAG,IAAI;IAC1K,MAAMO,SAAS,GAAG,IAAI,CAACF,WAAW,CAAC,CAAC;IACpC,MAAMG,OAAO,GAAG,IAAI,CAACX,IAAI,KAAKN,SAAS,GAAG,MAAM,GAAG,GAAG;IACtD;IACA;IACA,MAAMM,IAAI,GAAGF,QAAQ,GAAGJ,SAAS,GAAG,IAAI,CAACM,IAAI;IAC7C,MAAMY,IAAI,GAAGpC,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAMqC,KAAK,GAAGF,OAAO,KAAK,MAAM,GAC5B,CAAC,CAAC,GACF;MACAZ,QAAQ;MACRC,IAAI;MACJG;IACF,CAAC;IACH;IACA;IACA;IACA,MAAMW,aAAa,GAAGrB,IAAI,GAAG,KAAK,GAAGD,SAAS,GAAIG,sBAAsB,IAAI,CAACF,IAAI,GAAG,IAAI,GAAG,KAAK,GAAIS,SAAS;IAC7G,OAAQ3C,qDAAC,CAACE,iDAAI,EAAE;MAAEsD,OAAO,EAAGC,EAAE,IAAKjD,qDAAO,CAACiC,IAAI,EAAEgB,EAAE,EAAEZ,eAAe,EAAEC,eAAe,CAAC;MAAE,eAAe,EAAEP,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAEmB,KAAK,EAAEhD,qDAAkB,CAAC2B,KAAK,EAAE;QAChK,CAACgB,IAAI,GAAG,IAAI;QACZ,mBAAmB,EAAEf,MAAM;QAC3B,sBAAsB,EAAEL,SAAS;QACjC,qBAAqB,EAAEM,QAAQ;QAC/B,sBAAsB,EAAE5B,qDAAW,CAAC,wBAAwB,EAAEqC,EAAE,CAAC;QACjE,YAAY,EAAErC,qDAAW,CAAC,aAAa,EAAE,IAAI,CAACqC,EAAE,CAAC;QACjD,kBAAkB,EAAErC,qDAAW,CAAC,oBAAoB,EAAE,IAAI,CAACqC,EAAE,CAAC;QAC9D,iBAAiB,EAAEG,SAAS;QAC5B,eAAe,EAAEA;MACnB,CAAC;IAAE,CAAC,EAAEnD,qDAAC,CAACoD,OAAO,EAAEO,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEN,KAAK,EAAE;MAAEI,KAAK,EAAE,mBAAmB;MAAEG,IAAI,EAAE,QAAQ;MAAEtB,QAAQ,EAAEA,QAAQ;MAAEZ,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEE,MAAM,EAAE,IAAI,CAACA;IAAO,CAAC,EAAEH,mBAAmB,CAAC,EAAE1B,qDAAC,CAAC,MAAM,EAAE;MAAE8D,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAE9D,qDAAC,CAAC,MAAM,EAAE,IAAI,CAAC,EAAEA,qDAAC,CAAC,MAAM,EAAE;MAAE8D,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC,EAAE1B,sBAAsB,IAAKpC,qDAAC,CAAC,QAAQ,EAAE;MAAE6D,IAAI,EAAE,qBAAqB;MAAE,YAAY,EAAE,uBAAuB;MAAEL,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC1B,uBAAuB,CAAC,CAAC;MAAEiC,GAAG,EAAGC,WAAW,IAAM,IAAI,CAAChC,YAAY,GAAGgC,WAAY;MAAEN,KAAK,EAAE;QACtd,iCAAiC,EAAE;MACrC;IAAE,CAAC,EAAE1D,qDAAC,CAAC,UAAU,EAAE;MAAE,aAAa,EAAE,MAAM;MAAEiE,IAAI,EAAElD,iDAAkB;MAAEmD,IAAI,EAAE;IAAM,CAAC,CAAC,CAAE,EAAEX,aAAa;IACvG;AACJ;AACA;AACA;AACA;IACIvD,qDAAC,CAAC,MAAM,EAAE;MAAE0D,KAAK,EAAE,sBAAsB;MAAEG,IAAI,EAAE,WAAW;MAAE,aAAa,EAAE;IAAO,CAAC,EAAE7D,qDAAC,CAAC,MAAM,EAAE;MAAE8D,IAAI,EAAE;IAAY,CAAC,EAAET,IAAI,KAAK,KAAK,GAAIrD,qDAAC,CAAC,UAAU,EAAE;MAAEiE,IAAI,EAAEpD,iDAAqB;MAAEqD,IAAI,EAAE,KAAK;MAAE,UAAU,EAAE;IAAK,CAAC,CAAC,GAAKlE,qDAAC,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAE,CAAC,CAAE,CAAC;EACvP;EACA,IAAIgD,EAAEA,CAAA,EAAG;IAAE,OAAO5C,qDAAU,CAAC,IAAI,CAAC;EAAE;AACtC,CAAC;AACDgB,UAAU,CAAC+C,KAAK,GAAG;EACjBC,GAAG,EAAElD,gBAAgB;EACrBmD,EAAE,EAAElD;AACN,CAAC;AAED,MAAMmD,iBAAiB,GAAG,wmBAAwmB;AAEloB,MAAMC,gBAAgB,GAAG,wjBAAwjB;AAEjlB,MAAMC,WAAW,GAAG,MAAM;EACxBnD,WAAWA,CAACC,OAAO,EAAE;IACnBzB,qDAAgB,CAAC,IAAI,EAAEyB,OAAO,CAAC;IAC/B,IAAI,CAACmD,iBAAiB,GAAG1E,qDAAW,CAAC,IAAI,EAAE,mBAAmB,EAAE,CAAC,CAAC;IAClE,IAAI,CAAC2E,eAAe,GAAG,MAAM;MAC3B,IAAI,CAACC,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAACC,WAAW,CAAC,CAAC;IACpB,CAAC;IACD,IAAI,CAACC,qBAAqB,GAAG,MAAM;MACjC,MAAMC,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACzC;MACA;MACA,MAAMC,gBAAgB,GAAGF,WAAW,CAACG,IAAI,CAAEC,UAAU,IAAKA,UAAU,CAAC5C,MAAM,CAAC;MAC5E,IAAI0C,gBAAgB,IAAI,IAAI,CAACG,aAAa,EAAE;QAC1CH,gBAAgB,CAAC1C,MAAM,GAAG,KAAK;MACjC;IACF,CAAC;IACD,IAAI,CAACsC,WAAW,GAAG,MAAM;MACvB,MAAM;QAAEQ,kBAAkB;QAAEC,mBAAmB;QAAEC;MAAS,CAAC,GAAG,IAAI;MAClE,MAAMR,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACzC,KAAK,MAAMG,UAAU,IAAIJ,WAAW,EAAE;QACpCI,UAAU,CAAC9C,sBAAsB,GAAG,KAAK;QACzC8C,UAAU,CAACjD,SAAS,GAAG,KAAK;MAC9B;MACA;MACA;MACA;MACA,MAAMsD,cAAc,GAAGD,QAAQ,KAAKnD,SAAS,IAAI2C,WAAW,CAACU,MAAM,GAAGF,QAAQ,IAAID,mBAAmB,GAAGD,kBAAkB,IAAIE,QAAQ;MACtI,IAAIC,cAAc,EAAE;QAClB;QACAT,WAAW,CAACW,OAAO,CAAC,CAACP,UAAU,EAAEQ,KAAK,KAAK;UACzC,IAAIA,KAAK,KAAKL,mBAAmB,EAAE;YACjCH,UAAU,CAAC9C,sBAAsB,GAAG,IAAI;UAC1C;UACA;UACA;UACA;UACA,IAAIsD,KAAK,IAAIL,mBAAmB,IAAIK,KAAK,GAAGZ,WAAW,CAACU,MAAM,GAAGJ,kBAAkB,EAAE;YACnFF,UAAU,CAACjD,SAAS,GAAG,IAAI;UAC7B;QACF,CAAC,CAAC;MACJ;IACF,CAAC;IACD,IAAI,CAAC0C,sBAAsB,GAAG,MAAM;MAClC,MAAM;QAAES,kBAAkB;QAAEC,mBAAmB;QAAEC;MAAS,CAAC,GAAG,IAAI;MAClE,MAAMR,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACzC;MACA,MAAMzC,MAAM,GAAGwC,WAAW,CAACG,IAAI,CAAEC,UAAU,IAAKA,UAAU,CAAC5C,MAAM,CAAC;MAClE;MACA,KAAK,MAAM4C,UAAU,IAAIJ,WAAW,EAAE;QACpC;QACA;QACA;QACA,MAAM5C,IAAI,GAAGoD,QAAQ,KAAKnD,SAAS,IAAIiD,kBAAkB,KAAK,CAAC,GAC3DF,UAAU,KAAKJ,WAAW,CAACO,mBAAmB,CAAC,GAC/CH,UAAU,KAAKJ,WAAW,CAACA,WAAW,CAACU,MAAM,GAAG,CAAC,CAAC;QACtDN,UAAU,CAAChD,IAAI,GAAGA,IAAI;QACtB;QACA;QACA;QACA,MAAMS,SAAS,GAAGuC,UAAU,CAACvC,SAAS,KAAKR,SAAS,GAAG+C,UAAU,CAACvC,SAAS,GAAGT,IAAI,GAAGC,SAAS,GAAG,IAAI;QACrG+C,UAAU,CAACvC,SAAS,GAAGA,SAAS;QAChC;QACA;QACA,IAAI,CAACL,MAAM,IAAIJ,IAAI,EAAE;UACnBgD,UAAU,CAAC5C,MAAM,GAAG,IAAI;UACxB,IAAI,CAAC6C,aAAa,GAAG,IAAI;QAC3B;MACF;IACF,CAAC;IACD,IAAI,CAACJ,cAAc,GAAG,MAAM;MAC1B,OAAOY,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC5C,EAAE,CAAC6C,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;IAC/D,CAAC;IACD,IAAI,CAACC,WAAW,GAAG,MAAM;MACvB,IAAI,CAACjB,qBAAqB,CAAC,CAAC;MAC5B,IAAI,CAACH,eAAe,CAAC,CAAC;IACxB,CAAC;IACD,IAAI,CAACzC,SAAS,GAAGE,SAAS;IAC1B,IAAI,CAACgD,aAAa,GAAGhD,SAAS;IAC9B,IAAI,CAACE,KAAK,GAAGF,SAAS;IACtB,IAAI,CAACmD,QAAQ,GAAGnD,SAAS;IACzB,IAAI,CAACkD,mBAAmB,GAAG,CAAC;IAC5B,IAAI,CAACD,kBAAkB,GAAG,CAAC;EAC7B;EACAW,gBAAgBA,CAACtC,EAAE,EAAE;IACnB,MAAMqB,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACzC,MAAMiB,oBAAoB,GAAGlB,WAAW,CAACmB,MAAM,CAAEf,UAAU,IAAKA,UAAU,CAACjD,SAAS,CAAC;IACrF,IAAI,CAACwC,iBAAiB,CAAC7C,IAAI,CAAC+B,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,EAAE,CAACyC,MAAM,CAAC,EAAE;MAAEF;IAAqB,CAAC,CAAC,CAAC;EACpG;EACAG,eAAeA,CAAA,EAAG;IAChB,IAAI,CAACtB,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACH,eAAe,CAAC,CAAC;EACxB;EACA3B,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAAC2B,eAAe,CAAC,CAAC;EACxB;EACAxB,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEb,KAAK;MAAEJ;IAAU,CAAC,GAAG,IAAI;IACjC,MAAMoB,IAAI,GAAGpC,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQjB,qDAAC,CAACE,iDAAI,EAAE;MAAEwD,KAAK,EAAEhD,qDAAkB,CAAC2B,KAAK,EAAE;QAC/C,CAACgB,IAAI,GAAG,IAAI;QACZ,YAAY,EAAE1C,qDAAW,CAAC,aAAa,EAAE,IAAI,CAACqC,EAAE,CAAC;QACjD,kBAAkB,EAAErC,qDAAW,CAAC,oBAAoB,EAAE,IAAI,CAACqC,EAAE,CAAC;QAC9D,uBAAuB,EAAEf;MAC3B,CAAC;IAAE,CAAC,EAAEjC,qDAAC,CAAC,MAAM,EAAE;MAAEoG,YAAY,EAAE,IAAI,CAACN;IAAY,CAAC,CAAC,CAAC;EACxD;EACA,IAAI9C,EAAEA,CAAA,EAAG;IAAE,OAAO5C,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWiG,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC7B,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,qBAAqB,EAAE,CAAC,iBAAiB,CAAC;MAC1C,oBAAoB,EAAE,CAAC,iBAAiB;IAC1C,CAAC;EAAE;AACL,CAAC;AACD7B,WAAW,CAACL,KAAK,GAAG;EAClBC,GAAG,EAAEE,iBAAiB;EACtBD,EAAE,EAAEE;AACN,CAAC;;;;;;;;;;;;;;;;;;;;ACvND;AACA;AACA;AACA,MAAM5D,WAAW,GAAGA,CAAC6F,QAAQ,EAAExD,EAAE,KAAK;EACpC,OAAOA,EAAE,CAACyD,OAAO,CAACD,QAAQ,CAAC,KAAK,IAAI;AACtC,CAAC;AACD;AACA;AACA;AACA,MAAM9F,kBAAkB,GAAGA,CAAC2B,KAAK,EAAEqE,WAAW,KAAK;EACjD,OAAO,OAAOrE,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACmD,MAAM,GAAG,CAAC,GAChD7B,MAAM,CAACC,MAAM,CAAC;IAAE,WAAW,EAAE,IAAI;IAAE,CAAE,aAAYvB,KAAM,EAAC,GAAG;EAAK,CAAC,EAAEqE,WAAW,CAAC,GAAGA,WAAW;AACnG,CAAC;AACD,MAAMC,YAAY,GAAIC,OAAO,IAAK;EAChC,IAAIA,OAAO,KAAKzE,SAAS,EAAE;IACzB,MAAM0E,KAAK,GAAGlB,KAAK,CAACmB,OAAO,CAACF,OAAO,CAAC,GAAGA,OAAO,GAAGA,OAAO,CAACG,KAAK,CAAC,GAAG,CAAC;IACnE,OAAOF,KAAK,CACTZ,MAAM,CAAExF,CAAC,IAAKA,CAAC,IAAI,IAAI,CAAC,CACxBuG,GAAG,CAAEvG,CAAC,IAAKA,CAAC,CAACwG,IAAI,CAAC,CAAC,CAAC,CACpBhB,MAAM,CAAExF,CAAC,IAAKA,CAAC,KAAK,EAAE,CAAC;EAC5B;EACA,OAAO,EAAE;AACX,CAAC;AACD,MAAMyG,WAAW,GAAIN,OAAO,IAAK;EAC/B,MAAMI,GAAG,GAAG,CAAC,CAAC;EACdL,YAAY,CAACC,OAAO,CAAC,CAACnB,OAAO,CAAEhF,CAAC,IAAMuG,GAAG,CAACvG,CAAC,CAAC,GAAG,IAAK,CAAC;EACrD,OAAOuG,GAAG;AACZ,CAAC;AACD,MAAMG,MAAM,GAAG,sBAAsB;AACrC,MAAM3G,OAAO;EAAA,IAAA4G,IAAA,GAAAC,8KAAA,CAAG,WAAOC,GAAG,EAAE7D,EAAE,EAAE8D,SAAS,EAAEC,SAAS,EAAK;IACvD,IAAIF,GAAG,IAAI,IAAI,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAACH,MAAM,CAACM,IAAI,CAACH,GAAG,CAAC,EAAE;MACtD,MAAMI,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,YAAY,CAAC;MACnD,IAAIF,MAAM,EAAE;QACV,IAAIjE,EAAE,IAAI,IAAI,EAAE;UACdA,EAAE,CAACoE,cAAc,CAAC,CAAC;QACrB;QACA,OAAOH,MAAM,CAACI,IAAI,CAACR,GAAG,EAAEC,SAAS,EAAEC,SAAS,CAAC;MAC/C;IACF;IACA,OAAO,KAAK;EACd,CAAC;EAAA,gBAXKhH,OAAOA,CAAAuH,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAd,IAAA,CAAAe,KAAA,OAAAC,SAAA;EAAA;AAAA,GAWZ", "sources": ["./node_modules/@ionic/core/dist/esm/ion-breadcrumb_2.entry.js", "./node_modules/@ionic/core/dist/esm/theme-17531cdf.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement } from './index-2d388930.js';\nimport { i as inheritAriaAttributes } from './helpers-3379ba19.js';\nimport { o as openURL, c as createColorClasses, h as hostContext } from './theme-17531cdf.js';\nimport { m as chevronForwardOutline, n as ellipsisHorizontal } from './index-ecfc2c9f.js';\nimport { b as getIonMode } from './ionic-global-b3fc28dd.js';\n\nconst breadcrumbIosCss = \":host{display:-ms-flexbox;display:flex;-ms-flex:0 0 auto;flex:0 0 auto;-ms-flex-align:center;align-items:center;color:var(--color);font-size:16px;font-weight:400;line-height:1.5}.breadcrumb-native{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;width:100%;outline:none;background:inherit}:host(.breadcrumb-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.breadcrumb-active){color:var(--color-active)}:host(.ion-focused){color:var(--color-focused)}:host(.ion-focused) .breadcrumb-native{background:var(--background-focused)}@media (any-hover: hover){:host(.ion-activatable:hover){color:var(--color-hover)}:host(.ion-activatable.in-breadcrumbs-color:hover),:host(.ion-activatable.ion-color:hover){color:var(--ion-color-shade)}}.breadcrumb-separator{display:-ms-inline-flexbox;display:inline-flex}:host(.breadcrumb-collapsed) .breadcrumb-native{display:none}:host(.in-breadcrumbs-color),:host(.in-breadcrumbs-color.breadcrumb-active){color:var(--ion-color-base)}:host(.in-breadcrumbs-color) .breadcrumb-separator{color:var(--ion-color-base)}:host(.ion-color){color:var(--ion-color-base)}:host(.in-toolbar-color),:host(.in-toolbar-color) .breadcrumb-separator{color:rgba(var(--ion-color-contrast-rgb), 0.8)}:host(.in-toolbar-color.breadcrumb-active){color:var(--ion-color-contrast)}.breadcrumbs-collapsed-indicator{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;-webkit-margin-start:14px;margin-inline-start:14px;-webkit-margin-end:14px;margin-inline-end:14px;margin-top:0;margin-bottom:0;display:-ms-flexbox;display:flex;-ms-flex:1 1 100%;flex:1 1 100%;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:32px;height:18px;border:0;outline:none;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}.breadcrumbs-collapsed-indicator ion-icon{margin-top:1px;font-size:22px}:host{--color:var(--ion-color-step-850, #2d4665);--color-active:var(--ion-text-color, #03060b);--color-hover:var(--ion-text-color, #03060b);--color-focused:var(--color-active);--background-focused:var(--ion-color-step-50, rgba(233, 237, 243, 0.7))}:host(.breadcrumb-active){font-weight:600}.breadcrumb-native{border-radius:4px;-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:5px;padding-bottom:5px;border:1px solid transparent}:host(.ion-focused) .breadcrumb-native{border-radius:8px}:host(.in-breadcrumbs-color.ion-focused) .breadcrumb-native,:host(.ion-color.ion-focused) .breadcrumb-native{background:rgba(var(--ion-color-base-rgb), 0.1);color:var(--ion-color-base)}:host(.ion-focused) ::slotted(ion-icon),:host(.in-breadcrumbs-color.ion-focused) ::slotted(ion-icon),:host(.ion-color.ion-focused) ::slotted(ion-icon){color:var(--ion-color-step-750, #445b78)}.breadcrumb-separator{color:var(--ion-color-step-550, #73849a)}::slotted(ion-icon){color:var(--ion-color-step-400, #92a0b3);font-size:18px}::slotted(ion-icon[slot=start]){-webkit-margin-end:8px;margin-inline-end:8px}::slotted(ion-icon[slot=end]){-webkit-margin-start:8px;margin-inline-start:8px}:host(.breadcrumb-active) ::slotted(ion-icon){color:var(--ion-color-step-850, #242d39)}.breadcrumbs-collapsed-indicator{border-radius:4px;background:var(--ion-color-step-100, #e9edf3);color:var(--ion-color-step-550, #73849a)}.breadcrumbs-collapsed-indicator:hover{opacity:0.45}.breadcrumbs-collapsed-indicator:focus{background:var(--ion-color-step-150, #d9e0ea)}\";\n\nconst breadcrumbMdCss = \":host{display:-ms-flexbox;display:flex;-ms-flex:0 0 auto;flex:0 0 auto;-ms-flex-align:center;align-items:center;color:var(--color);font-size:16px;font-weight:400;line-height:1.5}.breadcrumb-native{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;width:100%;outline:none;background:inherit}:host(.breadcrumb-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.breadcrumb-active){color:var(--color-active)}:host(.ion-focused){color:var(--color-focused)}:host(.ion-focused) .breadcrumb-native{background:var(--background-focused)}@media (any-hover: hover){:host(.ion-activatable:hover){color:var(--color-hover)}:host(.ion-activatable.in-breadcrumbs-color:hover),:host(.ion-activatable.ion-color:hover){color:var(--ion-color-shade)}}.breadcrumb-separator{display:-ms-inline-flexbox;display:inline-flex}:host(.breadcrumb-collapsed) .breadcrumb-native{display:none}:host(.in-breadcrumbs-color),:host(.in-breadcrumbs-color.breadcrumb-active){color:var(--ion-color-base)}:host(.in-breadcrumbs-color) .breadcrumb-separator{color:var(--ion-color-base)}:host(.ion-color){color:var(--ion-color-base)}:host(.in-toolbar-color),:host(.in-toolbar-color) .breadcrumb-separator{color:rgba(var(--ion-color-contrast-rgb), 0.8)}:host(.in-toolbar-color.breadcrumb-active){color:var(--ion-color-contrast)}.breadcrumbs-collapsed-indicator{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;-webkit-margin-start:14px;margin-inline-start:14px;-webkit-margin-end:14px;margin-inline-end:14px;margin-top:0;margin-bottom:0;display:-ms-flexbox;display:flex;-ms-flex:1 1 100%;flex:1 1 100%;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:32px;height:18px;border:0;outline:none;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}.breadcrumbs-collapsed-indicator ion-icon{margin-top:1px;font-size:22px}:host{--color:var(--ion-color-step-600, #677483);--color-active:var(--ion-text-color, #03060b);--color-hover:var(--ion-text-color, #03060b);--color-focused:var(--ion-color-step-800, #35404e);--background-focused:var(--ion-color-step-50, #fff)}:host(.breadcrumb-active){font-weight:500}.breadcrumb-native{-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px}.breadcrumb-separator{-webkit-margin-start:10px;margin-inline-start:10px;-webkit-margin-end:10px;margin-inline-end:10px;margin-top:-1px}:host(.ion-focused) .breadcrumb-native{border-radius:4px;-webkit-box-shadow:0px 1px 2px rgba(0, 0, 0, 0.2), 0px 2px 8px rgba(0, 0, 0, 0.12);box-shadow:0px 1px 2px rgba(0, 0, 0, 0.2), 0px 2px 8px rgba(0, 0, 0, 0.12)}.breadcrumb-separator{color:var(--ion-color-step-550, #73849a)}::slotted(ion-icon){color:var(--ion-color-step-550, #7d8894);font-size:18px}::slotted(ion-icon[slot=start]){-webkit-margin-end:8px;margin-inline-end:8px}::slotted(ion-icon[slot=end]){-webkit-margin-start:8px;margin-inline-start:8px}:host(.breadcrumb-active) ::slotted(ion-icon){color:var(--ion-color-step-850, #222d3a)}.breadcrumbs-collapsed-indicator{border-radius:2px;background:var(--ion-color-step-100, #eef1f3);color:var(--ion-color-step-550, #73849a)}.breadcrumbs-collapsed-indicator:hover{opacity:0.7}.breadcrumbs-collapsed-indicator:focus{background:var(--ion-color-step-150, #dfe5e8)}\";\n\nconst Breadcrumb = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.collapsedClick = createEvent(this, \"collapsedClick\", 7);\n    this.inheritedAttributes = {};\n    this.onFocus = () => {\n      this.ionFocus.emit();\n    };\n    this.onBlur = () => {\n      this.ionBlur.emit();\n    };\n    this.collapsedIndicatorClick = () => {\n      this.collapsedClick.emit({ ionShadowTarget: this.collapsedRef });\n    };\n    this.collapsed = false;\n    this.last = undefined;\n    this.showCollapsedIndicator = undefined;\n    this.color = undefined;\n    this.active = false;\n    this.disabled = false;\n    this.download = undefined;\n    this.href = undefined;\n    this.rel = undefined;\n    this.separator = undefined;\n    this.target = undefined;\n    this.routerDirection = 'forward';\n    this.routerAnimation = undefined;\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAriaAttributes(this.el);\n  }\n  isClickable() {\n    return this.href !== undefined;\n  }\n  render() {\n    const { color, active, collapsed, disabled, download, el, inheritedAttributes, last, routerAnimation, routerDirection, separator, showCollapsedIndicator, target, } = this;\n    const clickable = this.isClickable();\n    const TagType = this.href === undefined ? 'span' : 'a';\n    // Links can still be tabbed to when set to disabled if they have an href\n    // in order to truly disable them we can keep it as an anchor but remove the href\n    const href = disabled ? undefined : this.href;\n    const mode = getIonMode(this);\n    const attrs = TagType === 'span'\n      ? {}\n      : {\n        download,\n        href,\n        target,\n      };\n    // If the breadcrumb is collapsed, check if it contains the collapsed indicator\n    // to show the separator as long as it isn't also the last breadcrumb\n    // otherwise if not collapsed use the value in separator\n    const showSeparator = last ? false : collapsed ? (showCollapsedIndicator && !last ? true : false) : separator;\n    return (h(Host, { onClick: (ev) => openURL(href, ev, routerDirection, routerAnimation), \"aria-disabled\": disabled ? 'true' : null, class: createColorClasses(color, {\n        [mode]: true,\n        'breadcrumb-active': active,\n        'breadcrumb-collapsed': collapsed,\n        'breadcrumb-disabled': disabled,\n        'in-breadcrumbs-color': hostContext('ion-breadcrumbs[color]', el),\n        'in-toolbar': hostContext('ion-toolbar', this.el),\n        'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n        'ion-activatable': clickable,\n        'ion-focusable': clickable,\n      }) }, h(TagType, Object.assign({}, attrs, { class: \"breadcrumb-native\", part: \"native\", disabled: disabled, onFocus: this.onFocus, onBlur: this.onBlur }, inheritedAttributes), h(\"slot\", { name: \"start\" }), h(\"slot\", null), h(\"slot\", { name: \"end\" })), showCollapsedIndicator && (h(\"button\", { part: \"collapsed-indicator\", \"aria-label\": \"Show more breadcrumbs\", onClick: () => this.collapsedIndicatorClick(), ref: (collapsedEl) => (this.collapsedRef = collapsedEl), class: {\n        'breadcrumbs-collapsed-indicator': true,\n      } }, h(\"ion-icon\", { \"aria-hidden\": \"true\", icon: ellipsisHorizontal, lazy: false }))), showSeparator && (\n    /**\n     * Separators should not be announced by narrators.\n     * We add aria-hidden on the span so that this applies\n     * to any custom separators too.\n     */\n    h(\"span\", { class: \"breadcrumb-separator\", part: \"separator\", \"aria-hidden\": \"true\" }, h(\"slot\", { name: \"separator\" }, mode === 'ios' ? (h(\"ion-icon\", { icon: chevronForwardOutline, lazy: false, \"flip-rtl\": true })) : (h(\"span\", null, \"/\")))))));\n  }\n  get el() { return getElement(this); }\n};\nBreadcrumb.style = {\n  ios: breadcrumbIosCss,\n  md: breadcrumbMdCss\n};\n\nconst breadcrumbsIosCss = \":host{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center}:host(.in-toolbar-color),:host(.in-toolbar-color) .breadcrumbs-collapsed-indicator ion-icon{color:var(--ion-color-contrast)}:host(.in-toolbar-color) .breadcrumbs-collapsed-indicator{background:rgba(var(--ion-color-contrast-rgb), 0.11)}:host(.in-toolbar){-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:0;padding-bottom:0;-ms-flex-pack:center;justify-content:center}\";\n\nconst breadcrumbsMdCss = \":host{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center}:host(.in-toolbar-color),:host(.in-toolbar-color) .breadcrumbs-collapsed-indicator ion-icon{color:var(--ion-color-contrast)}:host(.in-toolbar-color) .breadcrumbs-collapsed-indicator{background:rgba(var(--ion-color-contrast-rgb), 0.11)}:host(.in-toolbar){-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0}\";\n\nconst Breadcrumbs = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionCollapsedClick = createEvent(this, \"ionCollapsedClick\", 7);\n    this.breadcrumbsInit = () => {\n      this.setBreadcrumbSeparator();\n      this.setMaxItems();\n    };\n    this.resetActiveBreadcrumb = () => {\n      const breadcrumbs = this.getBreadcrumbs();\n      // Only reset the active breadcrumb if we were the ones to change it\n      // otherwise use the one set on the component\n      const activeBreadcrumb = breadcrumbs.find((breadcrumb) => breadcrumb.active);\n      if (activeBreadcrumb && this.activeChanged) {\n        activeBreadcrumb.active = false;\n      }\n    };\n    this.setMaxItems = () => {\n      const { itemsAfterCollapse, itemsBeforeCollapse, maxItems } = this;\n      const breadcrumbs = this.getBreadcrumbs();\n      for (const breadcrumb of breadcrumbs) {\n        breadcrumb.showCollapsedIndicator = false;\n        breadcrumb.collapsed = false;\n      }\n      // If the number of breadcrumbs exceeds the maximum number of items\n      // that should show and the items before / after collapse do not\n      // exceed the maximum items then we need to collapse the breadcrumbs\n      const shouldCollapse = maxItems !== undefined && breadcrumbs.length > maxItems && itemsBeforeCollapse + itemsAfterCollapse <= maxItems;\n      if (shouldCollapse) {\n        // Show the collapsed indicator in the first breadcrumb that collapses\n        breadcrumbs.forEach((breadcrumb, index) => {\n          if (index === itemsBeforeCollapse) {\n            breadcrumb.showCollapsedIndicator = true;\n          }\n          // Collapse all breadcrumbs that have an index greater than or equal to\n          // the number before collapse and an index less than the total number\n          // of breadcrumbs minus the items that should show after the collapse\n          if (index >= itemsBeforeCollapse && index < breadcrumbs.length - itemsAfterCollapse) {\n            breadcrumb.collapsed = true;\n          }\n        });\n      }\n    };\n    this.setBreadcrumbSeparator = () => {\n      const { itemsAfterCollapse, itemsBeforeCollapse, maxItems } = this;\n      const breadcrumbs = this.getBreadcrumbs();\n      // Check if an active breadcrumb exists already\n      const active = breadcrumbs.find((breadcrumb) => breadcrumb.active);\n      // Set the separator on all but the last breadcrumb\n      for (const breadcrumb of breadcrumbs) {\n        // The only time the last breadcrumb changes is when\n        // itemsAfterCollapse is set to 0, in this case the\n        // last breadcrumb will be the collapsed indicator\n        const last = maxItems !== undefined && itemsAfterCollapse === 0\n          ? breadcrumb === breadcrumbs[itemsBeforeCollapse]\n          : breadcrumb === breadcrumbs[breadcrumbs.length - 1];\n        breadcrumb.last = last;\n        // If the breadcrumb has defined whether or not to show the\n        // separator then use that value, otherwise check if it's the\n        // last breadcrumb\n        const separator = breadcrumb.separator !== undefined ? breadcrumb.separator : last ? undefined : true;\n        breadcrumb.separator = separator;\n        // If there is not an active breadcrumb already\n        // set the last one to active\n        if (!active && last) {\n          breadcrumb.active = true;\n          this.activeChanged = true;\n        }\n      }\n    };\n    this.getBreadcrumbs = () => {\n      return Array.from(this.el.querySelectorAll('ion-breadcrumb'));\n    };\n    this.slotChanged = () => {\n      this.resetActiveBreadcrumb();\n      this.breadcrumbsInit();\n    };\n    this.collapsed = undefined;\n    this.activeChanged = undefined;\n    this.color = undefined;\n    this.maxItems = undefined;\n    this.itemsBeforeCollapse = 1;\n    this.itemsAfterCollapse = 1;\n  }\n  onCollapsedClick(ev) {\n    const breadcrumbs = this.getBreadcrumbs();\n    const collapsedBreadcrumbs = breadcrumbs.filter((breadcrumb) => breadcrumb.collapsed);\n    this.ionCollapsedClick.emit(Object.assign(Object.assign({}, ev.detail), { collapsedBreadcrumbs }));\n  }\n  maxItemsChanged() {\n    this.resetActiveBreadcrumb();\n    this.breadcrumbsInit();\n  }\n  componentWillLoad() {\n    this.breadcrumbsInit();\n  }\n  render() {\n    const { color, collapsed } = this;\n    const mode = getIonMode(this);\n    return (h(Host, { class: createColorClasses(color, {\n        [mode]: true,\n        'in-toolbar': hostContext('ion-toolbar', this.el),\n        'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n        'breadcrumbs-collapsed': collapsed,\n      }) }, h(\"slot\", { onSlotchange: this.slotChanged })));\n  }\n  get el() { return getElement(this); }\n  static get watchers() { return {\n    \"maxItems\": [\"maxItemsChanged\"],\n    \"itemsBeforeCollapse\": [\"maxItemsChanged\"],\n    \"itemsAfterCollapse\": [\"maxItemsChanged\"]\n  }; }\n};\nBreadcrumbs.style = {\n  ios: breadcrumbsIosCss,\n  md: breadcrumbsMdCss\n};\n\nexport { Breadcrumb as ion_breadcrumb, Breadcrumbs as ion_breadcrumbs };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst hostContext = (selector, el) => {\n  return el.closest(selector) !== null;\n};\n/**\n * Create the mode and color classes for the component based on the classes passed in\n */\nconst createColorClasses = (color, cssClassMap) => {\n  return typeof color === 'string' && color.length > 0\n    ? Object.assign({ 'ion-color': true, [`ion-color-${color}`]: true }, cssClassMap) : cssClassMap;\n};\nconst getClassList = (classes) => {\n  if (classes !== undefined) {\n    const array = Array.isArray(classes) ? classes : classes.split(' ');\n    return array\n      .filter((c) => c != null)\n      .map((c) => c.trim())\n      .filter((c) => c !== '');\n  }\n  return [];\n};\nconst getClassMap = (classes) => {\n  const map = {};\n  getClassList(classes).forEach((c) => (map[c] = true));\n  return map;\n};\nconst SCHEME = /^[a-z][a-z0-9+\\-.]*:/;\nconst openURL = async (url, ev, direction, animation) => {\n  if (url != null && url[0] !== '#' && !SCHEME.test(url)) {\n    const router = document.querySelector('ion-router');\n    if (router) {\n      if (ev != null) {\n        ev.preventDefault();\n      }\n      return router.push(url, direction, animation);\n    }\n  }\n  return false;\n};\n\nexport { createColorClasses as c, getClassMap as g, hostContext as h, openURL as o };\n"], "names": ["r", "registerInstance", "d", "createEvent", "h", "H", "Host", "f", "getElement", "i", "inheritAriaAttributes", "o", "openURL", "c", "createColorClasses", "hostContext", "m", "chevronForwardOutline", "n", "ellipsisHorizontal", "b", "getIonMode", "breadcrumbIosCss", "breadcrumbMdCss", "Breadcrumb", "constructor", "hostRef", "ionFocus", "ionBlur", "collapsedClick", "inheritedAttributes", "onFocus", "emit", "onBlur", "collapsedIndicatorClick", "ionShadowTarget", "collapsedRef", "collapsed", "last", "undefined", "showCollapsedIndicator", "color", "active", "disabled", "download", "href", "rel", "separator", "target", "routerDirection", "routerAnimation", "componentWillLoad", "el", "isClickable", "render", "clickable", "TagType", "mode", "attrs", "showSeparator", "onClick", "ev", "class", "Object", "assign", "part", "name", "ref", "collapsedEl", "icon", "lazy", "style", "ios", "md", "breadcrumbsIosCss", "breadcrumbsMdCss", "Breadcrumbs", "ionCollapsedClick", "breadcrumbsInit", "setBreadcrumbSeparator", "setMaxItems", "resetActiveBreadcrumb", "breadcrumbs", "getBreadcrumbs", "activeBreadcrumb", "find", "breadcrumb", "activeChanged", "itemsAfterCollapse", "itemsBeforeCollapse", "maxItems", "shouldCollapse", "length", "for<PERSON>ach", "index", "Array", "from", "querySelectorAll", "slotChanged", "onCollapsedClick", "collapsedBreadcrumbs", "filter", "detail", "maxItemsChanged", "onSlotchange", "watchers", "ion_breadcrumb", "ion_breadcrumbs", "selector", "closest", "cssClassMap", "getClassList", "classes", "array", "isArray", "split", "map", "trim", "getClassMap", "SCHEME", "_ref", "_asyncToGenerator", "url", "direction", "animation", "test", "router", "document", "querySelector", "preventDefault", "push", "_x", "_x2", "_x3", "_x4", "apply", "arguments", "g"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0, 1]}