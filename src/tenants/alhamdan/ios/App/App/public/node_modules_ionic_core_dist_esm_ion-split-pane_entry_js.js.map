{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-split-pane_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC6G;AAChD;AAE7D,MAAMW,eAAe,GAAG,8vDAA8vD;AAEtxD,MAAMC,cAAc,GAAG,uwDAAuwD;;AAE9xD;AACA,MAAMC,eAAe,GAAG,iBAAiB;AACzC,MAAMC,eAAe,GAAG,iBAAiB;AACzC,MAAMC,KAAK,GAAG;EACZC,EAAE,EAAE,kBAAkB;EACtBC,EAAE,EAAE,oBAAoB;EACxBC,EAAE,EAAE,oBAAoB;EACxBC,EAAE,EAAE,oBAAoB;EACxBC,EAAE,EAAE,qBAAqB;EACzBC,KAAK,EAAE;AACT,CAAC;AACD,MAAMC,SAAS,GAAG,MAAM;EACtBC,WAAWA,CAACC,OAAO,EAAE;IACnBvB,qDAAgB,CAAC,IAAI,EAAEuB,OAAO,CAAC;IAC/B,IAAI,CAACC,mBAAmB,GAAGtB,qDAAW,CAAC,IAAI,EAAE,qBAAqB,EAAE,CAAC,CAAC;IACtE,IAAI,CAACuB,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,SAAS,GAAGC,SAAS;IAC1B,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,IAAI,GAAGf,KAAK,CAAC,IAAI,CAAC;EACzB;EACAgB,cAAcA,CAACL,OAAO,EAAE;IACtB,MAAMM,MAAM,GAAG;MAAEN,OAAO;MAAEO,MAAM,EAAE,IAAI,CAACA,MAAM,CAACC,IAAI,CAAC,IAAI;IAAE,CAAC;IAC1D,IAAI,CAACT,mBAAmB,CAACU,IAAI,CAACH,MAAM,CAAC;EACvC;EACMI,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,8KAAA;MACxB;MACA;MACA,IAAI,OAAOC,cAAc,KAAK,WAAW,IAAIA,cAAc,IAAI,IAAI,EAAE;QACnE,MAAMA,cAAc,CAACC,WAAW,CAAC,gBAAgB,CAAC;MACpD;MACAH,KAAI,CAACI,aAAa,CAAC,CAAC;MACpBJ,KAAI,CAACK,WAAW,CAAC,CAAC;IAAC;EACrB;EACAC,oBAAoBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACC,GAAG,EAAE;MACZ,IAAI,CAACA,GAAG,CAAC,CAAC;MACV,IAAI,CAACA,GAAG,GAAGhB,SAAS;IACtB;EACF;EACAc,WAAWA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACE,GAAG,EAAE;MACZ,IAAI,CAACA,GAAG,CAAC,CAAC;MACV,IAAI,CAACA,GAAG,GAAGhB,SAAS;IACtB;IACA;IACA,IAAI,IAAI,CAACC,QAAQ,EAAE;MACjB,IAAI,CAACH,OAAO,GAAG,KAAK;MACpB;IACF;IACA;IACA,MAAMmB,KAAK,GAAG,IAAI,CAACf,IAAI;IACvB,IAAI,OAAOe,KAAK,KAAK,SAAS,EAAE;MAC9B,IAAI,CAACnB,OAAO,GAAGmB,KAAK;MACpB;IACF;IACA;IACA,MAAMC,UAAU,GAAG/B,KAAK,CAAC8B,KAAK,CAAC,IAAIA,KAAK;IACxC;IACA,IAAIC,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE;MAC3B,IAAI,CAACrB,OAAO,GAAG,KAAK;MACpB;IACF;IACA,IAAIsB,MAAM,CAACC,UAAU,EAAE;MACrB;MACA,MAAMC,QAAQ,GAAIC,CAAC,IAAK;QACtB,IAAI,CAACzB,OAAO,GAAGyB,CAAC,CAACC,OAAO;MAC1B,CAAC;MACD,MAAMC,SAAS,GAAGL,MAAM,CAACC,UAAU,CAACH,UAAU,CAAC;MAC/CO,SAAS,CAACC,WAAW,CAACJ,QAAQ,CAAC;MAC/B,IAAI,CAACN,GAAG,GAAG,MAAMS,SAAS,CAACE,cAAc,CAACL,QAAQ,CAAC;MACnD,IAAI,CAACxB,OAAO,GAAG2B,SAAS,CAACD,OAAO;IAClC;EACF;EACAnB,MAAMA,CAACuB,OAAO,EAAE;IACd,IAAI,CAAC,IAAI,CAAC9B,OAAO,EAAE;MACjB,OAAO,KAAK;IACd;IACA,OAAO8B,OAAO,CAACC,aAAa,KAAK,IAAI,CAACC,EAAE,IAAIF,OAAO,CAACG,SAAS,CAACC,QAAQ,CAAC9C,eAAe,CAAC;EACzF;EACA2B,aAAaA,CAAA,EAAG;IACd,MAAMd,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,MAAMkC,QAAQ,GAAG,IAAI,CAACH,EAAE,CAACG,QAAQ;IACjC,MAAMC,EAAE,GAAG,IAAI,CAACJ,EAAE,CAACK,iBAAiB;IACpC,IAAIC,SAAS,GAAG,KAAK;IACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,EAAE,EAAEG,CAAC,EAAE,EAAE;MAC3B,MAAMC,KAAK,GAAGL,QAAQ,CAACI,CAAC,CAAC;MACzB,MAAME,MAAM,GAAGxC,SAAS,KAAKC,SAAS,IAAIsC,KAAK,CAACE,EAAE,KAAKzC,SAAS;MAChE,IAAIwC,MAAM,EAAE;QACV,IAAIH,SAAS,EAAE;UACbK,OAAO,CAACC,IAAI,CAAC,gDAAgD,CAAC;UAC9D;QACF;QACAN,SAAS,GAAG,IAAI;MAClB;MACAO,YAAY,CAACL,KAAK,EAAEC,MAAM,CAAC;IAC7B;IACA,IAAI,CAACH,SAAS,EAAE;MACdK,OAAO,CAACC,IAAI,CAAC,gDAAgD,CAAC;IAChE;EACF;EACAE,MAAMA,CAAA,EAAG;IACP,MAAMC,IAAI,GAAG/D,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQN,qDAAC,CAACE,iDAAI,EAAE;MAAEoE,KAAK,EAAE;QACrB,CAACD,IAAI,GAAG,IAAI;QACZ;QACA,CAAE,cAAaA,IAAK,EAAC,GAAG,IAAI;QAC5B,oBAAoB,EAAE,IAAI,CAAC/C;MAC7B;IAAE,CAAC,EAAEtB,qDAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;EACzB;EACA,IAAIsD,EAAEA,CAAA,EAAG;IAAE,OAAOlD,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWmE,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC7B,SAAS,EAAE,CAAC,gBAAgB,CAAC;MAC7B,UAAU,EAAE,CAAC,aAAa,CAAC;MAC3B,MAAM,EAAE,CAAC,aAAa;IACxB,CAAC;EAAE;AACL,CAAC;AACD,MAAMJ,YAAY,GAAGA,CAACb,EAAE,EAAES,MAAM,KAAK;EACnC,IAAIS,KAAK;EACT,IAAIC,QAAQ;EACZ,IAAIV,MAAM,EAAE;IACVS,KAAK,GAAG/D,eAAe;IACvBgE,QAAQ,GAAG/D,eAAe;EAC5B,CAAC,MACI;IACH8D,KAAK,GAAG9D,eAAe;IACvB+D,QAAQ,GAAGhE,eAAe;EAC5B;EACA,MAAM8C,SAAS,GAAGD,EAAE,CAACC,SAAS;EAC9BA,SAAS,CAACmB,GAAG,CAACF,KAAK,CAAC;EACpBjB,SAAS,CAACoB,MAAM,CAACF,QAAQ,CAAC;AAC5B,CAAC;AACDvD,SAAS,CAAC0D,KAAK,GAAG;EAChBC,GAAG,EAAEtE,eAAe;EACpBO,EAAE,EAAEN;AACN,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-split-pane.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement } from './index-2d388930.js';\nimport { b as getIonMode } from './ionic-global-b3fc28dd.js';\n\nconst splitPaneIosCss = \":host{--side-width:100%;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:nowrap;flex-wrap:nowrap;contain:strict}::slotted(ion-menu.menu-pane-visible){-ms-flex:0 1 auto;flex:0 1 auto;width:var(--side-width);min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.split-pane-visible) ::slotted(.split-pane-side),:host(.split-pane-visible) ::slotted(.split-pane-main){left:0;right:0;top:0;bottom:0;position:relative;-webkit-box-shadow:none !important;box-shadow:none !important;z-index:0}:host(.split-pane-visible) ::slotted(.split-pane-main){-ms-flex:1;flex:1}:host(.split-pane-visible) ::slotted(.split-pane-side:not(ion-menu)),:host(.split-pane-visible) ::slotted(ion-menu.split-pane-side.menu-enabled){display:-ms-flexbox;display:flex;-ms-flex-negative:0;flex-shrink:0}::slotted(.split-pane-side:not(ion-menu)){display:none}:host(.split-pane-visible) ::slotted(.split-pane-side){-ms-flex-order:-1;order:-1}:host(.split-pane-visible) ::slotted(.split-pane-side[side=end]){-ms-flex-order:1;order:1}:host{--border:0.55px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));--side-min-width:270px;--side-max-width:28%}:host(.split-pane-visible) ::slotted(.split-pane-side){-webkit-border-start:0;border-inline-start:0;-webkit-border-end:var(--border);border-inline-end:var(--border);border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.split-pane-visible) ::slotted(.split-pane-side[side=end]){-webkit-border-start:var(--border);border-inline-start:var(--border);-webkit-border-end:0;border-inline-end:0;border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}\";\n\nconst splitPaneMdCss = \":host{--side-width:100%;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:nowrap;flex-wrap:nowrap;contain:strict}::slotted(ion-menu.menu-pane-visible){-ms-flex:0 1 auto;flex:0 1 auto;width:var(--side-width);min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.split-pane-visible) ::slotted(.split-pane-side),:host(.split-pane-visible) ::slotted(.split-pane-main){left:0;right:0;top:0;bottom:0;position:relative;-webkit-box-shadow:none !important;box-shadow:none !important;z-index:0}:host(.split-pane-visible) ::slotted(.split-pane-main){-ms-flex:1;flex:1}:host(.split-pane-visible) ::slotted(.split-pane-side:not(ion-menu)),:host(.split-pane-visible) ::slotted(ion-menu.split-pane-side.menu-enabled){display:-ms-flexbox;display:flex;-ms-flex-negative:0;flex-shrink:0}::slotted(.split-pane-side:not(ion-menu)){display:none}:host(.split-pane-visible) ::slotted(.split-pane-side){-ms-flex-order:-1;order:-1}:host(.split-pane-visible) ::slotted(.split-pane-side[side=end]){-ms-flex-order:1;order:1}:host{--border:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))));--side-min-width:270px;--side-max-width:28%}:host(.split-pane-visible) ::slotted(.split-pane-side){-webkit-border-start:0;border-inline-start:0;-webkit-border-end:var(--border);border-inline-end:var(--border);border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.split-pane-visible) ::slotted(.split-pane-side[side=end]){-webkit-border-start:var(--border);border-inline-start:var(--border);-webkit-border-end:0;border-inline-end:0;border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}\";\n\n// TODO(FW-2832): types\nconst SPLIT_PANE_MAIN = 'split-pane-main';\nconst SPLIT_PANE_SIDE = 'split-pane-side';\nconst QUERY = {\n  xs: '(min-width: 0px)',\n  sm: '(min-width: 576px)',\n  md: '(min-width: 768px)',\n  lg: '(min-width: 992px)',\n  xl: '(min-width: 1200px)',\n  never: '',\n};\nconst SplitPane = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionSplitPaneVisible = createEvent(this, \"ionSplitPaneVisible\", 7);\n    this.visible = false;\n    this.contentId = undefined;\n    this.disabled = false;\n    this.when = QUERY['lg'];\n  }\n  visibleChanged(visible) {\n    const detail = { visible, isPane: this.isPane.bind(this) };\n    this.ionSplitPaneVisible.emit(detail);\n  }\n  async connectedCallback() {\n    // TODO: connectedCallback is fired in CE build\n    // before WC is defined. This needs to be fixed in Stencil.\n    if (typeof customElements !== 'undefined' && customElements != null) {\n      await customElements.whenDefined('ion-split-pane');\n    }\n    this.styleChildren();\n    this.updateState();\n  }\n  disconnectedCallback() {\n    if (this.rmL) {\n      this.rmL();\n      this.rmL = undefined;\n    }\n  }\n  updateState() {\n    if (this.rmL) {\n      this.rmL();\n      this.rmL = undefined;\n    }\n    // Check if the split-pane is disabled\n    if (this.disabled) {\n      this.visible = false;\n      return;\n    }\n    // When query is a boolean\n    const query = this.when;\n    if (typeof query === 'boolean') {\n      this.visible = query;\n      return;\n    }\n    // When query is a string, let's find first if it is a shortcut\n    const mediaQuery = QUERY[query] || query;\n    // Media query is empty or null, we hide it\n    if (mediaQuery.length === 0) {\n      this.visible = false;\n      return;\n    }\n    if (window.matchMedia) {\n      // Listen on media query\n      const callback = (q) => {\n        this.visible = q.matches;\n      };\n      const mediaList = window.matchMedia(mediaQuery);\n      mediaList.addListener(callback);\n      this.rmL = () => mediaList.removeListener(callback);\n      this.visible = mediaList.matches;\n    }\n  }\n  isPane(element) {\n    if (!this.visible) {\n      return false;\n    }\n    return element.parentElement === this.el && element.classList.contains(SPLIT_PANE_SIDE);\n  }\n  styleChildren() {\n    const contentId = this.contentId;\n    const children = this.el.children;\n    const nu = this.el.childElementCount;\n    let foundMain = false;\n    for (let i = 0; i < nu; i++) {\n      const child = children[i];\n      const isMain = contentId !== undefined && child.id === contentId;\n      if (isMain) {\n        if (foundMain) {\n          console.warn('split pane cannot have more than one main node');\n          return;\n        }\n        foundMain = true;\n      }\n      setPaneClass(child, isMain);\n    }\n    if (!foundMain) {\n      console.warn('split pane does not have a specified main node');\n    }\n  }\n  render() {\n    const mode = getIonMode(this);\n    return (h(Host, { class: {\n        [mode]: true,\n        // Used internally for styling\n        [`split-pane-${mode}`]: true,\n        'split-pane-visible': this.visible,\n      } }, h(\"slot\", null)));\n  }\n  get el() { return getElement(this); }\n  static get watchers() { return {\n    \"visible\": [\"visibleChanged\"],\n    \"disabled\": [\"updateState\"],\n    \"when\": [\"updateState\"]\n  }; }\n};\nconst setPaneClass = (el, isMain) => {\n  let toAdd;\n  let toRemove;\n  if (isMain) {\n    toAdd = SPLIT_PANE_MAIN;\n    toRemove = SPLIT_PANE_SIDE;\n  }\n  else {\n    toAdd = SPLIT_PANE_SIDE;\n    toRemove = SPLIT_PANE_MAIN;\n  }\n  const classList = el.classList;\n  classList.add(toAdd);\n  classList.remove(toRemove);\n};\nSplitPane.style = {\n  ios: splitPaneIosCss,\n  md: splitPaneMdCss\n};\n\nexport { SplitPane as ion_split_pane };\n"], "names": ["r", "registerInstance", "d", "createEvent", "h", "H", "Host", "f", "getElement", "b", "getIonMode", "splitPaneIosCss", "splitPaneMdCss", "SPLIT_PANE_MAIN", "SPLIT_PANE_SIDE", "QUERY", "xs", "sm", "md", "lg", "xl", "never", "SplitPane", "constructor", "hostRef", "ionSplitPaneVisible", "visible", "contentId", "undefined", "disabled", "when", "visibleChanged", "detail", "isPane", "bind", "emit", "connectedCallback", "_this", "_asyncToGenerator", "customElements", "whenDefined", "style<PERSON><PERSON><PERSON>n", "updateState", "disconnectedCallback", "rmL", "query", "mediaQuery", "length", "window", "matchMedia", "callback", "q", "matches", "mediaList", "addListener", "removeListener", "element", "parentElement", "el", "classList", "contains", "children", "nu", "childElementCount", "<PERSON><PERSON><PERSON>", "i", "child", "is<PERSON><PERSON>", "id", "console", "warn", "setPaneClass", "render", "mode", "class", "watchers", "toAdd", "toRemove", "add", "remove", "style", "ios", "ion_split_pane"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}