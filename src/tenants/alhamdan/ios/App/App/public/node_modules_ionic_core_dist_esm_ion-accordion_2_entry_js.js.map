{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-accordion_2_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC6G;AACmC;AACzF;AACmB;AACf;AAE3D,MAAMyB,eAAe,GAAG,qxCAAqxC;AAE7yC,MAAMC,cAAc,GAAG,wrCAAwrC;AAE/sC,MAAMC,SAAS,GAAG,MAAM;EACtBC,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAC,KAAA;IACnB7B,qDAAgB,CAAC,IAAI,EAAE4B,OAAO,CAAC;IAC/B,IAAI,CAACE,cAAc,GAAG,MAAM,IAAI,CAACC,WAAW,CAAC,KAAK,CAAC;IACnD,IAAI,CAACC,eAAe,GAAG,MAAM;MAC3B,MAAMC,OAAO,GAAG,IAAI,CAACC,uBAAuB,CAAC,CAAC;MAC9C,IAAI,CAACD,OAAO,EAAE;QACZ;MACF;MACA;AACN;AACA;AACA;AACA;AACA;MACMA,OAAO,CAACE,MAAM,GAAG,IAAI;MACrBF,OAAO,CAACG,MAAM,GAAG,KAAK;MACtB;AACN;AACA;AACA;AACA;AACA;MACM,IAAIH,OAAO,CAACI,KAAK,KAAKC,SAAS,EAAE;QAC/BL,OAAO,CAACI,KAAK,GAAG,MAAM;MACxB;IACF,CAAC;IACD,IAAI,CAACH,uBAAuB,GAAG,MAAM;MACnC,MAAM;QAAEK;MAAS,CAAC,GAAG,IAAI;MACzB,IAAI,CAACA,QAAQ,EAAE;QACb;MACF;MACA;AACN;AACA;AACA;MACM,MAAMC,IAAI,GAAGD,QAAQ,CAACE,aAAa,CAAC,MAAM,CAAC;MAC3C,IAAI,CAACD,IAAI,EAAE;QACT;MACF;MACA;MACA,IAAIA,IAAI,CAACE,gBAAgB,KAAKJ,SAAS,EACrC;MACF,OAAOE,IAAI,CAACE,gBAAgB,CAAC,CAAC,CAACC,IAAI,CAAEC,EAAE,IAAKA,EAAE,CAACC,OAAO,KAAK,UAAU,CAAC;IACxE,CAAC;IACD,IAAI,CAACC,OAAO,GAAG,CAACC,QAAQ,GAAG,KAAK,KAAK;MACnC,MAAMd,OAAO,GAAG,IAAI,CAACC,uBAAuB,CAAC,CAAC;MAC9C,IAAI,CAACD,OAAO,EAAE;QACZ;MACF;MACA;AACN;AACA;AACA;MACM,MAAMe,IAAI,GAAGhC,uDAAc,CAACiB,OAAO,CAAC;MACpC,MAAME,MAAM,GAAGa,IAAI,CAACP,aAAa,CAAC,QAAQ,CAAC;MAC3C,IAAI,CAACN,MAAM,EAAE;QACX;MACF;MACAA,MAAM,CAACc,YAAY,CAAC,eAAe,EAAG,GAAEF,QAAS,EAAC,CAAC;IACrD,CAAC;IACD,IAAI,CAACG,cAAc,GAAG,MAAM;MAC1B,MAAMjB,OAAO,GAAG,IAAI,CAACC,uBAAuB,CAAC,CAAC;MAC9C,IAAI,CAACD,OAAO,EAAE;QACZ;MACF;MACA,MAAM;QAAEkB,cAAc;QAAEC;MAAW,CAAC,GAAG,IAAI;MAC3C;AACN;AACA;AACA;MACM,MAAMC,kBAAkB,GAAGpB,OAAO,CAACQ,aAAa,CAAC,4BAA4B,CAAC;MAC9E,IAAIY,kBAAkB,EAAE;QACtB;MACF;MACA,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;MACjDF,MAAM,CAACd,IAAI,GAAGW,cAAc;MAC5BG,MAAM,CAACG,IAAI,GAAG,KAAK;MACnBH,MAAM,CAACI,SAAS,CAACC,GAAG,CAAC,2BAA2B,CAAC;MACjDL,MAAM,CAACM,IAAI,GAAGR,UAAU;MACxBE,MAAM,CAACL,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;MAC1ChB,OAAO,CAAC4B,WAAW,CAACP,MAAM,CAAC;IAC7B,CAAC;IACD,IAAI,CAACQ,eAAe,GAAG,CAACC,aAAa,GAAG,KAAK,KAAK;MAChD,MAAM;QAAEC,SAAS;QAAEC;MAAiB,CAAC,GAAG,IAAI;MAC5C,IAAIF,aAAa,IAAIC,SAAS,KAAK1B,SAAS,IAAI2B,gBAAgB,KAAK3B,SAAS,EAAE;QAC9E,IAAI,CAAC4B,KAAK,GAAG,CAAC,CAAC;QACf;MACF;MACA,IAAI,IAAI,CAACA,KAAK,KAAK,CAAC,CAAC,+BAA+B;QAClD;MACF;MACA,IAAI,IAAI,CAACC,UAAU,KAAK7B,SAAS,EAAE;QACjC8B,oBAAoB,CAAC,IAAI,CAACD,UAAU,CAAC;MACvC;MACA,IAAI,IAAI,CAACE,aAAa,CAAC,CAAC,EAAE;QACxB7D,uDAAG,CAAC,MAAM;UACR,IAAI,CAAC0D,KAAK,GAAG,CAAC,CAAC;UACf,IAAI,CAACC,UAAU,GAAG3D,uDAAG,eAAA8D,8KAAA,CAAC,aAAY;YAChC,MAAMC,aAAa,GAAGN,gBAAgB,CAACO,YAAY;YACnD,MAAMC,iBAAiB,GAAG/D,uDAAkB,CAACsD,SAAS,EAAE,IAAI,CAAC;YAC7DA,SAAS,CAACU,KAAK,CAACC,WAAW,CAAC,YAAY,EAAG,GAAEJ,aAAc,IAAG,CAAC;YAC/D,MAAME,iBAAiB;YACvB5C,KAAI,CAACqC,KAAK,GAAG,CAAC,CAAC;YACfF,SAAS,CAACU,KAAK,CAACE,cAAc,CAAC,YAAY,CAAC;UAC9C,CAAC,EAAC;QACJ,CAAC,CAAC;MACJ,CAAC,MACI;QACH,IAAI,CAACV,KAAK,GAAG,CAAC,CAAC;MACjB;IACF,CAAC;;IACD,IAAI,CAACW,iBAAiB,GAAG,CAACd,aAAa,GAAG,KAAK,KAAK;MAClD,MAAM;QAAEC;MAAU,CAAC,GAAG,IAAI;MAC1B,IAAID,aAAa,IAAIC,SAAS,KAAK1B,SAAS,EAAE;QAC5C,IAAI,CAAC4B,KAAK,GAAG,CAAC,CAAC;QACf;MACF;MACA,IAAI,IAAI,CAACA,KAAK,KAAK,CAAC,CAAC,gCAAgC;QACnD;MACF;MACA,IAAI,IAAI,CAACC,UAAU,KAAK7B,SAAS,EAAE;QACjC8B,oBAAoB,CAAC,IAAI,CAACD,UAAU,CAAC;MACvC;MACA,IAAI,IAAI,CAACE,aAAa,CAAC,CAAC,EAAE;QACxB,IAAI,CAACF,UAAU,GAAG3D,uDAAG,eAAA8D,8KAAA,CAAC,aAAY;UAChC,MAAMC,aAAa,GAAGP,SAAS,CAACQ,YAAY;UAC5CR,SAAS,CAACU,KAAK,CAACC,WAAW,CAAC,YAAY,EAAG,GAAEJ,aAAc,IAAG,CAAC;UAC/D/D,uDAAG,eAAA8D,8KAAA,CAAC,aAAY;YACd,MAAMG,iBAAiB,GAAG/D,uDAAkB,CAACsD,SAAS,EAAE,IAAI,CAAC;YAC7DnC,KAAI,CAACqC,KAAK,GAAG,CAAC,CAAC;YACf,MAAMO,iBAAiB;YACvB5C,KAAI,CAACqC,KAAK,GAAG,CAAC,CAAC;YACfF,SAAS,CAACU,KAAK,CAACE,cAAc,CAAC,YAAY,CAAC;UAC9C,CAAC,EAAC;QACJ,CAAC,EAAC;MACJ,CAAC,MACI;QACH,IAAI,CAACV,KAAK,GAAG,CAAC,CAAC;MACjB;IACF,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACG,aAAa,GAAG,MAAM;MACzB,IAAI,OAAOS,MAAM,KAAK,WAAW,EAAE;QACjC,OAAO,KAAK;MACd;MACA,MAAMC,oBAAoB,GAAGC,UAAU,CAAC,kCAAkC,CAAC,CAACC,OAAO;MACnF,IAAIF,oBAAoB,EAAE;QACxB,OAAO,KAAK;MACd;MACA,MAAMG,QAAQ,GAAG9D,wDAAM,CAAC+D,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC;MAC7C,IAAI,CAACD,QAAQ,EAAE;QACb,OAAO,KAAK;MACd;MACA,IAAI,IAAI,CAACE,gBAAgB,IAAI,CAAC,IAAI,CAACA,gBAAgB,CAACF,QAAQ,EAAE;QAC5D,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IACb,CAAC;IACD,IAAI,CAACnD,WAAW,gBAAAuC,8KAAA,CAAG,WAAOP,aAAa,GAAG,KAAK,EAAK;MAClD,MAAMsB,cAAc,GAAGxD,KAAI,CAACuD,gBAAgB;MAC5C,MAAME,cAAc,GAAGzD,KAAI,CAAC0D,KAAK;MACjC,IAAI,CAACF,cAAc,EAAE;QACnB;MACF;MACA,MAAME,KAAK,GAAGF,cAAc,CAACE,KAAK;MAClC,MAAMC,YAAY,GAAGC,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,GAAGA,KAAK,CAACI,QAAQ,CAACL,cAAc,CAAC,GAAGC,KAAK,KAAKD,cAAc;MACrG,IAAIE,YAAY,EAAE;QAChB3D,KAAI,CAACiC,eAAe,CAACC,aAAa,CAAC;QACnClC,KAAI,CAAC+D,MAAM,GAAG/D,KAAI,CAACgE,UAAU,GAAG,KAAK;MACvC,CAAC,MACI;QACHhE,KAAI,CAACgD,iBAAiB,CAACd,aAAa,CAAC;QACrC;AACR;AACA;AACA;AACA;AACA;AACA;QACQ,MAAM+B,aAAa,GAAGjE,KAAI,CAACkE,cAAc,CAAC,CAAC;QAC3C,MAAMC,kBAAkB,GAAGF,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACP,KAAK;QAC5G,IAAIS,kBAAkB,KAAK1D,SAAS,EAAE;UACpCT,KAAI,CAACgE,UAAU,GAAGJ,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,GAAGA,KAAK,CAACI,QAAQ,CAACK,kBAAkB,CAAC,GAAGT,KAAK,KAAKS,kBAAkB;QAC5G;QACA,MAAMC,iBAAiB,GAAGpE,KAAI,CAACqE,kBAAkB,CAAC,CAAC;QACnD,MAAMC,sBAAsB,GAAGF,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACV,KAAK;QAC5H,IAAIY,sBAAsB,KAAK7D,SAAS,EAAE;UACxCT,KAAI,CAAC+D,MAAM,GAAGH,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,GAAGA,KAAK,CAACI,QAAQ,CAACQ,sBAAsB,CAAC,GAAGZ,KAAK,KAAKY,sBAAsB;QAChH;MACF;IACF,CAAC;IACD,IAAI,CAACJ,cAAc,GAAG,MAAM;MAC1B,IAAI,CAAC,IAAI,CAACnD,EAAE,EAAE;QACZ;MACF;MACA,MAAMwD,WAAW,GAAG,IAAI,CAACxD,EAAE,CAACyD,kBAAkB;MAC9C,IAAI,CAACD,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACvD,OAAO,MAAM,eAAe,EAAE;QACvG;MACF;MACA,OAAOuD,WAAW;IACpB,CAAC;IACD,IAAI,CAACF,kBAAkB,GAAG,MAAM;MAC9B,IAAI,CAAC,IAAI,CAACtD,EAAE,EAAE;QACZ;MACF;MACA,MAAM0D,eAAe,GAAG,IAAI,CAAC1D,EAAE,CAAC2D,sBAAsB;MACtD,IAAI,CAACD,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACzD,OAAO,MAAM,eAAe,EAAE;QACnH;MACF;MACA,OAAOyD,eAAe;IACxB,CAAC;IACD,IAAI,CAACpC,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAAC0B,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACN,KAAK,GAAI,iBAAgBiB,YAAY,EAAG,EAAC;IAC9C,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACtD,UAAU,GAAGlC,iDAAW;IAC7B,IAAI,CAACiC,cAAc,GAAG,KAAK;EAC7B;EACAwD,YAAYA,CAAA,EAAG;IACb,IAAI,CAAC5E,WAAW,CAAC,CAAC;EACpB;EACA6E,iBAAiBA,CAAA,EAAG;IAClB,IAAIC,EAAE;IACN,MAAMzB,gBAAgB,GAAI,IAAI,CAACA,gBAAgB,GAAG,CAACyB,EAAE,GAAG,IAAI,CAACjE,EAAE,MAAM,IAAI,IAAIiE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,OAAO,CAAC,qBAAqB,CAAE;IACxI,IAAI1B,gBAAgB,EAAE;MACpB,IAAI,CAACrD,WAAW,CAAC,IAAI,CAAC;MACtBnB,uDAAgB,CAACwE,gBAAgB,EAAE,gBAAgB,EAAE,IAAI,CAACtD,cAAc,CAAC;IAC3E;EACF;EACAiF,oBAAoBA,CAAA,EAAG;IACrB,MAAM3B,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAC9C,IAAIA,gBAAgB,EAAE;MACpBtE,uDAAmB,CAACsE,gBAAgB,EAAE,gBAAgB,EAAE,IAAI,CAACtD,cAAc,CAAC;IAC9E;EACF;EACAkF,gBAAgBA,CAAA,EAAG;IACjB,IAAI,CAAChF,eAAe,CAAC,CAAC;IACtB,IAAI,CAACkB,cAAc,CAAC,CAAC;IACrB;AACJ;AACA;AACA;AACA;IACI1C,uDAAG,CAAC,MAAM;MACR;AACN;AACA;AACA;MACM,MAAMuC,QAAQ,GAAG,IAAI,CAACmB,KAAK,KAAK,CAAC,CAAC,iCAAiC,IAAI,CAACA,KAAK,KAAK,CAAC,CAAC;MACpF,IAAI,CAACpB,OAAO,CAACC,QAAQ,CAAC;IACxB,CAAC,CAAC;EACJ;EACAkE,cAAcA,CAAA,EAAG;IACf,MAAM;MAAE7B,gBAAgB;MAAEG,KAAK;MAAErB;IAAM,CAAC,GAAG,IAAI;IAC/C,IAAIkB,gBAAgB,EAAE;MACpB;AACN;AACA;AACA;AACA;AACA;AACA;AACA;MACM,MAAM8B,MAAM,GAAGhD,KAAK,KAAK,CAAC,CAAC,kCAAkCA,KAAK,KAAK,CAAC,CAAC;MACzEkB,gBAAgB,CAAC+B,sBAAsB,CAAC5B,KAAK,EAAE2B,MAAM,CAAC;IACxD;EACF;EACAE,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEX,QAAQ;MAAEC;IAAS,CAAC,GAAG,IAAI;IACnC,MAAMW,IAAI,GAAGhG,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM0B,QAAQ,GAAG,IAAI,CAACmB,KAAK,KAAK,CAAC,CAAC,iCAAiC,IAAI,CAACA,KAAK,KAAK,CAAC,CAAC;IACpF,MAAMoD,UAAU,GAAGvE,QAAQ,GAAG,iBAAiB,GAAG,QAAQ;IAC1D,MAAMwE,WAAW,GAAGxE,QAAQ,GAAG,kBAAkB,GAAG,SAAS;IAC7D,IAAI,CAACD,OAAO,CAACC,QAAQ,CAAC;IACtB,OAAQ9C,qDAAC,CAACE,iDAAI,EAAE;MAAEqH,KAAK,EAAE;QACrB,CAACH,IAAI,GAAG,IAAI;QACZ,qBAAqB,EAAE,IAAI,CAACnD,KAAK,KAAK,CAAC,CAAC;QACxC,oBAAoB,EAAE,IAAI,CAACA,KAAK,KAAK,CAAC,CAAC;QACvC,sBAAsB,EAAE,IAAI,CAACA,KAAK,KAAK,CAAC,CAAC;QACzC,qBAAqB,EAAE,IAAI,CAACA,KAAK,KAAK,CAAC,CAAC;QACxC,gBAAgB,EAAE,IAAI,CAAC0B,MAAM;QAC7B,oBAAoB,EAAE,IAAI,CAACC,UAAU;QACrC,oBAAoB,EAAEY,QAAQ;QAC9B,oBAAoB,EAAEC,QAAQ;QAC9B,oBAAoB,EAAE,IAAI,CAACrC,aAAa,CAAC;MAC3C;IAAE,CAAC,EAAEpE,qDAAC,CAAC,KAAK,EAAE;MAAEwH,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACR,cAAc,CAAC,CAAC;MAAES,EAAE,EAAE,QAAQ;MAAEC,IAAI,EAAEL,UAAU;MAAE,eAAe,EAAE,SAAS;MAAEM,GAAG,EAAGrF,QAAQ,IAAM,IAAI,CAACA,QAAQ,GAAGA;IAAU,CAAC,EAAEtC,qDAAC,CAAC,MAAM,EAAE;MAAE4H,IAAI,EAAE;IAAS,CAAC,CAAC,CAAC,EAAE5H,qDAAC,CAAC,KAAK,EAAE;MAAEyH,EAAE,EAAE,SAAS;MAAEC,IAAI,EAAEJ,WAAW;MAAEO,IAAI,EAAE,QAAQ;MAAE,iBAAiB,EAAE,QAAQ;MAAEF,GAAG,EAAG5D,SAAS,IAAM,IAAI,CAACA,SAAS,GAAGA;IAAW,CAAC,EAAE/D,qDAAC,CAAC,KAAK,EAAE;MAAEyH,EAAE,EAAE,iBAAiB;MAAEE,GAAG,EAAG3D,gBAAgB,IAAM,IAAI,CAACA,gBAAgB,GAAGA;IAAkB,CAAC,EAAEhE,qDAAC,CAAC,MAAM,EAAE;MAAE4H,IAAI,EAAE;IAAU,CAAC,CAAC,CAAC,CAAC,CAAC;EACje;EACA,WAAWE,cAAcA,CAAA,EAAG;IAAE,OAAO,IAAI;EAAE;EAC3C,IAAInF,EAAEA,CAAA,EAAG;IAAE,OAAOvC,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW2H,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC7B,OAAO,EAAE,CAAC,cAAc;IAC1B,CAAC;EAAE;AACL,CAAC;AACD,IAAIxB,YAAY,GAAG,CAAC;AACpB9E,SAAS,CAACgD,KAAK,GAAG;EAChBuD,GAAG,EAAEzG,eAAe;EACpB0G,EAAE,EAAEzG;AACN,CAAC;AAED,MAAM0G,oBAAoB,GAAG,sXAAsX;AAEnZ,MAAMC,mBAAmB,GAAG,y+DAAy+D;AAErgE,MAAMC,cAAc,GAAG,MAAM;EAC3B1G,WAAWA,CAACC,OAAO,EAAE;IACnB5B,qDAAgB,CAAC,IAAI,EAAE4B,OAAO,CAAC;IAC/B,IAAI,CAAC0G,SAAS,GAAG/H,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACgI,cAAc,GAAGhI,qDAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAAC2E,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACsD,QAAQ,GAAGlG,SAAS;IACzB,IAAI,CAACiD,KAAK,GAAGjD,SAAS;IACtB,IAAI,CAACmE,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACQ,MAAM,GAAG,SAAS;EACzB;EACAP,YAAYA,CAAA,EAAG;IACb,MAAM;MAAEpB,KAAK;MAAEiD;IAAS,CAAC,GAAG,IAAI;IAChC,IAAI,CAACA,QAAQ,IAAI/C,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE;MACrC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;MACMhE,qDAAe,CAAE;AACvB;AACA,mBAAmBgE,KAAK,CAACkD,GAAG,CAAEC,CAAC,IAAM,IAAGA,CAAE,GAAE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAE;AACzD,CAAC,EAAE,IAAI,CAAC/F,EAAE,CAAC;IACP;IACA;AACJ;AACA;AACA;IACI,IAAI,CAAC2F,cAAc,CAACK,IAAI,CAAC;MAAErD,KAAK,EAAE,IAAI,CAACA;IAAM,CAAC,CAAC;EACjD;EACMsD,eAAeA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAxE,8KAAA;MACtB,MAAM;QAAEmC;MAAS,CAAC,GAAGqC,MAAI;MACzB,MAAMC,UAAU,SAASD,MAAI,CAACE,aAAa,CAAC,CAAC;MAC7C,KAAK,MAAMC,SAAS,IAAIF,UAAU,EAAE;QAClCE,SAAS,CAACxC,QAAQ,GAAGA,QAAQ;MAC/B;IAAC;EACH;EACMyC,eAAeA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA7E,8KAAA;MACtB,MAAM;QAAEoC;MAAS,CAAC,GAAGyC,MAAI;MACzB,MAAMJ,UAAU,SAASI,MAAI,CAACH,aAAa,CAAC,CAAC;MAC7C,KAAK,MAAMC,SAAS,IAAIF,UAAU,EAAE;QAClCE,SAAS,CAACvC,QAAQ,GAAGA,QAAQ;MAC/B;IAAC;EACH;EACM0C,SAASA,CAACC,EAAE,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAhF,8KAAA;MAClB,MAAMiF,aAAa,GAAGhG,QAAQ,CAACgG,aAAa;MAC5C,IAAI,CAACA,aAAa,EAAE;QAClB;MACF;MACA;AACJ;AACA;AACA;AACA;AACA;MACI,MAAMC,qBAAqB,GAAGD,aAAa,CAACzC,OAAO,CAAC,+BAA+B,CAAC;MACpF,IAAI,CAAC0C,qBAAqB,EAAE;QAC1B;MACF;MACA,MAAMC,WAAW,GAAGF,aAAa,CAAC1G,OAAO,KAAK,eAAe,GAAG0G,aAAa,GAAGA,aAAa,CAACzC,OAAO,CAAC,eAAe,CAAC;MACtH,IAAI,CAAC2C,WAAW,EAAE;QAChB;MACF;MACA,MAAMC,YAAY,GAAGD,WAAW,CAAC3C,OAAO,CAAC,qBAAqB,CAAC;MAC/D,IAAI4C,YAAY,KAAKJ,MAAI,CAAC1G,EAAE,EAAE;QAC5B;MACF;MACA;MACA,MAAMmG,UAAU,SAASO,MAAI,CAACN,aAAa,CAAC,CAAC;MAC7C,MAAMW,aAAa,GAAGZ,UAAU,CAACa,SAAS,CAAEjJ,CAAC,IAAKA,CAAC,KAAK8I,WAAW,CAAC;MACpE,IAAIE,aAAa,KAAK,CAAC,CAAC,EAAE;QACxB;MACF;MACA,IAAIV,SAAS;MACb,IAAII,EAAE,CAACQ,GAAG,KAAK,WAAW,EAAE;QAC1BZ,SAAS,GAAGK,MAAI,CAACQ,iBAAiB,CAACf,UAAU,EAAEY,aAAa,CAAC;MAC/D,CAAC,MACI,IAAIN,EAAE,CAACQ,GAAG,KAAK,SAAS,EAAE;QAC7BZ,SAAS,GAAGK,MAAI,CAACS,qBAAqB,CAAChB,UAAU,EAAEY,aAAa,CAAC;MACnE,CAAC,MACI,IAAIN,EAAE,CAACQ,GAAG,KAAK,MAAM,EAAE;QAC1BZ,SAAS,GAAGF,UAAU,CAAC,CAAC,CAAC;MAC3B,CAAC,MACI,IAAIM,EAAE,CAACQ,GAAG,KAAK,KAAK,EAAE;QACzBZ,SAAS,GAAGF,UAAU,CAACA,UAAU,CAACiB,MAAM,GAAG,CAAC,CAAC;MAC/C;MACA,IAAIf,SAAS,KAAK3G,SAAS,IAAI2G,SAAS,KAAKM,aAAa,EAAE;QAC1DN,SAAS,CAACgB,KAAK,CAAC,CAAC;MACnB;IAAC;EACH;EACMjD,gBAAgBA,CAAA,EAAG;IAAA,IAAAkD,MAAA;IAAA,OAAA5F,8KAAA;MACvB,IAAI4F,MAAI,CAACzD,QAAQ,EAAE;QACjByD,MAAI,CAACrB,eAAe,CAAC,CAAC;MACxB;MACA,IAAIqB,MAAI,CAACxD,QAAQ,EAAE;QACjBwD,MAAI,CAAChB,eAAe,CAAC,CAAC;MACxB;IAAC;EACH;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEiB,QAAQA,CAAC7E,cAAc,EAAE;IACvB,MAAMC,KAAK,GAAI,IAAI,CAACA,KAAK,GAAGD,cAAe;IAC3C,IAAI,CAACgD,SAAS,CAACM,IAAI,CAAC;MAAErD;IAAM,CAAC,CAAC;EAChC;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACQ4B,sBAAsBA,CAAC7B,cAAc,EAAE8E,eAAe,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAA/F,8KAAA;MAC5D,MAAM;QAAEkE,QAAQ;QAAEjD,KAAK;QAAEmB,QAAQ;QAAED;MAAS,CAAC,GAAG4D,MAAI;MACpD,IAAI3D,QAAQ,IAAID,QAAQ,EAAE;QACxB;MACF;MACA,IAAI2D,eAAe,EAAE;QACnB;AACN;AACA;AACA;AACA;AACA;QACM,IAAI5B,QAAQ,EAAE;UACZ,MAAM8B,UAAU,GAAG/E,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,EAAE;UAClE,MAAMgF,cAAc,GAAG9E,KAAK,CAACC,OAAO,CAAC4E,UAAU,CAAC,GAAGA,UAAU,GAAG,CAACA,UAAU,CAAC;UAC5E,MAAME,WAAW,GAAGD,cAAc,CAAC5H,IAAI,CAAE+F,CAAC,IAAKA,CAAC,KAAKpD,cAAc,CAAC;UACpE,IAAIkF,WAAW,KAAKlI,SAAS,IAAIgD,cAAc,KAAKhD,SAAS,EAAE;YAC7D+H,MAAI,CAACF,QAAQ,CAAC,CAAC,GAAGI,cAAc,EAAEjF,cAAc,CAAC,CAAC;UACpD;QACF,CAAC,MACI;UACH+E,MAAI,CAACF,QAAQ,CAAC7E,cAAc,CAAC;QAC/B;MACF,CAAC,MACI;QACH;AACN;AACA;AACA;QACM,IAAIkD,QAAQ,EAAE;UACZ,MAAM8B,UAAU,GAAG/E,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,EAAE;UAClE,MAAMgF,cAAc,GAAG9E,KAAK,CAACC,OAAO,CAAC4E,UAAU,CAAC,GAAGA,UAAU,GAAG,CAACA,UAAU,CAAC;UAC5ED,MAAI,CAACF,QAAQ,CAACI,cAAc,CAACE,MAAM,CAAE/B,CAAC,IAAKA,CAAC,KAAKpD,cAAc,CAAC,CAAC;QACnE,CAAC,MACI;UACH+E,MAAI,CAACF,QAAQ,CAAC7H,SAAS,CAAC;QAC1B;MACF;IAAC;EACH;EACAwH,iBAAiBA,CAACf,UAAU,EAAEY,aAAa,EAAE;IAC3C,MAAM7D,aAAa,GAAGiD,UAAU,CAACY,aAAa,GAAG,CAAC,CAAC;IACnD,IAAI7D,aAAa,KAAKxD,SAAS,EAAE;MAC/B,OAAOyG,UAAU,CAAC,CAAC,CAAC;IACtB;IACA,OAAOjD,aAAa;EACtB;EACAiE,qBAAqBA,CAAChB,UAAU,EAAEY,aAAa,EAAE;IAC/C,MAAMe,aAAa,GAAG3B,UAAU,CAACY,aAAa,GAAG,CAAC,CAAC;IACnD,IAAIe,aAAa,KAAKpI,SAAS,EAAE;MAC/B,OAAOyG,UAAU,CAACA,UAAU,CAACiB,MAAM,GAAG,CAAC,CAAC;IAC1C;IACA,OAAOU,aAAa;EACtB;EACA;AACF;AACA;EACQ1B,aAAaA,CAAA,EAAG;IAAA,IAAA2B,MAAA;IAAA,OAAArG,8KAAA;MACpB,OAAOmB,KAAK,CAACmF,IAAI,CAACD,MAAI,CAAC/H,EAAE,CAACiI,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;IAAC;EACxE;EACAzD,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEX,QAAQ;MAAEC,QAAQ;MAAEQ;IAAO,CAAC,GAAG,IAAI;IAC3C,MAAMG,IAAI,GAAGhG,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQpB,qDAAC,CAACE,iDAAI,EAAE;MAAEqH,KAAK,EAAE;QACrB,CAACH,IAAI,GAAG,IAAI;QACZ,0BAA0B,EAAEZ,QAAQ;QACpC,0BAA0B,EAAEC,QAAQ;QACpC,CAAE,0BAAyBQ,MAAO,EAAC,GAAG;MACxC,CAAC;MAAEY,IAAI,EAAE;IAAe,CAAC,EAAE7H,qDAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;EAC/C;EACA,IAAI2C,EAAEA,CAAA,EAAG;IAAE,OAAOvC,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW2H,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC7B,OAAO,EAAE,CAAC,cAAc,CAAC;MACzB,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,UAAU,EAAE,CAAC,iBAAiB;IAChC,CAAC;EAAE;AACL,CAAC;AACDK,cAAc,CAAC3D,KAAK,GAAG;EACrBuD,GAAG,EAAEE,oBAAoB;EACzBD,EAAE,EAAEE;AACN,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-accordion_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, H as Host, f as getElement, d as createEvent } from './index-2d388930.js';\nimport { r as raf, t as transitionEndAsync, a as addEventListener, b as removeEventListener, g as getElementRoot } from './helpers-3379ba19.js';\nimport { l as chevronDown } from './index-ecfc2c9f.js';\nimport { c as config, b as getIonMode } from './ionic-global-b3fc28dd.js';\nimport { p as printIonWarning } from './index-595d62c9.js';\n\nconst accordionIosCss = \":host{display:block;position:relative;width:100%;background-color:var(--ion-background-color, #ffffff);overflow:hidden;z-index:0}:host(.accordion-expanding) ::slotted(ion-item[slot=header]),:host(.accordion-expanded) ::slotted(ion-item[slot=header]){--border-width:0px}:host(.accordion-animated){-webkit-transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}:host(.accordion-animated) #content{-webkit-transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}#content{overflow:hidden;will-change:max-height}:host(.accordion-collapsing) #content{max-height:0 !important}:host(.accordion-collapsed) #content{display:none}:host(.accordion-expanding) #content{max-height:0}:host(.accordion-expanding) #content-wrapper{overflow:auto}:host(.accordion-disabled) #header,:host(.accordion-readonly) #header,:host(.accordion-disabled) #content,:host(.accordion-readonly) #content{pointer-events:none}:host(.accordion-disabled) #header,:host(.accordion-disabled) #content{opacity:0.4}@media (prefers-reduced-motion: reduce){:host,#content{-webkit-transition:none !important;transition:none !important}}:host(.accordion-next) ::slotted(ion-item[slot=header]){--border-width:0.55px 0px 0.55px 0px}\";\n\nconst accordionMdCss = \":host{display:block;position:relative;width:100%;background-color:var(--ion-background-color, #ffffff);overflow:hidden;z-index:0}:host(.accordion-expanding) ::slotted(ion-item[slot=header]),:host(.accordion-expanded) ::slotted(ion-item[slot=header]){--border-width:0px}:host(.accordion-animated){-webkit-transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}:host(.accordion-animated) #content{-webkit-transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}#content{overflow:hidden;will-change:max-height}:host(.accordion-collapsing) #content{max-height:0 !important}:host(.accordion-collapsed) #content{display:none}:host(.accordion-expanding) #content{max-height:0}:host(.accordion-expanding) #content-wrapper{overflow:auto}:host(.accordion-disabled) #header,:host(.accordion-readonly) #header,:host(.accordion-disabled) #content,:host(.accordion-readonly) #content{pointer-events:none}:host(.accordion-disabled) #header,:host(.accordion-disabled) #content{opacity:0.4}@media (prefers-reduced-motion: reduce){:host,#content{-webkit-transition:none !important;transition:none !important}}\";\n\nconst Accordion = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.updateListener = () => this.updateState(false);\n    this.setItemDefaults = () => {\n      const ionItem = this.getSlottedHeaderIonItem();\n      if (!ionItem) {\n        return;\n      }\n      /**\n       * For a11y purposes, we make\n       * the ion-item a button so users\n       * can tab to it and use keyboard\n       * navigation to get around.\n       */\n      ionItem.button = true;\n      ionItem.detail = false;\n      /**\n       * By default, the lines in an\n       * item should be full here, but\n       * only do that if a user has\n       * not explicitly overridden them\n       */\n      if (ionItem.lines === undefined) {\n        ionItem.lines = 'full';\n      }\n    };\n    this.getSlottedHeaderIonItem = () => {\n      const { headerEl } = this;\n      if (!headerEl) {\n        return;\n      }\n      /**\n       * Get the first ion-item\n       * slotted in the header slot\n       */\n      const slot = headerEl.querySelector('slot');\n      if (!slot) {\n        return;\n      }\n      // This is not defined in unit tests\n      if (slot.assignedElements === undefined)\n        return;\n      return slot.assignedElements().find((el) => el.tagName === 'ION-ITEM');\n    };\n    this.setAria = (expanded = false) => {\n      const ionItem = this.getSlottedHeaderIonItem();\n      if (!ionItem) {\n        return;\n      }\n      /**\n       * Get the native <button> element inside of\n       * ion-item because that is what will be focused\n       */\n      const root = getElementRoot(ionItem);\n      const button = root.querySelector('button');\n      if (!button) {\n        return;\n      }\n      button.setAttribute('aria-expanded', `${expanded}`);\n    };\n    this.slotToggleIcon = () => {\n      const ionItem = this.getSlottedHeaderIonItem();\n      if (!ionItem) {\n        return;\n      }\n      const { toggleIconSlot, toggleIcon } = this;\n      /**\n       * Check if there already is a toggle icon.\n       * If so, do not add another one.\n       */\n      const existingToggleIcon = ionItem.querySelector('.ion-accordion-toggle-icon');\n      if (existingToggleIcon) {\n        return;\n      }\n      const iconEl = document.createElement('ion-icon');\n      iconEl.slot = toggleIconSlot;\n      iconEl.lazy = false;\n      iconEl.classList.add('ion-accordion-toggle-icon');\n      iconEl.icon = toggleIcon;\n      iconEl.setAttribute('aria-hidden', 'true');\n      ionItem.appendChild(iconEl);\n    };\n    this.expandAccordion = (initialUpdate = false) => {\n      const { contentEl, contentElWrapper } = this;\n      if (initialUpdate || contentEl === undefined || contentElWrapper === undefined) {\n        this.state = 4 /* AccordionState.Expanded */;\n        return;\n      }\n      if (this.state === 4 /* AccordionState.Expanded */) {\n        return;\n      }\n      if (this.currentRaf !== undefined) {\n        cancelAnimationFrame(this.currentRaf);\n      }\n      if (this.shouldAnimate()) {\n        raf(() => {\n          this.state = 8 /* AccordionState.Expanding */;\n          this.currentRaf = raf(async () => {\n            const contentHeight = contentElWrapper.offsetHeight;\n            const waitForTransition = transitionEndAsync(contentEl, 2000);\n            contentEl.style.setProperty('max-height', `${contentHeight}px`);\n            await waitForTransition;\n            this.state = 4 /* AccordionState.Expanded */;\n            contentEl.style.removeProperty('max-height');\n          });\n        });\n      }\n      else {\n        this.state = 4 /* AccordionState.Expanded */;\n      }\n    };\n    this.collapseAccordion = (initialUpdate = false) => {\n      const { contentEl } = this;\n      if (initialUpdate || contentEl === undefined) {\n        this.state = 1 /* AccordionState.Collapsed */;\n        return;\n      }\n      if (this.state === 1 /* AccordionState.Collapsed */) {\n        return;\n      }\n      if (this.currentRaf !== undefined) {\n        cancelAnimationFrame(this.currentRaf);\n      }\n      if (this.shouldAnimate()) {\n        this.currentRaf = raf(async () => {\n          const contentHeight = contentEl.offsetHeight;\n          contentEl.style.setProperty('max-height', `${contentHeight}px`);\n          raf(async () => {\n            const waitForTransition = transitionEndAsync(contentEl, 2000);\n            this.state = 2 /* AccordionState.Collapsing */;\n            await waitForTransition;\n            this.state = 1 /* AccordionState.Collapsed */;\n            contentEl.style.removeProperty('max-height');\n          });\n        });\n      }\n      else {\n        this.state = 1 /* AccordionState.Collapsed */;\n      }\n    };\n    /**\n     * Helper function to determine if\n     * something should animate.\n     * If prefers-reduced-motion is set\n     * then we should not animate, regardless\n     * of what is set in the config.\n     */\n    this.shouldAnimate = () => {\n      if (typeof window === 'undefined') {\n        return false;\n      }\n      const prefersReducedMotion = matchMedia('(prefers-reduced-motion: reduce)').matches;\n      if (prefersReducedMotion) {\n        return false;\n      }\n      const animated = config.get('animated', true);\n      if (!animated) {\n        return false;\n      }\n      if (this.accordionGroupEl && !this.accordionGroupEl.animated) {\n        return false;\n      }\n      return true;\n    };\n    this.updateState = async (initialUpdate = false) => {\n      const accordionGroup = this.accordionGroupEl;\n      const accordionValue = this.value;\n      if (!accordionGroup) {\n        return;\n      }\n      const value = accordionGroup.value;\n      const shouldExpand = Array.isArray(value) ? value.includes(accordionValue) : value === accordionValue;\n      if (shouldExpand) {\n        this.expandAccordion(initialUpdate);\n        this.isNext = this.isPrevious = false;\n      }\n      else {\n        this.collapseAccordion(initialUpdate);\n        /**\n         * When using popout or inset,\n         * the collapsed accordion items\n         * may need additional border radius\n         * applied. Check to see if the\n         * next or previous accordion is selected.\n         */\n        const nextAccordion = this.getNextSibling();\n        const nextAccordionValue = nextAccordion === null || nextAccordion === void 0 ? void 0 : nextAccordion.value;\n        if (nextAccordionValue !== undefined) {\n          this.isPrevious = Array.isArray(value) ? value.includes(nextAccordionValue) : value === nextAccordionValue;\n        }\n        const previousAccordion = this.getPreviousSibling();\n        const previousAccordionValue = previousAccordion === null || previousAccordion === void 0 ? void 0 : previousAccordion.value;\n        if (previousAccordionValue !== undefined) {\n          this.isNext = Array.isArray(value) ? value.includes(previousAccordionValue) : value === previousAccordionValue;\n        }\n      }\n    };\n    this.getNextSibling = () => {\n      if (!this.el) {\n        return;\n      }\n      const nextSibling = this.el.nextElementSibling;\n      if ((nextSibling === null || nextSibling === void 0 ? void 0 : nextSibling.tagName) !== 'ION-ACCORDION') {\n        return;\n      }\n      return nextSibling;\n    };\n    this.getPreviousSibling = () => {\n      if (!this.el) {\n        return;\n      }\n      const previousSibling = this.el.previousElementSibling;\n      if ((previousSibling === null || previousSibling === void 0 ? void 0 : previousSibling.tagName) !== 'ION-ACCORDION') {\n        return;\n      }\n      return previousSibling;\n    };\n    this.state = 1 /* AccordionState.Collapsed */;\n    this.isNext = false;\n    this.isPrevious = false;\n    this.value = `ion-accordion-${accordionIds++}`;\n    this.disabled = false;\n    this.readonly = false;\n    this.toggleIcon = chevronDown;\n    this.toggleIconSlot = 'end';\n  }\n  valueChanged() {\n    this.updateState();\n  }\n  connectedCallback() {\n    var _a;\n    const accordionGroupEl = (this.accordionGroupEl = (_a = this.el) === null || _a === void 0 ? void 0 : _a.closest('ion-accordion-group'));\n    if (accordionGroupEl) {\n      this.updateState(true);\n      addEventListener(accordionGroupEl, 'ionValueChange', this.updateListener);\n    }\n  }\n  disconnectedCallback() {\n    const accordionGroupEl = this.accordionGroupEl;\n    if (accordionGroupEl) {\n      removeEventListener(accordionGroupEl, 'ionValueChange', this.updateListener);\n    }\n  }\n  componentDidLoad() {\n    this.setItemDefaults();\n    this.slotToggleIcon();\n    /**\n     * We need to wait a tick because we\n     * just set ionItem.button = true and\n     * the button has not have been rendered yet.\n     */\n    raf(() => {\n      /**\n       * Set aria label on button inside of ion-item\n       * once the inner content has been rendered.\n       */\n      const expanded = this.state === 4 /* AccordionState.Expanded */ || this.state === 8 /* AccordionState.Expanding */;\n      this.setAria(expanded);\n    });\n  }\n  toggleExpanded() {\n    const { accordionGroupEl, value, state } = this;\n    if (accordionGroupEl) {\n      /**\n       * Because the accordion group may or may\n       * not allow multiple accordions open, we\n       * need to request the toggling of this\n       * accordion and the accordion group will\n       * make the decision on whether or not\n       * to allow it.\n       */\n      const expand = state === 1 /* AccordionState.Collapsed */ || state === 2 /* AccordionState.Collapsing */;\n      accordionGroupEl.requestAccordionToggle(value, expand);\n    }\n  }\n  render() {\n    const { disabled, readonly } = this;\n    const mode = getIonMode(this);\n    const expanded = this.state === 4 /* AccordionState.Expanded */ || this.state === 8 /* AccordionState.Expanding */;\n    const headerPart = expanded ? 'header expanded' : 'header';\n    const contentPart = expanded ? 'content expanded' : 'content';\n    this.setAria(expanded);\n    return (h(Host, { class: {\n        [mode]: true,\n        'accordion-expanding': this.state === 8 /* AccordionState.Expanding */,\n        'accordion-expanded': this.state === 4 /* AccordionState.Expanded */,\n        'accordion-collapsing': this.state === 2 /* AccordionState.Collapsing */,\n        'accordion-collapsed': this.state === 1 /* AccordionState.Collapsed */,\n        'accordion-next': this.isNext,\n        'accordion-previous': this.isPrevious,\n        'accordion-disabled': disabled,\n        'accordion-readonly': readonly,\n        'accordion-animated': this.shouldAnimate(),\n      } }, h(\"div\", { onClick: () => this.toggleExpanded(), id: \"header\", part: headerPart, \"aria-controls\": \"content\", ref: (headerEl) => (this.headerEl = headerEl) }, h(\"slot\", { name: \"header\" })), h(\"div\", { id: \"content\", part: contentPart, role: \"region\", \"aria-labelledby\": \"header\", ref: (contentEl) => (this.contentEl = contentEl) }, h(\"div\", { id: \"content-wrapper\", ref: (contentElWrapper) => (this.contentElWrapper = contentElWrapper) }, h(\"slot\", { name: \"content\" })))));\n  }\n  static get delegatesFocus() { return true; }\n  get el() { return getElement(this); }\n  static get watchers() { return {\n    \"value\": [\"valueChanged\"]\n  }; }\n};\nlet accordionIds = 0;\nAccordion.style = {\n  ios: accordionIosCss,\n  md: accordionMdCss\n};\n\nconst accordionGroupIosCss = \":host{display:block}:host(.accordion-group-expand-inset){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:16px;margin-bottom:16px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanding),:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanded){border-bottom:none}\";\n\nconst accordionGroupMdCss = \":host{display:block}:host(.accordion-group-expand-inset){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:16px;margin-bottom:16px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion){-webkit-box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanding),:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanded){margin-left:0;margin-right:0;margin-top:16px;margin-bottom:16px;border-radius:6px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-previous){border-bottom-right-radius:6px;border-bottom-left-radius:6px}:host-context([dir=rtl]):host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-previous),:host-context([dir=rtl]).accordion-group-expand-inset ::slotted(ion-accordion.accordion-previous){border-bottom-right-radius:6px;border-bottom-left-radius:6px}@supports selector(:dir(rtl)){:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-previous):dir(rtl){border-bottom-right-radius:6px;border-bottom-left-radius:6px}}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-next){border-top-left-radius:6px;border-top-right-radius:6px}:host-context([dir=rtl]):host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-next),:host-context([dir=rtl]).accordion-group-expand-inset ::slotted(ion-accordion.accordion-next){border-top-left-radius:6px;border-top-right-radius:6px}@supports selector(:dir(rtl)){:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-next):dir(rtl){border-top-left-radius:6px;border-top-right-radius:6px}}:host(.accordion-group-expand-inset) ::slotted(ion-accordion):first-of-type{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}\";\n\nconst AccordionGroup = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionValueChange = createEvent(this, \"ionValueChange\", 7);\n    this.animated = true;\n    this.multiple = undefined;\n    this.value = undefined;\n    this.disabled = false;\n    this.readonly = false;\n    this.expand = 'compact';\n  }\n  valueChanged() {\n    const { value, multiple } = this;\n    if (!multiple && Array.isArray(value)) {\n      /**\n       * We do some processing on the `value` array so\n       * that it looks more like an array when logged to\n       * the console.\n       * Example given ['a', 'b']\n       * Default toString() behavior: a,b\n       * Custom behavior: ['a', 'b']\n       */\n      printIonWarning(`ion-accordion-group was passed an array of values, but multiple=\"false\". This is incorrect usage and may result in unexpected behaviors. To dismiss this warning, pass a string to the \"value\" property when multiple=\"false\".\n\n  Value Passed: [${value.map((v) => `'${v}'`).join(', ')}]\n`, this.el);\n    }\n    /**\n     * Do not use `value` here as that will be\n     * not account for the adjustment we make above.\n     */\n    this.ionValueChange.emit({ value: this.value });\n  }\n  async disabledChanged() {\n    const { disabled } = this;\n    const accordions = await this.getAccordions();\n    for (const accordion of accordions) {\n      accordion.disabled = disabled;\n    }\n  }\n  async readonlyChanged() {\n    const { readonly } = this;\n    const accordions = await this.getAccordions();\n    for (const accordion of accordions) {\n      accordion.readonly = readonly;\n    }\n  }\n  async onKeydown(ev) {\n    const activeElement = document.activeElement;\n    if (!activeElement) {\n      return;\n    }\n    /**\n     * Make sure focus is in the header, not the body, of the accordion. This ensures\n     * that if there are any interactable elements in the body, their keyboard\n     * interaction doesn't get stolen by the accordion. Example: using up/down keys\n     * in ion-textarea.\n     */\n    const activeAccordionHeader = activeElement.closest('ion-accordion [slot=\"header\"]');\n    if (!activeAccordionHeader) {\n      return;\n    }\n    const accordionEl = activeElement.tagName === 'ION-ACCORDION' ? activeElement : activeElement.closest('ion-accordion');\n    if (!accordionEl) {\n      return;\n    }\n    const closestGroup = accordionEl.closest('ion-accordion-group');\n    if (closestGroup !== this.el) {\n      return;\n    }\n    // If the active accordion is not in the current array of accordions, do not do anything\n    const accordions = await this.getAccordions();\n    const startingIndex = accordions.findIndex((a) => a === accordionEl);\n    if (startingIndex === -1) {\n      return;\n    }\n    let accordion;\n    if (ev.key === 'ArrowDown') {\n      accordion = this.findNextAccordion(accordions, startingIndex);\n    }\n    else if (ev.key === 'ArrowUp') {\n      accordion = this.findPreviousAccordion(accordions, startingIndex);\n    }\n    else if (ev.key === 'Home') {\n      accordion = accordions[0];\n    }\n    else if (ev.key === 'End') {\n      accordion = accordions[accordions.length - 1];\n    }\n    if (accordion !== undefined && accordion !== activeElement) {\n      accordion.focus();\n    }\n  }\n  async componentDidLoad() {\n    if (this.disabled) {\n      this.disabledChanged();\n    }\n    if (this.readonly) {\n      this.readonlyChanged();\n    }\n  }\n  /**\n   * Sets the value property and emits ionChange.\n   * This should only be called when the user interacts\n   * with the accordion and not for any update\n   * to the value property. The exception is when\n   * the app sets the value of a single-select\n   * accordion group to an array.\n   */\n  setValue(accordionValue) {\n    const value = (this.value = accordionValue);\n    this.ionChange.emit({ value });\n  }\n  /**\n   * This method is used to ensure that the value\n   * of ion-accordion-group is being set in a valid\n   * way. This method should only be called in\n   * response to a user generated action.\n   * @internal\n   */\n  async requestAccordionToggle(accordionValue, accordionExpand) {\n    const { multiple, value, readonly, disabled } = this;\n    if (readonly || disabled) {\n      return;\n    }\n    if (accordionExpand) {\n      /**\n       * If group accepts multiple values\n       * check to see if value is already in\n       * in values array. If not, add it\n       * to the array.\n       */\n      if (multiple) {\n        const groupValue = value !== null && value !== void 0 ? value : [];\n        const processedValue = Array.isArray(groupValue) ? groupValue : [groupValue];\n        const valueExists = processedValue.find((v) => v === accordionValue);\n        if (valueExists === undefined && accordionValue !== undefined) {\n          this.setValue([...processedValue, accordionValue]);\n        }\n      }\n      else {\n        this.setValue(accordionValue);\n      }\n    }\n    else {\n      /**\n       * If collapsing accordion, either filter the value\n       * out of the values array or unset the value.\n       */\n      if (multiple) {\n        const groupValue = value !== null && value !== void 0 ? value : [];\n        const processedValue = Array.isArray(groupValue) ? groupValue : [groupValue];\n        this.setValue(processedValue.filter((v) => v !== accordionValue));\n      }\n      else {\n        this.setValue(undefined);\n      }\n    }\n  }\n  findNextAccordion(accordions, startingIndex) {\n    const nextAccordion = accordions[startingIndex + 1];\n    if (nextAccordion === undefined) {\n      return accordions[0];\n    }\n    return nextAccordion;\n  }\n  findPreviousAccordion(accordions, startingIndex) {\n    const prevAccordion = accordions[startingIndex - 1];\n    if (prevAccordion === undefined) {\n      return accordions[accordions.length - 1];\n    }\n    return prevAccordion;\n  }\n  /**\n   * @internal\n   */\n  async getAccordions() {\n    return Array.from(this.el.querySelectorAll(':scope > ion-accordion'));\n  }\n  render() {\n    const { disabled, readonly, expand } = this;\n    const mode = getIonMode(this);\n    return (h(Host, { class: {\n        [mode]: true,\n        'accordion-group-disabled': disabled,\n        'accordion-group-readonly': readonly,\n        [`accordion-group-expand-${expand}`]: true,\n      }, role: \"presentation\" }, h(\"slot\", null)));\n  }\n  get el() { return getElement(this); }\n  static get watchers() { return {\n    \"value\": [\"valueChanged\"],\n    \"disabled\": [\"disabledChanged\"],\n    \"readonly\": [\"readonlyChanged\"]\n  }; }\n};\nAccordionGroup.style = {\n  ios: accordionGroupIosCss,\n  md: accordionGroupMdCss\n};\n\nexport { Accordion as ion_accordion, AccordionGroup as ion_accordion_group };\n"], "names": ["r", "registerInstance", "h", "H", "Host", "f", "getElement", "d", "createEvent", "raf", "t", "transitionEndAsync", "a", "addEventListener", "b", "removeEventListener", "g", "getElementRoot", "l", "chevronDown", "c", "config", "getIonMode", "p", "printIonWarning", "accordionIosCss", "accordionMdCss", "Accordion", "constructor", "hostRef", "_this", "updateListener", "updateState", "setItemDefaults", "ionItem", "getSlottedHeaderIonItem", "button", "detail", "lines", "undefined", "headerEl", "slot", "querySelector", "assignedElements", "find", "el", "tagName", "setAria", "expanded", "root", "setAttribute", "slotToggleIcon", "toggleIconSlot", "toggleIcon", "existingToggleIcon", "iconEl", "document", "createElement", "lazy", "classList", "add", "icon", "append<PERSON><PERSON><PERSON>", "expandAccordion", "initialUpdate", "contentEl", "contentElWrapper", "state", "currentRaf", "cancelAnimationFrame", "shouldAnimate", "_asyncToGenerator", "contentHeight", "offsetHeight", "waitForTransition", "style", "setProperty", "removeProperty", "collapseAccordion", "window", "prefersReducedMotion", "matchMedia", "matches", "animated", "get", "accordionGroupEl", "accordionGroup", "accordionValue", "value", "shouldExpand", "Array", "isArray", "includes", "isNext", "isPrevious", "nextAccordion", "getNextSibling", "nextAccordionValue", "previousAccordion", "getPrevious<PERSON><PERSON>ling", "previousAccordionValue", "nextS<PERSON>ling", "nextElement<PERSON><PERSON>ling", "previousSibling", "previousElementSibling", "accordionIds", "disabled", "readonly", "valueChanged", "connectedCallback", "_a", "closest", "disconnectedCallback", "componentDidLoad", "toggleExpanded", "expand", "requestAccordion<PERSON><PERSON>gle", "render", "mode", "headerPart", "contentPart", "class", "onClick", "id", "part", "ref", "name", "role", "delegatesFocus", "watchers", "ios", "md", "accordionGroupIosCss", "accordionGroupMdCss", "AccordionGroup", "ionChange", "ionValueChange", "multiple", "map", "v", "join", "emit", "disabled<PERSON><PERSON>ed", "_this2", "accordions", "getAccordions", "accordion", "readonly<PERSON><PERSON>ed", "_this3", "onKeydown", "ev", "_this4", "activeElement", "activeAccordionHeader", "accordion<PERSON>l", "closestGroup", "startingIndex", "findIndex", "key", "findNextAccordion", "find<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "focus", "_this5", "setValue", "accordionExpand", "_this6", "groupValue", "processedValue", "valueExists", "filter", "prevAccordion", "_this7", "from", "querySelectorAll", "ion_accordion", "ion_accordion_group"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}