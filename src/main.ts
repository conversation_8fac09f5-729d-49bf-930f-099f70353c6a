import { enableProdMode } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';

import { AppModule } from './app/app.module';
import { environment } from './environments/environment';

import Echo from 'laravel-echo';
import Pusher from 'pusher-js/with-encryption';

if (environment.production) {
  enableProdMode();
}

platformBrowserDynamic().bootstrapModule(AppModule)
  .catch(err => console.log(err));

declare global {
  interface Window {
    Pusher: any;
    Echo: Echo<any>;
  }
}
window.Pusher = Pusher;

/*
window.Echo.channel('messenger').listen('MessageSent', (e: any) => {
  console.log(e);
});*/
