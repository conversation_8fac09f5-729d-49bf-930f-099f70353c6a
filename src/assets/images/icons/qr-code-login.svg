<?xml version="1.0" encoding="UTF-8"?>
<svg version="1.1" viewBox="0 0 1067 1067" width="1067" height="1067" xmlns="http://www.w3.org/2000/svg">
  <path transform="translate(730,349)" d="m0 0h7l14 4 10 6 6 7 4 6 4 13v10l-4 13-7 11-4 5-8 7-8 9-6 5-92 92-11 10-10 6-5 2-1 3h411l14 4 10 7 9 11 2 6 2 1v25l-3 3-3 6-9 11-8 5-12 4-413 1 3 3 14 8 16 15 78 78v2l4 2 17 17 5 6 6 5 7 10 4 8 2 8v11l-4 13-9 12-14 7-12 3-10-1-12-4-9-6-7-8-6-5-6-7-7-6-7-8-5-5-13-12-5-6-6-5-7-8-12-12-6-5-5-6-7-6-7-8-5-5-13-12-5-6-5-4-7-8-6-5-7-8-5-4-5-6-7-6-7-8-5-5-13-12-5-6-6-5-10-13-2-7h-2v-26l2-1 3-7 11-13 31-31 4-5 8-7 4-5 8-7 5-6 7-5 5-7 8-7 5-6 8-7 84-84 4-5 8-7 4-5 16-14 10-5z" fill="var(--app-svg-second-color, #06244f)"/>
  <path transform="translate(130,500)" d="m0 0h307l3 2v61l-124 1-1 185 62 1v61l-3 2-58 1-1 60h123l1-1 1-59 1-1h46l1 2 1 59 73 1 3 2v59l-4 2h-305l-4-2-1-59-2-1-59-1v-62l59-1 2-1v-122l-61-1-1-59 3-4 60-1-1-60-120-1-4-2v-59z" fill="var(--app-svg-color, #34578a)"/>
  <path transform="translate(130,125)" d="m0 0h307l3 2 1 59 2 1 59 1v62l-59 1-2 1-1 184-4 2h-305l-4-2v-309zm61 63-1 1v185l2 1h183l2-1v-185l-1-1z" fill="var(--app-svg-color, #34578a)"/>
  <path transform="translate(630,125)" d="m0 0h307l3 2v309l-4 2h-75l-3-3v-57l2-3 17-1-1-185h-185l-1 159-2 2h-59l-2-2v-221z" fill="var(--app-svg-color, #34578a)"/>
  <path transform="translate(881,671)" d="m0 0h57l2 2v263l-4 2h-305l-4-2v-92l2-2h59l2 2v30h187v-118l1-2 1-81z" fill="var(--app-svg-color, #34578a)"/>
  <path transform="translate(1e3 817)" d="m0 0h62v250h-250v-62l2-1 184-1 1-184z" fill="var(--app-svg-color, #34578a)"/>
  <path transform="translate(0,817)" d="m0 0h62l1 2 1 184 184 1 2 1v62h-250z" fill="var(--app-svg-color, #34578a)"/>
  <path transform="translate(817)" d="m0 0h250v250h-62l-1-2-1-184-184-1-2-1z" fill="var(--app-svg-color, #34578a)"/>
  <path transform="translate(0)" d="m0 0h250v62l-2 1-184 1-1 184-1 2h-62z" fill="var(--app-svg-color, #34578a)"/>
  <path transform="translate(438,624)" d="m0 0 2 1v123l-2 2h-61v-123l3-2z" fill="var(--app-svg-color, #34578a)"/>
  <path transform="translate(129,688)" d="m0 0h61v125h-59l-4-2v-121z" fill="var(--app-svg-color, #34578a)"/>
  <path transform="translate(502,250)" d="m0 0h60l3 2v121l-2 2h-59l-2-2z" fill="var(--app-svg-color, #34578a)"/>
  <path transform="translate(505,125)" d="m0 0h57l3 2v59l-4 2h-59v-61z" fill="var(--app-svg-color, #34578a)"/>
  <path transform="translate(130,875)" d="m0 0h60v61l-4 2h-55l-4-2v-59z" fill="var(--app-svg-color, #34578a)"/>
  <path transform="translate(755,250)" d="m0 0h57l3 2v59l-4 2h-55l-4-2v-59z" fill="var(--app-svg-color, #34578a)"/>
  <path transform="translate(255,250)" d="m0 0h57l3 2v59l-4 2h-55l-4-2v-59z" fill="var(--app-svg-color, #34578a)"/>
  <path transform="translate(130,500)" d="m0 0h307l3 2v61l-124 1-1 185 62 1v61l-3 2-58 1-1 60h123l1-1 1-59 1-1h46l-1 2h-44v58l-3 3h-124l-2-2v-60l2-2 58-1 2-1v-57l-1-1-59-1-2-2v-185l2-2 122-1 1-59h-309v59l2 1-4-1v-59z" fill="var(--app-svg-color, #34578a)"/>
  <path transform="translate(130,125)" d="m0 0h307l3 2 1 59 2 1 59 1v62l-59 1-2 1-1 184-4 1 2-1v-185l3-2 59-1v-58l-58-1-4-3v-59h-309v309l2 1-4-1v-309z" fill="var(--app-svg-color, #34578a)"/>
  <path transform="translate(131,562)" d="m0 0h121l2 2v60l-2 2-59 1-1 5v53l1 1 59 1 2 2v122l-4 3-58 1v58l59 1 3 2v60l2 1-4-1-1-59-2-1-59-1v-62l59-1 2-1v-122l-61-1-1-59 3-4 60-1-1-60-120-1z" fill="var(--app-svg-color, #34578a)"/>
  <path transform="translate(631,547)" d="m0 0 2 1-11 6-2 1-1 3h411l14 4 10 7 9 11 2 6 2 1v4l-3-1-4-10-9-11-8-5-11-3-413-1-3-2 1-4z" fill="var(--app-svg-second-color, #06244f)"/>
  <path transform="translate(1053,628)" d="m0 0v3l-9 6-12 4-413 1 3 3 14 8 16 15 78 78-1 2-86-86-11-9-16-8-1-3 5-3h407l16-4z" fill="var(--app-svg-second-color, #06244f)"/>
  <path transform="translate(438,624)" d="m0 0 2 1v123l-2 2h-61v-123l3-2zm-57 2-2 1v121l1 1h57l1-1v-121l-1-1z" fill="var(--app-svg-color, #34578a)"/>
  <path transform="translate(129,688)" d="m0 0h61v125h-59l-4-2v-121zm1 1-1 1v121l2 1h55l2-1v-121l-1-1z" fill="var(--app-svg-color, #34578a)"/>
  <path transform="translate(502,250)" d="m0 0h60l3 2v121l-2 2h-59l-2-2zm3 1-1 1v121l1 1h57l1-1v-121l-2-1z" fill="var(--app-svg-color, #34578a)"/>
  <path transform="translate(505,125)" d="m0 0h57l3 2v59l-4 2h-59v-61zm1 1-2 1v59l2 1h55l2-1v-59l-2-1z" fill="var(--app-svg-color, #34578a)"/>
  <path transform="translate(130,875)" d="m0 0h60v61l-4 1 2-1-1-59h-58v59l2 1-4-1v-59z" fill="var(--app-svg-color, #34578a)"/>
  <path transform="translate(190,815)" d="m0 0h1l1 5v53l59 1 3 2v60l2 1-4-1-1-59-2-1-59-1z" fill="var(--app-svg-color, #34578a)"/>
  <path transform="translate(438,503)" d="m0 0h2v60h-121v-1l118-1z" fill="var(--app-svg-color, #34578a)"/>
  <path transform="translate(617,640)" d="m0 0h2l1 4 16 9 16 15 78 78-1 2-86-86-11-9-16-8-1-3z" fill="var(--app-svg-second-color, #06244f)"/>
  <path transform="translate(636,768)" d="m0 0 4 2 8 9 7 6 5 6 8 7 12 12 5 6 8 7 16 16 9 5 11 3h10l11-3 3 1-11 4-10 1-12-3-7-3-8-5-7-8-6-5-6-7-7-6-7-8-5-5-13-12-5-6-6-5-7-8z" fill="var(--app-svg-second-color, #06244f)"/>
  <path transform="translate(190,630)" d="m0 0 2 2v53l1 1 55 1v1h-58z" fill="var(--app-svg-color, #34578a)"/>
  <path transform="translate(762,780)" d="m0 0 5 5 5 8 3 11v11l-4 13-9 12-7 4-2-1 6-4h2l2-5 6-7 3-10v-15l-3-10-7-10z" fill="var(--app-svg-second-color, #06244f)"/>
  <path transform="translate(1032,559)" d="m0 0 9 2 8 4 6 5 8 10 2 6 2 1v4l-3-1-4-10-9-11-8-5-11-3z" fill="var(--app-svg-second-color, #06244f)"/>
  <path transform="translate(317,750)" d="m0 0h60v7l-2-4-1-1-57-1z" fill="var(--app-svg-color, #34578a)"/>
  <path transform="translate(508,557)" d="m0 0v3l-10 10-9 12-3 9h-2v-5l2-1 3-7 11-13z" fill="var(--app-svg-second-color, #06244f)"/>
  <path transform="translate(631,547)" d="m0 0 2 1-11 6-2 1-1 3h11l-1 2h-10l-3-2 1-4z" fill="var(--app-svg-second-color, #06244f)"/>
  <path transform="translate(773,816)" d="m0 0 1 3-4 10-8 11-7 4-2-1 6-4h2l2-5 6-7 3-10z" fill="var(--app-svg-second-color, #06244f)"/>
  <path transform="translate(1065,608)" d="m0 0h2v4l-3 3-3 6-6 7v-3l6-8z" fill="var(--app-svg-second-color, #06244f)"/>
  <path transform="translate(617,640)" d="m0 0h2l1 4 10 6-2 1-12-6-1-3z" fill="var(--app-svg-second-color, #06244f)"/>
  <path transform="translate(631,547)" d="m0 0 2 1-11 6-4 3-1-3z" fill="var(--app-svg-second-color, #06244f)"/>
</svg>
