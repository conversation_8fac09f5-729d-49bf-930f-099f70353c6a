/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import '@ionic/angular/css/display.css';

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";
@import "@emran-alhaddad/saudi-riyal-font/index.css";

/* Dark Mode Support - Uses CSS class for manual control */
@import "@ionic/angular/css/palettes/dark.class.css";
//@import '@ionic/angular/css/palettes/dark.always.css';




@font-face {
  font-family: 'helvetica';
  src: url('/assets/font/helveticaneuelt_arabic_45_light.woff2') format('woff2'),
  url('/assets/font/helveticaneuelt_arabic_45_light.woff') format('woff'),
  url('/assets/font/helveticaneuelt_arabic_45_light.ttf') format('truetype');;
  font-weight: 300;
  font-style: normal;

}


@font-face {
  font-family: 'helvetica';
  src: url('/assets/font/helveticaneuelt-arabic-55-roman.woff2') format('woff2'),
  url('/assets/font/helveticaneuelt-arabic-55-roman.woff') format('woff'),
  url('/assets/font/helveticaneuelt-arabic-55-roman.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;

}

@font-face {
  font-family: 'helvetica';
  src: url('/assets/font/helveticaneuelt_arabic_75_bold.woff2') format('woff2'),
  url('/assets/font/helveticaneuelt_arabic_75_bold.woff') format('woff'),
  url('/assets/font/helveticaneuelt_arabic_75_bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
}

@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@100;200;300;400;500;600;700;800;900&display=swap');

.secondary-font {
  font-family: 'Noto Sans Arabic', sans-serif;
}

.news-image {
  max-width: 500px;
  margin: auto;
}

.max-width {
  max-width: 370px;
  margin: auto;
}

.altwijry-card {
  width: 100%;
  max-width: 370px;
  margin: auto;
  border-radius: 8px;
  background: var(--ion-color-light);
  min-height: 200px;
  display: flex;
  justify-content: center;
  flex-wrap: nowrap;
  flex-direction: column;
  align-items: center;

}

h1 {
  font-size: 1.7rem !important;
}

h2 {
  font-size: 1.4rem !important;
}

h3 {
  font-size: 1.3rem !important;
}

h4 {
  font-size: 1rem !important;
}

h5, div, p {
  font-size: 0.8rem !important;
}

h6 {
  font-size: 0.7rem !important;
}

.altwijry-delimiter:before {
  display: inline-block;
  content: "***";
  font-size: 50px;
  line-height: 65px;
  text-align: center;
  margin: auto;
  letter-spacing: 0.2em;
  vertical-align: middle;
  width: 100%;
}

swiper-container {
  --swiper-pagination-bullet-inactive-color: var(--ion-color-step-200, #cccccc);
  --swiper-pagination-color: var(--ion-color-secondary, #DBA852);
  --swiper-pagination-progressbar-bg-color: rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.25);
  --swiper-scrollbar-bg-color: rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.1);
  --swiper-scrollbar-drag-bg-color: rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.5);
}

.ion-flex {
  display: flex;
}

.ion-flex-column {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.visibly-hidden {
  visibility: hidden;
}

ion-item:not(:first-of-type):not(:last-of-type) {
  --border-radius: 0px;
}

ion-item:first-of-type:not(:last-of-type) {
  --border-radius: 8px 8px 0 0px;
}

ion-item:last-of-type:not(:first-of-type) {
  --border-radius: 0px 0px 8px 8px;
}

ion-item:only-of-type {
  --border-radius: 8px;
}

ion-title {
  line-height: 2.5;
  color: var(--ion-color-medium);
}

.ion-color-light {
  --ion-color-base: transaprent !important;
}

@media (prefers-color-scheme: dark) {
  ion-note {
    color: var(--ion-color-light-contrast);
  }

}

.saudi_riyal-font {
  font-family: 'saudi_riyal', var(--ion-font-family), system-ui;
  line-height: 2 !important;
}

input {
  line-height: 2 !important;
}

.pointer {
  cursor: pointer;
}
