import {AfterViewInit, Component, ElementRef, Inject, NgZone, Renderer2} from '@angular/core';
import {addIcons} from "ionicons";
import {App, URLOpenListenerEvent} from '@capacitor/app';
import {Router} from "@angular/router";
import {GenericService} from "./shared/services/generic.service";
import {AppDataService} from "./shared/services/app-data.service";
import {DOCUMENT} from '@angular/common';
import {SplashScreen} from '@capacitor/splash-screen';
import {Platform} from "@ionic/angular";
import {ThemeService} from "./shared/services/theme.service";

@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.scss'],
})
export class AppComponent implements AfterViewInit {
  constructor(@Inject(DOCUMENT) private document: Document,
              private router: Router,
              private platform: Platform,
              private el: ElementRef,
              private zone: NgZone,
              public appDataService: AppDataService,
              private themeService: ThemeService,
              private genericService: GenericService) {
    this.initializeApp();
    addIcons({
      'person-circle-outline': 'assets/images/icons/person-circle-outline.svg',
      'altwijry-branches': 'assets/images/icons/branches.svg',
      'arrow-expand': 'assets/images/icons/arrow-expand.svg',
      'altwijry-commerce': 'assets/images/icons/ecommerce.svg',
      'altwijry-book': 'assets/images/icons/book.svg',
      'altwijry-ideas': 'assets/images/icons/ideas.svg',
      'altwijry-quran': 'assets/images/icons/quran.svg',
      'altwijry-document': 'assets/images/icons/document.svg',
      'altwijry-hall': 'assets/images/icons/hall.svg',
      'altwijry-org-chart': 'assets/images/icons/org-chart.svg',
      'altwijry-survey': 'assets/images/icons/survey.svg',
      'altwijry-companies': 'assets/images/icons/companies.svg',
      'altwijry-news': 'assets/images/icons/news.svg',
      'altwijry-active-members': 'assets/images/icons/active-members.svg',
      'altwijry-badge': 'assets/images/icons/badge.svg',
      'eid-icon': 'assets/images/icons/eid-icon.svg',
      'congrats-arabic': 'assets/images/icons/congrats_arabic.svg',
      'wqfk-icon': 'assets/images/icons/wqfk.svg',
      'altwijry-back-button': 'assets/images/icons/back-button.svg',
      'altwijry-mobile': 'assets/images/icons/mobile-login.svg',
      'altwijry-checked': 'assets/images/icons/checked.svg',
      'altwijry-cancel': 'assets/images/icons/cancel.svg',
      'altwijry-mail': 'assets/images/icons/mail-icon.svg',
      'altwijry-login': 'assets/images/icons/login-rounded.svg',
      'altwijry-key': 'assets/images/icons/key.svg',
      'alrajhi': 'assets/images/icons/alrajhi-light.svg',
      'alrajhi-dark': 'assets/images/icons/alrajhi-dark.svg',
      'altwijry-offers': 'assets/images/icons/offers.svg',
      'award-users': 'assets/images/icons/award-users.svg',
      'strava-timer': 'assets/images/icons/strava-timer.svg',
      'strava-map': 'assets/images/icons/strava-map.svg',
      'strava-walk': 'assets/images/icons/strava-walk.svg',
      'altwijry-username-error': 'assets/images/icons/username-error.svg',
      'membership-premium': 'assets/images/icons/membership-premium.svg',
      'membership-moshark': 'assets/images/icons/membership-moshark.svg',
      'membership-3amel': 'assets/images/icons/membership-3amel.svg',
      'membership-montseb': 'assets/images/icons/membership-montseb.svg',
      'altwijry-logo-gold': 'assets/images/icons/logo-gold.svg',
      'check': 'assets/images/icons/check.svg',
      'logo-x': 'assets/images/icons/x_logo.svg',
      'logo-snapchat': 'assets/images/icons/logo_snapchat.svg',
      'logo-telegram': 'assets/images/icons/logo_telegram.svg',
      'hand': 'assets/images/icons/hand.svg',
      'people': 'assets/d3-images/people.svg',
      'male-icon': 'assets/d3-images/male-icon.svg',
      'female-icon': 'assets/d3-images/female-icon.svg',
      'qr-code-login': 'assets/images/icons/qr-code-login.svg',
      'two-points-route': 'assets/images/icons/2points_route_map.svg',
      'speed-watch': 'assets/images/icons/speed-watch.svg',
      'eclipse': 'assets/images/icons/eclipse.svg',
      'letter-message': 'assets/images/icons/letter.svg',
      'custom-stats': 'assets/images/icons/stats.svg',
      'custom-quran-competition': 'assets/images/icons/quran_competition.svg',
    });
  }

  ngAfterViewInit() {
    const root = this.document.documentElement;
    const body = this.document.body;
    const titleTag = this.document.querySelector('title');
    this.appDataService.data$.subscribe(data => {
      if (titleTag && data['name'] && typeof data['name'] === 'string')
        titleTag.innerText = data['name'];
      const dummyColors: { light: { [key: string]: string }; dark: { [key: string]: string } } = {
        light: {
          "primary": '#004AAC',         // Material Red 500
          "secondary": '#7ea9e3',       // Deep Purple 500
          "--app-svg-color": '#004AAC',
          "--app-svg-second-color": '#7ea9e3'
        },
        dark: {
          "primary": '#004AAC',         // Material Red 400
          "secondary": '#7ea9e3',       // Deep Purple 400
          "--app-svg-color": '#004AAC',
          "--app-svg-second-color": '#7ea9e3'
        }
      }

      this.themeService.initializeTheme(data['themeColors'] ?? dummyColors);

    });
  }

  async initializeApp() {
    this.platform.ready().then(() => {
      SplashScreen.hide();
    })


    this.initDeepLinks();
  }

  private initDeepLinks() {
    // Only initialize deeplinks after the platform is ready
    this.platform.ready().then(() => {
      App.addListener('appUrlOpen', (event: URLOpenListenerEvent) => {
        console.log('Deep link received:', event.url);

        this.zone.run(() => {
          try {
            // Better URL parsing using URL API
            const url = new URL(event.url);
            const fullPath = url.pathname + url.search;

            // List of paths to ignore (not handle as deeplinks)
            const ignorePaths = [
              '/pay',
              '/external-auth',
              '/me/strava/callback'
            ];

            // Check if the URL should be ignored
            const shouldIgnore = ignorePaths.some(path => fullPath.includes(path));

            if (shouldIgnore) {
              console.log(`Ignoring deeplink for path: ${fullPath}`);
              return;
            }

            // Extract query parameters if needed
            const params = Object.fromEntries(url.searchParams.entries());
            console.log('Deeplink params:', params);

            // If there's a path to navigate to
            if (fullPath && fullPath !== '/') {
              console.log(`Navigating to: ${fullPath}`);

              // Remove any extra slashes at the beginning
              const normalizedPath = fullPath.replace(/^\/+/, '/');

              this.router.navigateByUrl(normalizedPath, {
                replaceUrl: true,
                state: {deepLink: true, params}
              });
            } else {
              console.log('Deeplink has no specific path, using default routing');
              // Navigate to default route if needed
              // this.router.navigate(['/home']);
            }
          } catch (error) {
            console.error('Error handling deeplink:', error);
            // Fallback to your original approach if URL parsing fails
            const slug = event.url.split(".sa").pop();
            if (slug && !slug.includes('/pay?uuid=')) {
              this.router.navigateByUrl(slug);
            }
          }
        });
      });

      console.log('Deeplink listener initialized');
    });
  }
}
