import {LOCALE_ID, NgModule} from '@angular/core';
import {BrowserModule} from '@angular/platform-browser';
import {RouteReuseStrategy, RouterModule} from '@angular/router';
import {IonicModule, IonicRouteStrategy} from '@ionic/angular';
import {AppComponent} from './app.component';
import {HeaderComponent} from "./layout/header/header.component";
import {HomePage} from "./pages/home/<USER>";
import {SoonPage} from "./pages/soon/soon.page";
import {CardComponent} from "./shared/components/card/card.component";
import player from 'lottie-web';

// register locale
import {DatePipe, registerLocaleData} from '@angular/common';
import localeAr from '@angular/common/locales/ar';
import {LoginPage} from "./pages/login/login-page.component";
import {CUSTOM_ELEMENTS_SCHEMA} from '@angular/core';
import {HTTP_INTERCEPTORS, HttpClient} from "@angular/common/http";
import {AltwijryHttpInterceptor} from "./shared/services/http.interceptor";

// import function to register Swiper custom elements
import {BrowserAnimationsModule} from "@angular/platform-browser/animations";
import {NgxGoogleAnalyticsModule, NgxGoogleAnalyticsRouterModule} from "@dustfoundation/ngx-google-analytics";
import { QRCodeModule } from 'angularx-qrcode';

/** Import Alyle UI */
import {
  LyTheme2,
  StyleRenderer,
  LY_THEME,
  LY_THEME_NAME,
} from '@alyle/ui';

/** Import themes */
import {MinimaLight, MinimaDark} from '@alyle/ui/themes/minima';


registerLocaleData(localeAr, 'ar');
const PAGES = [HomePage, SoonPage, LoginPage]
const STANDALONE_COMPONENTS = [HeaderComponent, CardComponent]

import { APP_INITIALIZER, ErrorHandler } from "@angular/core";
import { Router } from "@angular/router";
import {NgxSplideModule} from "ngx-splide";
import {AppDataService} from "./shared/services/app-data.service";
import {FeaturesService} from "./shared/services/features.service";
import {firstValueFrom} from "rxjs";
import {IonicStorageModule} from "@ionic/storage-angular";
import { Drivers } from '@ionic/storage';
import {StorageService} from "./shared/services/storage.service";
import {provideLottieOptions} from "ngx-lottie";

function initializeAppFactory(
  httpClient: HttpClient,
  appdataService: AppDataService,
  featuresService: FeaturesService,
  storageService: StorageService,
  router: Router
): () => Promise<any> {
  return async () => {
    await storageService.init();
    await appdataService.initialize();
    await firstValueFrom(featuresService.loadFeatures());

    // Load routes after storage initialization
    const routes = await import('./app-routing.module').then(m => m.routes);
    await router.resetConfig(routes);
  };
}



// Necessary to solve the problem of losing internet connection
@NgModule({
  declarations: [AppComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [
    QRCodeModule,
    BrowserModule,
    BrowserAnimationsModule,
    NgxGoogleAnalyticsModule.forRoot('G-T6CPKS9052'),
    NgxGoogleAnalyticsRouterModule,
    NgxSplideModule,
    IonicStorageModule.forRoot({
      name: '__altuwaijri',
      driverOrder: [Drivers.LocalStorage]
    }),
    RouterModule.forRoot([], {
      bindToComponentInputs: true
    }),
    IonicModule.forRoot(
      {
        mode: 'ios',
        swipeBackEnabled: true,
        animated: true,
        hardwareBackButton: true,
        backButtonText: 'رجوع',
        backButtonIcon: 'chevron-back-outline',
      }
    ),
    // BrowserModule.withServerTransition({appId: 'serverApp'}),
    ...STANDALONE_COMPONENTS,
    ...PAGES,
  ],
  providers: [
    [LyTheme2],
    [StyleRenderer],
    // Theme that will be applied to this module
    {provide: LY_THEME_NAME, useValue: 'minima-light'},
    {provide: LY_THEME, useClass: MinimaLight, multi: true}, // name: `minima-light`
    {provide: LY_THEME, useClass: MinimaDark, multi: true}, // name: `minima-dark`

    {provide: RouteReuseStrategy, useClass: IonicRouteStrategy},
    DatePipe,
    {
      provide: LOCALE_ID,
      useValue: 'ar'
    },
    {provide: HTTP_INTERCEPTORS, useClass: AltwijryHttpInterceptor, multi: true},
    {provide: 'Window', useValue: window},
    {
      provide: APP_INITIALIZER,
      useFactory: initializeAppFactory,
      deps: [HttpClient, AppDataService, FeaturesService, StorageService, Router],
      multi: true
    },
    provideLottieOptions({
      player: () => player,
    }),
  ],
  bootstrap: [AppComponent],
})
export class AppModule {
}
