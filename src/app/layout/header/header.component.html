<ion-header mode="ios" collapse="fade">
  <ion-toolbar>
    <ion-grid [fixed]="true" style="max-width: 370px">
      <ion-row class="ion-justify-content-center">
        <ion-col size="12" class="ion-justify-content-center ion-text-center ion-align-items-center"
                 style="display: flex">
          <ion-button fill="clear" (click)="goBack()" *ngIf="showBackButton" class="ion-justify-content-end"
                      style="position: absolute; right: -10px; rotate: 180deg;">
            <ion-icon name="altwijry-back-button" slot="icon-only"></ion-icon>
          </ion-button>
          <!--<img *ngIf="!hideLogo"
               width="20%" height="100%" [style]="{'max-width': logoSize}"
               src="/assets/icon/logo.svg"
               alt="الشعار" routerLink="/home">-->
          <img *ngIf="!hideLogo"
               width="20%" height="100%" [style]="{'max-width': logoSize}"
               [src]="(colorScheme === 'dark' ? ((appDataService.data$ | async)!['logoPathDark']) : ((appDataService.data$ | async)!['logoPathLight'])) || ((appDataService.data$ | async)!['iconPath']) || '/assets/icon/logo.svg'"
               alt="الشعار" routerLink="/home">
          <ng-container *ngIf="leftButtonTemplate; else defaultLeftButton">
            <ng-container *ngTemplateOutlet="leftButtonTemplate"></ng-container>
          </ng-container>
          <ng-template #defaultLeftButton>
            <ion-button fill="clear" routerLink="/settings"
                        *ngIf="supportedVersion && !hideProfileIcon" class="ion-justify-content-start"
                        style="position: absolute; left: -10px;">
              <ion-icon name="person-circle-outline" slot="icon-only" size="large"></ion-icon>
            </ion-button>
          </ng-template>
        </ion-col>
      </ion-row>
      <ion-row *ngIf="title">
        <ion-col size="12" class="ion-justify-content-center ion-text-center ion-align-items-center">
          <ion-text class="ion-text-center">
            <h1 style="margin-top: 0">
              {{ title }}
            </h1>
          </ion-text>
        </ion-col>
      </ion-row>
    </ion-grid>
  </ion-toolbar>
</ion-header>
