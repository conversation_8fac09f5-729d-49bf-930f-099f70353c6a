import {Component, Input, OnInit, TemplateRef} from '@angular/core';
import {IonicModule, NavController, Platform} from "@ionic/angular";
import {AsyncPipe, NgIf, NgOptimizedImage, NgTemplateOutlet} from "@angular/common";
import {RouterModule} from "@angular/router";
import {ThemeService} from "../../shared/services/theme.service";
import {AppDataService} from "../../shared/services/app-data.service";
import * as semver from 'semver';
import {App} from '@capacitor/app';
import {GenericService} from "../../shared/services/generic.service";

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    NgIf,
    RouterModule,
    NgOptimizedImage,
    NgTemplateOutlet,
    AsyncPipe,
  ]
})
export class HeaderComponent implements OnInit {
  @Input() hideProfileIcon = false;
  @Input() hideLogo = false;
  @Input() showBackButton = false
  @Input() backTo = ''
  @Input() title = ''
  @Input() logoSize = '150px'
  @Input() leftButtonTemplate: TemplateRef<any> | null = null;
  supportedVersion = false;
  colorScheme = 'light'

  constructor(
    private navCtrl: NavController,
    public themeService: ThemeService,
    public appDataService: AppDataService,
    public platform: Platform,
    public genericService: GenericService,
  ) {
  }

  ngOnInit() {
    this.themeService.isDark.subscribe(res => {
      this.colorScheme = res === true ? 'dark' : 'light'
    })
    this.appDataService.data$.subscribe(data => {
      if (!data) return;
      if (this.genericService.isCapacitorApp()) {
        if (data['iosMinVersion'])
          App.getInfo().then(res => {
            let appVersion = res.version
            let minVersion = data['iosMinVersion'] as string;
            this.supportedVersion = semver.gte(appVersion, minVersion);
          })
      } else {
        this.supportedVersion = true;
      }
    });
  }

  goBack() {
    if (this.backTo)
      this.navCtrl.navigateBack(this.backTo).then();
    else
      this.navCtrl.pop().then();
  }
}
