import {Component, ElementRef, Input, OnChanges, OnDestroy, SimpleChanges, ViewChild, AfterViewInit} from '@angular/core';
import {IonicModule} from "@ionic/angular";
import {RouterModule} from "@angular/router";
import {FormsModule} from "@angular/forms";
import {NgxGoogleAnalyticsModule} from "@dustfoundation/ngx-google-analytics";
import {GenericService} from "../../../shared/services/generic.service";
import {Directory, Filesystem} from "@capacitor/filesystem";
import {Share} from '@capacitor/share';
import {Card, CardTextField} from "../../../shared/models";
import {ApiService} from 'src/app/shared/services/api.service';
import * as fabric from 'fabric';

@Component({
  selector: 'card-box',
  templateUrl: './card-box.component.html',
  styleUrls: ['./card-box.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    RouterModule,
    FormsModule,
    NgxGoogleAnalyticsModule
  ]
})
export class CardBoxComponent implements OnChanges, AfterViewInit, OnDestroy {
  @ViewChild('myCanvas', {static: false}) canvasEl!: ElementRef;

  private fabricCanvas: fabric.Canvas | null = null;
  public finalImage: any;

  // Display configuration
  private readonly MAX_DISPLAY_WIDTH = 370;

  // Store original dimensions for high-res export
  private originalWidth: number = 0;
  private originalHeight: number = 0;
  private originalZoom: number = 1;

  @Input() card!: Card;
  @Input() inputValues: { [key: string]: string } = {};
  @Input() userAvatarUrl: string | null = null;
  @Input() showAvatar: boolean = false;

  constructor(
    private genericService: GenericService,
    private apiService: ApiService
  ) {
  }

  ngAfterViewInit() {
    // Initialize Fabric.js canvas after view is initialized
    if (this.canvasEl && this.canvasEl.nativeElement) {
      this.initializeFabricCanvas();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    // If card changes and we have fabric JSON, load it
    if (changes['card'] && this.card?.fabric_json && this.fabricCanvas) {
      this.loadFabricJSON();
    }

    // If input values change, update the text fields
    if (changes['inputValues'] && this.fabricCanvas) {
      this.updateTextFields();
    }

    // If avatar changes, update it
    if ((changes['userAvatarUrl'] || changes['showAvatar']) && this.fabricCanvas) {
      this.updateAvatar();
    }
  }

  ngOnDestroy() {
    // Clean up Fabric.js canvas
    if (this.fabricCanvas) {
      this.fabricCanvas.dispose();
    }
  }

  private initializeFabricCanvas() {
    if (!this.canvasEl?.nativeElement) return;

    // Configure Fabric.js to use CORS for images globally
    (fabric as any).config.configure({
      crossOrigin: 'anonymous'
    });
    
    // Also set on the Image prototype for backward compatibility
    if ((fabric as any).FabricImage) {
      (fabric as any).FabricImage.prototype.crossOrigin = 'anonymous';
    }

    // Set canvas dimensions - we'll adjust these after loading if needed
    const canvasElement = this.canvasEl.nativeElement;
    if (this.card?.canvas) {
      // Start with the canvas dimensions
      canvasElement.width = this.card.canvas.width;
      canvasElement.height = this.card.canvas.height;
    } else {
      // Default dimensions
      canvasElement.width = 800;
      canvasElement.height = 1200;
    }

    // Create Fabric.js canvas with proper scaling
    this.fabricCanvas = new fabric.Canvas(canvasElement, {
      preserveObjectStacking: true,
      selection: false,
      enableRetinaScaling: false  // Disable retina scaling to prevent dimension doubling
    });

    // Load the fabric JSON if available
    if (this.card?.fabric_json) {
      this.loadFabricJSON();
    }
  }

  private prepareFabricJSONForCORS(fabricJSON: any): any {
    const preparedJSON = JSON.parse(JSON.stringify(fabricJSON)); // Deep clone
    
    // Add crossOrigin to background image if exists
    if (preparedJSON.backgroundImage) {
      preparedJSON.backgroundImage.crossOrigin = 'anonymous';
    }
    
    // Add crossOrigin to all image objects
    if (preparedJSON.objects && Array.isArray(preparedJSON.objects)) {
      preparedJSON.objects.forEach((obj: any) => {
        if (obj.type === 'image' || obj.type === 'Image') {
          obj.crossOrigin = 'anonymous';
        }
        // Handle group objects that might contain images
        if (obj.type === 'group' || obj.type === 'Group') {
          if (obj.objects && Array.isArray(obj.objects)) {
            obj.objects.forEach((childObj: any) => {
              if (childObj.type === 'image' || childObj.type === 'Image') {
                childObj.crossOrigin = 'anonymous';
              }
            });
          }
        }
      });
    }
    
    return preparedJSON;
  }

  private async loadFabricJSON() {
    if (!this.fabricCanvas || !this.card?.fabric_json) return;

    const fabricJSON = this.card.fabric_json;

    // Extract all unique fonts from the fabric objects
    const fonts = this.extractFonts(fabricJSON);

    // Load all fonts before rendering
    if (fonts.length > 0) {
      await this.loadGoogleFonts(fonts);
    }

    // Ensure all images have crossOrigin set before loading
    const preparedJSON = this.prepareFabricJSONForCORS(fabricJSON);
    
    // Load the canvas from JSON with CORS support
    this.fabricCanvas.loadFromJSON(preparedJSON).then(() => {
      // Check if we have editor config for scaling
      let targetWidth = fabricJSON.width || 800;
      let targetHeight = fabricJSON.height || 1200;
      let scaleFactor = 1;

      if (fabricJSON.editorConfig) {
        // Use editor config to determine scaling
        targetWidth = fabricJSON.editorConfig.designWidth;
        targetHeight = fabricJSON.editorConfig.designHeight;
        scaleFactor = 1 / fabricJSON.editorConfig.scaleFactor;

        console.log('Using editor config:', {
          editorConfig: fabricJSON.editorConfig,
          targetSize: { width: targetWidth, height: targetHeight },
          scaleFactor: scaleFactor
        });
      } else if (this.card?.canvas) {
        // Fall back to canvas dimensions
        targetWidth = this.card.canvas.width;
        targetHeight = this.card.canvas.height;
      }

      // For display, use smaller dimensions
      const displayWidth = Math.min(this.MAX_DISPLAY_WIDTH, window.innerWidth * 0.9);
      const displayScale = displayWidth / targetWidth;

      this.fabricCanvas!.setDimensions({
        width: targetWidth * displayScale,
        height: targetHeight * displayScale
      });
      this.fabricCanvas!.setZoom(scaleFactor * displayScale);

      // Store the full-size dimensions for export
      this.originalWidth = targetWidth;
      this.originalHeight = targetHeight;
      this.originalZoom = scaleFactor;

      // Disable all interactions
      this.fabricCanvas!.forEachObject((obj) => {
        obj.selectable = false;
        obj.evented = false;
      });

      // After loading, update text fields with user input
      this.updateTextFields();

      // Update avatar if needed
      this.updateAvatar();

      // Render the canvas
      this.fabricCanvas!.renderAll();

      // Update final image after rendering
      this.updateFinalImage();

      console.log('Canvas loaded:', {
        width: this.fabricCanvas!.getWidth(),
        height: this.fabricCanvas!.getHeight(),
        backgroundImage: this.fabricCanvas!.backgroundImage
      });
    });
  }

  private updateTextFields() {
    if (!this.fabricCanvas) return;

    // Get all objects from the canvas
    const objects = this.fabricCanvas.getObjects();
    
    console.log('All canvas objects:', objects.map(obj => ({ type: obj.type, text: (obj as any).text })));

    // Filter Textbox objects - check both uppercase and lowercase
    const textboxes = objects.filter((obj: any) => 
      obj.type === 'Textbox' || obj.type === 'textbox' || obj.type === 'IText' || obj.type === 'i-text'
    );
    
    console.log('Found textboxes:', textboxes.length);

    // Update each textbox based on its position in the array
    let textboxIndex = 0;
    textboxes.forEach((obj: any) => {
      textboxIndex++;
      
      // Generate the input key based on the index (matching the extraction logic)
      const inputKey = `${this.card.id}_${textboxIndex}`;
      const inputValue = this.inputValues[inputKey];

      console.log('Updating textbox:', {
        textboxIndex: textboxIndex,
        inputKey: inputKey,
        inputValue: inputValue,
        currentText: obj.text,
        type: obj.type
      });

      if (inputValue !== undefined && inputValue !== obj.text) {
        // Update the text
        obj.set('text', inputValue || '');
        // Force the object to update its dimensions
        obj.set('dirty', true);
      }
    });

    // Re-render the canvas
    this.fabricCanvas.renderAll();
    this.updateFinalImage();
  }

  private updateAvatar() {
    if (!this.fabricCanvas) return;

    // Find the avatar placeholder using the API structure
    const avatarPlaceholder = this.fabricCanvas.getObjects().find((obj: any) => {
      return obj.id === 'avatar_placeholder' && obj.isAvatar === true;
    });

    if (avatarPlaceholder) {
      if (this.showAvatar && this.userAvatarUrl) {
        // Get avatar settings from the API data
        const avatarSettings = (avatarPlaceholder as any).avatarSettings;
        console.log('Avatar settings from API:', avatarSettings);
        console.log('Current avatar properties:', {
          left: avatarPlaceholder.left,
          top: avatarPlaceholder.top,
          scaleX: avatarPlaceholder.scaleX,
          scaleY: avatarPlaceholder.scaleY,
          width: avatarPlaceholder.width,
          height: avatarPlaceholder.height
        });
        
        (avatarPlaceholder as any).setSrc(this.userAvatarUrl, { crossOrigin: 'anonymous' })
          .then(() => {
            if (avatarSettings) {
              // Keep original position, only fix the scaling using avatar settings
              const targetWidth = avatarSettings.width;
              const targetHeight = avatarSettings.height;
              
              // Calculate the scale needed to fit the avatar in the designated area
              const scaleX = targetWidth / avatarPlaceholder.width!;
              const scaleY = targetHeight / avatarPlaceholder.height!;
              
              avatarPlaceholder.set({
                scaleX: scaleX,
                scaleY: scaleY,
                visible: true
              });
              
              console.log('Applied avatar scaling only:', {
                width: targetWidth,
                height: targetHeight,
                scaleX: scaleX,
                scaleY: scaleY
              });
            } else {
              // Fallback: just make it visible
              avatarPlaceholder.set('visible', true);
            }
            
            this.fabricCanvas!.renderAll();
            this.updateFinalImage();
          })
          .catch((err: any) => {
            console.error('Failed to update avatar src:', err);
          });
      } else {
        // Hide avatar if not showing avatar
        avatarPlaceholder.set('visible', false);
        this.fabricCanvas.renderAll();
        this.updateFinalImage();
      }
    }
  }

  private updateFinalImage() {
    if (!this.fabricCanvas) return;

    // Check if we have original dimensions stored
    if (this.originalWidth > 0) {
      // Store current dimensions
      const currentWidth = this.fabricCanvas.getWidth();
      const currentHeight = this.fabricCanvas.getHeight();
      const currentZoom = this.fabricCanvas.getZoom();

      // Temporarily set to full resolution
      this.fabricCanvas.setDimensions({
        width: this.originalWidth,
        height: this.originalHeight
      });
      this.fabricCanvas.setZoom(this.originalZoom);

      // Export at full resolution
      this.finalImage = this.fabricCanvas.toDataURL({
        format: 'jpeg',
        quality: 1,
        multiplier: 1
      });

      // Restore display dimensions
      this.fabricCanvas.setDimensions({
        width: currentWidth,
        height: currentHeight
      });
      this.fabricCanvas.setZoom(currentZoom);
    } else {
      // Export as-is
      this.finalImage = this.fabricCanvas.toDataURL({
        format: 'jpeg',
        quality: 1,
        multiplier: 1
      });
    }
  }

  /**
   * Extract all unique font families from Fabric.js JSON
   */
  private extractFonts(fabricJSON: any): string[] {
    const fonts = new Set<string>();

    // Check if there are objects in the fabric JSON
    if (fabricJSON.objects && Array.isArray(fabricJSON.objects)) {
      fabricJSON.objects.forEach((obj: any) => {
        // Check for fontFamily in text objects (all possible types)
        if (obj.type === 'text' || obj.type === 'Text' || 
            obj.type === 'i-text' || obj.type === 'IText' || 
            obj.type === 'textbox' || obj.type === 'Textbox') {
          if (obj.fontFamily && obj.fontFamily !== 'Times New Roman') {
            // Clean the font name - remove quotes if present
            const fontName = obj.fontFamily.replace(/["']/g, '');
            // Skip system fonts that don't need to be loaded from Google Fonts
            const systemFonts = ['Arial', 'Helvetica', 'Times New Roman', 'Georgia', 'Courier', 'Verdana', 'Tahoma'];
            if (!systemFonts.includes(fontName)) {
              fonts.add(fontName);
            }
          }
        }
      });
    }

    return Array.from(fonts);
  }

  /**
   * Load Google Fonts dynamically
   */
  private async loadGoogleFonts(fontFamilies: string[]): Promise<void> {
    if (fontFamilies.length === 0) return;

    // Check if fonts are already loaded
    const loadedFonts = new Set<string>();
    document.fonts.forEach((font) => {
      loadedFonts.add(font.family.replace(/["']/g, ''));
    });

    // Filter out already loaded fonts
    const fontsToLoad = fontFamilies.filter(font => !loadedFonts.has(font));

    if (fontsToLoad.length === 0) {
      console.log('All fonts already loaded');
      return;
    }

    console.log('Loading Google Fonts:', fontsToLoad);

    // Create Google Fonts URL
    const googleFontsUrl = `https://fonts.googleapis.com/css2?${
      fontsToLoad.map(font => `family=${font.replace(/\s+/g, '+')}`).join('&')
    }&display=swap`;

    // Check if link already exists
    const existingLink = document.querySelector(`link[href="${googleFontsUrl}"]`);
    if (existingLink) {
      console.log('Google Fonts link already exists');
      return;
    }

    // Create and append link element
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = googleFontsUrl;

    // Wait for fonts to load
    return new Promise((resolve) => {
      link.onload = () => {
        console.log('Google Fonts loaded successfully');
        // Give browser time to process the fonts
        setTimeout(resolve, 100);
      };
      link.onerror = () => {
        console.error('Failed to load Google Fonts');
        resolve();
      };
      document.head.appendChild(link);
    });
  }

  /**
   * Extract text fields from Fabric.js JSON
   */
  private extractTextFields(): CardTextField[] {
    if (!this.card?.fabric_json?.objects) return [];

    const textFields: CardTextField[] = [];
    let fieldIndex = 0;

    // Look for Textbox objects (with capital T) in the new API structure
    this.card.fabric_json.objects.forEach((obj: any) => {
      if (obj.type === 'Textbox') {
        fieldIndex++;
        // Generate a unique ID based on index since objects don't have IDs anymore
        const fieldId = fieldIndex;
        
        textFields.push({
          id: fieldId,
          card_id: this.card.id,
          label: `Field ${fieldIndex}`,
          placeholder: obj.text || '',
          text_font: obj.fontFamily || 'Arial',
          text_size: obj.fontSize || 16,
          text_x: obj.left || 0,
          text_y: obj.top || 0,
          text_color: obj.fill || '#000000',
          text_align: obj.textAlign || 'left',
          text_width: obj.width || 200,
          text_bold: obj.fontWeight === 'bold',
          line_height: obj.lineHeight || 1.16,
          order: fieldIndex,
          variable_name: `field_${fieldIndex}`
        });
      }
    });

    return textFields;
  }

  download() {
    if (!this.fabricCanvas || !this.finalImage) return;

    // Extract text fields from fabric_json
    const textFields = this.extractTextFields();

    // Check if we have any text inputs with values
    const hasText = textFields.some(field =>
      this.inputValues[`${this.card.id}_${field.id}`]
    );

    if (hasText) {
      this.apiService.storeCardDownload(this.card.id).subscribe();

      // Convert data URL to blob
      this.dataURLtoBlob(this.finalImage).then(async (blob) => {
        const _name = `${this.card.title}.jpg`;

        if (this.genericService.isCapacitorApp()) {
          let base64 = await this.genericService.convertBlobToBase64(blob) as string;
          Filesystem.writeFile({
            path: _name,
            data: base64,
            directory: Directory.Cache,
          }).then(async (res) => {
            await Share.share({
              files: [res.uri],
            });
          });
        } else {
          let ImageUrlLink = document.createElement('a');
          ImageUrlLink.href = window.URL.createObjectURL(blob);
          ImageUrlLink.download = _name;
          ImageUrlLink.target = '_blank';
          ImageUrlLink.click();
        }
      });
    }
  }

  private dataURLtoBlob(dataURL: string): Promise<Blob> {
    return new Promise((resolve) => {
      const arr = dataURL.split(',');
      const mime = arr[0].match(/:(.*?);/)![1];
      const bstr = atob(arr[1]);
      let n = bstr.length;
      const u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      resolve(new Blob([u8arr], {type: mime}));
    });
  }
}
