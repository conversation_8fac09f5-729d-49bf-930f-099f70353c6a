<app-header [hideProfileIcon]="false" [showBackButton]="true" backTo="/cards"></app-header>

<ion-content>
  <ion-grid [fixed]="true" class="ion-justify-content-center">
    <!-- Loading state -->
    <ion-row *ngIf="loading" class="ion-justify-content-center">
      <ion-spinner name="circular"></ion-spinner>
      <ion-text class="ion-padding">جاري التحميل...</ion-text>
    </ion-row>

    <!-- Error state -->
    <ion-row *ngIf="error" class="ion-justify-content-center">
      <ion-card color="danger" class="ion-text-center ion-margin-bottom" style="width: 100%;">
        <ion-card-content>
          {{ error }}
          <ion-button color="light" expand="block" class="ion-margin-top" (click)="selectDifferentCard()">
            اختر بطاقة أخرى
          </ion-button>
        </ion-card-content>
      </ion-card>
    </ion-row>

    <!-- Card details with text inputs above image -->
    <ng-container *ngIf="!loading && !error && card">
      <!-- Text input form above the image -->


      <!-- Card preview -->
      <ion-list>
        <!-- Avatar Toggle -->
        <ion-item lines="none" *ngIf="card.avatar?.enabled && user?.gender === 'MALE'">
          <ion-label>إظهار الصورة الشخصية</ion-label>
          <ion-toggle slot="end" [(ngModel)]="showAvatar" (ionChange)="toggleAvatar()"></ion-toggle>
        </ion-item>

        <ng-container class="ion-justify-content-center">
          <ion-item lines="none">
            <form [formGroup]="cardForm" style="width: 100%">
              <ng-container *ngFor="let field of textFields">
                <ion-label position="fixed">{{ field.label }}</ion-label>
                <ion-textarea [formControlName]="'field_' + field.id"
                              [placeholder]="field.placeholder || ''"/>
              </ng-container>
            </form>
          </ion-item>
          <ion-item lines="none">
            <card-box
              [card]="card"
              [inputValues]="inputValues"
              [userAvatarUrl]="userAvatarUrl"
              [showAvatar]="showAvatar">
            </card-box>
          </ion-item>
        </ng-container>
      </ion-list>

    </ng-container>
  </ion-grid>
</ion-content>
