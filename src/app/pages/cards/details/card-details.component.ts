import {Component, Inject, OnInit} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {IonicModule} from '@ionic/angular';
import {NgForOf, NgIf} from '@angular/common';
import {FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators} from '@angular/forms';
import {HeaderComponent} from '../../../layout/header/header.component';
import {IS_SERVER_PLATFORM} from '../../../shared/IS_SERVER_PLATFORM.token';
import {ApiService} from '../../../shared/services/api.service';
import {finalize, take} from 'rxjs/operators';
import {Card, CardTextField, User} from "../../../shared/models";
import {CardBoxComponent} from "../card-box/card-box.component";
import {ProfileService} from "../../../shared/services/profile.service";

@Component({
  selector: 'app-card-details',
  templateUrl: './card-details.component.html',
  styleUrls: ['./card-details.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    NgIf,
    NgForOf,
    FormsModule,
    ReactiveFormsModule,
    CardBoxComponent,
  ]
})
export class CardDetailsComponent implements OnInit {
  // Current card
  card: Card | null = null;

  // Form for all text fields
  cardForm: FormGroup;

  // Extracted text fields from fabric_json
  textFields: CardTextField[] = [];

  // Input values for the card component
  inputValues: { [key: string]: string } = {};

  // Loading state
  loading = false;
  error: string | null = null;

  user: User | null = null;
  userAvatarUrl: string | null = null;
  showAvatar: boolean = false;

  constructor(
    @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
    private apiService: ApiService,
    private route: ActivatedRoute,
    private router: Router,
    private fb: FormBuilder,
    private profileService: ProfileService
  ) {
    this.cardForm = this.fb.group({});
  }

  ngOnInit() {
    // Subscribe to user profile to get avatar
    if (!this.isServer) {
      this.profileService.$user.subscribe(user => {
        if (user) {
          this.user = user;
          this.userAvatarUrl = user.profile_photo_url || null;
        }
      });

      this.route.params.pipe(take(1)).subscribe(params => {
        if (params['id']) {
          this.loadCard(parseInt(params['id'], 10));
        } else {
          this.error = 'No card selected';
        }
      });
    }
  }

  loadCard(id: number) {
    this.loading = true;
    this.error = null;

    this.apiService.getCard(id)
      .pipe(
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe({
        next: (response) => {
          this.card = response.data;
          this.setupForm();
        },
        error: (err) => {
          console.error('Failed to load card', err);
          this.error = 'Failed to load card. Please try again later.';
        }
      });
  }

  // Extract text fields from fabric_json
  private extractTextFields(): CardTextField[] {
    if (!this.card?.fabric_json?.objects) return [];

    const textFields: CardTextField[] = [];
    let fieldIndex = 0;

    // Look for Textbox objects - check both cases and IText
    this.card.fabric_json.objects.forEach((obj: any) => {
      if (obj.type === 'Textbox' || obj.type === 'textbox' || obj.type === 'IText' || obj.type === 'i-text') {
        fieldIndex++;
        // Generate a unique ID based on index since objects don't have IDs anymore
        const fieldId = fieldIndex;

        // Extract label from the text content or generate a default
        console.log(obj.fieldData)
        textFields.push({
          id: fieldId,
          card_id: this.card!.id,
          label: obj.fieldData.label,
          placeholder: obj.text || '',
          text_font: obj.fontFamily || 'Arial',
          text_size: obj.fontSize || 16,
          text_x: obj.left || 0,
          text_y: obj.top || 0,
          text_color: obj.fill || '#000000',
          text_align: obj.textAlign || 'left',
          text_width: obj.width || 200,
          text_bold: obj.fontWeight === 'bold',
          line_height: obj.lineHeight || 1.16,
          order: fieldIndex,
          variable_name: `field_${fieldIndex}`
        });
      }
    });

    return textFields;
  }


  // Create form controls for each text field
  setupForm() {
    if (!this.card) return;

    // Initialize showAvatar for male users with avatar-supported cards
    // Start with false, user can toggle it on if they want
    if (this.card.avatar && this.user?.gender === 'MALE') {
      // Keep the initial false value, don't override with API's enabled state
      // User has full control over this toggle
    } else {
      this.showAvatar = false;
    }

    const formControls: any = {};

    // Extract text fields from fabric_json
    this.textFields = this.extractTextFields();

    this.textFields.forEach(field => {
      formControls[`field_${field.id}`] = [
        '', // Default empty value
        [Validators.required]
      ];
    });

    this.cardForm = this.fb.group(formControls);

    // Subscribe to form value changes to update the preview
    this.cardForm.valueChanges.subscribe(values => {
      if (!this.card) return;

      // Update input values for the card component
      Object.keys(values).forEach(key => {
        const fieldId = parseInt(key.replace('field_', ''), 10);
        this.inputValues[`${this.card!.id}_${fieldId}`] = values[key];
        this.inputValues = Object.assign({}, this.inputValues);
      });
    });
  }

  // Select a different card
  selectDifferentCard() {
    this.router.navigate(['/cards']).then();
  }

  // Toggle avatar visibility
  toggleAvatar() {
    // Don't toggle showAvatar here - it's already handled by [(ngModel)]
    // Just trigger a redraw by creating a new card reference
    if (this.card?.avatar) {
      // Create a new card object to trigger change detection
      this.card = { ...this.card };
    }
  }
}
