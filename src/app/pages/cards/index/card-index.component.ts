import { Component, OnInit, Inject } from '@angular/core';
import { Router } from '@angular/router';
import { IonicModule, LoadingController, ToastController } from '@ionic/angular';
import { Async<PERSON>ip<PERSON>, DatePipe, NgClass, NgForOf, <PERSON><PERSON><PERSON>, Ng<PERSON>emplateOutlet } from '@angular/common';
import { HeaderComponent } from '../../../layout/header/header.component';
import { CardComponent } from '../../../shared/components/card/card.component';
import { IS_SERVER_PLATFORM } from '../../../shared/IS_SERVER_PLATFORM.token';
import { ApiService } from '../../../shared/services/api.service';
import { finalize } from 'rxjs/operators';
import { Card } from "../../../shared/models";

@Component({
  selector: 'app-card-index',
  templateUrl: './card-index.component.html',
  styleUrls: ['./card-index.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    <PERSON><PERSON>f,
    CardComponent,
    NgTemplateOutlet,
    NgForOf,
    AsyncPipe,
    DatePipe,
    NgClass
  ]
})
export class CardIndexComponent implements OnInit {
  // Array to store categories of cards
  categories: string[] = [];

  // Active category filter
  selectedCategory: string | 'all' = "all";

  // Loading state
  loading = false;
  error: string | null = null;

  // Array to store cards from API
  cards: Card[] = [];

  constructor(
    @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
    private apiService: ApiService,
    private router: Router,
    private toastController: ToastController,
    private loadingController: LoadingController
  ) {}

  ngOnInit() {
    if (!this.isServer) {
      this.loadCards();
    }
  }

  async loadCards(category?: string) {
    this.loading = true;
    this.error = null;

    const params: any = {};
    if (category) {
      params.category = category;
      this.selectedCategory = category;
    } else {
      this.selectedCategory = "all";
    }

    this.apiService.getCards(params)
      .pipe(
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe({
        next: (response) => {
          this.cards = response.data;

          // Extract unique categories from cards
          if (!category) {
            const categorySet = new Set<string>();
            this.cards.forEach(card => {
              if (card.category) {
                categorySet.add(card.category);
              }
            });
            this.categories = Array.from(categorySet);
          }
        },
        error: (err) => {
          console.error('Failed to load cards', err);
          this.error = 'فشل في تحميل البطاقات. يرجى المحاولة مرة أخرى لاحقاً.';
        }
      });
  }

  // Filter cards by category
  filterByCategory(category: string | null) {
    if (category === null || category === 'all') {
      this.loadCards();
    } else {
      this.loadCards(category);
    }
  }

  // Select a card and navigate to the editor
  async selectCard(card: Card) {
      this.router.navigate(['/cards', card.id]);
  }
}
