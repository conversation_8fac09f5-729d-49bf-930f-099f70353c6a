<app-header [hideProfileIcon]="false" [showBackButton]="true" backTo="/home"></app-header>

<ion-content>
  <ion-grid [fixed]="true" class="ion-padding">
    <ion-row>
      <ion-col size="12">
        <h1 class="page-title">اختر البطاقة</h1>
      </ion-col>
    </ion-row>

    <!-- Category filter chips -->
    <ion-segment  *ngIf="categories.length > 0" [value]="selectedCategory" (ionChange)="filterByCategory($event.detail.value!.toString())" style="width: 370px; margin: 0 auto;">
      <ion-segment-button value="all">
        <ion-label>الكل</ion-label>
      </ion-segment-button>
      <ion-segment-button *ngFor="let category of categories" [value]="category">
        <ion-label>{{ category }}</ion-label>
      </ion-segment-button>
    </ion-segment>
    <!-- Loading state -->
    <ion-row *ngIf="loading" class="state-container">
      <ion-col size="12" class="ion-text-center">
        <ion-spinner name="crescent"></ion-spinner>
        <p>جاري تحميل البطاقات...</p>
      </ion-col>
    </ion-row>

    <!-- Error state -->
    <ion-row *ngIf="error" class="state-container">
      <ion-col size="12" class="ion-text-center">
        <ion-icon name="alert-circle-outline" color="danger" size="large"></ion-icon>
        <p class="error-message">{{ error }}</p>
        <ion-button (click)="loadCards()" fill="outline" size="small">
          إعادة المحاولة
        </ion-button>
      </ion-col>
    </ion-row>

    <!-- No cards found -->
    <ion-row *ngIf="!loading && cards.length === 0 && !error" class="state-container">
      <ion-col size="12" class="ion-text-center">
        <ion-icon name="images-outline" size="large" color="medium"></ion-icon>
        <p class="empty-message">لا توجد بطاقات متاحة حالياً</p>
      </ion-col>
    </ion-row>

    <!-- Cards Grid -->
    <ion-row *ngIf="!loading && cards.length > 0 && !error">
      <ion-col size="12" size-md="12" *ngFor="let card of cards" class="card-column">
        <ion-card class="card-item" (click)="selectCard(card)">
          <div class="card-image-container">
            <ion-img [src]="card.image_url" alt="{{ card.title }}" class="card-image"></ion-img>
          </div>
          <ion-card-content class="ion-no-padding">
            <ion-button expand="block" color="primary" class="select-button">
              اختر
            </ion-button>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
