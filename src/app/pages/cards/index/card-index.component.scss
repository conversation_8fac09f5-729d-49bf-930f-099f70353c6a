:host {
  max-width: var(--page-width);
  margin: auto;
}
/* Global styles */
:host {
  --card-border-radius: 12px;
  --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --transition-speed: 0.2s;
}

/* Page title */
.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 16px;
  color: var(--ion-color-dark);
  text-align: center;
}

/* Category chips container */
.category-container {
  margin-bottom: 16px;
}

.chip-scroll-container {
  display: flex;
  overflow-x: auto;
  padding: 4px 0;
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.chip-scroll-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.category-chip {
  margin-right: 8px;
  border-radius: 20px;
  font-weight: 500;
  transition: transform var(--transition-speed);
}

.category-chip:last-child {
  margin-right: 0;
}

.category-chip:active {
  transform: scale(0.95);
}

/* Loading, error, and empty states */
.state-container {
  padding: 40px 0;
}

.error-message {
  color: var(--ion-color-danger);
  margin: 12px 0;
}

.empty-message {
  color: var(--ion-color-medium);
  margin: 12px 0;
}

/* Card grid */
.card-column {
  padding: 8px;
}

.card-item {
  margin: 0;
  border-radius: var(--card-border-radius);
  box-shadow: var(--card-shadow);
  overflow: hidden;
  transition: transform var(--transition-speed), box-shadow var(--transition-speed);
}

.card-item:active {
  transform: scale(0.98);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-image-container {
  position: relative;
  overflow: hidden;
  height: 0;
  padding-bottom: 100%; /* 1:1 Aspect ratio */
  background-color: var(--ion-color-light);
}

.card-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.select-button {
  margin: 0;
  font-weight: 500;
  --border-radius: 0;
}
