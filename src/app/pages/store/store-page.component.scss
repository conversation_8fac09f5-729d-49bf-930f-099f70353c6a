:host {
  max-width: var(--page-width);
  margin: auto;
}

ion-avatar {
  --border-radius: 4px;
}

.ads {
  border-radius: 8px;
  cursor: pointer;
}

/* In your StorePage component's CSS */
ion-modal {
  --border-radius: 16px;
  --box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

ion-modal::part(handle) {
  background: var(--ion-color-step-350, #c0c0c0);
  width: 40px;
  height: 6px;
  border-radius: 4px;
  margin-top: 10px;
}

ion-modal {
  //--background: var(--ion-color-secondary);
  //--border-radius: 16px 16px 0 0;
  //--height: 90%;
  --backdrop-opacity: 0.5;

}

.donation-modal {
  height: 100%;
  width: 100%;
}
