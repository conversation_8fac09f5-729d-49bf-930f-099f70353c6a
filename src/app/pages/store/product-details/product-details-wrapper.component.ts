import {
  Component,
  Input,
  OnDestroy,
  OnInit,
  ViewChild,
  ElementRef,
  CUSTOM_ELEMENTS_SCHEMA,
  AfterViewInit, EventEmitter, Output
} from '@angular/core';
import {
  IonicModule, ModalController,
} from "@ionic/angular";
import {Product} from "../../../shared/models";
import {RouterModule} from '@angular/router';
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {ProductDetailsContentComponent} from "./product-details-content/product-details-content.component";
import {PaymentScreenComponent} from "./payment-screen/payment-screen.component";
import {Subject} from "rxjs";

// Import Swiper and register Swiper Element
import {register} from 'swiper/element/bundle';

register(); // Register Swiper custom elements

@Component({
  selector: 'app-product-details-wrapper',
  templateUrl: './product-details-wrapper.component.html',
  styleUrls: ['./product-details-wrapper.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    ProductDetailsContentComponent,
    PaymentScreenComponent,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA] // Required for custom elements
})
export class ProductDetailsWrapperComponent implements OnInit, OnDestroy, AfterViewInit {
  @Input() product!: Product;
  @Input() selectedCategoryTitle!: string;
  @ViewChild('swiper') swiperRef!: ElementRef;
  @ViewChild(PaymentScreenComponent) paymentScreenComponent!: PaymentScreenComponent;

  // Donation parameters to pass to payment screen
  donationParams: any = null;

  // Flag to indicate if payment screen should be active
  paymentScreenActive = false;

  private destroy$ = new Subject<void>();
  // Add output to communicate with parent modal
  @Output() breakpointChange = new EventEmitter<number>();

  constructor(
    private modalController: ModalController
  ) {
  }

  ngOnInit(): void {
  }

  ngAfterViewInit() {
    // Initialize the Swiper
    const swiperEl = this.swiperRef.nativeElement;

    // Add event listener for Swiper initialization
    swiperEl.addEventListener('swiperready', () => {
      console.log('Swiper is ready!');
    });
  }

// Navigate to payment slide
  goToPaymentSlide(params: any) {
    // Set donation params
    this.donationParams = {
      name: params.name || '',
      phone: params.phone || '',
      amount: params.amount || ''
    };

    // Emit event to change modal size
    this.breakpointChange.emit(0.5);

    // Update current modal if it exists
    this.updateModalBreakpoint(0.5);

    // Navigate to payment slide
    setTimeout(() => {
      const swiperEl = this.swiperRef?.nativeElement;
      if (swiperEl?.swiper) {
        swiperEl.swiper.slideTo(1);
      }

      setTimeout(() => {
        if (this.paymentScreenComponent) {
          this.paymentScreenComponent.initiatePayment();
        }
      }, 100);
    }, 0);
  }

  // Navigate back to details slide
  goToDetailsSlide() {
    // Reset modal to original size
    this.breakpointChange.emit(0.86); // or whatever your default is

    // Update current modal if it exists
    this.updateModalBreakpoint(0.86); // or your default value

    const swiperEl = this.swiperRef?.nativeElement;
    if (swiperEl?.swiper) {
      swiperEl.swiper.slideTo(0);
    }
  }

  // Update the current modal's breakpoint
  private async updateModalBreakpoint(breakpoint: number) {
    try {
      const currentModal = await this.modalController.getTop();
      if (currentModal) {
        // Set the new breakpoint

        currentModal.breakpoints = [0, 0.3, 0.5, 0.86, breakpoint, 0.95, 1];
        currentModal.initialBreakpoint = breakpoint;

        // Force the modal to update its size
        if (typeof currentModal.setCurrentBreakpoint === 'function') {
          await currentModal.setCurrentBreakpoint(breakpoint);
        }
      }
    } catch (error) {
      console.error('Error updating modal breakpoint:', error);
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
