<div class="modal-content">
  <!-- Header -->
  <div class="header ion-padding">
    <h1>{{ category }}</h1>
    <ion-icon color="light" slot="icon-only" name="share-outline" (click)="shareProduct()"
              style="font-size: 26px;"></ion-icon>
  </div>

  <!-- Title -->
  <h2 class="title">{{ product?.title }}</h2>
  <div class="scroll-content">

    <!-- remaining and total -->
    <ion-row class="ion-justify-content-center ion-align-items-center summary-section ion-margin-vertical">
      <ion-col *ngIf="product" class="ion-justify-content-center ion-align-items-center ion-no-padding">
        <div class="ion-text-center ion-flex ion-align-items-center ion-justify-content-center ion-margin-vertical ">
          <div class="vertical-line" style="flex: 1 1 auto; border-left: var(--ion-color-medium) 2px solid"
               *ngIf="product?.limit">
            <ion-text color="medium">
              <h2><b>المبلغ</b></h2>
            </ion-text>
            <ion-text color="dark">{{ product.amount | currency:' ' }}
              <span class="icon-saudi_riyal" style="font-size: 15px; vertical-align: sub"></span>
            </ion-text>
          </div>
          <div class="vertical-line" style="flex: 1 1 auto; cursor: pointer;"
               (click)="showTheRemainingAmount = !showTheRemainingAmount">
            <ion-text color="medium">
              <h2>
                <b>
                  @if (!product?.limit || showTheRemainingAmount) {
                    تم جمع
                  } @else if (product?.limit && !showTheRemainingAmount) {
                    المتبقي
                  }
                </b>
              </h2>
            </ion-text>
            <ion-text *ngIf="product" color="dark">
              @if (!product?.limit || showTheRemainingAmount) {
                {{ product.gathered | currency:' ' }}
              } @else {
                {{ product.remaining | currency:' ' }}
              }
              <span class="icon-saudi_riyal" style="font-size: 15px; vertical-align: sub"></span>
            </ion-text>
          </div>
        </div>
        <div class="progress-outer" *ngIf="product?.limit">
          <div class="progress-inner" [style.width.%]="product.percentage">{{ product.percentage }}%</div>
        </div>
      </ion-col>
    </ion-row>

    <div *ngIf="product?.limit && product.remaining == 0;" class="plant-container"
         id="LoaderOverlay"
         style="height:150px; margin: 0; position:absolute; top: 70px;">
      <ng-lottie
        [options]="caseClosedOptions"
        (animationCreated)="animationCreated($event)"
      />
    </div>

    <!-- Donation amount section -->
    <div class="donation-section" *ngIf="!product?.limit || product.remaining > 0; else caseClosed">
      <h3 style="margin: 0">مبلغ التبرع</h3>

      <!-- Fixed amount buttons -->
      <div class="ion-padding-vertical">
        <ion-segment [(ngModel)]="amountValue" (ngModelChange)="onAmountChange($event)" #amountSegment>
          <ion-segment-button *ngFor="let option of amountOptions" [value]="option.value">
            <ion-label>
              <span class="icon-saudi_riyal" style="font-size: 15px; vertical-align: sub"></span>
              {{ option.label }}
            </ion-label>
          </ion-segment-button>
        </ion-segment>
      </div>

      <!-- Custom amount input -->
      <div class="custom-amount">
        <div class="input-wrapper">
          <ion-input
            placeholder="مبلغ آخر"
            id="amount-input"
            type="tel"
            step="1"
            required="true"
            class="ion-margin-bottom saudi_riyal-font"
            [value]="amountSegment.value ?? ''"
            [class.has-error]="donationForm.controls.amount.invalid && donationForm.controls.amount.touched"
            [formControl]="donationForm.controls.amount">
          </ion-input>

          <div class="custom-error"
               *ngIf="donationForm.controls.amount.invalid && donationForm.controls.amount.touched">
            يجب أن يكون المبلغ أكبر من {{ isSystemAdmin ? 1 : 5 }}
            <span class="icon-saudi_riyal"></span>
            @if(product.remaining > 0) {
              وأقل من
              {{ product.remaining | currency:' ' }}
              <span class="icon-saudi_riyal"></span>
            }
          </div>
        </div>

        <span class="icon-saudi_riyal ion-padding"
              style="height: 100%;font-size: 20px; background: var(--ion-color-medium-shade); border-radius: 8px 0 0 8px; color: var(--ion-color-dark)"></span>
      </div>

      <!-- Continue button -->
      <ion-button class="ion-margin-top" color="dark" expand="block" (click)="pay()"
                  [disabled]="formLoading || donationForm.controls.amount.invalid" mode="ios">
        <ion-spinner *ngIf="formLoading"></ion-spinner>
        <span *ngIf="!formLoading">الاستمرار للدفع</span>
      </ion-button>
    </div>

    <!-- Stats section -->
    <div class="stats-container" *ngIf="product.statistics">
      <div class="stat-card">
        <div class="stat-icon">
          <ion-icon name="time-outline" color="dark"></ion-icon>
        </div>
        <div class="stat-value">{{ product.statistics.last_payment | formatDistance:false }}
        </div>
        <div class="stat-label">آخر عملية تبرع</div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <ion-icon name="people-outline" color="dark"></ion-icon>
        </div>
        <div class="stat-value">{{ product.statistics.payments }}</div>
        <div class="stat-label">التبرعات</div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <ion-icon name="bar-chart-outline" color="dark"></ion-icon>
        </div>
        <div class="stat-value">{{ product.statistics.views }}</div>
        <div class="stat-label">الزيارات</div>
      </div>
    </div>

    <!-- Skeleton loader (shown when data is loading) -->
    <div class="stats-container" *ngIf="!product?.statistics">
      <div class="stat-card">
        <div class="stat-icon skeleton-icon">
          <ion-skeleton-text animated style="width: 24px; height: 24px; border-radius: 50%;"/>
        </div>
        <ion-skeleton-text animated style="width: 60px; height: 20px; top:10px;"/>
        <ion-skeleton-text animated style="width: 80px; height: 15px; bottom: -20px"/>
      </div>

      <div class="stat-card">
        <div class="stat-icon skeleton-icon">
          <ion-skeleton-text animated style="width: 24px; height: 24px; border-radius: 50%;"/>
        </div>
        <ion-skeleton-text animated style="width: 60px; height: 20px; top:10px;"/>
        <ion-skeleton-text animated style="width: 80px; height: 15px; bottom: -20px"/>
      </div>

      <div class="stat-card">
        <div class="stat-icon skeleton-icon">
          <ion-skeleton-text animated style="width: 24px; height: 24px; border-radius: 50%;"/>
        </div>
        <ion-skeleton-text animated style="width: 60px; height: 20px; top:10px;"/>
        <ion-skeleton-text animated style="width: 80px; height: 15px; bottom: -20px"/>
      </div>
    </div>

    <!-- Payment methods -->
    <div class="payment-methods ion-margin">
      <img src="assets/images/payment-methods/rounded-visa.svg" alt="Visa">
      <img src="assets/images/payment-methods/rounded-mastercard.svg" alt="MasterCard">
      <img src="assets/images/payment-methods/rounded-mada.svg" alt="Mada">
      <img src="assets/images/payment-methods/rounded-applepay.svg" alt="Apple Pay">
    </div>
  </div>
</div>

<ng-template #caseClosed>
  <ion-row>
    <ion-col class="ion-text-center">
      <ion-text color="secondary">
        <h2>بفضل من الله</h2>
        <h4>ثم بدعمكم حققنا الهدف، واكتمل المشروع.</h4>
      </ion-text>
    </ion-col>
  </ion-row>
</ng-template>
