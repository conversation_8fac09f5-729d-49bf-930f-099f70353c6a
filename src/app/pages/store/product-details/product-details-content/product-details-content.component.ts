import {
  Component, CUSTOM_ELEMENTS_SCHEMA,
  EventEmitter,
  Inject,
  Input,
  OnDestroy,
  OnInit,
  Output
} from '@angular/core';
import {
  <PERSON><PERSON><PERSON>ontroller,
  IonicModule,
  Platform,
} from "@ionic/angular";

import {Router, RouterModule} from '@angular/router';
import {<PERSON><PERSON><PERSON>cyPipe, DOCUMENT, NgForOf, NgIf} from "@angular/common";
import {FormBuilder, FormsModule, ReactiveFormsModule, Validators} from "@angular/forms";

import {AnimationOptions, LottieComponent} from "ngx-lottie";
import {AnimationItem} from "lottie-web";
import {Share} from "@capacitor/share";
import {environment} from "../../../../../environments/environment";
import {FormatDistancePipe} from "../../../../shared/pipes/format-distance.pipe";
import {Product} from "../../../../shared/models";
import {IS_SERVER_PLATFORM} from "../../../../shared/IS_SERVER_PLATFORM.token";
import {GenericService} from "../../../../shared/services/generic.service";
import {ProfileService} from "../../../../shared/services/profile.service";

@Component({
  selector: 'app-product-details-content',
  templateUrl: './product-details-content.component.html',
  styleUrls: ['./product-details-content.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    NgIf,
    FormsModule,
    ReactiveFormsModule,
    FormatDistancePipe,
    RouterModule,
    NgForOf,
    CurrencyPipe,
    LottieComponent,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class ProductDetailsContentComponent implements OnInit, OnDestroy {
  @Input() product!: Product;
  @Input() category = '';

  // New event emitter to request payment
  @Output() paymentRequested = new EventEmitter<any>();

  productId?: number;
  env = environment;
  formLoading: boolean = false;
  donationForm = this.fb.group({
    name: ['', [Validators.minLength(2)]],
    phone: ['', [Validators.minLength(10)]],
    amount: [0, [Validators.min(5)]],
  });
  showTheRemainingAmount = true;
  protected authedUser = false;
  private isCapacitorApp = false;
  isSystemAdmin = false;
  loading = true;
  loadError = false;

  amountOptions = [
    {value: "500", label: "500"},
    {value: "100", label: "100"},
    {value: "50", label: "50"}
  ];

  amountValue = "10";

  constructor(
    private platform: Platform,
    @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
    @Inject(DOCUMENT) private document: Document,
    private fb: FormBuilder,
    private alertCtrl: AlertController,
    private genericService: GenericService,
    public profileService: ProfileService,
  ) {
    this.profileService.$user.subscribe(user => {
      if(user !== null && user.is_system_admin) {
        this.isSystemAdmin = true;
        this.donationForm.get('amount')!.setValidators([Validators.min(1)]);
      } else {
        this.isSystemAdmin = false;
        this.donationForm.get('amount')!.setValidators([Validators.min(5)]);
      }
    });
  }

  ngOnInit() {
    this.isCapacitorApp = this.genericService.isCapacitorApp();
    this.donationForm.controls.amount.setValue(+this.amountValue);
    this.checkAuthedUser();

    if (this.product && this.product.id) {
      this.productId = this.product.id;
    } else {
      this.loadError = true;
      this.loading = false;
    }
  }

  ngOnDestroy(): void {
    // Cleanup logic
  }

  checkAuthedUser() {
    this.profileService.$user.subscribe(user => {
      if (user && user.phone && user.phone.length > 0) {
        this.donationForm.patchValue({
          name: user.name || '',
          phone: user.phone || ''
        });
        this.authedUser = true;
      }
    });

  }

  // Validate and initiate payment
  async pay() {
    this.donationForm.controls['amount'].markAsTouched();

    if (!this.authedUser) {
      this.donationForm.controls['name'].markAsTouched();
      this.donationForm.controls['phone'].markAsTouched();
    }

    if (
      !this.productId ||
      this.donationForm.invalid ||
      !this.donationForm.value.amount ||
      ((!this.donationForm.value.name || !this.donationForm.value.phone) && !this.authedUser)
    )
      return;

    if (!this.isSystemAdmin && this.donationForm.value.amount < 5) {
      const alert = await this.alertCtrl.create({
        header: 'خطأ',
        message: 'الحد الأدنى للتبرع هو 5 ريالات',
        buttons: ['موافق']
      });
      await alert.present();
      return;
    }

    // Instead of processing payment here, emit an event to the wrapper component
    this.paymentRequested.emit({
      productId: this.productId,
      name: this.donationForm.value.name || '',
      phone: this.donationForm.value.phone || '',
      amount: this.donationForm.value.amount
    });
  }

  lottieOptions: AnimationOptions = {
    path: 'https://lottie.host/97b42f0c-ea1f-4d90-ade9-6b604c38e1d9/DIkaDiE6od.json',
    autoplay: false,
    loop: false,
    renderer: 'svg'
  };
  caseClosedOptions: AnimationOptions = {
    path: 'https://lottie.host/69a9de79-cda1-4d37-8f39-c357db0b5936/i6EbOuXUkI.json',
    autoplay: true,
    loop: true,
    renderer: 'svg'
  };
  // Animation properties
  private animationItem: AnimationItem | null = null;
  private targetFrames = {
    default: 29,
    '50': 69,
    '100': 104,
    '500': 131
  };

  private currentSegment: 'default' | '50' | '100' | '500' = 'default';

  // Animation methods
  animationCreated(animItem: AnimationItem): void {
    this.animationItem = animItem;
    this.animationItem.goToAndStop(this.targetFrames.default, true);
    this.currentSegment = 'default';
  }

  playSegment(segment: 'default' | '50' | '100' | '500'): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.animationItem) {
        return reject('Animation not initialized');
      }

      const fromFrame = this.targetFrames.default; // always start from default
      const toFrame = this.targetFrames[segment];

      this.animationItem.setDirection(1); // always forward
      this.animationItem.playSegments([fromFrame, toFrame], true);

      const onComplete = () => {
        this.animationItem?.removeEventListener('complete', onComplete);
        resolve();
      };

      this.animationItem.addEventListener('complete', onComplete);
    });
  }

  async onAmountChange(event: any): Promise<void> {
    const value = event as '50' | '100' | '500' | string;
    const newSegment: 'default' | '50' | '100' | '500' = ['50', '100', '500'].includes(value)
      ? value as ('50' | '100' | '500')
      : 'default';

    if (newSegment === this.currentSegment) return;

    this.amountValue = value;
    this.donationForm.controls.amount.setValue(+this.amountValue);

    // Instantly reset to default without animation
    this.animationItem?.goToAndStop(this.targetFrames.default, true);

    // Play from default frame to desired segment
    await this.playSegment(newSegment);

    this.currentSegment = newSegment;
  }

  async shareProduct() {
    const title = this.product.title;
    const text = '';
    const currentUrl = window.location.href;

    try {
      // Use Capacitor Share plugin (works on iOS, Android)
      if (this.genericService.isCapacitorApp()) {
        await Share.share({
          title: title,
          text: text,
          url: currentUrl,
          dialogTitle: 'شارك' // Android only
        });
      }
      // Use Web Share API (modern browsers)
      else if (navigator.share) {
        await navigator.share({
          title: title,
          text: text,
          url: currentUrl
        });
      }
      // Fallback for browsers without Web Share API
      else {
        // Create a temporary input to copy the URL
        const selBox = document.createElement('textarea');
        selBox.style.position = 'fixed';
        selBox.style.left = '0';
        selBox.style.top = '0';
        selBox.style.opacity = '0';
        selBox.value = currentUrl;
        document.body.appendChild(selBox);
        selBox.focus();
        selBox.select();
        document.execCommand('copy');
        document.body.removeChild(selBox);
      }
    } catch (error) {
      console.error('Error sharing product:', error);
    }
  }
}
