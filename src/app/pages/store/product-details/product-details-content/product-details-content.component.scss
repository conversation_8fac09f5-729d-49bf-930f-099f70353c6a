:host {
  display: block;
  width: 100%;
  height: 100%;
  //overflow: scroll;
}

.modal-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 20px 20px;
  text-align: center;
}


.header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header h1 {
  font-size: 24px;
  margin: 0;
  font-weight: bold;
}


.plant-container {
  display: flex;
  justify-content: center;
  margin: 12px 0 24px;
}

.plant-icon {
  height: 120px;
}

.product-image {
  height: 120px;
  border-radius: 8px;
}

.donation-section {
  width: 100%;
  max-width: 500px;
}

.donation-section h3 {
  font-size: 22px;
  margin-bottom: 16px;
}

.amount-buttons {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}


.selected {
  --background: var(--ion-color-light-contrast);
  --color: var(--ion-color-secondary);
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.2);
}

.custom-amount {
  background-color: var(--ion-color-light);
  border-radius: 8px;
  position: relative;
  display: flex;
  align-items: center;
}

.custom-amount ion-input {
  --padding-start: 48px;
  --padding-end: 16px;
  --placeholder-color: #999;
  --placeholder-opacity: 1;
  color: var(--ion-color-dark);
  height: 48px;
  font-size: 16px;
  text-align: right;
  width: 100%;
}

.currency-symbol {
  position: absolute;
  left: 16px;
  font-size: 18px;
  color: var(--ion-color-dark);
}

.continue-btn {
  --background: var(--ion-color-light);
  --color: var(--ion-color-secondary);
  --border-radius: 8px;
  height: 48px;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stats-section {
  width: 100%;
  background-color: var(--ion-color-medium-tint);
  border-radius: 12px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 33%;
}

.stat-item ion-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.8;
}

ion-segment {
  background-color: var(--ion-color-medium-shade);
  border-radius: 10px;
  --background: transparent;
}

ion-segment-button {
  --indicator-color: var(--ion-color-light);
  --color: var(--ion-color-medium);
  --color-checked: var(--ion-color-dark);
  --indicator-height: 100%;
  font-weight: 500;
  transition: all 0.3s ease-in-out;
  min-height: 40px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  direction: rtl;
}

.page-title {
  color: var(--ion-color-secondary);
  font-size: 24px;
  font-weight: bold;
  margin: 0;
}

.share-icon {
  color: var(--ion-color-secondary);
  font-size: 26px;
}

.title {
  color: var(--ion-color-secondary);
  font-size: 15px !important;
  text-align: start;
  margin: 0 0 8px;
  direction: rtl;
}

.plant-container {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

.plant-icon {
  height: 150px;
}

.donation-section {
  display: flex;
  flex-direction: column;
  margin: 0;
}

.section-heading {
  text-align: center;
  color: var(--ion-color-secondary);
  font-size: 20px;
  margin: 10px 0;
}

.amount-options {
  margin: 15px 0;
}


.hashtag {
  color: var(--ion-color-secondary);
  margin-right: 2px;
}


.amount-input-container {
  display: flex;
  border: 1px solid var(--ion-color-light);
  border-radius: 10px;
  overflow: hidden;
}

ion-input {
  --padding-end: 0;
  text-align: right;
  --placeholder-color: var(--ion-color-medium);
  --placeholder-opacity: 1;
}

.currency-symbol {
  display: flex;
  align-items: center;
  padding: 0 15px;
  background-color: var(--ion-color-light);
  color: var(--ion-color-secondary);
  font-weight: bold;
}

.continue-button {
  margin: 20px 0;
  --background: var(--ion-color-secondary);
  --color: var(--ion-color-light);
  --border-radius: 10px;
  height: 44px;
  font-size: 16px;
}

.stats-container {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  gap: 10px;
  width: 100%;
  max-width: 500px;
}

.stat-card {
  flex: 1;
  background-color: var(--ion-color-light);
  border-radius: 10px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  text-align: center;
  align-items: flex-start;
  height: 115px;
}

.stat-icon ion-icon {
  font-size: 24px;
  color: var(--ion-color-medium-tint);
}

.stat-value {
  font-weight: bold;
  font-size: 18px !important;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: var(--ion-color-medium-tint);
}

.payment-methods {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;
  padding: 15px 0;
}

.payment-methods img {
  height: 30px;
  width: auto;
}

.input-wrapper {
  position: relative;
  width: 100%;
}

.custom-error {
  color: var(--ion-color-danger);
  font-size: 12px;
  margin-top: 4px;
  text-align: right;
  font-weight: 500;
}

.summary-section {
  width: 100%;
  max-width: 500px;
  border-radius: 8px 8px 0 0;
  background: var(--ion-color-neutral);
}

.progress-outer {
  height: 15px;
  width: 100%;
  text-align: center;
  background-color: var(--ion-color-medium-shade);
  color: var(--ion-text-color);
}

.progress-inner {
  height: 100%;
  min-width: 15%;
  width: 90%;
  white-space: nowrap;
  overflow: hidden;
  background-color: var(--ion-color-secondary);
  color: white;
  line-height: 1;
}

.vertical-line {
  min-width: 175px;
}
