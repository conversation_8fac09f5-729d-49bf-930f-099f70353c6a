import {Component, EventEmitter, inject, Inject, Input, OnDestroy, OnInit, Output} from '@angular/core';
import {ActivatedRoute, NavigationExtras, Router} from "@angular/router";
import {ApiService} from "../../../../shared/services/api.service";
import {Product} from "../../../../shared/models";
import {ModalService} from "../../../../shared/services/ModalService";
import {NavController, Platform, ToastController} from "@ionic/angular";
import {
  IonButton, IonIcon,
  IonSpinner,
} from "@ionic/angular/standalone";
import {NgIf} from "@angular/common";
import {catchError, Subject, takeUntil, throwError} from "rxjs";
import {Browser} from "@capacitor/browser";
import {environment} from "../../../../../environments/environment";
import {GenericService} from "../../../../shared/services/generic.service";
import {NotificationService} from "../../../../shared/services/notification.service";
import {convertArabicToEnglishNumbers} from "../../../../shared/helpers/helper.functions";
import {DOCUMENT} from '@angular/common';

declare var myFatoorahAP: any;
declare var myFatoorah: any;

@Component({
  selector: 'app-payment-screen',
  templateUrl: './payment-screen.component.html',
  styleUrls: ['./payment-screen.component.scss'],
  standalone: true,
  imports: [
    IonSpinner,
    IonButton,
    NgIf,
    IonIcon,
  ]
})
export class PaymentScreenComponent implements OnInit, OnDestroy {
  @Input() product!: Product;
  @Input() donationParams: any;
  @Input() active = false; // Add this to control when to initialize payment
  @Output() goBackRequested = new EventEmitter<void>();
  toastCtrl = inject(ToastController);

  transactionUUID = '';
  myFatoorahAppleSessionId = '';
  myFatoorahCreditCardSessionId = '';
  showPaymentsSpinner = false;
  creditCardProcessing = false;
  private channelName!: string;
  private isCapacitorApp = false;
  private destroy$ = new Subject<void>();
  private paymentInitiated = false;

  constructor(
    private apiService: ApiService,
    @Inject('Window') private window: Window,
    @Inject(DOCUMENT) private document: Document,
    private modalService: ModalService,
    private activatedRoute: ActivatedRoute,
    private navCtrl: NavController,
    private router: Router,
    private platform: Platform,
    private genericService: GenericService,
    private notificationService: NotificationService,
  ) { }

  ngOnInit() {
    this.isCapacitorApp = this.genericService.isCapacitorApp();

    // Don't automatically initiate payment on component init
    // We'll do it explicitly when the slide is active
  }

  goBack() {
    this.goBackRequested.emit();
  }

  addScript(src: string, id: string, callback: any) {
    const script = this.document.createElement('script');
    script.type = 'text/javascript';
    script.src = src;
    script.id = id;
    script.onload = (obj) => {
      callback('success', obj);
    }
    script.onerror = (obj) => {
      callback('error', obj);
    }
    this.document.body.appendChild(script);
  }

  // Make this method public so it can be called from the wrapper
  initiatePayment() {
    console.log('Initiating payment with params:', this.donationParams);
    console.log('Product ID:', this.product?.id);

    // Prevent multiple initiations
    if (this.paymentInitiated) {
      console.log('Payment already initiated');
      return;
    }

    if (!this.donationParams || !this.product?.id) {
      console.error('Missing donation params or product ID');
      this.goBack();
      return;
    }

    this.paymentInitiated = true;
    this.showPaymentsSpinner = true;

    this.apiService
      .getMyfatoorahSessionId(
        this.product.id,
        this.donationParams.name,
        this.donationParams.phone,
        this.donationParams.amount
      )
      .pipe(
        catchError((err) => {
          this.showPaymentsSpinner = false;
          this.paymentInitiated = false;
          let messages: string[] = [];
          if (err.error && err.error.errors) {
            Object.keys(err.error.errors).forEach((key) => {
              if (err.error.errors[key].find((err: string) => err === 'INVALID_FORMAT'))
                messages = [...messages, 'رقم الهاتف غير صحيح'];
              if (err.error.errors[key].find((err: string) => err === 'NUMERIC'))
                messages = [...messages, 'المبلغ غير صحيح'];
              if (err.error.errors[key].find((err: string) => err === 'MIN'))
                messages = [...messages, 'لم تبلغ الحد الأدنى للتبرع'];
              if (err.error.errors[key].find((err: string) => err === 'MAX'))
                messages = [...messages, 'تجاوزت الحد الأقصى للتبرع'];
            });
          }

          if (messages.length > 0) {
            // Show error and go back to details screen
            alert(messages.join(' و'));
            this.goBack();
          } else {
            // Generic error
            alert('حدث خطأ أثناء معالجة الدفع');
            this.goBack();
          }

          return throwError(() => err);
        }),
        takeUntil(this.destroy$)
      )
      .subscribe(async (res: any) => {
        if (this.isCapacitorApp) {
          const uuid = res['uuid'];
          if (this.genericService.isAndroidApp()) {
            Browser.open({
              url: `${environment.appURL}/pay?uuid=${uuid}`,
              presentationStyle: 'popover',
            })
              .then(() => {
                Browser.addListener('browserFinished', async () => {
                  let navigationExtras: NavigationExtras = {
                    queryParams: {
                      productId: this.product.id,
                      transactionUUID: uuid
                    }
                  };
                  this.apiService.getTransaction(uuid)
                    .subscribe(async (res: any) => {
                      if (res['payment_status'] === 'paid') {
                        await this.closeModal();
                        this.showPaymentsSpinner = false;
                        this.navCtrl.navigateForward('/checkout/success', navigationExtras).then();
                      } else if (res['payment_status'] === 'failed') {
                        await this.closeModal();
                        this.showPaymentsSpinner = false;
                        this.navCtrl.navigateForward('/checkout/failure', navigationExtras).then();
                      } else
                        this.recheckTransaction(uuid);
                    });
                });
              });
          } else {
            await Browser.open({
              url: `${environment.appURL}/pay?uuid=${uuid}`,
              presentationStyle: 'popover',
            });

            this.channelName = `ProductTransaction.${uuid}`;
            if (this.notificationService.Echo)
              this.notificationService.Echo.channel(this.channelName).listen('PaymentTransactionStatusUpdated', async (data: any) => {
                let navigationExtras: NavigationExtras = {
                  queryParams: {
                    productId: this.product.id,
                    transactionUUID: uuid
                  }
                };
                try {
                  await this.closeModal();
                  if (data['status'] === 'SUCCESS')
                    this.navCtrl.navigateForward('/checkout/success', navigationExtras).then();
                  else
                    this.navCtrl.navigateForward('/checkout/failure', navigationExtras).then();
                } catch {
                  this.navCtrl.navigateForward('/checkout/failure', navigationExtras).then();
                }
                try {
                  Browser.close();
                } catch {
                }

                if (this.channelName && this.notificationService.Echo)
                  this.notificationService.Echo.leave(this.channelName);

                this.showPaymentsSpinner = false;
              });
          }
        } else {
          this.transactionUUID = res['uuid'];
          this.myFatoorahAppleSessionId = res['apple_session_id'];
          this.myFatoorahCreditCardSessionId = res['session_id'];
          this.initiateApplePayPayment();
          this.initiateCreditCardPayment();
          this.showPaymentsSpinner = false;
        }
      });
  }

  // Rest of the component remains the same...
  payWithCreditCard() {
    this.creditCardProcessing = true;

    (this.window as any).myFatoorah.submit().then(
      (response: { sessionId: string, cardBrand: string }) => {
        // In case of success
        const sessionId = response.sessionId;
        let navigationExtras: NavigationExtras = {
          queryParams: {
            productId: this.product.id,
            transactionUUID: this.transactionUUID
          }
        };
        this.apiService.getInvoiceFromPaymentSessionId(this.transactionUUID, sessionId).subscribe(async (res: any) => {
          await this.closeModal();
          if (res['success'] === true) {
            window.location = res['invoice_url'];
          } else
            this.navCtrl.navigateForward('/checkout/failure', navigationExtras).then();
        });
        this.creditCardProcessing = false;
      }
    ).catch(
      (error: any) => {
        // In case of errors
        console.error(error);
        this.creditCardProcessing = false;
      }
    );
  }

  private initiateApplePayPayment() {
    this.showPaymentsSpinner = true;
    const amount = this.donationParams.amount;

    if (amount) {
      const config = {
        sessionId: this.myFatoorahAppleSessionId,
        countryCode: environment.myfatoorah.countryCode,
        currencyCode: environment.myfatoorah.currencyCode,
        amount: convertArabicToEnglishNumbers(amount),
        cardViewId: "card-applepay",
        Language: "ar",
        callback: ((response: any) => {
          this.showPaymentsSpinner = true;

          this.apiService.getInvoiceFromPaymentSessionId(this.transactionUUID, response.sessionId).subscribe(async (res: any) => {
            let navigationExtras: NavigationExtras = {
              queryParams: {
                productId: this.product.id,
                transactionUUID: this.transactionUUID
              }
            };
            await this.closeModal();
            if (res['success'] === true)
              this.navCtrl.navigateForward('/checkout/success', navigationExtras).then();
            else
              this.navCtrl.navigateForward('/checkout/failure', navigationExtras).then();

            this.showPaymentsSpinner = false;
          });
        }),
        sessionStarted: ((sessionStarted: any) => {
          this.showPaymentsSpinner = true;
        }),
        sessionCanceled: ((sessionCanceled: any) => {
          // Handle cancel if needed
        }),
      };

      myFatoorahAP.init(config);
    }
  }

  private initiateCreditCardPayment() {
    this.showPaymentsSpinner = true;

    const amount = this.donationParams.amount;
    if (amount) {
      const config = {
        sessionId: this.myFatoorahCreditCardSessionId,
        countryCode: environment.myfatoorah.countryCode,
        currencyCode: environment.myfatoorah.currencyCode,
        amount: amount,
        cardViewId: "credit-card",
        Language: "ar",
        callback: ((response: any) => {
          // Callback implementation if needed
        }),
        sessionStarted: ((sessionStarted: any) => {
          this.showPaymentsSpinner = true;
        }),
        sessionCanceled: ((sessionCanceled: any) => {
          // Handle cancel if needed
        }),
      };

      myFatoorah.init(config);
    }
  }

  async closeModal() {
    // Dismiss the modal
    await this.modalService.closeAllModals();

    // Get current query params
    const currentQueryParams = {...this.activatedRoute.snapshot.queryParams};

    // Remove specific query params
    delete currentQueryParams['productId'];

    // Navigate to same route without the modal-related query params
    return this.router.navigate(
      [],
      {
        relativeTo: this.activatedRoute,
        queryParams: currentQueryParams,
        replaceUrl: true
      }
    );
  }

  ngOnDestroy(): void {
    if (this.channelName && this.notificationService.Echo) {
      this.notificationService.Echo.leave(this.channelName);
    }

    Browser.removeAllListeners().then();
    this.destroy$.next();
    this.destroy$.complete();
  }

  private recheckTransaction(uuid: string, maxAttempts = 3, delayMs = 2000) {
    let attempts = 0;

    const checkStatus = () => {
      attempts++;
      this.apiService.getTransaction(uuid)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: async (res: any) => {
            let navigationExtras: NavigationExtras = {
              queryParams: {
                productId: this.product.id,
                transactionUUID: uuid
              }
            };

            if (res['payment_status'] === 'paid') {
              this.navCtrl.navigateForward('/checkout/success', navigationExtras).then();
              this.showPaymentsSpinner = false;
            } else if (res['payment_status'] === 'failed') {
              this.navCtrl.navigateForward('/checkout/failure', navigationExtras).then();
              this.showPaymentsSpinner = false;
            } else if (attempts < maxAttempts) {
              // Still processing, try again after delay
              setTimeout(checkStatus, delayMs);
            } else {
              // Max attempts reached, show failure or pending message
              this.toastCtrl.create({
                message: 'لم نتمكن من التحقق من حالة الدفع',
                duration: 2000,
                color: 'danger'
              }).then(toast => toast.present());
              await this.closeModal();
              this.showPaymentsSpinner = false;
              this.navCtrl.navigateForward('/checkout/failure', navigationExtras).then();
            }
          },
          error: async () => {
            // Handle error in recheck
            if (attempts < maxAttempts) {
              setTimeout(checkStatus, delayMs);
            } else {
              await this.closeModal();
              this.showPaymentsSpinner = false;
              this.toastCtrl.create({
                message: 'حدث خطأ أثناء التحقق من حالة الدفع',
                duration: 2000,
                color: 'danger'
              }).then(toast => toast.present());
            }
          }
        });
    };

    checkStatus();
  }
}
