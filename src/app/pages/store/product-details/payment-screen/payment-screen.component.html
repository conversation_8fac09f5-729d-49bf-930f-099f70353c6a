<div class="modal-content">
  <!-- Header -->
  <div class="header ion-padding-vertical">
    <ion-icon
      class="pointer"
      (click)="goBack()"
      name="arrow-back-outline"
      style="font-size: 26px;color: var(--ion-text-color)"/>
    <ion-icon
      class="pointer"
      (click)="closeModal()"
      name="close-circle-outline"
      style="font-size: 26px; color: var(--ion-text-color);"/>
  </div>
    <ion-spinner *ngIf="showPaymentsSpinner" style="margin: auto; display: block; width: 100%; height: 40px;"/>

    <div id="card-applepay" class="ion-margin-top" [hidden]="showPaymentsSpinner"></div>
    <div class="altwijry-delimiter" *ngIf="!creditCardProcessing"></div>
    <div id="credit-card" [hidden]="showPaymentsSpinner"></div>

    <ion-button *ngIf="myFatoorahCreditCardSessionId"
                color="secondary"
                expand="block"
                class="ion-margin-top"
                [hidden]="showPaymentsSpinner"
                (click)="payWithCreditCard()">
      <ion-spinner *ngIf="creditCardProcessing"/>
      <span *ngIf="!creditCardProcessing" [hidden]="showPaymentsSpinner">ادفع</span>
    </ion-button>
  </div>

