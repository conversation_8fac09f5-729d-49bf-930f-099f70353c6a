import {
  ChangeDetectorRef,
  Component,
  Inject,
  Input,
  OnInit,
  Signal,
  ViewChild,
  computed,
  effect,
  input,
  model,
  signal, numberAttribute
} from '@angular/core';
import {
  AsyncPipe,
  DOCUMENT,
  NgForOf,
  NgIf,
  NgOptimizedImage,
} from "@angular/common";
import {InfiniteScrollCustomEvent, IonicModule, IonModal} from "@ionic/angular";
import {HeaderComponent} from "../../layout/header/header.component";
import {ApiService} from "../../shared/services/api.service";
import {Observable, finalize, map, take} from "rxjs";
import {ActivatedRoute, Router, RouterModule} from "@angular/router";
import {AdBanner, Category, Product} from "../../shared/models";
import {CategoriesService} from "../../shared/services/categories.service";
import {NgxSplideModule} from "ngx-splide";
import {FormsModule} from "@angular/forms";
import {StoreBannersService} from "../../shared/services/store-banners.service";
import {ProductDetailsWrapperComponent} from "./product-details/product-details-wrapper.component";

@Component({
  selector: 'app-store',
  templateUrl: 'store-page.component.html',
  styleUrls: ['store-page.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    NgIf,
    NgForOf,
    AsyncPipe,
    RouterModule,
    NgxSplideModule,
    NgOptimizedImage,
    FormsModule,
    ProductDetailsWrapperComponent
  ],
})
export class StorePage implements OnInit {
  @ViewChild('productModal') productModal!: IonModal;

  // Router inputs with withComponentInputBinding
  @Input({transform: numberAttribute}) set productId(id: number) {
    if (id) {
      this.openProductById(id);
    }
  }


  @Input() set categoryId(id: string | null) {
    if (id) {
      this.selectCategoryById(parseInt(id));
    }
  }

  // State management with signals
  products = signal<Product[]>([]);
  selectedCategory = signal<Category | null>(null);
  selectedProduct = signal<Product | null>(null);
  ads = signal<AdBanner[]>([]);

  // UI state
  isLoading = signal(false);
  productsLoaded = signal(false);
  modalOpen = signal(false);
  categoriesLoaded = signal(false);

  // Pagination
  productsNextPageUrl = signal<string | undefined>(undefined);

  // Computed values
  selectedCategoryId = computed(() => this.selectedCategory()?.id ?? null);
  selectedCategoryTitle = computed(() => this.selectedCategory()?.title ?? '');

  // DOM elements

  // External data
  categories$: Observable<Category[]>;

  constructor(
    @Inject(DOCUMENT) private document: Document,
    private apiService: ApiService,
    protected categoriesService: CategoriesService,
    protected storeBannersService: StoreBannersService,
    private router: Router,
    private route: ActivatedRoute,
    private cdr: ChangeDetectorRef
  ) {
    // Load and filter categories
    this.categories$ = this.categoriesService.loadCategories().pipe(
      map(response => response?.data || []),
      map(categories => categories.filter(cat => (cat.data['products_count'] || 0) > 0))
    );
  }

  ngOnInit(): void {

    // Check for product ID from route parameters (legacy URL format)
    this.route.parent?.paramMap.pipe(take(1)).subscribe(params => {
      const legacyProductId = params.get('id');
      if (legacyProductId) {
        this.openProductById(+legacyProductId);
      }
    });

    // Handle initialization
    this.initializeSelectedCategory();
    this.loadAds();
  }

  // Initialize the category selection
  private initializeSelectedCategory(): void {
    this.categories$.pipe(take(1)).subscribe(categories => {
      this.categoriesLoaded.set(true);

      // If we already have a category (from router inputs), don't override it
      if (this.selectedCategory() !== null) {
        return;
      }

      // Otherwise select the first category with products
      if (categories.length > 0) {
        this.selectedCategory.set(categories[0]);
        this.loadProducts();
      }
    });
  }

  // Load banners/ads
  loadAds(): void {
    this.storeBannersService.loadStoreBanners()
      .pipe(take(1))
      .subscribe(({data}) => {
        this.ads.set(data.map(banner => ({
          id: banner.id,
          image_url: banner.image_url,
          type: banner.type,
          data: banner.data,
        })));
        this.cdr.detectChanges();
      });
  }

// Select a category by ID
  selectCategoryById(categoryId: number): void {
    if (this.selectedCategoryId() === categoryId) return;
    // Reset UI state first
    this.resetProductsList();

    this.categories$.pipe(take(1)).subscribe(categories => {
      const category = categories.find(c => c.id === categoryId);
      if (category) {
        // Then update category
        this.selectedCategory.set(category);

        // Update URL with the new category ID
        this.updateUrlWithCategory(categoryId);

        // Finally load products
        this.loadProducts();
      }
    });
  }


// Reset products list with immediate loading state
  private resetProductsList(): void {
    // Set loading state immediately
    this.isLoading.set(true);
    this.productsLoaded.set(false);
    this.products.set([]); // Clear products to show loading skeletons
    this.productsNextPageUrl.set(undefined);
  }


// Handle category selection from UI
  selectCategory(event: CustomEvent): void {
    const newCategoryId = parseInt(event.detail.value);
    if (this.selectedCategoryId() === newCategoryId) return;

    // Let selectCategoryById handle everything
    this.selectCategoryById(newCategoryId);
  }


  // Open product by ID
  openProductById(productId: number): void {
    this.apiService.getProductById(productId).pipe(take(1)).subscribe(product => {
      if (product) {
        // Update category if product has category information
        if (product.primary_category_id) {
          this.selectCategoryById(product.primary_category_id);
        }

        this.openProductModal(product);
      }
    });
  }

  // Open product modal
  openProductModal(product: Product): void {
    this.selectedProduct.set({
      ...product,
      id: product.id,
      title: product.title,
      image_url: product.image_url,
      type: product.type,
    } as Product);

    this.productModal.initialBreakpoint = 0.86;
    this.productModal.breakpoints = [0, 0.3, 0.5, 0.86, 0.95, 1];
    this.modalOpen.set(true);

    // Update URL with product ID
    this.updateUrlWithProduct(product.id);
  }

  // Handle modal close
  onModalDismiss(): void {
    this.closeModal();
  }

  // Close the product modal
  closeModal(): void {
    this.modalOpen.set(false);
    this.selectedProduct.set(null);

    // Remove product ID from URL
    this.updateUrlWithProduct(null);
  }

// Handle ad clicks
  handleAdClick(ad: AdBanner): void {
    // Always track the banner click
    this.apiService.storeBannerClick(ad.id).subscribe();

    if (ad.type === 'PRODUCT' && ad.data['product_id']) {
      this.apiService.getProductById(ad.data['product_id']).pipe(take(1)).subscribe(product => {
        if (product) {
          // Update category if product has category information
          if (product.primary_category_id && product.primary_category_id !== this.selectedCategoryId()) {
            // Reset products first for immediate feedback
            this.resetProductsList();
            this.selectCategoryById(product.primary_category_id);
          }

          // Open the product modal
          this.openProductModal(product);
        }
      });
    } else if (ad.type === 'CATEGORY' && ad.data['category_id']) {
      // Reset products first for immediate feedback
      this.resetProductsList();
      this.selectCategoryById(parseInt(ad.data['category_id']));
    }
  }

  // Handle infinite scroll
  onIonInfinite(ev: any): void {
    this.loadMoreProducts();

    setTimeout(() => {
      (ev as InfiniteScrollCustomEvent).target.complete().then();
    }, 500);
  }

// Load products for current category (can be called after resetProductsList)
  loadProducts(): void {
    // Don't return if isLoading is true - we expect it to be true after resetProductsList
    // Only avoid duplicate fetches if products are already loaded
    if (this.productsLoaded()) return;

    // Ensure loading state is set (in case this is called directly)
    if (!this.isLoading()) {
      this.isLoading.set(true);
    }

    this.apiService.getProducts(this.selectedCategoryId(), this.productsNextPageUrl())
      .pipe(
        take(1),
        finalize(() => this.isLoading.set(false)),
      ).subscribe(res => {
      this.productsNextPageUrl.set(res.pagination?.next_url);
      this.products.set(res.data ?? []);
      this.productsLoaded.set(true);
      this.cdr.detectChanges();
    });
  }

  // Load more products (pagination)
  loadMoreProducts(): void {
    if (this.isLoading() || !this.productsNextPageUrl()) return;

    this.isLoading.set(true);

    this.apiService.getProducts(this.selectedCategoryId(), this.productsNextPageUrl())
      .pipe(
        take(1),
        finalize(() => this.isLoading.set(false)),
      ).subscribe(res => {
      this.productsNextPageUrl.set(res.pagination?.next_url);
      // Append new products to existing ones
      this.products.update(currentProducts => [
        ...currentProducts,
        ...(res.data ?? [])
      ]);
      this.cdr.detectChanges();
    });
  }

  // Update URL with category ID
  private updateUrlWithCategory(categoryId: number | null): void {
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: {categoryId},
      queryParamsHandling: 'merge'
    });
  }

  // Update URL with product ID
  private updateUrlWithProduct(productId: number | null): void {
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: {productId},
      queryParamsHandling: 'merge',
      replaceUrl: productId === null
    });
  }
}
