<app-header [hideProfileIcon]="false" [showBackButton]="true" backTo="/home"></app-header>
<ion-content [fullscreen]="true">
  <!-- Ads Slider -->
  <splide [options]="{direction: 'rtl', autoplay:true, arrows:false}">
    <splide-slide *ngFor="let ad of ads()">
      <img class="ads" (click)="handleAdClick(ad)" [ngSrc]="ad.image_url" alt="إعلان" width="500" height="150" priority/>
    </splide-slide>
  </splide>

  <!-- Category Segment -->
  <ion-segment class="ion-margin-vertical segment-container" color="primary"
               (ionChange)="selectCategory($event)" [value]="selectedCategoryId()">
    <ion-segment-button class="ion-no-margin" [value]="category.id"
                        *ngFor="let category of categories$ | async"
                        [disabled]="(category.data['products_count'] || 0) <= 0">
      <ion-label>{{ category.title }}</ion-label>
    </ion-segment-button>
  </ion-segment>

  <!-- Products List -->
  <ion-list>
    <ng-container *ngIf="isLoading() && !productsLoaded()">
      <ion-item *ngFor="let item of [1, 2, 3]">
        <ion-avatar slot="start">
          <ion-img [src]="'/assets/icon/logo.svg'"></ion-img>
        </ion-avatar>
        <ion-label>
          <ion-skeleton-text [animated]="true" style="width: 90%; height: 25px;"></ion-skeleton-text>
        </ion-label>
      </ion-item>
    </ng-container>

    <ion-item detail="false"
              [lines]="products().length > 0 ? 'full' : 'none'"
              *ngFor="let product of products()"
              [button]="true"
              (click)="openProductModal(product)">
      <ion-avatar slot="start">
        <ion-img [src]="product.image_url"></ion-img>
      </ion-avatar>
      <ion-label>
        <h4>{{ product.title }}</h4>
      </ion-label>
    </ion-item>
  </ion-list>

  <ion-infinite-scroll (ionInfinite)="onIonInfinite($event)" *ngIf="productsNextPageUrl()">
    <ion-infinite-scroll-content></ion-infinite-scroll-content>
  </ion-infinite-scroll>

  <!-- Product Modal -->
  <ion-modal
    #productModal
    [isOpen]="modalOpen()"
    [backdropDismiss]="true"
    [showBackdrop]="true"
    [initialBreakpoint]="0.86"
    [breakpoints]="[0, 0.3, 0.5, 0.86, 0.95, 1]"
    [handleBehavior]="'cycle'"
    (didDismiss)="onModalDismiss()">
    <ng-template>
      <app-product-details-wrapper
        *ngIf="selectedProduct()"
        [product]="selectedProduct()!"
        [selectedCategoryTitle]="selectedCategoryTitle()">
      </app-product-details-wrapper>
    </ng-template>
  </ion-modal>
</ion-content>
