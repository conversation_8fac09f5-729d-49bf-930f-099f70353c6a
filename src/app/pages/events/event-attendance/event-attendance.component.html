<app-header [hideProfileIcon]="false" [showBackButton]="true" backTo="/events/{{eventId}}"></app-header>
<ion-content>
  <ion-spinner *ngIf="!event" style="margin: auto; width: 100%;height: 40px;"></ion-spinner>
  <ng-container *ngIf="event">
    <ion-grid [fixed]="true" class="ion-align-items-center ion-margin-bottom">
      <ion-card mode="ios" style="box-shadow: unset; border:solid 1px var(--ion-color-secondary, #113665); max-width: 370px; margin: auto">
        <ion-card-header>
          <ion-card-title>
            تسجيل حضور:
            {{ event.title }}
          </ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <ion-item *ngIf="event.attendances_count" class="ion-margin-bottom">
            <ion-label>عدد الحضور</ion-label>
            <ion-text color="primary">{{ event.attendances_count | number }}</ion-text>
          </ion-item>
          <ngx-scanner-qrcode #action="scanner" [config]="config"
                              (event)="onEvent($event, action)"></ngx-scanner-qrcode>
          <!-- Loading -->
          <p *ngIf="action.isLoading">⌛ Loading...</p>
          <!-- start -->
          <ion-button [color]="action.isStart ? 'danger' : 'primary'" expand="block"
                      (click)="handle(action, action.isStart ? 'stop' : 'start')">{{ action.isStart ? 'التوقف' : 'البدء' }}
          </ion-button>
        </ion-card-content>
      </ion-card>
    </ion-grid>
  </ng-container>
</ion-content>


