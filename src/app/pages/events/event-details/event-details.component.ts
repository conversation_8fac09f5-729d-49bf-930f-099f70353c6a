import {Component, Inject, Input, OnInit} from '@angular/core';
import {HeaderComponent} from "../../../layout/header/header.component";
import {IonicModule, Platform, NavController, ToastController, ModalController, AlertController} from "@ionic/angular";
import {Capacitor} from '@capacitor/core';
import {Event} from "../../../shared/models";
import {ApiService} from "../../../shared/services/api.service";
import {take, finalize, tap, catchError} from "rxjs";
import {Router, RouterModule} from '@angular/router';
import {AsyncPipe, DatePipe, DOCUMENT, DecimalPipe, NgForOf, NgIf} from "@angular/common";
import {ProfileService} from "../../../shared/services/profile.service";
import {FeaturesService} from "../../../shared/services/features.service";
import {IS_SERVER_PLATFORM} from "../../../shared/IS_SERVER_PLATFORM.token";
import {GenericService} from "../../../shared/services/generic.service";
import {CapacitorPassToWallet} from 'capacitor-pass-to-wallet';
import {QrCodeDialogComponent} from "../../../shared/components/qr-code/qr-code-dialog.component";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {Haptics, NotificationType} from "@capacitor/haptics";
import {AppDataService} from "../../../shared/services/app-data.service";

@Component({
  selector: 'app-event-details',
  templateUrl: './event-details.component.html',
  styleUrls: ['./event-details.component.scss'],
  standalone: true,
  imports: [
    AsyncPipe,
    IonicModule,
    HeaderComponent,
    NgIf,
    DatePipe,
    RouterModule,
    ReactiveFormsModule,
    FormsModule
  ]
})
export class EventDetailsComponent implements OnInit {
  @Input() event?: Event;
  @Input('id') eventId?: number;
  familyUserID = '';
  formLoading = false;
  BEEP = `data:audio/mpeg;base64,SUQzBAAAAAAAI1RTU0UAAAAPAAADTGF2ZjU4LjI5LjEwMAAAAAAAAAAAAAAA/+M4wAAAAAAAAAAAAEluZm8AAAAPAAAABQAAAkAAgICAgICAgICAgICAgICAgICAgKCgoKCgoKCgoKCgoKCgoKCgoKCgwMDAwMDAwMDAwMDAwMDAwMDAwMDg4ODg4ODg4ODg4ODg4ODg4ODg4P//////////////////////////AAAAAExhdmM1OC41NAAAAAAAAAAAAAAAACQEUQAAAAAAAAJAk0uXRQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/+MYxAANQAbGeUEQAAHZYZ3fASqD4P5TKBgocg+Bw/8+CAYBA4XB9/4EBAEP4nB9+UOf/6gfUCAIKyjgQ/Kf//wfswAAAwQA/+MYxAYOqrbdkZGQAMA7DJLCsQxNOij///////////+tv///3RWiZGBEhsf/FO/+LoCSFs1dFVS/g8f/4Mhv0nhqAieHleLy/+MYxAYOOrbMAY2gABf/////////////////usPJ66R0wI4boY9/8jQYg//g2SPx1M0N3Z0kVJLIs///Uw4aMyvHJJYmPBYG/+MYxAgPMALBucAQAoGgaBoFQVBUFQWDv6gZBUFQVBUGgaBr5YSgqCoKhIGg7+IQVBUFQVBoGga//SsFSoKnf/iVTEFNRTMu/+MYxAYAAANIAAAAADEwMFVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV`;
  DOUBLE_BEEP = `data:audio/mpeg;base64,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`;

  constructor(
    private apiService: ApiService,
    @Inject(DOCUMENT) private document: Document,
    @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
    public profileService: ProfileService,
    public featuresService: FeaturesService,
    private navCtrl: NavController,
    private modalCtrl: ModalController,
    public toastController: ToastController,
    @Inject('Window') private window: Window,
    private router: Router,
    public genericService: GenericService,
    public platform: Platform,
    public alertCtrl: AlertController,
    public toastCtrl: ToastController,
    public appDataService: AppDataService,
  ) {
  }

  loadEventData() {
    if (this.eventId)
      this.getEventById(this.eventId).subscribe({
        error: (error) => {
          if (error.status === 404)
            this.router.navigate(['/events'])
        }
      });
  }

  getEventById(id: number) {
    return this.apiService.checkAndGetData(
      this.apiService.getDynamicStateKey(`event-details-${this.eventId}`),
      this.apiService.getEventById(this.eventId ?? 0),
      []
    ).pipe(take(1), tap((data: Event) => {
      this.event = data;
    }))
  }

  ngOnInit() {
    if (!this.isServer) {
      this.browserSetup()
    }
    this.loadEventData()
  }

  browserSetup() {

  }

  failCallbackImage(event: any) {
    event.target.src = '/assets/icon/logo.svg'
  }

  getWalletPass() {
    if (this.event && this.event.in_guests_list) {
      this.formLoading = true
      this.apiService.getEventAppleWalletPass(this.event.id)
        .pipe(finalize(() => this.formLoading = false))
        .subscribe(async res => {
          if (this.genericService.isCapacitorApp() && Capacitor.getPlatform() === 'ios') {
            if (res.body) {
              let base64 = await this.genericService.convertBlobToBase64(res.body) as string;
              await CapacitorPassToWallet.addToWallet({base64})
            }
          } else {
            let filename = res.headers.get('Content-Disposition')?.split(';')[1].split('=')[1].replace(/\"/g, '')
              || (Date.now() + ".pkpass")
            if (res.body) {
              let PassUrl = window.URL.createObjectURL(res.body);
              let PASSLink = document.createElement('a');
              PASSLink.href = PassUrl;
              PASSLink.download = filename;
              PASSLink.click();
            }
          }
        });
    }
  }

  showQrCodePass() {
    if (this.event && this.event.in_guests_list) {
      this.formLoading = true
      this.apiService.getEventInvitation(this.event.id)
        .pipe(finalize(() => this.formLoading = false))
        .subscribe(async res => {
          const modal = await this.modalCtrl.create({
            component: QrCodeDialogComponent,
            componentProps: {
              data: `EventInvitation|${res.uuid}`,
            },
            canDismiss: async (data?: any, role?: string) => {
              return role !== 'gesture';
            },
            backdropDismiss: false,
            mode: 'ios',
          });
          await modal.present();
        });
    }
  }

  eventApply() {
    this.formLoading = true
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition((position) => this.positionFetched(position));
    } else {
      this.toastController.create({
        header: 'خطأ',
        message: 'يجب السماح للحصول على موقعك الحالي!',
        position: 'bottom',
        color: 'danger',
        buttons: [{
          side: 'end',
          text: 'x',
          role: 'cancel',
        }]
      }).then((toast) => {
        toast.present();
        this.formLoading = false
      });
    }
  }

  positionFetched(position: any) {
    if (this.eventId) {
      this.apiService.applyForEventById(this.eventId, {
        via: 'GPS',
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
      })
        .pipe(
          finalize(() => this.formLoading = false),
          catchError(({error}) => {
            let errorMessage = error.message || 'خطأ في تسجيل حضورك .';
            this.toastController.create({
              header: 'خطأ',
              message: errorMessage,
              position: 'bottom',
              color: 'danger',
              buttons: [{
                side: 'end',
                text: 'x',
                role: 'cancel',
              }]
            }).then((toast) => {
              toast.present();
            });
            throw new Error(JSON.stringify(error))
          })
        )
        .subscribe((res) => {
          if (res.success === true) {
            this.event = undefined;
            this.loadEventData()
          }
        })
    } else
      this.formLoading = false
  }

  async closeAttendanceModal() {
    await this.modalCtrl.dismiss();
  }

  confirmAttendanceModal() {
    if (this.eventId) {
      this.formLoading = true
      this.apiService.applyForEventById(this.eventId, {
        via: 'MANUAL',
        family_user_id: this.familyUserID,
      })
        .pipe(
          finalize(() => this.formLoading = false),
          catchError(({error}) => {
            let errorMessage = error.message || 'خطأ !';
            this.toastController.create({
              header: 'خطأ',
              message: errorMessage,
              position: 'top',
              color: 'danger',
              buttons: [{
                side: 'end',
                text: 'x',
                role: 'cancel',
              }]
            }).then((toast) => {
              toast.present();
              setTimeout(() => toast.dismiss(), 5000);
            });

            this.VIBRATE(1000);
            if (this.genericService.isCapacitorApp())
              Haptics.notification({type: NotificationType.Error})
            else
              this.PLAY_AUDIO(this.DOUBLE_BEEP);
            this.familyUserID = '';
            throw error;
          })
        )
        .subscribe((res: any) => {
          if (res.success === true) {
            this.VIBRATE(500);
            if (this.genericService.isCapacitorApp())
              Haptics.notification({type: NotificationType.Success})
            else
              this.PLAY_AUDIO(this.BEEP);
            if ('user' in res && 'name' in res.user)
              this.toastController.create({
                header: 'تمت العملية بنجاح',
                message: 'تم تسجيل حضور "' + res.user.name + '" بنجاح',
                position: 'top',
                color: 'success',
                buttons: [{
                  side: 'end',
                  text: 'x',
                  role: 'cancel',
                }]
              }).then((toast) => {
                toast.present();
                setTimeout(() => toast.dismiss(), 5000);
              });
          }
        });
    }
  }

  private VIBRATE(TIME: number) {
    try {
      if ('vibrate' in window?.navigator) {
        window?.navigator?.vibrate(TIME);
      } else if (this.genericService.isCapacitorApp()) {
        Haptics.vibrate({duration: TIME})
      }
    } catch (e) {
    }
  }

  private PLAY_AUDIO(BEEP: any) {
    const audio = new Audio(BEEP);
    // when the sound has been loaded, execute your code
    audio.oncanplaythrough = () => {
      const promise = audio.play();
      if (promise) {
        promise.catch((e) => {
          if (e.name === "NotAllowedError" || e.name === "NotSupportedError") {
            // console.log(e.name);
          }
        });
      }
    };
  };
}
