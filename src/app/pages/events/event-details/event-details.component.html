<app-header [hideProfileIcon]="false" [showBackButton]="true" backTo="/events"></app-header>
<ion-content>
  <ion-spinner *ngIf="!event" style="margin: auto; width: 100%;height: 40px;"></ion-spinner>
  <ng-container *ngIf="event">
    <ion-grid [fixed]="true" class="ion-align-items-center ion-margin-bottom">
      <ion-card mode="ios" style="box-shadow: unset; border:solid 1px var(--ion-color-secondary, #113665); max-width: 370px; margin: auto">
        <ion-card-header>
          <ion-card-title>{{ event.title }}</ion-card-title>
          <ion-text color="primary">
            <h6>
              <ion-row>
                <ion-col class="ion-margin-start ion-no-padding">
                  {{ event?.due_at | date:'shortDate' }}
                </ion-col>
                <ion-col class="ion-margin-start ion-no-padding">
                  {{ event?.start_at }}
                </ion-col>
                <ion-col size="auto" class="ion-no-padding"> ←</ion-col>
                <ion-col class="ion-margin-end ion-no-padding" style="text-align: left">
                  {{ event?.end_at }}
                </ion-col>
              </ion-row>
            </h6>
          </ion-text>
        </ion-card-header>
        <ion-card-content>
          <ion-row class="ion-justify-content-center ion-align-items-center">
            <ion-col class="ion-justify-content-center ion-align-items-center">
              <div *ngIf="event?.description" style="display: inline-block"
                   [innerHTML]="event?.description"></div>
              <ion-img [src]="event?.cover_url || ((appDataService.data$ | async)!['iconPath']) || '/assets/icon/logo.svg'" style="margin-top: 10px;"
                       (ionError)="failCallbackImage($event)"></ion-img>
            </ion-col>
          </ion-row>
          <!--!event?.is_past &&-->
          <ion-col class="ion-justify-content-center ion-align-items-center" *ngIf="event.guests_count!.allowded > 0">
            <ion-button color="primary" expand="block" mode="ios" style="color: white;"
                        routerLink="/events/{{eventId}}/guests">
              <span>
                دعوات الضيوف
                ({{ event.guests_count!.current }} / {{ event.guests_count!.allowded }})
              </span>
            </ion-button>
          </ion-col>
          <ion-row *ngIf="((featuresService.features$ | async)!['AppleWalletPasses'] === true || (featuresService.features$ | async)!['AndroidWalletPasses'] === true) && event?.in_guests_list">
            <ion-col class="ion-justify-content-center ion-align-items-center"
                     *ngIf="!genericService.isAndroidApp() && (featuresService.features$ | async)!['AppleWalletPasses'] === true">
              <ion-card style="margin: 0;cursor: pointer;box-shadow: none;" (click)="getWalletPass()">
                <ion-spinner style="position: absolute;left: calc(50% - 14px);top: calc(50% - 14px);"
                             *ngIf="formLoading"></ion-spinner>
                <ion-img [src]="'/assets/images/icons/add_to_wallet_ar.svg'"
                         [style]="{width: '100%', height: '100%', margin: 0, opacity: formLoading ? '25%' : '100%'}"></ion-img>
              </ion-card>
            </ion-col>
            <ion-col class="ion-justify-content-center ion-align-items-center"
                     *ngIf="!genericService.isIosApp() && (featuresService.features$ | async)!['AndroidWalletPasses'] === true">
              <ion-card style="margin: 0;cursor: pointer;box-shadow: none;" (click)="showQrCodePass()">
                <ion-spinner style="position: absolute;left: calc(50% - 14px);top: calc(50% - 14px);"
                             *ngIf="formLoading"></ion-spinner>
                <ion-img [src]="'/assets/images/icons/add_to_gpay_ar.svg'"
                         [style]="{width: '100%', height: '100%', margin: 0, opacity: formLoading ? '25%' : '100%'}"></ion-img>
              </ion-card>
            </ion-col>
          </ion-row>
          <ion-col class="ion-justify-content-center ion-align-items-center" *ngIf="event.is_responsible_user">
            <ion-button color="primary" expand="block"
                        fill="outline"
                        id="open-attendance-modal"
                        mode="ios" style="color: white;">
              <span>تسجيل الحضور</span>
            </ion-button>
          </ion-col>
          <ion-col class="ion-justify-content-center ion-align-items-center" *ngIf="event.is_responsible_user">
            <ion-button color="primary" expand="block" mode="ios" style="color: white;"
                        routerLink="/events/{{eventId}}/attendance">
              <span>مسح الدعوات "QRCODE"</span>
            </ion-button>
          </ion-col>
          <ion-col class="ion-justify-content-center ion-align-items-center" *ngIf="event.can_checkin">
            <ion-button color="primary" expand="block" mode="ios" style="color: white;"
                        (click)="eventApply()" [disabled]="formLoading">
              <ion-spinner *ngIf="formLoading"></ion-spinner>
              <ng-container *ngIf="!formLoading">
                <span>سجل حضورك</span>
              </ng-container>
            </ion-button>
          </ion-col>
          <ion-col class="ion-justify-content-center ion-align-items-center"
                   *ngIf="event?.is_attended">
            <ion-text color="secondary" class="center"><b>شكرا لك، تم تسجيل حضورك ..</b></ion-text>
          </ion-col>
        </ion-card-content>
      </ion-card>
    </ion-grid>
    <ion-modal trigger="open-attendance-modal" [initialBreakpoint]="0.90" [breakpoints]="[0,0.9]">
      <ng-template>
        <ion-header>
          <ion-toolbar>
            <ion-buttons slot="start">
              <ion-button (click)="closeAttendanceModal()">إلغاء</ion-button>
            </ion-buttons>
            <ion-title>تسجيل الحضور</ion-title>
            <ion-buttons slot="end">
              <ion-button (click)="confirmAttendanceModal()" [strong]="true">
                <ion-spinner *ngIf="formLoading"></ion-spinner>
                <ng-container *ngIf="!formLoading">
                  <span>تسجيل</span>
                </ng-container>
              </ion-button>
            </ion-buttons>
          </ion-toolbar>
        </ion-header>
        <ion-content class="ion-padding">
          <ion-icon name="letter-message" color="secondary"
                    style="font-size: 180px; text-align: center; width: 100%;"></ion-icon>
          <ion-item>
            <ion-input
              label="أضف الرقم التعريفي/رقم الجوال للضيف"
              labelPlacement="stacked"
              pattern="[0-9]*"
              maxlength="13"
              inputmode="numeric"
              placeholder="12345 / 055xxxxxxxxx"
              [(ngModel)]="familyUserID"
            ></ion-input>
          </ion-item>
          <div class="separator">أو</div>

          <!--        QR CODE Scan-->
          <ion-button fill="clear" expand="block"
                      (click)="closeAttendanceModal()"
                      routerLink="/events/{{eventId}}/attendance"
                      color="secondary">امسح رمز الـ QR</ion-button>
        </ion-content>
      </ng-template>
    </ion-modal>
  </ng-container>
</ion-content>


