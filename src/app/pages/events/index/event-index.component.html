<app-header [hideProfileIcon]="false" [showBackButton]="true"></app-header>
<ion-content>
  <ion-grid [fixed]="true">
    <ion-row>
      <ion-col size="12">
        <ng-container *ngTemplateOutlet="events"></ng-container>
      </ion-col>
    </ion-row>
    <ion-infinite-scroll (ionInfinite)="onIonInfinite($event)" *ngIf="eventsNextPageUrl">
      <ion-infinite-scroll-content></ion-infinite-scroll-content>
    </ion-infinite-scroll>
  </ion-grid>
</ion-content>

<ng-template #events>
  <div style="overflow-y: scroll; overflow-x: hidden; max-width: 370px; margin: auto;">
    <ng-container *ngIf="events$ | async as events;">
      <ion-spinner *ngIf="!isLoaded" style="margin: auto; width: 100%;height: 40px;"></ion-spinner>

      <ng-container *ngIf="isLoaded">
        <ng-container *ngIf="(events ?? []).length > 0; else emptyTableTemplate">
          <ion-item *ngFor="let oneEvent of events; let i = index"
                    [title]="oneEvent.title" button lines="full"
                    [routerLink]="'/events/' + oneEvent.id">
            <ion-avatar slot="start">
              <ion-img [src]="oneEvent.cover_thumb_url || ((appDataService.data$ | async)!['iconPath']) || '/assets/icon/logo.svg'"
                       (ionError)="failCallbackImage($event)"></ion-img>
            </ion-avatar>
            <ion-label>
              <h4 class="ion-text-wrap" style="line-height: 1.7 !important;">{{ oneEvent.title }}</h4>
              <p class="">{{ oneEvent.due_at | date:'shortDate' }}</p>
            </ion-label>
          </ion-item>
        </ng-container>
      </ng-container>
    </ng-container>
  </div>
</ng-template>

<ng-template #emptyTableTemplate>
  <div class="ion-text-center">
    <ion-text color="danger">لا يوجد مناسبات في الوقت الحالي!</ion-text>
  </div>
</ng-template>
