import { ChangeDetector<PERSON>ef, Component, Inject, OnInit, TransferState } from '@angular/core';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, DatePipe, DOCUMENT, <PERSON>sonPipe, <PERSON>ForO<PERSON>, <PERSON><PERSON><PERSON>, NgTemplateOutlet} from "@angular/common";
import {InfiniteScrollCustomEvent, IonicModule} from "@ionic/angular";
import {HeaderComponent} from "../../../layout/header/header.component";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {IS_SERVER_PLATFORM} from "../../../shared/IS_SERVER_PLATFORM.token";
import {CardComponent} from "../../../shared/components/card/card.component";
import {ApiService} from "../../../shared/services/api.service";
import {HttpClient} from "@angular/common/http";

import {BehaviorSubject, finalize, of, take, tap} from "rxjs";
import {RouterModule} from "@angular/router";
import {Event} from "../../../shared/models";
import {FormatDistancePipe} from "../../../shared/pipes/format-distance.pipe";
import {AppDataService} from "../../../shared/services/app-data.service";


@Component({
  selector: 'app-event-list',
  templateUrl: 'event-index.component.html',
  styleUrls: ['event-index.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    NgIf,
    NgTemplateOutlet,
    NgForOf,
    AsyncPipe,
    RouterModule,
    DatePipe,
  ],
})
export class EventIndexComponent implements OnInit {
  isLoading = false;
  eventsPage = 0;
  eventsNextPageUrl = '';
  isLoaded = false;
  private _events$ = new BehaviorSubject<Event[]>([]);
  events$ = this._events$.asObservable()


  constructor(@Inject(DOCUMENT) private document: Document,
              @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
              private apiService: ApiService,
              private transferState: TransferState,
              private http: HttpClient,
              public appDataService: AppDataService,
              private cdr: ChangeDetectorRef
  ) {

  }

  ngOnInit(): void {
    if (this.isServer) {
      console.log('is server')
    }
    this.getEvents().pipe(take(1)).subscribe();
    if (!this.isServer) {
      this.browserSetup();
    }
  }

  browserSetup() {
  }

  getEvents() {
    if (this.isLoading)
      return of();
    this.isLoading = true;
    this.eventsPage++;
    return this.apiService.checkAndGetData(
      this.apiService.getDynamicStateKey(`events-${this.eventsPage}`),
      this.apiService.getEvents(this.eventsNextPageUrl),
      []
    ).pipe(take(1), finalize(() => this.isLoading = false), tap((data) => {
      this.eventsNextPageUrl = data.pagination?.next_url;
      this._events$.next([...this._events$.value, ...data.data].sort(
        (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      ) ?? []);
      this.cdr.detectChanges();
    })).pipe(finalize(() => this.isLoaded = true));
  }

  onIonInfinite(ev: any) {
    this.getEvents().pipe(take(1)).subscribe();
    setTimeout(() => {
      (ev as InfiniteScrollCustomEvent).target.complete().then();
    }, 500);
  }

  failCallbackImage(event: any) {
    this.appDataService.data$.subscribe(data => {
      event.target.src = (data!['iconPath']) ||  '/assets/icon/logo.svg'
    })
  }
}
