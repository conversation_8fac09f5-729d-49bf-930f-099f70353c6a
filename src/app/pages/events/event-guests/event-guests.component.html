<app-header [hideProfileIcon]="false" [showBackButton]="true" backTo="/events/{{ eventId }}"></app-header>
<ion-content color="light" [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large" style="">دعوات الضيوف</ion-title>
    </ion-toolbar>
  </ion-header>
  <ion-grid [fixed]="true">
    <app-card [showExpandButton]="false" [scrollable]="false">
      <ng-container *ngIf="data$ | async as guestsData;">
        <ion-spinner *ngIf="!isReady" style="margin: auto; width: 100%;height: 40px;"></ion-spinner>
        <ion-list *ngIf="isReady">
          <ion-item *ngIf="addNewGuest">
            <ion-list [inset]="true" [formGroup]="updateForm" [style]="{width: '100%', margin: '0'}">
              <ion-item *ngIf="newGuestType === 'NON_FAMILY'">
                <ion-input label="اسم الضيف" formControlName="guest_name"
                           dir="rtl" style="text-align: right;direction: rtl;"></ion-input>
              </ion-item>
              <ion-item *ngIf="newGuestType === 'FAMILY'">
                <ion-input label="الرقم التعريفي للضيف" formControlName="family_user_id"
                           dir="rtl" style="text-align: right;direction: rtl;"></ion-input>
              </ion-item>
              <ion-item *ngIf="newGuestType === null">
                <ion-button color="primary" expand="block" mode="ios" style="color: white;width: 100%;height: 3rem;"
                            (click)="newGuestType = 'FAMILY'">
                  من داخل العائلة
                </ion-button>
                <ion-button color="primary" expand="block" mode="ios" style="color: white;width: 100%;height: 3rem;"
                            class="ion-margin-start" (click)="newGuestType = 'NON_FAMILY'">
                  من خارج العائلة
                </ion-button>
              </ion-item>
              <ion-item *ngIf="newGuestType !== null && errorMessage">
                <ion-text color="danger" class="ion-text-center ion-margin-top">
                  <h2 style="font-size: 1rem">{{ errorMessage }}</h2>
                </ion-text>
              </ion-item>
              <ion-item *ngIf="newGuestType !== null">
                <ion-button color="primary" expand="block" mode="ios" style="color: white;width: 100%;height: 3rem;"
                            class="ion-margin-top" [disabled]="formLoading" (click)="storeNewGuest()">
                  <ion-spinner *ngIf="formLoading"></ion-spinner>
                  <span *ngIf="!formLoading">إضافة</span>
                </ion-button>
                <ion-button color="medium" expand="block" mode="ios" style="color: white;width: 100%;height: 3rem;"
                            class="ion-margin-top" [disabled]="formLoading" (click)="cancelNewGuest()">
                  <ion-spinner *ngIf="formLoading"></ion-spinner>
                  <span *ngIf="!formLoading">إلغاء</span>
                </ion-button>
              </ion-item>
            </ion-list>
          </ion-item>
          <ng-container *ngIf="!addNewGuest">
            <ion-item>
              <ion-button color="primary" expand="block" mode="ios" style="color: white;width: 100%;height: 3rem;"
                          class="ion-margin-top" (click)="addNewGuest = true">
                إضافة ضيف جديد
              </ion-button>
            </ion-item>
            <ion-item *ngFor="let guest of guestsData; let i = index"
                      style="border-bottom: 1px dotted var(--ion-color-primary);">
              <ion-label>
                <h4 style="line-height: 1.7 !important;">
                  {{ guest.guest_name }}
                </h4>
                <ion-row *ngIf="(featuresService.features$ | async)!['AppleWalletPasses'] === true || (featuresService.features$ | async)!['AndroidWalletPasses'] === true">
                  <ion-col class="ion-justify-content-center ion-align-items-center" *ngIf="(featuresService.features$ | async)!['AppleWalletPasses'] === true">
                    <ion-card style="margin: 0;cursor: pointer;box-shadow: none;" (click)="download(guest, 'apple')">
                      <ion-spinner style="position: absolute;left: calc(50% - 14px);top: calc(50% - 14px);"
                                   *ngIf="formLoading"></ion-spinner>
                      <ion-img [src]="'/assets/images/icons/add_to_wallet_ar.svg'"
                               [style]="{width: '100%', height: '100%', margin: 0, opacity: formLoading ? '25%' : '100%'}"></ion-img>
                    </ion-card>
                  </ion-col>
                  <ion-col class="ion-justify-content-center ion-align-items-center" *ngIf="(featuresService.features$ | async)!['AndroidWalletPasses'] === true">
                    <ion-card style="margin: 0;cursor: pointer;box-shadow: none;" (click)="download(guest, 'image')">
                      <ion-spinner style="position: absolute;left: calc(50% - 14px);top: calc(50% - 14px);"
                                   *ngIf="formLoading"></ion-spinner>
                      <ion-img [src]="'/assets/images/icons/add_to_gpay_ar.svg'"
                               [style]="{width: '100%', height: '100%', margin: 0, opacity: formLoading ? '25%' : '100%'}"></ion-img>
                    </ion-card>
                  </ion-col>
                </ion-row>
              </ion-label>
            </ion-item>
          </ng-container>
        </ion-list>
      </ng-container>
    </app-card>
  </ion-grid>
</ion-content>


