import {ChangeDetectorRef, Component, Inject, Input, OnInit} from '@angular/core';
import {HeaderComponent} from "../../../layout/header/header.component";
import {IonicModule, Platform, ToastController} from "@ionic/angular";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {HttpClientModule} from "@angular/common/http";
import {Event} from "../../../shared/models";
import {ApiService} from "../../../shared/services/api.service";
import {BehaviorSubject, catchError, take, tap} from "rxjs";
import {Router, RouterModule} from '@angular/router';
import {AsyncPipe, DatePipe, DecimalPipe, DOCUMENT, NgForOf, NgIf} from "@angular/common";
import {RenderBlockPipe} from "../../../shared/pipes/render-block.pipe";
import {CardComponent} from "../../../shared/components/card/card.component";
import {FormatDistancePipe} from "../../../shared/pipes/format-distance.pipe";
import {OneCardComponent} from "../../eid-cards/one-card/one-card.component";
import {IS_SERVER_PLATFORM} from "../../../shared/IS_SERVER_PLATFORM.token";
import {EventService} from "../../../shared/services/event.service";
import {FeaturesService} from "../../../shared/services/features.service";
import {GenericService} from "../../../shared/services/generic.service";
import {Directory, Filesystem} from "@capacitor/filesystem";
import {FormBuilder, FormGroup, ReactiveFormsModule} from "@angular/forms";
import {Share} from '@capacitor/share';

@Component({
  selector: 'app-event-guests',
  templateUrl: './event-guests.component.html',
  styleUrls: ['./event-guests.component.scss'],
  standalone: true,
  imports: [
    AsyncPipe,
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    IfIsBrowserDirective,
    NgForOf,
    NgIf,
    RenderBlockPipe,
    DatePipe,
    CardComponent,
    FormatDistancePipe,
    OneCardComponent,
    DecimalPipe,
    RouterModule,
    ReactiveFormsModule,
  ]
})
export class EventGuestsComponent implements OnInit {
  @Input('id') eventId?: number;
  @Input() event?: Event;
  private _data$ = new BehaviorSubject<any[]>([]);
  data$ = this._data$.asObservable();
  formLoading = false;
  isReady = false;
  addNewGuest = false;
  errorMessage: string | null = null;
  newGuestType: 'FAMILY' | 'NON_FAMILY' | null = null;
  updateForm: FormGroup = this.formBuilder.group({
    guest_name: ['', []],
    family_user_id: ['', []],
  });

  constructor(@Inject(DOCUMENT) private document: Document,
              @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
              private apiService: ApiService,
              public eventService: EventService,
              public featuresService: FeaturesService,
              public genericService: GenericService,
              public toastController: ToastController,
              public formBuilder: FormBuilder,
              public platform: Platform,
              public cdr: ChangeDetectorRef,
              private router: Router,
  ) {
  }

  loadEventData() {
    if (this.eventId)
      this.getEventById(this.eventId).subscribe({
        error: (error) => {
          if (error.status === 404)
            this.router.navigate(['/events'])
        }
      });
  }

  loadGuestsData() {
    if (this.eventId)
      this.eventService.guestsIndex(this.eventId).subscribe(data => {
        this.isReady = true;
        this._data$.next(data.data);
        this.cdr.detectChanges();
      });
  }

  getEventById(id: number) {
    return this.apiService.checkAndGetData(
      this.apiService.getDynamicStateKey(`event-details-${this.eventId}`),
      this.apiService.getEventById(this.eventId ?? 0),
      []
    ).pipe(take(1), tap((data: Event) => {
      this.event = data;
    }))
  }

  ngOnInit() {
    if (!this.isServer) {
      this.browserSetup()
    }
    this.loadEventData()
    this.loadGuestsData()
  }

  browserSetup() {

  }

  download(guest: any, type: string) {
    if (this.formLoading)
      return;
    if (this.eventId) {
      this.formLoading = true;
      this.eventService.downloadEventInvitation(this.eventId, guest.uuid, {
        type: type,
      }).subscribe({
        next: async (res) => {
          if (res.body) {
            let fileName = 'دعوة ' + guest.guest_name;
            switch (type) {
              case 'apple':
                fileName = ('دعوة ' + guest.guest_name + '.pkpass');
                break;
              case 'image':
                fileName = ('دعوة ' + guest.guest_name + '.png');
                break;
            }
            if (this.genericService.isCapacitorApp() /*&& Capacitor.getPlatform() === 'ios'*/) {
              let base64 = await this.genericService.convertBlobToBase64(res.body) as string;
              Filesystem.writeFile({
                path: fileName,
                data: base64,
                directory: Directory.Cache,
              }).then(async (res) => {
                await Share.share({
                  files: [res.uri],
                })
              })
            } else {
              let PassUrl = window.URL.createObjectURL(res.body);
              let PASSLink = document.createElement('a');
              PASSLink.href = PassUrl;
              PASSLink.download = fileName;
              PASSLink.click();
            }
          }
        },
        complete: () => {
          this.formLoading = false;
        }
      });
    }
  }

  storeNewGuest() {
    this.errorMessage = null;
    if (this.newGuestType === 'NON_FAMILY' && (this.updateForm.get('guest_name')!.value || '').length === 0)
      this.errorMessage = 'الرجاء إدخال اسم الضيف.';
    else if (this.newGuestType === 'FAMILY' && (this.updateForm.get('family_user_id')!.value || '').length === 0)
      this.errorMessage = 'الرجاء إدخال الرقم التعريفي للعائلة.';
    else if (this.eventId) {
      this.formLoading = true;
      this.eventService.guestsStore(this.eventId, {
        guest_name: this.updateForm.get('guest_name')!.value,
        family_user_id: this.updateForm.get('family_user_id')!.value,
      }).pipe(
        catchError(({error}) => {
          this.formLoading = false

          if ((error.errors && Object.keys(error.errors).length === 1) || error.message)
            this.errorMessage = error.message;
          else
            this.errorMessage = 'خطأ في إضافة بيانات الضيف.';
          throw new Error(JSON.stringify(error))
        })
      ).subscribe({
        next: (res) => {
          this.cancelNewGuest();
          this.isReady = false;
          this.loadGuestsData();
        },
        complete: () => {
          this.formLoading = false;
          this.cdr.detectChanges();
        }
      });
    }
  }

  cancelNewGuest() {
    this.addNewGuest = false;
    this.newGuestType = null;
    this.errorMessage = null;
    this.updateForm.patchValue({
      guest_name: '',
      family_user_id: '',
    });
  }
}
