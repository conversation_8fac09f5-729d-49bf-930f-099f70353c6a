import {AfterViewInit, Component, Inject, Input, OnInit} from '@angular/core';
import {DOCUMENT, NgIf} from "@angular/common";
import {IonicModule, NavController} from "@ionic/angular";
import {IS_SERVER_PLATFORM} from "../../shared/IS_SERVER_PLATFORM.token";
import {ApiService} from "../../shared/services/api.service";
import {catchError} from "rxjs";
import {environment} from "../../../environments/environment";

declare var myFatoorahAP: any;
declare var myFatoorah: any;

@Component({
  selector: 'app-pay',
  templateUrl: 'pay.page.html',
  styleUrls: ['pay.page.scss'],
  standalone: true,
  imports: [
    IonicModule,
    NgIf,

  ],
})
export class PayPage implements AfterViewInit, OnInit {
  @Input('uuid') transactionUUID: string = ''
  amount: number = 0
  showPaymentsSpinner: boolean = true
  creditCardProcessing: boolean = false
  myFatoorahAppleSessionId = ''
  myFatoorahCreditCardSessionId = ''

  constructor(@Inject(DOCUMENT) private document: Document,
              @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
              private apiService: ApiService,
              private navCtrl: NavController,
              @Inject('Window') private window: Window,
  ) {
  }

  ngOnInit(): void {
    if (!this.isServer) {
      this.browserSetup()
    }
  }

  ngAfterViewInit(): void {
    // throw new Error('Method not implemented.');
    if (this.transactionUUID) {
      this.apiService
        .getTransaction(this.transactionUUID)
        .pipe(
          catchError(({error}) => {
            this.navCtrl.navigateForward('/pay/checkout-failure').then()
            throw new Error(error)
          })
        )
        .subscribe((res) => {
          this.amount = res['amount']
          this.myFatoorahAppleSessionId = res['apple_session_id'];
          this.myFatoorahCreditCardSessionId = res['session_id'];
          this.initiateApplePayPayment();
          this.initiateCreditCardPayment();
          this.showPaymentsSpinner = false;
        })
    }
  }


  browserSetup() {
  }

  private initiateApplePayPayment() {
    this.showPaymentsSpinner = true;

    const config = {
      sessionId: this.myFatoorahAppleSessionId, // Here you add the "SessionId" you receive from InitiateSession Endpoint.
      countryCode: environment.myfatoorah.countryCode,
      currencyCode: environment.myfatoorah.currencyCode,
      amount: this.amount,
      cardViewId: "card-applepay",
      Language: "ar",
      callback: ((response: any) => {
        this.showPaymentsSpinner = true;
        this.apiService.getInvoiceFromPaymentSessionId(this.transactionUUID, response.sessionId, {
          popup_pay: true,
        }).subscribe((res: any) => {
          if (res['success'] === true)
            this.navCtrl.navigateForward('/pay/checkout-success').then()
          else
            this.navCtrl.navigateForward('/pay/checkout-failure').then()
          this.showPaymentsSpinner = false;
        });
      }),
      sessionStarted: ((sessionStarted: any) => {
        this.showPaymentsSpinner = true;

      }),
      sessionCanceled: ((sessionCanceled: any) => {
        // this.showPaymentsSpinner = false;
      }),
    };
    myFatoorahAP.init(config);
  }

  private initiateCreditCardPayment() {
    this.showPaymentsSpinner = true;
    const config = {
      sessionId: this.myFatoorahCreditCardSessionId, // Here you add the "SessionId" you receive from InitiateSession Endpoint.
      countryCode: environment.myfatoorah.countryCode,
      currencyCode: environment.myfatoorah.currencyCode,
      amount: this.amount,
      cardViewId: "credit-card",
      Language: "ar",
      callback: ((response: any) => {
      }),
      sessionStarted: ((sessionStarted: any) => {
        this.showPaymentsSpinner = true;
      }),
      sessionCanceled: ((sessionCanceled: any) => {
        // this.showPaymentsSpinner = false;
      }),
    };

    myFatoorah.init(config);
  }

  payWithCreditCard() {
    this.creditCardProcessing = true;
    (this.window as any)
      .myFatoorah
      .submit()
      .then((response: { sessionId: string, cardBrand: string }) => {
        // In case of success
        // Here you need to pass session id to you backend here
        const sessionId = response.sessionId;
        const cardBrand = response.cardBrand; //cardBrand will be one of the following values: Master, Visa, Mada, Amex
        this.apiService.getInvoiceFromPaymentSessionId(this.transactionUUID, sessionId, {
          popup_pay: true,
        }).subscribe((res: any) => {
          if (res['success'] === true)
            window.location = res['invoice_url'];
          else
            this.navCtrl.navigateForward('/pay/checkout-failure').then()
        });
        this.creditCardProcessing = false;
      })
      .catch((error: any) => {
        this.creditCardProcessing = false;
      });
  }
}
