<ion-content class="ion-padding">
  <ion-spinner *ngIf="showPaymentsSpinner"
               style="margin: auto; width: 100%;height: 40px;"></ion-spinner>
  <div id="card-applepay" class="ion-margin-top" [hidden]="showPaymentsSpinner"></div>
  <div class="altwijry-delimiter" *ngIf="!creditCardProcessing"></div>
  <div id="credit-card" [hidden]="showPaymentsSpinner"></div>

  <ion-button *ngIf="myFatoorahCreditCardSessionId"
              color="secondary"
              expand="block"
              class="ion-margin-top"
              [hidden]="showPaymentsSpinner"
              (click)="payWithCreditCard()">
    <ion-spinner *ngIf="creditCardProcessing"></ion-spinner>
    <span *ngIf="!creditCardProcessing" [hidden]="showPaymentsSpinner">ادفع</span>
  </ion-button>
</ion-content>
