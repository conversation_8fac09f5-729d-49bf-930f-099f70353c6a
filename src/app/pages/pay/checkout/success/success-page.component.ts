import {Component, AfterViewInit, OnInit} from '@angular/core';
import {Async<PERSON>ipe, NgIf} from "@angular/common";
import {IonicModule} from "@ionic/angular";
import {RouterModule} from "@angular/router";
import {AppDataService} from "../../../../shared/services/app-data.service";

@Component({
  selector: 'app-pay-checkout-success',
  templateUrl: 'success-page.component.html',
  styleUrls: ['success-page.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    NgIf,
    AsyncPipe,
    RouterModule,
  ],
})
export class PayCheckoutSuccessPage implements OnInit, AfterViewInit {
  showCloseMessage = false;

  constructor(public appDataService: AppDataService) {

  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.showCloseMessage = true;
    }, 2000)
  }

  ngOnInit(): void {
    window.close()
  }

  protected readonly window = window;
}
