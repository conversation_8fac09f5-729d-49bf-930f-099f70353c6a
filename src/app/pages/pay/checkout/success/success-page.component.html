<ion-content>
  <ion-grid [fixed]="true">
    <ion-row>
      <ion-col class="ion-text-center">
        <ion-icon name="altwijry-checked" color="success" class="success-icon"></ion-icon>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col class="ion-text-center">
        <ion-text>
          <h3>تمت العملية بنجاح</h3>
        </ion-text>
      </ion-col>
      <ion-col class="ion-text-center" size="12">
        <ion-spinner style="margin: auto; width: 100%;height: 40px;"></ion-spinner>
      </ion-col>
      <ion-col class="ion-text-center" size="12">
        <ion-text color="warning">
          <h3>جاري تحويلك لتطبيق {{ (appDataService.data$ | async)!['name'] }} ...</h3>
        </ion-text>
      </ion-col>
      <ion-col class="ion-text-center" size="12" *ngIf="showCloseMessage">
        <!--<span>إذا لم يتم تحويلك للتطبيق، </span>
        <ion-text color="primary" class="close-link" (click)="window.close()">اضغط هنا</ion-text>-->
        <span>إذا لم يتم تحويلك للتطبيق يمكنك إغلاق الصفحة</span>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>

