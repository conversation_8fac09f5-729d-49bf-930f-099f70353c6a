<app-header [hideProfileIcon]="false" [showBackButton]="true" backTo="/home"></app-header>
<ion-content>
  <ion-grid [fixed]="true">
    <app-card title="" [showExpandButton]="false">
      <ng-container *ngIf="invoice$ | async as transaction; else spinner">
        <ion-row>
          <ion-col class="ion-text-center">
            <ion-icon name="altwijry-checked" color="success" class="success-icon"></ion-icon>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col class="ion-text-center">
            <ion-text>
              <h3>إيصال العملية رقم {{ transaction.slug }}</h3>
              <a style="text-decoration: unset;" href="{{transaction.pdf_url}}"
                 target="_blank" *ngIf="transaction.pdf_url"
                 download="{{transaction.product.title}}"><strong>حمّل</strong></a>
            </ion-text>
          </ion-col>
        </ion-row>
      </ng-container>
      <ng-template #spinner>
        <div class="ion-flex ion-justify-content-center">
          <ion-spinner size="large" name="crescent" style="width: 70px; height: 70px"></ion-spinner>
        </div>
      </ng-template>
    </app-card>
  </ion-grid>
</ion-content>

