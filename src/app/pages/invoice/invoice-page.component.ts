import {ChangeDetector<PERSON>ef, Component, Inject, OnInit, TransferState} from '@angular/core';
import {AsyncPipe, DatePipe, DOCUMENT, <PERSON>sonPipe, <PERSON>ForOf, <PERSON><PERSON>f, NgTemplateOutlet} from "@angular/common";
import {IonicModule} from "@ionic/angular";

import {HttpClient, HttpClientModule} from "@angular/common/http";

import {ActivatedRoute, RouterModule} from "@angular/router";
import {Observable, take} from "rxjs";
import {HeaderComponent} from "../../layout/header/header.component";
import {IfIsBrowserDirective} from "../../shared/IfIsBrowser.directive";
import {CardComponent} from "../../shared/components/card/card.component";
import {IS_SERVER_PLATFORM} from "../../shared/IS_SERVER_PLATFORM.token";
import {ApiService} from "../../shared/services/api.service";
import {environment} from "../../../environments/environment";
import {Transaction} from "../../shared/models";


@Component({
  selector: 'app-invoice',
  templateUrl: 'invoice-page.component.html',
  styleUrls: ['invoice-page.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    IfIsBrowserDirective,
    NgIf,
    CardComponent,
    NgTemplateOutlet,
    NgForOf,
    AsyncPipe,
    JsonPipe,
    RouterModule,
    DatePipe
  ],
})
export class InvoicePage implements OnInit {
  invoiceSlug = '';
  invoice$!: Observable<Transaction>
  protected readonly environment = environment;

  constructor(
    @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
    private apiService: ApiService,
    private transferState: TransferState,
    private activatedRoute: ActivatedRoute,
  ) {
  }

  ngOnInit(): void {
    if (this.isServer) {
      console.log('is server')
    }

    if (!this.isServer) {
      this.browserSetup();
    }
  }

  browserSetup() {
    // get query params using activated route
    this.activatedRoute.params.pipe(take(1)).subscribe(params => {
      this.invoiceSlug = params['invoiceSlug'];
      this.invoice$ = this.apiService.getInvoiceDetails(this.invoiceSlug);
    });
  }
}
