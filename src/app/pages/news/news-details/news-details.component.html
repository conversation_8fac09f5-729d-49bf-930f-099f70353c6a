<app-header [hideProfileIcon]="false" [showBackButton]="true" backTo="/news"></app-header>
<ion-content>
  <ion-grid [fixed]="true" class="ion-align-items-center">
    <ion-card mode="ios" style="box-shadow: unset; border:solid 1px var(--ion-color-secondary, #113665); max-width: 370px; margin: auto">
      <ion-card-header>
        <ion-card-title>{{ news?.title }}</ion-card-title>
        <ion-text color="primary">
          <h6>{{ news?.created_at| date: 'longDate' }}</h6>
        </ion-text>
      </ion-card-header>
      <ion-card-content>
        <ion-spinner *ngIf="!news" style="margin: auto; width: 100%;height: 40px;"></ion-spinner>
        <ion-row class="ion-justify-content-center ion-align-items-center"
                 *ngFor="let oneBlock of news?.content?.blocks; let i = index">
          <ion-col class="ion-justify-content-center ion-align-items-center">
            <pre *ngIf="oneBlock.type === 'code'"> {{ oneBlock.data.code }}</pre>
            <ion-item-divider *ngIf="oneBlock.type === 'delimiter'"></ion-item-divider>
            <div *ngIf="oneBlock.type !== 'code'" style="display: inline-block"
                 [innerHTML]="oneBlock | renderBlock"></div>
          </ion-col>
        </ion-row>
      </ion-card-content>
    </ion-card>
  </ion-grid>
</ion-content>

