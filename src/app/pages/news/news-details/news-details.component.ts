import { Component, Input, OnInit, TransferState } from '@angular/core';
import {HeaderComponent} from "../../../layout/header/header.component";
import {IonicModule} from "@ionic/angular";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {HttpClient, HttpClientModule} from "@angular/common/http";
import {APIResponse, NEWS} from "../../../shared/models";
import {ApiService} from "../../../shared/services/api.service";

import {take, tap} from "rxjs";
import {Router} from '@angular/router';
import {DatePipe, NgForOf, NgIf} from "@angular/common";
import {environment} from "../../../../environments/environment";
import {RenderBlockPipe} from "../../../shared/pipes/render-block.pipe";

@Component({
  selector: 'app-news-details',
  templateUrl: './news-details.component.html',
  styleUrls: ['./news-details.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    IfIsBrowserDirective,
    NgForOf,
    NgIf,
    RenderBlockPipe,
    DatePipe
  ]
})
export class NewsDetailsComponent implements OnInit {
  @Input() news?: NEWS;
  newsId?: number;
  env = environment;

  constructor(private apiService: ApiService,
              private http: HttpClient,
              private transferState: TransferState,
              private router: Router,) {
    //get id params from url parameters
    this.newsId = Number(this.router.url.split('/')[2]);

    if (this.newsId)
      this.getNewsById(this.newsId).subscribe();
    if (!this.news) {

    }
  }

  getNewsById(id: number) {
    return this.apiService.checkAndGetData(
      this.apiService.getDynamicStateKey(`news-details-${this.newsId}`),
      this.apiService.getNewsById(this.newsId ?? 0),
      []
    ).pipe(take(1), tap((data: NEWS) => {
      this.news = {...data, content: JSON.parse('' + data.content)};
    }))
  }

  ngOnInit() {
  }

}
