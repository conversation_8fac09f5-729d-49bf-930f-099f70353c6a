<app-header [hideProfileIcon]="true" [showBackButton]="true" backTo="home"></app-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large" class="ion-margin-vertical" style="">أهم الأخبار</ion-title>
    </ion-toolbar>
  </ion-header>
  <ion-list [inset]="true">
    <ng-container *ngTemplateOutlet="news"></ng-container>
  </ion-list>
</ion-content>

<ng-template #news>
  <ng-container *ngIf="news$ | async as news; ">
    <ng-container *ngIf="news.length === 0">
      <ng-container *ngTemplateOutlet="loading"></ng-container>
    </ng-container>
    <ion-item *ngFor="let oneNews of news; let i = index" [detail]="true" lines="full" [button]="true">
      <ion-avatar slot="start" [routerLink]="'/news/'+ oneNews.id">
        <ion-img
          [src]="((appDataService.data$ | async)!['iconPath']) || '/assets/icon/logo.svg'"></ion-img>
      </ion-avatar>
      <ion-label [routerLink]="'/news/'+ oneNews.id">
        <h4 class="" style="line-height: 1.7 !important;">{{ oneNews.title }}</h4>
        <p class="">{{ oneNews.created_at | date:'longDate' }}</p>
      </ion-label>
    </ion-item>
  </ng-container>
</ng-template>

<ng-template #loading>
  <ion-item *ngFor="let oneNews of [].constructor(5); let i = index">
    <ion-avatar slot="start">
      <ion-img
        [src]="((appDataService.data$ | async)!['iconPath']) || '/assets/icon/logo.svg'"></ion-img>
    </ion-avatar>
    <ion-label>
      <ion-skeleton-text style="width: 90%; height: 25px; margin-bottom: 15px;"></ion-skeleton-text>
      <ion-skeleton-text style="width: 40%; height: 18px; margin-top: 10px;"></ion-skeleton-text>
    </ion-label>
  </ion-item>
</ng-template>
