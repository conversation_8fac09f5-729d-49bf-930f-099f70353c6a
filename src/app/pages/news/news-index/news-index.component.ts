import {ChangeDetector<PERSON><PERSON>, Component, Inject, OnInit} from '@angular/core';
import {As<PERSON><PERSON><PERSON><PERSON>, DatePipe, DOCUMENT, JsonPipe, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgTemplateOutlet} from "@angular/common";
import {InfiniteScrollCustomEvent, IonicModule, Platform} from "@ionic/angular";
import {HeaderComponent} from 'src/app/layout/header/header.component';
import {BehaviorSubject, take, tap} from "rxjs";
import {NEWS} from "../../../shared/models";
import {ApiService} from "../../../shared/services/api.service";
import {RouterLink} from "@angular/router";
import {AppDataService} from "../../../shared/services/app-data.service";

@Component({
  selector: 'app-news-index',
  templateUrl: 'news-index.component.html',
  styleUrls: ['news-index.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    Header<PERSON>omponent,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>plateOutlet,
    <PERSON><PERSON>or<PERSON><PERSON>,
    <PERSON>ync<PERSON><PERSON><PERSON>,
    JsonPipe,
    DatePipe,
    RouterLink,
  ],
})
export class NewsIndexComponent implements OnInit {
  private _news$ = new BehaviorSubject<NEWS[]>([]);
  news$ = this._news$.asObservable();
  newsPage = 0;
  newsNextPageUrl = '';

  constructor(public platform: Platform,
              private apiService: ApiService,
              private cdr: ChangeDetectorRef,
              public appDataService: AppDataService,
  ) {

  }

  ngOnInit(): void {
    this.getNews().subscribe()
  }

  getNews() {
    this.newsPage++;
    return this.apiService.getNews(this.newsNextPageUrl).pipe(tap((data: any) => {
      this.newsNextPageUrl = data.pagination?.next_url;
      this._news$.next([...this._news$.value, ...data.data] ?? []);
      this.cdr.detectChanges();
    }));
  }
}
