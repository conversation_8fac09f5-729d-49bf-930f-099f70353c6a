import {Component, Inject, OnInit} from '@angular/core';
import {AsyncPipe, DOCUMENT} from "@angular/common";
import {IonicModule} from "@ionic/angular";
import {CheckboxCustomEvent} from '@ionic/angular';
import {HeaderComponent} from "../../layout/header/header.component";
import {IfIsBrowserDirective} from "../../shared/IfIsBrowser.directive";
import {IS_SERVER_PLATFORM} from "../../shared/IS_SERVER_PLATFORM.token";
import {AppDataService} from "../../shared/services/app-data.service";

@Component({
  selector: 'app-soon',
  templateUrl: 'soon.page.html',
  styleUrls: ['soon.page.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    IfIsBrowserDirective,
    AsyncPipe
  ],
})
export class SoonPage implements OnInit {
// Typically referenced to your ion-router-outlet
  canDismiss = false;

  constructor(@Inject(DOCUMENT) private document: Document,
              protected appDataService: AppDataService,
              @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
  ) {
  }

  ngOnInit(): void {
    if (!this.isServer) {
      this.browserSetup();
    }
  }

  browserSetup() {
  }
}
