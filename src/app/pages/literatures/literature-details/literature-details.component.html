<app-header [hideProfileIcon]="false" [showBackButton]="true" backTo="/literatures"></app-header>
<ion-content>
  <ion-spinner *ngIf="!literature" style="margin: auto; width: 100%;height: 40px;"></ion-spinner>
  <ion-grid [fixed]="true" class="ion-align-items-center" *ngIf="literature">
    <ion-card mode="ios" style="box-shadow: unset; border:solid 1px var(--ion-color-secondary, #113665); max-width: 370px; margin: auto">
      <ion-card-header>
        <ion-img [src]="literature.cover_image_url || ((appDataService.data$ | async)!['iconPath']) || '/assets/icon/logo.svg'"
                 style="margin-top: 10px;"
                 (ionError)="failCallbackImage($event)"></ion-img>
      </ion-card-header>
      <ion-card-content>
        <ion-row class="ion-justify-content-center ion-align-items-center">
          <ion-col class="ion-justify-content-center ion-align-items-center">
            <ion-item>
              <ion-label>العنوان</ion-label>
              <ion-text color="primary">{{literature.title}}</ion-text>
            </ion-item>
            <ion-item>
              <ion-label>التصنيف</ion-label>
              <ion-text color="primary">{{literature.category}}</ion-text>
            </ion-item>
            <ion-item>
              <ion-label>المؤلف</ion-label>
              <ion-text color="primary">{{literature.user}}</ion-text>
            </ion-item>
            <ion-item *ngIf="literature.brief">
              <ion-text color="primary" [innerHTML]="(literature.brief || '') | nl2br"></ion-text>
            </ion-item>
            <ion-button class="buy-button ion-no-margin ion-margin-top" style="width: 100%"
                        *ngIf="literature" [href]="literature.file_url"
                        [download]="literature.title + '.pdf'">
              <ion-icon name="document-outline"></ion-icon>
              &nbsp;
              تحميل الملف
            </ion-button>
          </ion-col>
        </ion-row>
      </ion-card-content>
    </ion-card>
  </ion-grid>
</ion-content>

