import { Component, inject, Input, OnInit, TransferState } from '@angular/core';
import {HeaderComponent} from "../../../layout/header/header.component";
import {IonicModule} from "@ionic/angular";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {HttpClient, HttpClientModule} from "@angular/common/http";
import {Literature, NEWS} from "../../../shared/models";
import {ApiService} from "../../../shared/services/api.service";

import {take, tap} from "rxjs";
import {ActivatedRoute, Router} from '@angular/router';
import {AsyncPipe, DatePipe, DecimalPipe, NgForOf, NgIf} from "@angular/common";
import {environment} from "../../../../environments/environment";
import {RenderBlockPipe} from "../../../shared/pipes/render-block.pipe";
import {CardComponent} from "../../../shared/components/card/card.component";
import {FormatDistancePipe} from "../../../shared/pipes/format-distance.pipe";
import {OneCardComponent} from "../../eid-cards/one-card/one-card.component";
import {nl2brPipe} from "../../../nl2br.pipe";
import {AppDataService} from "../../../shared/services/app-data.service";

@Component({
  selector: 'app-news-details',
  templateUrl: './literature-details.component.html',
  styleUrls: ['./literature-details.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    IfIsBrowserDirective,
    NgForOf,
    NgIf,
    RenderBlockPipe,
    DatePipe,
    CardComponent,
    FormatDistancePipe,
    OneCardComponent,
    DecimalPipe,
    nl2brPipe,
    AsyncPipe
  ]
})
export class LiteratureDetailsComponent implements OnInit {
  @Input() literature?: Literature;
  literatureId?: number;
  env = environment;

  constructor(private apiService: ApiService,
              private http: HttpClient,
              private transferState: TransferState,
              private activatedRoute: ActivatedRoute,
              public appDataService: AppDataService,
              //private nativeHTTP: HTTP,
              //private file: File,
              private router: Router) {
    this.literatureId = Number(this.router.url.split('/')[2]);
    if (this.literatureId)
      this.getLiteratureById(this.literatureId).subscribe();
    if (!this.literature) {

    }
  }

  getLiteratureById(id: number) {
    return this.apiService.checkAndGetData(
      this.apiService.getDynamicStateKey(`literature-details-${this.literatureId}`),
      this.apiService.getLiteratureById(this.literatureId ?? 0),
      []
    ).pipe(take(1), tap((data: Literature) => {
      this.literature = data;
    }))
  }

  ngOnInit() {
  }

  failCallbackImage(event: any) {
    this.appDataService.data$.subscribe(data => {
      event.target.src = (data!['iconPath']) ||  '/assets/icon/logo.svg'
    })
  }
}
