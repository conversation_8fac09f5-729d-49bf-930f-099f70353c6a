<app-header [hideProfileIcon]="false" [showBackButton]="true"></app-header>
<ion-content>
  <ion-grid [fixed]="true">
    <ion-row>
      <ion-col size="12">
        <ng-container *ngTemplateOutlet="literatures"></ng-container>
      </ion-col>
    </ion-row>
    <ion-infinite-scroll (ionInfinite)="onIonInfinite($event)" *ngIf="literaturesNextPageUrl">
      <ion-infinite-scroll-content></ion-infinite-scroll-content>
    </ion-infinite-scroll>
  </ion-grid>
</ion-content>

<ng-template #literatures>
  <div style=" overflow-y: scroll; overflow-x: hidden;   max-width: 370px;    margin: auto;">
    <ng-container *ngIf="literatures$ | async as literatures;">
      <ion-spinner *ngIf="loading" style="margin: auto; width: 100%;height: 40px;"></ion-spinner>
      <ion-item *ngIf="!loading && literatures.length === 0">
        <ion-text color="danger">لا يوجد مؤلفات في الوقت الحالي</ion-text>
      </ion-item>

      <ion-item *ngFor="let oneLiterature of literatures; let i = index" [title]="oneLiterature.title"
                [routerLink]="'/literatures/' + oneLiterature.id"
                lines="full"
                button>
        <ion-avatar slot="start">
          <ion-img [src]="oneLiterature.cover_image_url || ((appDataService.data$ | async)!['iconPath']) || '/assets/icon/logo.svg'"
                   (ionError)="failCallbackImage($event)"></ion-img>
        </ion-avatar>
        <ion-label>
          <h4 class="" style="line-height: 1.7 !important;">{{oneLiterature.title}}
            <!--            <h6>{{oneLiterature.user}}</h6>-->
          </h4>
          <p class="">{{oneLiterature.publish_date | date:'longDate'}}</p>
        </ion-label>
      </ion-item>
    </ng-container>
  </div>
</ng-template>
