<app-header [hideProfileIcon]="true" [showBackButton]="true" backTo="settings"></app-header>
<ion-content>
  <ion-list [inset]="true">
    <ion-item *ngIf="appId">
      <ion-label>appId</ion-label>
      <ion-note>{{ appId }}</ion-note>
    </ion-item>
    <ion-item *ngIf="appName">
      <ion-label>appName</ion-label>
      <ion-note>{{ appName }}</ion-note>
    </ion-item>
    <ion-item *ngIf="appBuild">
      <ion-label>appBuild</ion-label>
      <ion-note>{{ appBuild }}</ion-note>
    </ion-item>
    <ion-item *ngIf="appVersion">
      <ion-label>appVersion</ion-label>
      <ion-note>{{ appVersion }}</ion-note>
    </ion-item>
  </ion-list>
</ion-content>
