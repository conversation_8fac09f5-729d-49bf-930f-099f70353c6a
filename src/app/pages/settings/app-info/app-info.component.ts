import {Component, OnInit} from '@angular/core';
import {<PERSON>ync<PERSON><PERSON><PERSON>, DatePipe, JsonPipe, <PERSON>For<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ng<PERSON><PERSON>plateOutlet} from "@angular/common";
import {IonicModule, Platform} from "@ionic/angular";
import {HeaderComponent} from 'src/app/layout/header/header.component';
import {RouterLink} from "@angular/router";
import {App} from '@capacitor/app';
import {GenericService} from "../../../shared/services/generic.service";

@Component({
  selector: 'app-app-info',
  templateUrl: 'app-info.component.html',
  styleUrls: ['app-info.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    NgIf,
  ],
})
export class AppInfoComponent implements OnInit {
  public appId: string | undefined;
  public appName: string | undefined;
  public appBuild: string | undefined;
  public appVersion: string | undefined;

  constructor(
    public platform: Platform,
    private genericService: GenericService,
  ) {

  }

  ngOnInit(): void {
    if (this.genericService.isCapacitorApp())
      App.getInfo().then(res => {
        this.appId = res.id
        this.appName = res.name
        this.appBuild = res.build
        this.appVersion = res.version
      })
  }
}
