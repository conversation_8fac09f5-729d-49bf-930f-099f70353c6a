<app-header [hideProfileIcon]="true" [showBackButton]="true" backTo="home"></app-header>

<ion-content color="light" [fullscreen]="true" *ngIf="featuresService.features$ | async as features">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large" style="">الإعدادات</ion-title>
    </ion-toolbar>
  </ion-header>

  <ion-list [inset]="true">
    <ion-icon *ngIf="(profileService.$user|async)?.membership as _membership"
              style="position: absolute; top: 5px; right: 5px; z-index: 100; font-size: 35px;"
              name="altwijry-membership-{{_membership.package.slug}}">
    </ion-icon>
    <ion-icon
      id="open-qr-modal"
      title="رمز الاستجابة السريع QR"
      color="secondary"
      style="position: absolute; top: 5px; left: 5px; z-index: 100; font-size: 30px; cursor: pointer;"
      name="qr-code-outline"></ion-icon>
    <input lang="ar" dir="ltr" #fileInput type="file" (change)="openCropperDialog($event)" accept="image/*" hidden>
    <ion-item [detail]="false" lines="none">
      <ion-avatar style="margin: auto;width: 140px; height: 140px;">
        <div class="AvatarContainer">
          @let fallbackURL = ('/assets/d3-images/' + ((profileService.$user|async)?.gender ?? 'male').toLowerCase() + '-icon.svg');
          @let fallbackIcon = (((profileService.$user|async)?.gender ?? 'male').toLowerCase() + '-icon');
          @if ((profileService.$user|async)?.profile_photo_url) {
            <img
              [src]="(profileService.$user|async)?.profile_photo_url ?? fallbackURL"
              [appImgFallback]="fallbackURL"
              disabled="avatarUpdating"
              width="120" height="120">
          } @else {
            <ion-icon slot="start" [name]="fallbackIcon" style="font-size: 114px;"></ion-icon>
          }
          <div style="position: absolute;"
               *ngIf="(profileService.$user|async)?.can_update_picture && (profileService.$user|async)?.gender === 'MALE' && !avatarUpdating"
               id="avatarMenu" (click)="openAvatarActionsSheet()">
            <ion-icon name="camera-outline" color="primary"></ion-icon>
          </div>
          <ion-spinner *ngIf="avatarUpdating"></ion-spinner>
        </div>
      </ion-avatar>
    </ion-item>
    <ion-item lines="none">
      <ion-label class="ion-text-wrap ion-text-center ion-no-margin">
        <ion-text color="secondary">
          <h5
            style="line-height: 1.7!important; font-weight: bolder; letter-spacing: 4px">{{ (profileService.$user|async)?.family_user_id }}</h5>
        </ion-text>
        <h4 style="line-height: 1.7!important; font-weight: bolder">{{ (profileService.$user|async)?.full_name }}</h4>
      </ion-label>
    </ion-item>
    <ion-item lines="none">
      <ion-label class="ion-text-wrap ion-text-center">
        <h5 style="line-height: 1.7!important;">{{ (profileService.$user|async)?.bio }}</h5>
      </ion-label>
    </ion-item>
    <ion-item class="ion-justify-content-center ion-align-items-center"
              *ngIf="features['AppleWalletPasses'] === true || features['AndroidWalletPasses'] === true">
      <ion-col class="ion-justify-content-center ion-align-items-center"
               *ngIf="!genericService.isAndroidApp() && features['AppleWalletPasses'] === true">
        <ion-spinner style="position: absolute;left: calc(50% - 14px);top: calc(50% - 14px);"
                     *ngIf="formLoading"></ion-spinner>
        <ion-img [src]="'/assets/images/icons/add_to_wallet_ar.svg'"
                 [style]="{width: '50%', height: '100%', margin: 'auto', opacity: formLoading ? '25%' : '100%'}"
                 (click)="downloadWalletPass()"></ion-img>
      </ion-col>
      <ion-col class="ion-justify-content-center ion-align-items-center"
               *ngIf="genericService.isIosApp() && features['AndroidWalletPasses'] === true">
        <ion-spinner style="position: absolute;left: calc(50% - 14px);top: calc(50% - 14px);"
                     *ngIf="formLoading"></ion-spinner>
        <ion-img [src]="'/assets/images/icons/add_to_gpay_ar.svg'"
                 [style]="{width: '50%', height: '100%', margin: 'auto', opacity: formLoading ? '25%' : '100%'}"
                 (click)="downloadGooglePass()"></ion-img>
      </ion-col>
    </ion-item>
  </ion-list>

  <ion-list [inset]="true">
    <ion-item [button]="true" routerLink="/me/edit">
      <ion-icon color="secondary" slot="start" name="person-circle-outline" size="large"></ion-icon>
      <ion-label>البيانات الشخصية</ion-label>
    </ion-item>

    <ion-item [button]="true" routerLink="/me/strava" *ngIf="(features['Strava'] || false)">
      <ion-icon color="secondary" slot="start" name="watch-outline" size="large"></ion-icon>
      <ion-label>سترافا</ion-label>
    </ion-item>

    <ion-item [button]="true" routerLink="/me/children">
      <ion-icon color="secondary" slot="start" name="people" size="large"></ion-icon>
      <ion-label>الأبناء</ion-label>
    </ion-item>
    <ion-item [button]="true" routerLink="/me/friends">
      <ion-icon name="people-outline" color="secondary" style="font-size: 30px"></ion-icon>
      <ion-label class="ion-margin-start">الأقارب</ion-label>
    </ion-item>
    <ion-item [button]="true" (click)="openThemeActionSheet()">
      <ion-icon name="sunny" slot="start" color="secondary"></ion-icon>
      <ion-label>ألوان التطبيق</ion-label>
    </ion-item>

    <ion-item [button]="true" *ngIf="(features['Notifications'] || false)" routerLink="/settings/notifications">
      <ion-icon color="secondary" slot="start" name="notifications-outline" size="large"></ion-icon>
      <ion-label>الإشعارات والتنبيهات</ion-label>
    </ion-item>

    <ion-item [button]="true" id="login-with-qr-ion-item" (click)="loginWithQRCode()"
              *ngIf="(features['QRLogin'] || false) && Object.keys((profileService.$user|async)?.roles ?? []).length > 0">
      <ion-icon color="primary" slot="start" name="qr-code-login" size="large"></ion-icon>
      <ion-label>تسجيل الدخول QRCode</ion-label>
    </ion-item>

    <ion-item [button]="true" routerLink="change-app-icon"
              *ngIf="genericService.isCapacitorApp() && (features['ChangeIcon'] || false)">
      <img slot="start" width="30px" height="30px" src="assets/ios-app-icons/{{appIcon}}.png">
      <ion-label>أيقونة التطبيق</ion-label>
    </ion-item>

    <ng-container *ngIf="genericService.isCapacitorApp()">
      <ion-item [button]="true" routerLink="/settings/info" *ngIf="(features['Debugger'] || false)">
        <ion-icon color="secondary" slot="start" name="cog-outline" size="large"></ion-icon>
        <ion-label>App Info</ion-label>
      </ion-item>
    </ng-container>

    <ion-item [button]="true" routerLink="/me/delete" *ngIf="(features['AccountDelete'] || false)">
      <ion-icon color="danger" slot="start" name="trash-outline" size="large"></ion-icon>
      <ion-label color="danger">حذف الحساب</ion-label>
    </ion-item>
  </ion-list>

  <ng-container *ngIf="((appDataService.data$ | async)!['links'] ?? []) as linksData">
    <ion-list [inset]="true" *ngIf="Array.isArray(linksData) && linksData.length > 0">
      <ion-item [button]="true" detail="false" target="_blank" *ngFor="let link of linksData"
                [href]="link.url" (click)="apiService.appLinkClick(link.id).subscribe()">
        <img slot="start" [src]="link.image_url" *ngIf="link.image_url" style="height: 28px;"/>
        <ion-label>{{ link.title }}</ion-label>
      </ion-item>
    </ion-list>
  </ng-container>

  <!--<ion-list [inset]="true">
    <ion-item [button]="true" detail="false" target="_blank"
              href="https://whatsapp.com/channel/0029VaDhrkVLikg6tdnPGi0I">
      <ion-icon style="fill:#008069" slot="start" name="logo-whatsapp" size="large"></ion-icon>
      <ion-label>قناة الصندوق بالواتساب</ion-label>
    </ion-item>

    <ion-item [button]="true" detail="false" target="_blank" href="https://t.altwaijri.sa/management-whatsapp">
      <ion-icon style="fill:#008069" slot="start" name="logo-whatsapp" size="large"></ion-icon>
      <ion-label>تواصل معنا</ion-label>
    </ion-item>


    <ion-item [button]="true" detail="false" target="_blank" href="https://t.altwaijri.sa/twitter-account">
      <ion-icon slot="start" name="logo-x"></ion-icon>
      <ion-label>منصة إكس</ion-label>
    </ion-item>

    <ion-item [button]="true" detail="false" target="_blank" href="https://t.altwaijri.sa/snapchat-account">
      <ion-icon slot="start" name="logo-snapchat" size="large"></ion-icon>
      <ion-label>سناب شات</ion-label>
    </ion-item>

    <ion-item [button]="true" detail="false" target="_blank" href="https://t.altwaijri.sa/youtube-account">
      <ion-icon slot="start" name="logo-youtube" size="large" color="danger"></ion-icon>
      <ion-label>يوتيوب</ion-label>
    </ion-item>

    <ion-item [button]="true" detail="false" target="_blank" href="https://t.altwaijri.sa/telegram-account">
      <ion-icon slot="start" name="logo-telegram" size="large"></ion-icon>
      <ion-label>تلقرام</ion-label>
    </ion-item>
  </ion-list>-->

  <ion-row>
    <ion-col class="ion-text-center">
      <ion-button color="danger" fill="clear" (click)="logout()" [disabled]="loadingLogout">
        <ion-spinner name="crescent" *ngIf="loadingLogout"></ion-spinner>
        <ng-container *ngIf="!loadingLogout">
          <ion-icon name="log-out" slot="start"></ion-icon>
          تسجيل الخروج
        </ng-container>
      </ion-button>
    </ion-col>
  </ion-row>

  <ion-note color="medium" *ngIf="!genericService.isCapacitorApp()"
            class="ion-text-center">هذه نسخة تجريبية وليست نهائية
  </ion-note>

  <ion-modal #modal trigger="open-qr-modal" [canDismiss]="true" [initialBreakpoint]="0.5" [breakpoints]="[0, 0.7, 1]">
    <ng-template>
      <ion-header>
        <ion-toolbar>
          <ion-title>رمز الاستجابة السريعة</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="modal.dismiss()">إغلاق</ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content class="ion-padding">
        <ion-row>
          <ion-col class="ion-text-center">
            <div class="qrcodeImage">
              <div class="ion-text-center">
                <qrcode
                  [qrdata]="'User|' + (profileService.$user|async)?.family_user_id"
                  [allowEmptyString]="true"
                  [colorDark]="'#000000ff'"
                  [colorLight]="'#ffffffff'"
                  [elementType]="'canvas'"
                  [errorCorrectionLevel]="'M'"
                  [margin]="1"
                  [scale]="1"
                  [width]="240"
                ></qrcode>
              </div>
            </div>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col class="ion-text-center">
            <ion-text color="primary">
              <h1>
                {{ (profileService.$user|async)?.full_name }}
              </h1>
            </ion-text>
            <ion-text color="secondary">
              <h2>
                {{ (profileService.$user|async)?.family_user_id }}
              </h2>
            </ion-text>
          </ion-col>
        </ion-row>
      </ion-content>
    </ng-template>
  </ion-modal>

</ion-content>
