import {ChangeDete<PERSON><PERSON><PERSON>, <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild} from '@angular/core';
import {
  <PERSON>ync<PERSON><PERSON><PERSON>,
  DatePipe,
  DOCUMENT,
  JsonPipe,
  NgForOf,
  NgI<PERSON>,
  NgOptimizedImage,
  NgTemplateOutlet
} from "@angular/common";
import {
  ActionSheetController,
  IonicModule,
  ModalController,
  NavController,
  Platform,
  PopoverController,
  ToastController
} from "@ionic/angular";
import {Capacitor} from '@capacitor/core';
import {HeaderComponent} from 'src/app/layout/header/header.component';
import {BehaviorSubject, catchError, finalize, map, throwError} from "rxjs";
import {ApiService} from "../../../shared/services/api.service";
import {RouterLink} from "@angular/router";
import {ImgFallbackDirective} from "../../../img-fallback.directive";
import {ProfileService} from "../../../shared/services/profile.service";
import {ProfileImageUpload} from "../../profile/profile-image-upload";
import {IS_SERVER_PLATFORM} from "../../../shared/IS_SERVER_PLATFORM.token";
import {FeaturesService} from "../../../shared/services/features.service";
import {StorageService} from "../../../shared/services/storage.service";
import {AppIconService} from "../../../shared/services/app-icon.service";
import {GenericService} from "../../../shared/services/generic.service";
import {CapacitorPassToWallet} from 'capacitor-pass-to-wallet';
import {QRCodeModule} from 'angularx-qrcode';
import {QrCodeScannerComponent} from "../../../shared/components/qr-code-scanner/qr-code-scanner.component";
import {AppDataService} from "../../../shared/services/app-data.service";
import {AppLauncher} from "@capacitor/app-launcher";
import {ThemeService} from "../../../shared/services/theme.service";

@Component({
  selector: 'app-settings',
  templateUrl: 'settings.component.html',
  styleUrls: ['settings.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    NgIf,
    NgForOf,
    AsyncPipe,
    RouterLink,
    ImgFallbackDirective,
    QRCodeModule,
  ],
})
export class SettingsComponent extends ProfileImageUpload implements OnInit, OnDestroy {
  @ViewChild('fileInput', {static: false}) fileInput!: ElementRef;
  appIcon = 'goldGold';
  destroyed$ = new BehaviorSubject<boolean>(false);
  loadingLogout = false;
  formLoading = false;
// Typically referenced to your ion-router-outlet

  rolesCount$ = this.profileService.$user.pipe(
    map(user => user?.roles ? Object.keys(user.roles as object).length : 0),
    // Add a default value to prevent null
    map(count => count ?? 0)
  );

  constructor(@Inject(DOCUMENT) protected document: Document,
              @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
              private navCtrl: NavController,
              private cdr: ChangeDetectorRef,
              public featuresService: FeaturesService,
              public genericService: GenericService,
              _apiService: ApiService,
              _storageService: StorageService,
              _profileService: ProfileService,
              _modalCtrl: ModalController,
              _actionSheetCtrl: ActionSheetController,
              public platform: Platform,
              public toastController: ToastController,
              private appIconService: AppIconService,
              public appDataService: AppDataService,
              private themeService: ThemeService,
  ) {
    super(_apiService, _storageService, _profileService, _modalCtrl, _actionSheetCtrl);
    this.apiService.me().subscribe((res) => {
      this.profileService.setUser(res);
    });
    this.appIconService.appIconChanged$.pipe()
      .subscribe((appIcon) => {
        this.appIcon = appIcon;
        // todo fix fired exception
        //this.cdr.detectChanges();
      });
  }

  ngOnInit(): void {
  }

  override getFileInput(): ElementRef<any> {
    return this.fileInput;
  }

  ngOnDestroy(): void {
    this.destroyed$.next(true);
    this.destroyed$.complete();
  }

  logout() {
    this.loadingLogout = true;
    this.apiService.logout().pipe(
      finalize(() => {
        this.loadingLogout = false;
        this.storageService.removeItem('access_token');
        this.storageService.removeItem('expires_at');
        this.storageService.removeItem('user');
        this.profileService.removeUser();
        this.featuresService.loadFeatures();
        this.navCtrl.navigateRoot('/home').then();
      }),
      catchError((err) => {
        return err;
      })
    ).subscribe((res) => {
    })
  }

  downloadWalletPass() {
    this.formLoading = true
    this.apiService.getUserAppleWalletPass()
      .pipe(finalize(() => this.formLoading = false))
      .subscribe(async res => {
        if (this.genericService.isCapacitorApp() && Capacitor.getPlatform() === 'ios') {
          if (res.body) {
            let base64 = await this.genericService.convertBlobToBase64(res.body) as string;
            await CapacitorPassToWallet.addToWallet({base64})
          }
        } else {
          let filename = res.headers.get('Content-Disposition')?.split(';')[1].split('=')[1].replace(/\"/g, '')
            || (Date.now() + ".pkpass")
          if (res.body) {
            let PassUrl = window.URL.createObjectURL(res.body);
            let PASSLink = document.createElement('a');
            PASSLink.href = PassUrl;
            PASSLink.download = filename;
            PASSLink.click();
          }
        }
      });
  }

  downloadGooglePass() {
    this.formLoading = true
    this.apiService.getUserGoogleWalletPass()
      .pipe(finalize(() => this.formLoading = false))
      .subscribe((res: any) => {
        AppLauncher.openUrl({url: res.saveUrl});
      });
  }

  async loginWithQRCode() {
    const modal = await this.modalCtrl.create({
      component: QrCodeScannerComponent,
    });
    await modal.present();
    const {data, role} = await modal.onWillDismiss();
    if (data && 'qrCode' in data && data['qrCode'])
      this.apiService.qrcodeLogin(data['qrCode'])
        .pipe(catchError((err) => {
          this.toastController.create({
            message: 'خطأ !.',
            duration: 2000,
            color: 'danger'
          }).then(toast => toast.present());
          return throwError(err)
        }))
        .subscribe(res => {
          this.toastController.create({
            message: 'تم تسجيل الدخول بنجاح .',
            duration: 2000,
            color: 'success'
          }).then(toast => toast.present());
        });
  }

  protected readonly Object = Object;
  protected readonly Array = Array;

  setTheme(mode: 'system' | 'light' | 'dark') {
    this.themeService.setTheme(mode);
  }

  async openThemeActionSheet() {
    const actionSheet = await this.actionSheetCtrl.create({
      header: 'اختر الوضع',
      buttons: [
        {
          text: 'الوضع الفاتح',
          icon: 'sunny',
          handler: () => {
            this.setTheme('light');
          }
        },
        {
          text: 'الوضع الداكن',
          icon: 'moon',
          handler: () => {
            this.setTheme('dark');
          }
        },
        {
          text: 'وضع النظام',
          icon: 'contrast',
          handler: () => {
            this.setTheme('system');
          }
        },
        {
          text: 'إلغاء',
          role: 'cancel'
        }
      ]
    });

    await actionSheet.present();
  }
}
