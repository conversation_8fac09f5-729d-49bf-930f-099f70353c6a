:host {
  max-width: var(--page-width);
  margin: auto;
}

ion-note {
  display: block;
}


.AvatarContainer {
  height: 120px;
  width: 120px;
  border-radius: 50%;
  border-style: solid;
  border-color: #FFFFFF;
  box-shadow: 0 0 8px 2px #B8B8B8;
  position: relative;
  margin: 10px auto;

  img {
    height: 100%;
    width: 100%;
    max-width: unset;
    border-radius: 50%;
  }

  ion-spinner {
    position: absolute;
    top: calc(50% - 15px);
    right: calc(50% - 15px);
    height: 30px;
    width: 30px;
  }

  div {
    position: absolute;
    top: 10px;
    right: -8px;
    border-radius: 50%;
    height: 30px;
    width: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--ion-color-light);
    box-shadow: 0 0 8px 1px #B8B8B8;
    cursor: pointer;
    font-size: 20px !important;
  }
}
