import {Component, OnInit} from '@angular/core';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, DatePipe, JsonPipe, NgFor<PERSON><PERSON>, Ng<PERSON><PERSON>, NgTemplateOutlet} from "@angular/common";
import {IonicModule, Platform} from "@ionic/angular";
import {HeaderComponent} from 'src/app/layout/header/header.component';
import {RouterLink} from "@angular/router";
import {App} from '@capacitor/app';

@Component({
  selector: 'app-notifications-settings',
  templateUrl: 'notifications-settings.component.html',
  styleUrls: ['notifications-settings.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    NgIf,
    NgForOf,
  ],
})
export class NotificationsSettingsComponent implements OnInit {
notificationsChannels= [
  {
    id: 1,
    name: 'البرامج التنموية',
    channels: ['sms', 'whatsapp', 'app-notification']
  },
  {
    id: 2,
    name: 'أخبار الوفيات',
    channels: ['sms', 'whatsapp', 'app-notification']
  },
  {
    id: 3,
    name: 'أخبار التعيينات والترقيات',
    channels: ['sms', 'whatsapp', 'app-notification']
  },
  {
    id: 4,
    name: 'أخبار التبرعات والأوقاف',
    channels: ['sms', 'whatsapp', 'app-notification']
  },
]

  constructor() {
  }

  ngOnInit(): void {
  }
}
