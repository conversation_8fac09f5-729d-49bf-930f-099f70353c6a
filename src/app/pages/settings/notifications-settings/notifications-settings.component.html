<app-header [hideProfileIcon]="true" [showBackButton]="true" backTo="settings"></app-header>
<ion-content>
  <ion-header>
    <ion-toolbar>
      <ion-title size="meduim">الإشعارات</ion-title>
    </ion-toolbar>
  </ion-header>
  <ion-accordion-group expand="inset" value="1">
    <ion-accordion [value]="oneNotificationChannel.id" *ngFor="let oneNotificationChannel of notificationsChannels">
      <ion-item slot="header" color="secondary">
        <ion-label>{{ oneNotificationChannel.name }}</ion-label>
      </ion-item>
      <ion-list slot="content">
        <ion-item lines="none" *ngFor="let oneChannel of oneNotificationChannel.channels">
          <ion-label *ngIf="oneChannel === 'app-notification'">إشعارات التطبيق</ion-label>
          <ion-label *ngIf="oneChannel === 'whatsapp'">واتساب</ion-label>
          <ion-label *ngIf="oneChannel === 'sms'">رسالة نصية</ion-label>
          <ion-toggle slot="end"></ion-toggle>
        </ion-item>
      </ion-list>
    </ion-accordion>

  </ion-accordion-group>

</ion-content>
