import {ChangeDetectorRef, Component, Inject, OnInit} from '@angular/core';
import {As<PERSON><PERSON><PERSON><PERSON>, DatePipe, DOCUMENT, JsonPipe, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgTemplateOutlet} from "@angular/common";
import {InfiniteScrollCustomEvent, IonicModule, Platform} from "@ionic/angular";
import {HeaderComponent} from 'src/app/layout/header/header.component';
import {ApiService} from "../../../shared/services/api.service";
import {RouterLink} from "@angular/router";
import {AppIconService} from "../../../shared/services/app-icon.service";

@Component({
  selector: 'app-change-app-icon',
  templateUrl: 'change-app-icon.component.html',
  styleUrls: ['change-app-icon.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    NgIf,
    NgTemplateOutlet,
    <PERSON>ForOf,
    Async<PERSON><PERSON><PERSON>,
    <PERSON>son<PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    RouterLink,
  ],
})
export class ChangeAppIconComponent implements OnInit {
  appIcons: { slug: string, text: string, selected: boolean }[] = [];

  constructor(public platform: Platform,
              private apiService: ApiService,
              private cdr: ChangeDetectorRef,
              private appIconService: AppIconService,
  ) {
    this.appIconService.appIconChanged$.subscribe((appIcon) => {
      this.appIcons = this.appIconService.appIcons;
    });
  }

  ngOnInit(): void {
    //
  }


  setAppIcon(oneIcon: { text: string; slug: string; selected: boolean }) {
    this.appIconService.setIconSelected(oneIcon.slug)
  }

  resetAppIcon() {
    this.appIconService.setIconSelected(this.appIconService.defaultIcon)
  }
}
