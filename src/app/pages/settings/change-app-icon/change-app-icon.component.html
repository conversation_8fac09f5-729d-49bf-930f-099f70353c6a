<app-header [hideProfileIcon]="true" [showBackButton]="true" backTo="settings"></app-header>
<ion-content fullscreen="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large" class="ion-margin-vertical" style="">أيقونة التطبيق</ion-title>
    </ion-toolbar>
  </ion-header>

  <ion-grid fixed="true">
    <ion-row>
      <ion-col *ngFor="let oneIcon of appIcons; let i=index;" size="4" size-lg="2">
        <ion-button (click)="setAppIcon(oneIcon)" expand="block" fill="clear">
          <img alt='icon' src="/assets/ios-app-icons/{{oneIcon.slug}}.png"/>
        </ion-button>
        <ion-icon [style.visibility]="oneIcon.selected?'visible':'hidden'"
                  class="icon-size"
                  name="checkmark-circle"
                  color="primary"
                  style="width: 100%"></ion-icon>
      </ion-col>
    </ion-row>
  </ion-grid>

  <ion-button class="ion-margin" (click)="resetAppIcon()" expand="block" fill="clear">
    إستعادة الأيقونة الإفتراضية
  </ion-button>
</ion-content>



