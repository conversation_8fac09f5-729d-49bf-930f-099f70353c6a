import {Component, Input, OnInit} from '@angular/core';
import {HeaderComponent} from "../../../layout/header/header.component";
import {IonicModule} from "@ionic/angular";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {HttpClientModule} from "@angular/common/http";
import {CompanyModel} from "../../../shared/models";
import {ApiService} from "../../../shared/services/api.service";
import {take, tap} from "rxjs";
import {Router} from '@angular/router';
import {DatePipe, DecimalPipe, NgForOf, NgIf} from "@angular/common";
import {environment} from "../../../../environments/environment";
import {RenderBlockPipe} from "../../../shared/pipes/render-block.pipe";
import {CardComponent} from "../../../shared/components/card/card.component";
import {FormatDistancePipe} from "../../../shared/pipes/format-distance.pipe";
import {OneCardComponent} from "../../eid-cards/one-card/one-card.component";
import {nl2brPipe} from "../../../nl2br.pipe";
import {TruncatePipe} from "../../../truncate.pipe";
import {AppDataService} from "../../../shared/services/app-data.service";

@Component({
  selector: 'app-company-details',
  templateUrl: './company-details.component.html',
  styleUrls: ['./company-details.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    IfIsBrowserDirective,
    NgForOf,
    NgIf,
    RenderBlockPipe,
    DatePipe,
    CardComponent,
    FormatDistancePipe,
    OneCardComponent,
    DecimalPipe,
    nl2brPipe,
    TruncatePipe,
  ]
})
export class CompanyDetailsComponent implements OnInit {
  @Input() company?: CompanyModel;
  companyId?: number;
  env = environment;

  constructor(private apiService: ApiService,
              public appDataService: AppDataService,
              private router: Router) {
    this.companyId = Number(this.router.url.split('/')[2]);
    if (this.companyId)
      this.getCompanyById(this.companyId).subscribe();
    if (!this.company) {

    }
  }

  getCompanyById(id: number) {
    return this.apiService.checkAndGetData(
      this.apiService.getDynamicStateKey(`company-details-${this.companyId}`),
      this.apiService.getCompanyById(this.companyId ?? 0),
      []
    ).pipe(take(1), tap((data: CompanyModel) => {
      this.company = data;
    }))
  }

  ngOnInit() {
  }

  failCallbackImage(event: any) {
    this.appDataService.data$.subscribe(data => {
      event.target.src = (data!['iconPath']) ||  '/assets/icon/logo.svg'
    })
  }
}
