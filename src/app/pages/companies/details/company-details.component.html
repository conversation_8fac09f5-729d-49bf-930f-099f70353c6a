<app-header [hideProfileIcon]="false" [showBackButton]="true" backTo="/companies"></app-header>
<ion-content>
  <ion-spinner *ngIf="!company" style="margin: auto; width: 100%;height: 40px;"></ion-spinner>
  <ion-grid [fixed]="true" class="ion-align-items-center" *ngIf="company">
    <ion-card mode="ios" style="box-shadow: unset; border:solid 1px var(--ion-color-secondary, #113665); max-width: 370px; margin: auto">
      <ion-card-content>
        <ion-row class="ion-justify-content-center ion-align-items-center">
          <ion-col class="ion-justify-content-center ion-align-items-center">
            <ion-item>
              <ion-label>الاسم</ion-label>
              <ion-text color="primary">{{company.title || 'بدون'}}</ion-text>
            </ion-item>
            <ion-item *ngIf="company.description">
              <!--<ion-label>التفاصيل</ion-label>-->
              <ion-text class="ion-margin" color="primary" style="text-align: justify;text-justify: inter-word;"
                        [innerHTML]="(company.description || '') | nl2br"></ion-text>
            </ion-item>
            <ion-button class="buy-button ion-no-margin ion-margin-top" style="width: 100%"
                        *ngIf="company.website_url" [href]="company.website_url" target="_blank">
              <ion-icon name="globe-outline"></ion-icon>&nbsp;الموقع الإلكتروني
            </ion-button>
            <ion-button class="buy-button ion-no-margin ion-margin-top" style="width: 100%"
                        *ngIf="company.snapchat_url" [href]="company.snapchat_url" target="_blank">
              <ion-icon name="logo-snapchat"></ion-icon>&nbsp;سناب شات
            </ion-button>
            <ion-button class="buy-button ion-no-margin ion-margin-top" style="width: 100%"
                        *ngIf="company.linkedin_url" [href]="company.linkedin_url" target="_blank">
              <ion-icon name="logo-linkedin"></ion-icon>&nbsp;لينكدان
            </ion-button>
            <ion-button class="buy-button ion-no-margin ion-margin-top" style="width: 100%"
                        *ngIf="company.facebook_url" [href]="company.facebook_url" target="_blank">
              <ion-icon name="logo-facebook"></ion-icon>&nbsp;فيسبوك
            </ion-button>
            <ion-button class="buy-button ion-no-margin ion-margin-top" style="width: 100%"
                        *ngIf="company.instagram_url" [href]="company.instagram_url" target="_blank">
              <ion-icon name="logo-instagram"></ion-icon>&nbsp;أنستقرام
            </ion-button>
            <ion-button class="buy-button ion-no-margin ion-margin-top" style="width: 100%"
                        *ngIf="company.twitter_url" [href]="company.twitter_url" target="_blank">
              <ion-icon name="link-outline"></ion-icon>&nbsp;تويتر
            </ion-button>
            <ion-button class="buy-button ion-no-margin ion-margin-top" style="width: 100%"
                        *ngIf="company.google_maps_url" [href]="company.google_maps_url" target="_blank">
              <ion-icon name="map-outline"></ion-icon>&nbsp;جوجل ماب
            </ion-button>
          </ion-col>
        </ion-row>
      </ion-card-content>
    </ion-card>
  </ion-grid>
</ion-content>

