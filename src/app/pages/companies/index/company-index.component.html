<app-header [hideProfileIcon]="false" [showBackButton]="true"></app-header>
<ion-content>
  <ion-grid [fixed]="true">
    <ion-row>
      <ion-col size="12">
        <ng-container *ngTemplateOutlet="companies"></ng-container>
      </ion-col>
    </ion-row>
    <ion-infinite-scroll (ionInfinite)="onIonInfinite($event)" *ngIf="companiesNextPageUrl">
      <ion-infinite-scroll-content></ion-infinite-scroll-content>
    </ion-infinite-scroll>
  </ion-grid>
</ion-content>

<ng-template #companies>
  <div style=" overflow-y: scroll; overflow-x: hidden;   max-width: 370px;    margin: auto;">
    <ng-container *ngIf="companies$ | async as companies;">
      <ion-spinner *ngIf="loading" style="margin: auto; width: 100%;height: 40px;"></ion-spinner>
      <ion-item *ngIf="!loading && companies.length === 0">
        <ion-text color="danger">لا يوجد شركات في الوقت الحالي</ion-text>
      </ion-item>
      <ion-item *ngFor="let oneCompany of companies; let i = index" [title]="oneCompany.title"
                [routerLink]="'/companies/' + oneCompany.id"
                lines="full"
                button>
        <ion-avatar slot="start">
          <ion-img [src]="oneCompany.thumb_url || ((appDataService.data$ | async)!['iconPath']) || '/assets/icon/logo.svg'"
                   (ionError)="failCallbackImage($event)"></ion-img>
        </ion-avatar>
        <ion-label>
          <h4 class="ion-text-wrap" style="line-height: 1.7 !important;">
            <b>{{oneCompany.title}}</b>
          </h4>
          <!--<p class="" *ngIf="oneCompany.date">{{oneCompany.date.indexOf('ه') >= 0 ? oneCompany.date : (oneCompany.date | date: 'longDate')}}</p>
          <p class="" *ngIf="!oneCompany.date">{{oneCompany.created_at | date: 'longDate'}}</p>-->
        </ion-label>
      </ion-item>
    </ng-container>
  </div>
</ng-template>
