import {Component, ChangeDetectionStrategy, Inject, ViewChild, AfterViewInit, OnInit} from '@angular/core';
import {StyleRenderer, WithStyles, lyl, ThemeRef, ThemeVariables} from '@alyle/ui';
import {LyDialogRef, LY_DIALOG_DATA, LyDialogModule} from '@alyle/ui/dialog';
import {LySlider, LySliderChange, LySliderModule, STYLES as SLIDER_STYLES} from '@alyle/ui/slider';
import {
  LyImageCropper,
  ImgCropperConfig,
  ImgCropperEvent,
  ImgCropperErrorEvent, LyImageCropperModule
} from '@alyle/ui/image-cropper';
import {LyButton, LyButtonModule} from "@alyle/ui/button";
import {FormsModule} from "@angular/forms";
import {LyIconModule} from "@alyle/ui/icon";
import {NgIf, NgOptimizedImage} from "@angular/common";
import {ImgFallbackDirective} from "../../../img-fallback.directive";
import {IonicModule, ModalController} from "@ionic/angular";

@Component({
  templateUrl: './cropper-dialog.component.html',
  styleUrls: ['cropper-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  //standalone: true,
  providers: [
    StyleRenderer
  ],
  standalone: true,
  imports: [
    LyImageCropperModule,
    LySliderModule,
    LyButtonModule,
    LyIconModule,
    FormsModule,
    LyDialogModule,
    NgIf,
    ImgFallbackDirective,
    IonicModule,
    NgOptimizedImage,
  ]
})

export class CropperDialog implements AfterViewInit {
  // @ts-ignore
  ready: boolean;
  // @ts-ignore
  scale: number;
  // @ts-ignore
  minScale: number;
  // @ts-ignore
  @ViewChild(LyImageCropper, {static: true}) cropper: LyImageCropper;

  myConfig: ImgCropperConfig = {
    width: 300,
    height: 300,
    minWidth: 300,
    minHeight: 300,
    // type: 'image/png',
    keepAspectRatio: true,
    responsiveArea: true,
    round: true,
    output: {
      width: 600,
      height: 600
    },
    resizableArea: false
  };
  // @ts-ignore
  private data: Event;

  constructor(
    private modalCtrl: ModalController
  ) {
  }

  ngAfterViewInit(): void {
    /*setTimeout(function (cropper: any, data: any) {
      cropper.selectInputEvent(data);
    }, 1000, this.cropper, this.data)*/
    // @ts-ignore
    this.cropper.selectInputEvent(this.data);
  }

  onError(e: ImgCropperErrorEvent) {
    console.warn(`'${e.name}' is not a valid image`, e);
    return this.modalCtrl.dismiss(null, 'cancel');
  }

  onSliderInput(event: LySliderChange) {
    this.scale = event.value as number;
  }

  cancel() {
    return this.modalCtrl.dismiss(null, 'cancel');
  }

  confirm() {
    return this.modalCtrl.dismiss(this.cropper.crop(), 'confirm');
  }
}
