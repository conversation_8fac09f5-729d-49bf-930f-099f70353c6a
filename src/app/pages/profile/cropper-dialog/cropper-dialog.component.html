<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button color="medium" (click)="cancel()">رجوع</ion-button>
    </ion-buttons>
    <ion-title>قص الصورة</ion-title>
  </ion-toolbar>
</ion-header>
<ion-content class="ion-padding">
  <ion-grid [fixed]="true" class="ion-flex-column">
    <ly-img-cropper
      style="width: 100%"
      [config]="myConfig"
      [(scale)]="scale"
      (ready)="ready = true"
      (cleaned)="ready = false"
      (minScale)="minScale = $event"
      (error)="onError($event)"
    >
      <span>Drag and drop image</span>
    </ly-img-cropper>

    <div class="sliderContainer" *ngIf="minScale < 1">
      <ly-slider
        [thumbVisible]="false"
        [min]="minScale"
        [max]="1"
        [(ngModel)]="scale"
        (input)="onSliderInput($event)"
        [step]="0.000001"></ly-slider>
    </div>
    <ion-row style="width: 100%">
      <ion-col size="6">
        <ion-button color="primary" class="ion-no-margin ion-margin-top" style="width: 100%" (click)="confirm()">تأكيد
        </ion-button>
      </ion-col>
      <ion-col size="6">
        <ion-button color="medium" class="ion-no-margin ion-margin-top" style="width: 100%" (click)="cancel()">إلغاء
        </ion-button>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
