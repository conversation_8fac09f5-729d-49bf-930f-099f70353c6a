<app-header [hideProfileIcon]="true" [showBackButton]="true" backTo="/settings"></app-header>
<ion-content>
  <app-card [showExpandButton]="false" [scrollable]="false" class="strava-card">
    <ion-spinner *ngIf="!isReady" style="margin: auto; width: 100%;height: 40px;"></ion-spinner>
    <ng-container *ngIf="isReady">
      <ng-container *ngIf="athlete !== null">
        <img class="athlete-profile"
             [ngSrc]="(profileService.$user | async)?.profile_photo_url"
             [appImgFallback]="('/assets/d3-images/male-icon.svg')"
             width="120" height="120">

        <ion-row>
          <ion-col class="ion-no-padding">
            <ion-label class="ion-text-wrap ion-text-center">
              <ion-text color="secondary">
                <h4 style="line-height: 2 !important; font-weight: bolder">{{ (profileService.$user | async)?.full_name }}</h4>
              </ion-text>
            </ion-label>
          </ion-col>
        </ion-row>

        <ion-item>
          <ion-label>عدد الأنشطة</ion-label>
          <ion-text color="primary">{{ athlete.activities_count }}</ion-text>
        </ion-item>

        <ion-item>
          <ion-label>إجمالي وقت الأنشطة</ion-label>
          <ion-text color="primary">{{ athlete.moving_time | secondsFormatter }}</ion-text>
        </ion-item>

        <ion-item>
          <ion-label>إجمالي المسافة المقطوعة</ion-label>
          <ion-text color="primary">{{ athlete.distance | kiloFormatter }}</ion-text>
        </ion-item>
      </ng-container>
      <ng-container *ngIf="athlete === null">
        <ion-button [href]="stravaAuthURL" color="primary" class="ion-justify-content-center ion-text-center">
          الربط مع سترافا
        </ion-button>
      </ng-container>

      <ng-container *ngIf="athlete !== null">
        <ion-button (click)="deauthorize()" fill="clear" expand="block" color="danger" class="ion-justify-content-center ion-text-center">
          إزالة ارتباط سترافا
        </ion-button>
      </ng-container>
    </ng-container>
  </app-card>
</ion-content>
