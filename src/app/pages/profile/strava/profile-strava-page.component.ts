import {<PERSON><PERSON><PERSON>w<PERSON>nit, Component, Inject, OnInit, inject} from '@angular/core';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DatePipe,
  DOCUMENT,
  JsonPipe,
  NgForOf,
  <PERSON>I<PERSON>,
  NgOptimizedImage,
  NgTemplateOutlet
} from "@angular/common";
import {AlertController, IonicModule, NavController, ToastController} from "@ionic/angular";
import {HttpClientModule} from "@angular/common/http";
import {RouterModule} from "@angular/router";
import {HeaderComponent} from "../../../layout/header/header.component";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {CardComponent} from "../../../shared/components/card/card.component";
import {IS_SERVER_PLATFORM} from "../../../shared/IS_SERVER_PLATFORM.token";
import {ProfileService} from "../../../shared/services/profile.service";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {NgxGoogleAnalyticsModule} from "@dustfoundation/ngx-google-analytics";
import {ApiService} from "../../../shared/services/api.service";
import {StorageService} from "../../../shared/services/storage.service";
import {Athlete} from "../../../shared/models";
import {TranslateIonicTextsDirective} from "../../../shared/directives/translate-ionic-texts.directive";
import {StravaService} from "../../../shared/services/strava.service";
import {ImgFallbackDirective} from "../../../img-fallback.directive";
import {FormatDistancePipe} from "../../../shared/pipes/format-distance.pipe";
import {KiloFormatterPipe} from "../../../shared/pipes/kilo-formatter-pipe";
import {SecondsFormatterPipe} from "../../../shared/pipes/seconds-formatter-pipe";
import {GenericService} from "../../../shared/services/generic.service";
import {StravaReloadService} from "../strava-reload.service";
import {catchError} from 'rxjs';

@Component({
  selector: 'app-profile-strava',
  templateUrl: 'profile-strava-page.component.html',
  styleUrls: ['profile-strava-page.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    IfIsBrowserDirective,
    NgIf,
    CardComponent,
    NgTemplateOutlet,
    NgForOf,
    AsyncPipe,
    JsonPipe,
    RouterModule,
    DatePipe,
    NgOptimizedImage,
    FormsModule,
    NgxGoogleAnalyticsModule,
    ReactiveFormsModule,
    TranslateIonicTextsDirective,
    ImgFallbackDirective,
    FormatDistancePipe,
    KiloFormatterPipe,
    SecondsFormatterPipe,
  ],
})
export class ProfileStravaPageComponent implements OnInit, AfterViewInit {
  public isReady: boolean = false;
  errorMessage: string | null = null;
  stravaAuthURL: string | null = null;
  athlete: Athlete | null = null;
  access_token_expired: boolean = false;
  nowTime = new Date().toISOString();

  constructor(
    @Inject(DOCUMENT) private document: Document,
    @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
    public profileService: ProfileService,
    public apiService: ApiService,
    public storageService: StorageService,
    private stravaService: StravaService,
    private stravaReloadService: StravaReloadService,
    public toastController: ToastController,
    public genericService: GenericService,
    private navCtrl: NavController,
  ) {
    try {
      this.stravaAuthURL = stravaService.getAuthorizationUrl([
        'read',
        'activity:read',
      ]);
    } catch (e) {
    }
  }

  ngOnInit(): void {
    if (this.isServer) {
      console.log('is server')
    } else {
      this.browserSetup();
    }
  }

  ngAfterViewInit(): void {
    this.fetchUserData()
    if (this.genericService.isCapacitorApp()) {
      this.stravaReloadService.reload$.subscribe(() => {
        this.isReady = false;
        this.fetchUserData();
      });
    }
  }

  alertCtrl = inject(AlertController);

  deauthorize(): void {
    this.alertCtrl.create({
      header: 'هل أنت متأكد؟',
      message: 'هل أنت متأكد من إزالة ارتباط حساب سترافا؟\nإزالة الارتباط يعني حذف جميع الأنشطة المسجلة، ويعني انسحاب من مبادرة كلنا نمشي.',
      buttons: [
        {
          text: 'أبق الربط',
          role: 'cancel',
        },
        {
          text: 'نعم أزل الارتباط',
          role: 'confirm',
          handler: () => {
            this.isReady = false;
            this.stravaService.deauthorize().subscribe((res) => {
              this.isReady = true;
              window.location.reload();
            }, ({error}) => {
              window.location.reload();
            });
          }
        }
      ]
    }).then(alert => {
      alert.present();
    })



  }

  fetchUserData(): void {
    this.stravaService.athlete().subscribe((res) => {
      this.athlete = res.athlete;
      this.access_token_expired = res.access_token_expired;
      this.isReady = true;
    }, ({error}) => {
      this.apiService.logout().pipe(
        catchError((err) => {
          this.storageService.removeItem('access_token');
          this.storageService.removeItem('expires_at');
          this.storageService.removeItem('user');
          this.profileService.removeUser();
          this.navCtrl.navigateRoot('/login', {
            queryParams: {
              returnURL: '/me/strava'
            }
          }).then();
          return err;
        })
      ).subscribe(
        (res) => {
          this.storageService.removeItem('access_token');
          this.storageService.removeItem('expires_at');
          this.storageService.removeItem('user');
          this.profileService.removeUser();
          this.navCtrl.navigateRoot('/login', {
            queryParams: {
              returnURL: '/me/strava'
            }
          }).then();
        }
      )
    });
  }

  browserSetup() {

  }
}
