import {Component, Inject, Input, OnInit} from '@angular/core';
import {
  <PERSON>ync<PERSON><PERSON><PERSON>,
  DatePipe,
  DOCUMENT,
  JsonPipe,
  NgF<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ptimizedImage,
  NgTemplateOutlet
} from "@angular/common";
import {<PERSON><PERSON><PERSON><PERSON>roller, IonicModule, NavController, ToastController} from "@ionic/angular";
import {HttpClientModule} from "@angular/common/http";
import {Router, RouterModule} from "@angular/router";
import {HeaderComponent} from "../../../layout/header/header.component";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {CardComponent} from "../../../shared/components/card/card.component";
import {IS_SERVER_PLATFORM} from "../../../shared/IS_SERVER_PLATFORM.token";
import {ProfileService} from "../../../shared/services/profile.service";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {NgxGoogleAnalyticsModule} from "@dustfoundation/ngx-google-analytics";
import {ApiService} from "../../../shared/services/api.service";
import {StorageService} from "../../../shared/services/storage.service";
import {TranslateIonicTextsDirective} from "../../../shared/directives/translate-ionic-texts.directive";
import {StravaService} from "../../../shared/services/strava.service";
import {StravaReloadService} from "../strava-reload.service";

@Component({
  selector: 'app-profile-strava-callback',
  templateUrl: 'profile-strava-callback-page.component.html',
  styleUrls: ['profile-strava-callback-page.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    IfIsBrowserDirective,
    NgIf,
    CardComponent,
    NgTemplateOutlet,
    NgForOf,
    AsyncPipe,
    JsonPipe,
    RouterModule,
    DatePipe,
    NgOptimizedImage,
    FormsModule,
    NgxGoogleAnalyticsModule,
    ReactiveFormsModule,
    TranslateIonicTextsDirective,
  ],
})
export class ProfileStravaCallbackPageComponent implements OnInit {
  @Input('code') code?: string
  @Input('state') state: string | null = null

  constructor(
    @Inject(DOCUMENT) private document: Document,
    @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
    public profileService: ProfileService,
    public apiService: ApiService,
    public storageService: StorageService,
    private stravaService: StravaService,
    private stravaReloadService: StravaReloadService,
    public toastController: ToastController,
    private router: Router,
  ) {

  }

  ngOnInit(): void {
    if (this.isServer) {
      console.log('is server')
    } else {
      this.browserSetup();
    }
    if (this.code)
      this.stravaService.authorizeCode(this.code, this.state).subscribe(res => {
        if (res.success === true)
          this.toastController.create({
            message: 'تم الربط مع strava بنجاح',
            position: 'top',
            color: 'success',
            cssClass: 'custom-toast',
            buttons: [{
              side: 'end',
              text: 'إلغاء',
              role: 'cancel',
            }],
          }).then((toast) => {
            toast.present();
            this.router.navigate(['/me/strava']);
            this.stravaReloadService.triggerReload();
            setTimeout(() => toast.dismiss(), 5000);
          });
        else
          this.failedToast()
      })
    else
      this.failedToast()
  }

  browserSetup() {

  }

  failedToast() {
    this.toastController.create({
      message: 'لم تتم عملية الربط بنجاح',
      position: 'top',
      color: 'danger',
      cssClass: 'custom-toast',
      buttons: [{
        side: 'end',
        text: 'إلغاء',
        role: 'cancel',
      }],
    }).then((toast) => {
      toast.present();
      this.router.navigate(['/me/strava']);
      setTimeout(() => toast.dismiss(), 5000);
    });
  }
}
