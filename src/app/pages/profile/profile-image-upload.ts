import {CropperDialog} from "./cropper-dialog/cropper-dialog.component";
import {fromEvent, ReplaySubject, take, takeUntil, tap} from "rxjs";
import {ActionSheetController, ModalController} from "@ionic/angular";
import {ApiService} from "../../shared/services/api.service";
import {StorageService} from "../../shared/services/storage.service";
import {ProfileService} from "../../shared/services/profile.service";
import {User} from "../../shared/models";
import {ElementRef} from "@angular/core";
import {ProfileImageUploadInterface} from "./profile-image-upload.interface";

export class ProfileImageUpload implements ProfileImageUploadInterface {
  public avatarUpdating = false;

  constructor(public apiService: ApiService,
              public storageService: StorageService,
              public profileService: ProfileService,
              public modalCtrl: ModalController,
              public actionSheetCtrl: ActionSheetController,
  ) {

  }

  public async openCropperDialog(event: Event) {
    this.actionSheetCtrl.dismiss();
    const modal = await this.modalCtrl.create({
      component: CropperDialog,
      componentProps: {
        data: event,
      },
      canDismiss: async (data?: any, role?: string) => {
        return role !== 'gesture';
      },
      backdropDismiss: false,
      mode: 'ios',
    });
    await modal.present();
    const elm = document.getElementById(modal.id)
    const destroy$: ReplaySubject<boolean> = new ReplaySubject<boolean>();

    if (elm) {
      fromEvent(elm, 'mousedown').pipe(
        takeUntil(destroy$),
        //throttleTime(150),
        tap((e: Event) => {
          // @ts-ignore
          if (e.target.tagName === 'ION-BUTTON')
            return;
          e.preventDefault()
          e.stopImmediatePropagation()
          e.stopPropagation()
        })
      ).subscribe();

      fromEvent(elm, 'touchstart').pipe(
        takeUntil(destroy$),
        //throttleTime(150),
        tap((e: Event) => {
          // @ts-ignore
          if (e.target.tagName === 'ION-BUTTON')
            return;
          e.preventDefault()
          e.stopImmediatePropagation()
          e.stopPropagation()
        })
      ).subscribe();
      elm.dispatchEvent(new MouseEvent('mousedown'))
      elm.dispatchEvent(new TouchEvent('touchstart'))
    }
    const {data, role} = await modal.onWillDismiss();
    destroy$.next(true);
    destroy$.unsubscribe();
    if (role === 'confirm') {
      this.avatarUpdating = true;
      this.apiService.updateUserAvatar(data.dataURL).subscribe((res) => {
        this.afterSuccess(res.data, 'update');
        this.storageService.setItem('user', JSON.stringify(res));
        this.profileService.setUser(res.data);
        this.avatarUpdating = false;
      });
    }
  }

  public async openAvatarActionsSheet() {
    this.profileService.$user.pipe(take(1)).subscribe(user => this.presentAvatarActionsSheet(user))
  }

  public async presentAvatarActionsSheet(user: User | null) {
    const actionSheet = await this.actionSheetCtrl.create({
      //header: 'Actions',
      buttons: [
        {
          text: 'تعديل',
          data: {
            action: 'update',
          },
          handler: () => {
            this.getFileInput()?.nativeElement.click();
            return false;
          }
        },
        ...((user && user.profile_photo_url !== undefined && user.profile_photo_url !== null) ? [{
          text: 'حذف',
          role: 'destructive',
          data: {
            action: 'delete',
          },
          handler: () => {
            this.avatarUpdating = true;
            this.apiService.deleteUserAvatar().subscribe((res) => {
              this.afterSuccess(res.data, 'delete');
              this.storageService.setItem('user', JSON.stringify(res));
              this.profileService.setUser(res.data);
              this.avatarUpdating = false;
            });
            return true;
          }
        }] : []),
        {
          text: 'إلغاء',
          role: 'cancel',
          data: {
            action: 'cancel',
          },
        },
      ],
    });
    await actionSheet.present()
  }

  getFileInput(): ElementRef<any> {
    throw new Error("Method not implemented.");
  }

  afterSuccess(usr: User, action: string) {

  }
}
