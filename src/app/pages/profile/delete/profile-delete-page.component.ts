import {Component} from '@angular/core';
import {AsyncPipe} from "@angular/common";
import {IonicModule} from "@ionic/angular";
import {HttpClientModule} from "@angular/common/http";
import {firstValueFrom} from "rxjs";
import {RouterModule} from "@angular/router";
import {HeaderComponent} from "../../../layout/header/header.component";
import {CardComponent} from "../../../shared/components/card/card.component";
import {FormsModule, FormGroup, FormBuilder, ReactiveFormsModule, Validators} from "@angular/forms";
import {NgxGoogleAnalyticsModule} from "@dustfoundation/ngx-google-analytics";
import {AppDataService} from "../../../shared/services/app-data.service";
import {ApiService} from "../../../shared/services/api.service";

@Component({
  selector: 'app-profile-delete',
  templateUrl: 'profile-delete-page.component.html',
  styleUrls: ['profile-delete-page.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    CardComponent,
    AsyncPipe,
    RouterModule,
    FormsModule,
    NgxGoogleAnalyticsModule,
    ReactiveFormsModule,
  ],
})
export class ProfileDeletePageComponentPage {
  deleteForm: FormGroup = new FormGroup({});
  isSubmitted = false;
  hasExistingRequest = false;
  isLoading = false;

  deleteReasons = [
    {id: 1, text: 'لم أعد بحاجة للتطبيق'},
    {id: 2, text: 'واجهت مشاكل تقنية'},
    {id: 3, text: 'التطبيق غير مفيد بالنسبة لي'},
    {id: 4, text: 'أخرى'}
  ];

  constructor(
    public appDataService: AppDataService,
    public apiService: ApiService,
    private formBuilder: FormBuilder,
  ) {
    this.deleteForm = this.formBuilder.group({
      reason: ['', Validators.required],
      otherReason: [''],
      confirmation: [false, Validators.requiredTrue]
    });
  }

  ngOnInit() {
    this.checkExistingDeleteRequest();
  }

  async checkExistingDeleteRequest() {
    this.isLoading = true;
    try {
      // Replace with your actual API endpoint
      const response = await firstValueFrom(this.apiService.accountHasDelete());
      this.hasExistingRequest = response.hasRequest;
    } catch (error) {
      console.error('Failed to check delete request status', error);
    } finally {
      this.isLoading = false;
    }
  }

  get f() {
    return this.deleteForm.controls;
  }

  onReasonChange() {
    if (this.f['reason'].value === 4) { // Other reason selected
      this.f['otherReason'].setValidators([Validators.required]);
    } else {
      this.f['otherReason'].clearValidators();
    }
    this.f['otherReason'].updateValueAndValidity();
  }

  async submitDeleteRequest() {
    if (this.deleteForm.invalid) {
      return;
    }

    this.isLoading = true;
    try {
      const reasonId = this.f['reason'].value;
      const reason: string = reasonId === 4 ? this.f['otherReason'].value : this.deleteReasons.find((reason) => reason.id === reasonId)!.text;
      const payload = {reason};

      // Replace with your actual API endpoint
      await firstValueFrom(this.apiService.accountDelete(payload));
      this.isSubmitted = true;
      this.hasExistingRequest = true;
    } catch (error) {
      console.error('Failed to submit delete request', error);
    } finally {
      this.isLoading = false;
    }
  }
}
