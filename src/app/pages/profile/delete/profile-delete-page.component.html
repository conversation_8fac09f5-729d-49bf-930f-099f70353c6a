<app-header [hideProfileIcon]="true" [showBackButton]="true" backTo="/settings"></app-header>
<ion-content color="light" [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large" class="ion-margin-vertical">حذف الحساب</ion-title>
    </ion-toolbar>
  </ion-header>

  <ion-grid [fixed]="true">
    <app-card [showExpandButton]="false" [scrollable]="false">
      <!-- Loading state -->
      @if (isLoading) {
        <div class="ion-text-center">
          <ion-spinner></ion-spinner>
          <p>جاري التحميل...</p>
        </div>
      }

      <!-- Existing delete request -->
      @else if (hasExistingRequest) {
        <div class="ion-padding">
          <ion-text color="warning">
            <h4>تم استلام طلب حذف حسابك</h4>
            <p>سيتم الاتصال بك قريبًا لتأكيد طلب الحذف.</p>
            <p>إذا كان لديك أي استفسار، يمكنك التواصل معنا على البريد الإلكتروني:
              <a
                href="mailto:{{ (appDataService.data$ | async)!['infoMail'] }}">{{ (appDataService.data$ | async)!['infoMail'] }}</a>
            </p>
          </ion-text>
        </div>
      }

      <!-- Submission confirmation -->
      @else if (isSubmitted) {
        <div class="ion-padding">
          <ion-text color="success">
            <h4>تم استلام طلب حذف حسابك بنجاح</h4>
            <p>سيتم الاتصال بك قريبًا لتأكيد عملية الحذف.</p>
            <p>شكرًا لاستخدامك تطبيقنا.</p>
          </ion-text>
        </div>
      }

      <!-- Delete account form -->
      @else {
        <div class="ion-padding">
          <h4>حذف الحساب</h4>
          <p>نأسف لرغبتك في حذف حسابك. يرجى ملاحظة أنه بمجرد حذف حسابك، سيتم حذف جميع بياناتك ولن تتمكن من
            استعادتها.</p>

          <form [formGroup]="deleteForm" (ngSubmit)="submitDeleteRequest()">
            <ion-item>
              <ion-label position="stacked">سبب الحذف*</ion-label>
              <ion-select formControlName="reason" (ionChange)="onReasonChange()" interface="action-sheet"
                          cancelText="إلغاء">
                @for (reason of deleteReasons; track reason.id) {
                  <ion-select-option [value]="reason.id">{{ reason.text }}</ion-select-option>
                }
              </ion-select>
              @if (f['reason'].invalid && (f['reason'].dirty || f['reason'].touched)) {
                <ion-text color="danger">
                  <p class="ion-padding-start">يرجى اختيار سبب الحذف</p>
                </ion-text>
              }
            </ion-item>

            @if (f['reason'].value === 4) {
              <ion-item>
                <ion-label position="stacked">يرجى ذكر السبب*</ion-label>
                <ion-textarea formControlName="otherReason" rows="3"></ion-textarea>
                @if (f['otherReason'].invalid && (f['otherReason'].dirty || f['otherReason'].touched)) {
                  <ion-text color="danger">
                    <p class="ion-padding-start">يرجى ذكر سبب الحذف</p>
                  </ion-text>
                }
              </ion-item>
            }

            <ion-item lines="none">
              <ion-checkbox formControlName="confirmation" slot="start"></ion-checkbox>
              <ion-label class="ion-text-wrap">أفهم أنه بمجرد حذف حسابي، سيتم حذف جميع بياناتي ولن أتمكن من استعادتها.
              </ion-label>
            </ion-item>
            @if (f['confirmation'].invalid && (f['confirmation'].dirty || f['confirmation'].touched)) {
              <ion-text color="danger">
                <p class="ion-padding-start">يرجى الموافقة على الشروط</p>
              </ion-text>
            }

            <div class="ion-padding-top">
              <ion-button type="submit" expand="block" [disabled]="deleteForm.invalid || isLoading">
                @if (isLoading) {
                  <ion-spinner name="dots"></ion-spinner>
                } @else {
                  تأكيد حذف الحساب
                }
              </ion-button>
            </div>
          </form>
        </div>
      }
    </app-card>
  </ion-grid>
</ion-content>
