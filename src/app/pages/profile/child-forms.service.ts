import {Injectable} from "@angular/core";
import {HttpClient} from "@angular/common/http";
import {environment} from "../../../environments/environment";

@Injectable({
  providedIn: 'root'
})
export class ChildFormsService {
  env = environment

  constructor(public http: HttpClient) {
  }

  getSpouses() {
    return this.http.get<{
      id: number,
      full_name: string,
    }[]>(`${this.env.baseUrl}/auth/me/spouses`);
  }

  getForms() {
    return this.http.get(`${this.env.baseUrl}/child-forms`);
  }

  store(data: object) {
    return this.http.post(`${this.env.baseUrl}/child-forms`, data);
  }
}
