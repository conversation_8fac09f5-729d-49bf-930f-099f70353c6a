<app-header [hideProfileIcon]="true " [showBackButton]="true" title="الملف الشخصي"></app-header>
<ion-content>
  <ion-grid [fixed]="true" class="ion-flex-column">
    <input #fileInput type="file" (change)="openCropperDialog($event)" accept="image/*" hidden>
    <div class="profile-card">
      <ion-grid>
        <ion-row>
          <ion-col class="ion-text-start visibly-hidden">
            <ion-button color="secondary" fill="clear" class="ion-no-padding ion-no-margin">
              <ion-icon [style.color]="'red'" name="membership-montseb"></ion-icon>
            </ion-button>
          </ion-col>
          <ion-col>
            <ion-avatar style="margin: auto;width: 120px; height: 120px;" class="ion-margin-bottom">
              <div class="AvatarContainer">
                <img
                  [ngSrc]="(profileService.$user|async)?.profile_photo_url ?? ('/assets/d3-images/' + ((profileService.$user|async)?.gender ?? 'male').toLowerCase() + '-icon.svg')"
                  [appImgFallback]="('/assets/d3-images/' + ((profileService.$user|async)?.gender ?? 'male').toLowerCase() + '-icon.svg')"
                  disabled="avatarUpdating"
                  width="120" height="120">
                <div style="position: absolute;"
                     *ngIf="(profileService.$user|async)?.can_update_picture && (profileService.$user|async)?.gender === 'MALE' && !avatarUpdating" id="avatarMenu"
                     (click)="openAvatarActionsSheet()">
                  <ion-icon name="camera-outline" color="primary"></ion-icon>
                </div>
                <ion-spinner *ngIf="avatarUpdating"></ion-spinner>
              </div>
            </ion-avatar>
            <!--<ion-popover trigger="avatarMenu" triggerAction="click">
              <ng-template>
                <ion-list>
                  <ion-item (click)="dismissPopover();_fileInput.click();"><ion-label>تعديل</ion-label></ion-item>
                  <ion-item><ion-label>حذف</ion-label></ion-item>
                </ion-list>
              </ng-template>
            </ion-popover>-->
          </ion-col>
          <ion-col class="ion-text-end">
            <ion-button fill="clear" class="ion-no-padding ion-no-margin" id="profileMenu">
              <ion-icon name="ellipsis-vertical"></ion-icon>
            </ion-button>
            <ion-popover trigger="profileMenu" triggerAction="click">
              <ng-template>
                <ion-list>
                  <ion-item routerLink="/me/edit" (click)="dismissPopover()">
                    <ion-label>تعديل</ion-label>
                  </ion-item>
                </ion-list>
              </ng-template>
            </ion-popover>
          </ion-col>
        </ion-row>

        <ion-row>
          <ion-col class="ion-no-padding">
            <ion-label class="ion-text-wrap ion-text-center">
              <ion-text color="secondary">
                <h5
                  style="line-height: 1.7; font-weight: bolder; letter-spacing: 4px">{{(profileService.$user|async)?.family_user_id}}</h5>
              </ion-text>
              <h4 style="line-height: 1.7; font-weight: bolder">{{(profileService.$user|async)?.full_name}}</h4>
            </ion-label>
          </ion-col>
        </ion-row>

        <ion-row class="ion-margin-bottom ion-margin-top">
          <ion-col class="ion-no-padding">
            <ion-label class="ion-text-wrap ion-text-center">
              <h5 style="line-height: 1.7;">{{(profileService.$user|async)?.bio}}</h5>
            </ion-label>
          </ion-col>
        </ion-row>

        <ion-row *ngIf="(profileService.$user|async)?.membership as _membership">
          <ion-col size="auto" class="ion-no-padding">
            <ion-label>
              <h4 class="ion-text-wrap" style="line-height: 1.7 !important;">
                {{_membership.package.title}}
              </h4>
            </ion-label>
          </ion-col>
          <ion-col size="auto" class="ion-no-padding">
            <ion-img *ngIf="_membership.package.slug" style="width: 18px;margin: 8px 4px 0 0;"
                     [src]="'/assets/images/icons/membership-'+_membership.package.slug+'.svg'"></ion-img>
          </ion-col>
          <ion-col class="ion-margin-start ion-no-padding">
            {{_membership.start_at | date:'longDate'}}
          </ion-col>
          <ion-col size="auto" class="ion-no-padding"> ←</ion-col>
          <ion-col class="ion-margin-end ion-no-padding" style="text-align: left">
            {{_membership.expired_at | date:'longDate'}}
          </ion-col>
        </ion-row>
      </ion-grid>
    </div>

    <div class="ion-margin-top" style="width: 370px;" *ngIf="false">
      <ion-icon *ngIf="familyGraphFullscreen" (click)="familyGraphFullscreen = false" name="arrow-expand"
                class="ion-align-self-center fullscreen-expanded expand-icon"></ion-icon>
      <app-card title="شجرة العائلة" (expandButtonClicked)="fullscreenClicked()"
                *ngIf="false">

      </app-card>
    </div>

    <ion-row>
      <ion-col class="ion-text-center">
        <ion-button color="danger" fill="clear" (click)="logout()" [disabled]="loadingLogout">
          <ion-spinner name="crescent" *ngIf="loadingLogout"></ion-spinner>
          <ng-container *ngIf="!loadingLogout">
            <ion-icon name="log-out" slot="start"></ion-icon>
            تسجيل الخروج
          </ng-container>
        </ion-button>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
