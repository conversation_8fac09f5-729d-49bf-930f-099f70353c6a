
.profile-card {
  width: 370px;
  border-radius: 8px;
  background: var(--ion-color-light);
  min-height: 200px;
}

.fullscreen {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1000;
  background: var(--ion-color-light-shade);
}

.fullscreen-expanded {
  font-size: 20px;
  cursor: pointer;
  position: absolute;
  z-index: 10001;
  left: 20px;
  top: 20px;
  margin-bottom: 8px;

  &.right {
    left: auto;
    right: 20px;
  }
}

.expand-icon {
  transform: scaleX(-1);
}

.AvatarContainer {
  height: 100%;
  width: 100%;
  border-radius: 50%;
  border-style: solid;
  border-color: #FFFFFF;
  box-shadow: 0 0 8px 2px #B8B8B8;
  position: relative;

  img {
    height: 100%;
    width: 100%;
    max-width: unset;
    border-radius: 50%;
  }

  ion-spinner {
    position: absolute;
    top: calc(50% - 15px);
    right: calc(50% - 15px);
    height: 30px;
    width: 30px;
  }

  div {
    position: absolute;
    top: 10px;
    right: -8px;
    border-radius: 50%;
    height: 30px;
    width: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--ion-color-light);
    box-shadow: 0 0 8px 1px #B8B8B8;
    cursor: pointer;
    font-size: 20px !important;
  ;
  }
}

:host ::ng-deep ion-modal, :host ::ng-deep ion-modal * {
  pointer-events: none;
}
