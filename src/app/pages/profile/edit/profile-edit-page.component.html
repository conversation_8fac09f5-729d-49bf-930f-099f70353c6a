<app-header [hideProfileIcon]="true" [showBackButton]="true" backTo="/settings"></app-header>
<ion-content color="light" [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large" class="ion-margin-vertical" style="">البيانات الشخصية</ion-title>
    </ion-toolbar>
  </ion-header>
  <ng-container *ngIf="isReady; else loading">
    <ion-list [inset]="true">
      <ion-item detail="false">
        <ion-label>الاسم</ion-label>
        <ion-note>{{ user?.name }}</ion-note>
      </ion-item>

      <ion-item detail="false">
        <ion-label>الرقم التعريفي</ion-label>
        <ion-note>{{ user?.family_user_id }}</ion-note>
      </ion-item>

      <ion-item detail="false">
        <ion-label>البريد الإلكتروني</ion-label>
        <ion-note>{{ user?.email ?? 'لا يوجد' }}</ion-note>
      </ion-item>

      <ion-item *ngIf="user?.dob" detail="false">
        <ion-label>تاريخ الميلاد (ميلادي)</ion-label>
        <ion-note>{{ (updateForm.controls.dob.value) | date: 'yyyy-MM-dd' }}</ion-note>
      </ion-item>
    </ion-list>


    <ion-list [inset]="true" [formGroup]="updateForm">
      <ion-item detail="false">
        <ion-input label="رقم الجوال" formControlName="phone"
                   dir="rtl" style="text-align: left;direction: rtl;"
                   class=""></ion-input>
      </ion-item>

      <ion-item lines="none" *ngIf="!user?.dob">
        <div>تاريخ الميلاد</div>
      </ion-item>
      <ion-item [formGroupName]="'dobGroup'" title="تاريخ الميلاد" *ngIf="!user?.dob">
        <ion-input label="اليوم" type="number" formControlName="day"></ion-input>
        <ion-input label="الشهر" type="number" formControlName="month"></ion-input>
        <ion-input label="السنة" type="number" formControlName="year"></ion-input>
      </ion-item>

      <ion-item class="" style="font-size: 0.8rem">
        <ion-icon slot="start" *ngIf="updateForm.touched && updateForm.controls.marital_status.invalid"
                  name="alert-circle-outline" color="danger"></ion-icon>
        <ion-select label="الحالة الإجتماعية" formControlName="marital_status" TranslateIonicTexts>
          <ion-select-option value="SINGLE">أعزب</ion-select-option>
          <ion-select-option value="MARRIED">متزوج</ion-select-option>
          <ion-select-option value="DIVORCED">مطلق</ion-select-option>
          <ion-select-option value="WIDOWER">أرمل</ion-select-option>
        </ion-select>
      </ion-item>

      <ion-item style="font-size: 0.8rem">
        <ion-icon slot="start" *ngIf="updateForm.touched && updateForm.controls.health_status.invalid"
                  name="alert-circle-outline" color="danger"></ion-icon>
        <ion-select label="الحالة الصحية" formControlName="health_status" TranslateIonicTexts>
          <ion-select-option value="سليم">سليم</ion-select-option>
          <ion-select-option value="مريض بمرض مزمن">مريض بمرض مزمن</ion-select-option>
          <ion-select-option value="معاق">معاق</ion-select-option>
        </ion-select>
      </ion-item>

      <ion-item style="font-size: 0.8rem">
        <ion-icon slot="start" *ngIf="updateForm.touched && updateForm.controls.educational_status.invalid"
                  name="alert-circle-outline" color="danger"></ion-icon>
        <ion-select label="التعليم" formControlName="educational_status" TranslateIonicTexts>
          <ion-select-option value="طفل">طفل</ion-select-option>
          <ion-select-option value="ابتدائي">ابتدائي</ion-select-option>
          <ion-select-option value="متوسط">متوسط</ion-select-option>
          <ion-select-option value="دبلوم">دبلوم</ion-select-option>
          <ion-select-option value="ثانوي">ثانوي</ion-select-option>
          <ion-select-option value="جامعي">جامعي</ion-select-option>
          <ion-select-option value="بكالوريوس">بكالوريوس</ion-select-option>
          <ion-select-option value="دكتوراه">دكتوراه</ion-select-option>
          <ion-select-option value="ماجستير">ماجستير</ion-select-option>
        </ion-select>
      </ion-item>

      <ion-item style="font-size: 0.8rem">
        <ion-icon slot="start" *ngIf="updateForm.touched && updateForm.controls.user_region_id.invalid"
                  name="alert-circle-outline" color="danger"></ion-icon>
        <ion-select label="مكان الإقامة" formControlName="user_region_id" TranslateIonicTexts>
          <ion-select-option *ngFor="let region$ of (profileService.$regions | async); let i= index"
                             [value]="region$.id.toString()">{{ region$.title_ar }}
          </ion-select-option>
        </ion-select>
      </ion-item>

      <ion-item class="">
        <ion-input label="رابط لينكدان (اختياري)" formControlName="linkedin_url"
                   class=""></ion-input>
      </ion-item>

      <ion-item class=" ">
        <ion-textarea label="النبذة الشخصية (اختياري)" labelPlacement="floating" formControlName="bio"
                      [counter]="true"
                      maxlength="240"></ion-textarea>
      </ion-item>

      <ion-chip *ngIf="errorMessage" color="danger">
        <ion-icon name="alert-circle-outline" color="danger"></ion-icon>
        <ion-label>{{ errorMessage }}</ion-label>
      </ion-chip>

      <ion-button color="primary" expand="block" mode="ios" style="color: white;"
                  class=" "
                  (click)="store()" [disabled]="formLoading">
        <ion-spinner *ngIf="formLoading"></ion-spinner>
        <span *ngIf="!formLoading">
            تعديل الملف الشخصي
          </span>
      </ion-button>
    </ion-list>
  </ng-container>
</ion-content>
<ng-template #loading>
  <ion-list [inset]="true">
    <ion-item detail="false">
      <ion-label>
        <ion-skeleton-text style="width: 40%; height: 20px;"></ion-skeleton-text>
      </ion-label>
      <ion-note>
        <ion-skeleton-text style="width: 30px; height: 20px;"></ion-skeleton-text>
      </ion-note>
    </ion-item>
    <ion-item detail="false">
      <ion-label>
        <ion-skeleton-text style="width: 40%; height: 20px;"></ion-skeleton-text>
      </ion-label>
      <ion-note>
        <ion-skeleton-text style="width: 30px; height: 20px;"></ion-skeleton-text>
      </ion-note>
    </ion-item>
    <ion-item detail="false">
      <ion-label>
        <ion-skeleton-text style="width: 40%; height: 20px;"></ion-skeleton-text>
      </ion-label>
      <ion-note>
        <ion-skeleton-text style="width: 30px; height: 20px;"></ion-skeleton-text>
      </ion-note>
    </ion-item>
  </ion-list>
  <ion-list [inset]="true">
    <ion-item detail="false">
      <ion-label>
        <ion-skeleton-text style="width: 40%; height: 20px;"></ion-skeleton-text>
      </ion-label>
      <ion-note>
        <ion-skeleton-text style="width: 30px; height: 20px;"></ion-skeleton-text>
      </ion-note>
    </ion-item>
    <ion-item detail="false">
      <ion-label>
        <ion-skeleton-text style="width: 40%; height: 20px;"></ion-skeleton-text>
      </ion-label>
      <ion-note>
        <ion-skeleton-text style="width: 30px; height: 20px;"></ion-skeleton-text>
      </ion-note>
    </ion-item>
    <ion-item detail="false">
      <ion-label>
        <ion-skeleton-text style="width: 40%; height: 20px;"></ion-skeleton-text>
      </ion-label>
      <ion-note>
        <ion-skeleton-text style="width: 30px; height: 20px;"></ion-skeleton-text>
      </ion-note>
    </ion-item>
    <ion-item detail="false">
      <ion-label>
        <ion-skeleton-text style="width: 40%; height: 20px;"></ion-skeleton-text>
      </ion-label>
      <ion-note>
        <ion-skeleton-text style="width: 30px; height: 20px;"></ion-skeleton-text>
      </ion-note>
    </ion-item>
    <ion-item detail="false">
      <ion-label>
        <ion-skeleton-text style="width: 40%; height: 20px;"></ion-skeleton-text>
      </ion-label>
      <ion-note>
        <ion-skeleton-text style="width: 30px; height: 20px;"></ion-skeleton-text>
      </ion-note>
    </ion-item>
    <ion-item detail="false">
      <ion-label>
        <ion-skeleton-text style="width: 40%; height: 20px;"></ion-skeleton-text>
      </ion-label>
      <ion-note>
        <ion-skeleton-text style="width: 30px; height: 20px;"></ion-skeleton-text>
      </ion-note>
    </ion-item>
  </ion-list>
</ng-template>
