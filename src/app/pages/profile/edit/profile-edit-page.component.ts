import {Component, Inject, Input, OnInit} from '@angular/core';
import {
  <PERSON>ync<PERSON><PERSON><PERSON>,
  DatePipe,
  DOCUMENT,
  JsonPipe,
  NgFor<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ptimizedImage,
  NgTemplateOutlet
} from "@angular/common";
import {<PERSON><PERSON><PERSON><PERSON>roller, IonicModule, NavController, ToastController} from "@ionic/angular";
import {HttpClientModule} from "@angular/common/http";
import {Router, RouterModule} from "@angular/router";
import {HeaderComponent} from "../../../layout/header/header.component";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {CardComponent} from "../../../shared/components/card/card.component";
import {IS_SERVER_PLATFORM} from "../../../shared/IS_SERVER_PLATFORM.token";
import {ProfileService} from "../../../shared/services/profile.service";
import {FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators} from "@angular/forms";
import {NgxGoogleAnalyticsModule} from "@dustfoundation/ngx-google-analytics";
import {ApiService} from "../../../shared/services/api.service";
import {catchError} from "rxjs";
import {StorageService} from "../../../shared/services/storage.service";
import {User} from "../../../shared/models";
import {TranslateIonicTextsDirective} from "../../../shared/directives/translate-ionic-texts.directive";
import {format, isValid, parseISO, subYears} from "date-fns";

// @ts-ignore
import { hijriToJulian, julianToGregorian} from '../../../shared/helpers/hijri';


@Component({
  selector: 'app-profile-edit',
  templateUrl: 'profile-edit-page.component.html',
  styleUrls: ['profile-edit-page.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    IfIsBrowserDirective,
    NgIf,
    CardComponent,
    NgTemplateOutlet,
    NgForOf,
    AsyncPipe,
    JsonPipe,
    RouterModule,
    DatePipe,
    NgOptimizedImage,
    FormsModule,
    NgxGoogleAnalyticsModule,
    ReactiveFormsModule,
    TranslateIonicTextsDirective,
  ],
})
export class ProfileEditPageComponentPage implements OnInit {
  @Input() returnURL?: string
  public formLoading: boolean = true;
  public isReady: boolean = false;
  public otpUUID?: string;
  errorMessage: string | null = null;
  dateError: string = '';
  age = 0;
  user?: User;
  nowTime: Date = new Date();
  updateForm = this.formBuilder.group({
    name: ['', []],
    family_user_id: ['', []],
    dob: ['', []],
    dobGroup: this.formBuilder.group({
      day: ['', [Validators.required, Validators.min(1), Validators.max(31)]],
      month: ['', [Validators.required, Validators.min(1), Validators.max(12)]],
      year: ['', [Validators.required, Validators.min(1300), Validators.max(this.nowTime.getFullYear())]],
    }),
    linkedin_url: ['', [
      Validators.nullValidator,
    ]],
    phone: ['', [
      //Validators.required,
      Validators.nullValidator,
      Validators.minLength(10)
    ]],
    bio: ['', [
      Validators.minLength(0),
      Validators.maxLength(240)
    ]],
    email: ['', [
      Validators.nullValidator,
      Validators.email,
    ]],
    marital_status: ['', [
      Validators.required
    ]],
    health_status: ['', [
      Validators.required
    ]],
    educational_status: ['', [
      Validators.required
    ]],
    user_region_id: ['', [
      Validators.required,
    ]],
  });


  constructor(@Inject(DOCUMENT) private document: Document,
              @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
              private formBuilder: FormBuilder,
              public profileService: ProfileService,
              public apiService: ApiService,
              public storageService: StorageService,
              private navCtrl: NavController,
              public toastController: ToastController,
              public alertController: AlertController,
              private router: Router,
  ) {

  }

  ngOnInit(): void {
    if (this.isServer) {
      console.log('is server')
    } else {
      this.browserSetup();
    }
    this.fetchUserData();

    this.updateForm.get('dobGroup')?.valueChanges.subscribe(() => {
      this.calculateDate();
    });
  }

  fetchUserData(): void {
    this.otpUUID = undefined
    this.apiService.me().subscribe((res) => {
      this.formLoading = false;
      this.user = res;
      this.isReady = true;
      this.updateForm.controls.name.setValue(res.name);
      this.updateForm.controls.family_user_id.setValue(res.family_user_id);
      this.updateForm.controls.phone.setValue(res.phone);
      this.updateForm.controls.bio.setValue(res.bio);
      this.updateForm.controls.email.setValue(res.email);
      this.updateForm.controls.dob.setValue(res.dob);
      this.updateForm.controls.dobGroup.setValue({
        day: new Date(res.dob).getDate()+'',
        month: new Date(res.dob).getMonth() + 1+'',
        year: new Date(res.dob).getFullYear()+'',
      });
      this.updateForm.controls.linkedin_url.setValue(res.linkedin_url);
      this.updateForm.controls.marital_status.setValue(res.marital_status);
      this.updateForm.controls.health_status.setValue(res.health_status);
      this.updateForm.controls.educational_status.setValue(res.educational_status);
      this.updateForm.controls.user_region_id.setValue(res.user_region_id?.toString() ?? '');
    }, ({error}) => {
      this.apiService.logout().pipe(
        catchError((err) => {
          this.storageService.removeItem('access_token');
          this.storageService.removeItem('expires_at');
          this.storageService.removeItem('user');
          this.profileService.removeUser();
          this.navCtrl.navigateRoot('/login', {
            queryParams: {
              returnURL: window.location.pathname
            }
          }).then();
          return err;
        })
      ).subscribe(
        (res) => {
          this.storageService.removeItem('access_token');
          this.storageService.removeItem('expires_at');
          this.storageService.removeItem('user');
          this.profileService.removeUser();
          this.navCtrl.navigateRoot('/login', {
            queryParams: {
              returnURL: window.location.pathname
            }
          }).then();
        }
      )
    });
  }

  store(otp: string | undefined = undefined) {
    this.errorMessage = null;
    // make updated form touched
    this.updateForm.markAllAsTouched();
    if (this.updateForm.invalid) {
      this.errorMessage = 'الرجاء إكمال جميع البيانات المطلوبة'
      return;
    }

    if (this.otpUUID !== undefined && (
      otp === undefined || otp.length < 4
    )) {
      this.otpAlertInput('invalid-otp')
      return;
    }
    this.formLoading = true;
    this.apiService.updateUserProfile({
      phone: this.updateForm.controls.phone.value,
      bio: this.updateForm.controls.bio.value,
      email: this.updateForm.controls.email.value,
      dob: this.updateForm.controls.dob.value,
      linkedin_url: this.updateForm.controls.linkedin_url.value,
      health_status: this.updateForm.controls.health_status.value,
      marital_status: this.updateForm.controls.marital_status.value,
      educational_status: this.updateForm.controls.educational_status.value,
      user_region_id: this.updateForm.controls.user_region_id.value,
      ...(this.otpUUID !== undefined ? {
        otp: otp,
        'otp-uuid': this.otpUUID,
      } : {})
    }).subscribe(async (res: any) => {
      this.formLoading = false;
      if (res.success === true) {
        this.otpUUID = undefined
        this.toastController.create({
          message: 'تم تحديث البيانات بنجاح',
          position: 'top',
          color: 'success',
          cssClass: 'custom-toast',
          buttons: [{
            side: 'end',
            text: 'إلغاء',
            role: 'cancel',
          }],
        }).then((toast) => {
          toast.present();
          this.apiService.me().subscribe((res) => {
            this.storageService.setItem('user', JSON.stringify(res));
            this.router.navigate([this.returnURL || '/settings']).then(() => window.location.reload());
          })
          setTimeout(() => toast.dismiss(), 5000);
        });
      } else {
        switch (res.action) {
          case 'otp':
          case 'invalid-otp':
            if (res.uuid)
              this.otpUUID = res.uuid
            this.otpAlertInput(res.action)
            break;
          case 'expired-otp':
            this.otpUUID = undefined
            this.errorMessage = 'رمز التحقق منتهي يمكنك إرسال الرمز مرة أخرى'
            break;
        }
      }
    });
  }

  sendOTP() {
    this.otpUUID = undefined
    this.store()
  }

  browserSetup() {

  }

  private async otpAlertInput(action: string) {
    const alert = await this.alertController.create({
      header: 'أدخل رمز التحقق',
      message: action === 'invalid-otp' ? 'رمز التحقق غير صحيح' : undefined,
      buttons: [{text: 'إرسال الرمز', role: 'confirm'}, {text: 'إلغاء', role: 'cancel'}],
      inputs: [{placeholder: 'رمز التحقق', type: 'tel'}]
    });
    await alert.present();
    const {role, data} = await alert.onDidDismiss();
    if (role === 'cancel') {
      window.location.reload();
    } else if (role === 'confirm') {
      this.store(data.values[0]);
    }
  }


  calculateDate() {
    const dobGroup = this.updateForm.get('dobGroup') as FormGroup;

    if (dobGroup.valid) {
      const { day, month, year } = dobGroup.value;
      const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      const date = parseISO(dateStr);

      if (!isValid(date)) {
        this.dateError = 'قم بإدخال تاريخ الميلاد بشكل صحيح رجاء';
        return;
      }
      let gregorianDate = date;

      if(this.isHijriDate(+year)) {
        // convert and handle Hijri to Gregorian if needed
        const hijriToGregorian = this.handleHijriToGregorianConversion(day, month, year);
        gregorianDate = new Date(hijriToGregorian.year, hijriToGregorian.month -1, hijriToGregorian.day);
      }

      const maxDate = subYears(gregorianDate, 100);
      if (gregorianDate < maxDate && year > 1900) {
        this.dateError = 'لا يمكن أن يكون تاريخ الميلاد أكبر من 100 سنة';
        return;
      }

      this.dateError = '';
      this.updateForm.controls.dob.setValue(format(gregorianDate, 'yyyy-MM-dd'));
      this.calculateAge();

    } else {
      this.dateError = 'قم بإدخال تاريخ الميلاد بشكل صحيح رجاء';
    }
  }

  handleHijriToGregorianConversion(day:string, month:string, year:string) {
      const hijriToJulianDate =  hijriToJulian(+year, +month, +day);
    return julianToGregorian(hijriToJulianDate) as { year: number, month: number, day: number };
  }

  isHijriDate(year: number): boolean {
    return year > 1300 && year < 1600;
  }

  calculateAge(): void {
    const dob = this.updateForm.controls.dob;
    if (!dob.value) {
      this.dateError = 'قم بإدخال تاريخ الميلاد بشكل صحيح رجاء';
      return;
    }
    const date = parseISO(dob.value);
    if (!isValid(date)) {
      this.dateError = 'قم بإدخال تاريخ الميلاد بشكل صحيح رجاء';
      return;
    }


    const birthDateObj = new Date(date);
    const currentDate = new Date();

    if (birthDateObj > currentDate) {
      this.age = 0;
      return;
    }

    this.age = currentDate.getFullYear() - birthDateObj.getFullYear();

    const monthDifference = currentDate.getMonth() - birthDateObj.getMonth();
    if (monthDifference < 0 || (monthDifference === 0 && currentDate.getDate() < birthDateObj.getDate())) {
      this.age--;
    }
  }

}
