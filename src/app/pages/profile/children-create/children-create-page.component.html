<app-header [hideProfileIcon]="true" [showBackButton]="true" backTo="/me/children"></app-header>
<ion-content color="light" [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large" class="ion-margin-vertical" style="">البيانات الشخصية</ion-title>
    </ion-toolbar>
  </ion-header>
  <ng-container *ngIf="isReady; else loading">
    <ion-list [inset]="true" [formGroup]="updateForm">
      <ion-item detail="false">
        <ion-label>الأب</ion-label>
        <ion-note>{{ (profileService.$user|async)?.full_name }}</ion-note>
      </ion-item>

      <ion-item class="" style="font-size: 0.8rem">
        <ion-icon slot="start" *ngIf="updateForm.touched && updateForm.controls.gender.invalid"
                  name="alert-circle-outline" color="danger"></ion-icon>
        <ion-select label="الجنس" formControlName="gender" TranslateIonicTexts>
          <ion-select-option value="MALE">ذكر</ion-select-option>
          <ion-select-option value="FEMALE">أنثى</ion-select-option>
        </ion-select>
      </ion-item>

      <ion-item detail="false">
        <ion-icon slot="start" *ngIf="updateForm.touched && updateForm.controls.name.invalid"
                  name="alert-circle-outline" color="danger"></ion-icon>
        <ion-input label="الاسم" formControlName="name"
                   dir="rtl" style="text-align: left;direction: rtl;"
                   class=""></ion-input>
      </ion-item>

      <ion-item class="" style="font-size: 0.8rem" *ngIf="spouses.length > 0">
        <ion-icon slot="start" *ngIf="updateForm.touched && updateForm.controls.mother_id.invalid"
                  name="alert-circle-outline" color="danger"></ion-icon>
        <ion-select label="الأم" formControlName="mother_id" TranslateIonicTexts>
          <ion-select-option [value]="spouse.id"
                             *ngFor="let spouse of spouses;">{{ spouse.full_name }}</ion-select-option>
        </ion-select>
      </ion-item>

      <ion-item detail="false">
        <ion-icon slot="start" *ngIf="updateForm.touched && updateForm.controls.phone.invalid"
                  name="alert-circle-outline" color="danger"></ion-icon>
        <ion-input label="رقم الجوال" formControlName="phone"
                   dir="rtl" style="text-align: left;direction: rtl;"
                   class=""></ion-input>
      </ion-item>

      <ion-item>
        <ion-input label="تاريخ الميلاد"
                   value="{{ (updateForm.controls.dob.value) | date: 'yyyy-MM-dd' }}"
                   id="dobPicker" class="" readonly="true"></ion-input>
        <ion-popover trigger="dobPicker" size="cover">
          <ng-template>
            <ion-datetime presentation="date" [max]="nowTime" formControlName="dob"></ion-datetime>
          </ng-template>
        </ion-popover>
      </ion-item>

      <ion-item detail="false">
        <ion-icon slot="start" *ngIf="updateForm.touched && updateForm.controls.national_id.invalid"
                  name="alert-circle-outline" color="danger"></ion-icon>
        <ion-input label="رقم الهوية" formControlName="national_id"
                   dir="rtl" style="text-align: left;direction: rtl;"
                   class=""></ion-input>
      </ion-item>

      <ion-item style="font-size: 0.8rem">
        <ion-icon slot="start" *ngIf="updateForm.touched && updateForm.get('user_region_id')!.invalid"
                  name="alert-circle-outline" color="danger"></ion-icon>
        <ion-select label="مكان الإقامة" formControlName="user_region_id" TranslateIonicTexts>
          <ion-select-option *ngFor="let region$ of (profileService.$regions | async); let i= index"
                             [value]="region$.id.toString()">{{ region$.title_ar }}
          </ion-select-option>
        </ion-select>
      </ion-item>

      <ion-item class="" style="font-size: 0.8rem">
        <ion-icon slot="start" *ngIf="updateForm.touched && updateForm.controls.marital_status.invalid"
                  name="alert-circle-outline" color="danger"></ion-icon>
        <ion-select label="الحالة الإجتماعية" formControlName="marital_status" TranslateIonicTexts>
          <ion-select-option value="SINGLE">أعزب</ion-select-option>
          <ion-select-option value="MARRIED">متزوج</ion-select-option>
          <ion-select-option value="DIVORCED">مطلق</ion-select-option>
          <ion-select-option value="WIDOWER">أرمل</ion-select-option>
        </ion-select>
      </ion-item>

      <ion-item style="font-size: 0.8rem">
        <ion-icon slot="start" *ngIf="updateForm.touched && updateForm.controls.health_status.invalid"
                  name="alert-circle-outline" color="danger"></ion-icon>
        <ion-select label="الحالة الصحية" formControlName="health_status" TranslateIonicTexts>
          <ion-select-option [value]="status"
                             *ngFor="let status of ['سليم', 'مريض بمرض مزمن', 'معاق']">
            {{ status }}
          </ion-select-option>
        </ion-select>
      </ion-item>

      <ion-item style="font-size: 0.8rem">
        <ion-icon slot="start" *ngIf="updateForm.touched && updateForm.controls.educational_status.invalid"
                  name="alert-circle-outline" color="danger"></ion-icon>
        <ion-select label="التعليم" formControlName="educational_status" TranslateIonicTexts>
          <ion-select-option [value]="status"
                             *ngFor="let status of ['طفل', 'ابتدائي', 'متوسط', 'دبلوم', 'ثانوي', 'جامعي', 'بكالوريوس', 'دكتوراه', 'ماجستير']">
            {{ status }}
          </ion-select-option>
        </ion-select>
      </ion-item>

      <ion-button color="primary" expand="block" mode="ios" style="color: white;"
                  (click)="store()" [disabled]="formLoading">
        <ion-spinner *ngIf="formLoading"></ion-spinner>
        <span *ngIf="!formLoading">إضافة</span>
      </ion-button>
    </ion-list>
  </ng-container>
</ion-content>

<ng-template #loading>
  <ion-list [inset]="true">
    <ion-item detail="false">
      <ion-label>
        <ion-skeleton-text style="width: 40%; height: 20px;"></ion-skeleton-text>
      </ion-label>
      <ion-note>
        <ion-skeleton-text style="width: 30px; height: 20px;"></ion-skeleton-text>
      </ion-note>
    </ion-item>
    <ion-item detail="false">
      <ion-label>
        <ion-skeleton-text style="width: 40%; height: 20px;"></ion-skeleton-text>
      </ion-label>
      <ion-note>
        <ion-skeleton-text style="width: 30px; height: 20px;"></ion-skeleton-text>
      </ion-note>
    </ion-item>
    <ion-item detail="false">
      <ion-label>
        <ion-skeleton-text style="width: 40%; height: 20px;"></ion-skeleton-text>
      </ion-label>
      <ion-note>
        <ion-skeleton-text style="width: 30px; height: 20px;"></ion-skeleton-text>
      </ion-note>
    </ion-item>
    <ion-item detail="false">
      <ion-label>
        <ion-skeleton-text style="width: 40%; height: 20px;"></ion-skeleton-text>
      </ion-label>
      <ion-note>
        <ion-skeleton-text style="width: 30px; height: 20px;"></ion-skeleton-text>
      </ion-note>
    </ion-item>
    <ion-item detail="false">
      <ion-label>
        <ion-skeleton-text style="width: 40%; height: 20px;"></ion-skeleton-text>
      </ion-label>
      <ion-note>
        <ion-skeleton-text style="width: 30px; height: 20px;"></ion-skeleton-text>
      </ion-note>
    </ion-item>
    <ion-item detail="false">
      <ion-label>
        <ion-skeleton-text style="width: 40%; height: 20px;"></ion-skeleton-text>
      </ion-label>
      <ion-note>
        <ion-skeleton-text style="width: 30px; height: 20px;"></ion-skeleton-text>
      </ion-note>
    </ion-item>
    <ion-item detail="false">
      <ion-label>
        <ion-skeleton-text style="width: 40%; height: 20px;"></ion-skeleton-text>
      </ion-label>
      <ion-note>
        <ion-skeleton-text style="width: 30px; height: 20px;"></ion-skeleton-text>
      </ion-note>
    </ion-item>
    <ion-item detail="false">
      <ion-label>
        <ion-skeleton-text style="width: 40%; height: 20px;"></ion-skeleton-text>
      </ion-label>
      <ion-note>
        <ion-skeleton-text style="width: 30px; height: 20px;"></ion-skeleton-text>
      </ion-note>
    </ion-item>
    <ion-item detail="false">
      <ion-label>
        <ion-skeleton-text style="width: 40%; height: 20px;"></ion-skeleton-text>
      </ion-label>
      <ion-note>
        <ion-skeleton-text style="width: 30px; height: 20px;"></ion-skeleton-text>
      </ion-note>
    </ion-item>
  </ion-list>
</ng-template>
