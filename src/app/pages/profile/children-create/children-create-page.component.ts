import {Component, Inject, OnInit} from '@angular/core';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DatePipe,
  DOCUMENT,
  JsonPipe,
  <PERSON>For<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>imizedImage,
  NgTemplateOutlet
} from "@angular/common";
import {IonicModule, ToastController} from "@ionic/angular";
import {HttpClientModule} from "@angular/common/http";
import {Router, RouterModule} from "@angular/router";
import {HeaderComponent} from "../../../layout/header/header.component";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {CardComponent} from "../../../shared/components/card/card.component";
import {IS_SERVER_PLATFORM} from "../../../shared/IS_SERVER_PLATFORM.token";
import {ProfileService} from "../../../shared/services/profile.service";
import {<PERSON><PERSON><PERSON><PERSON>, FormsModule, ReactiveFormsModule, Validators} from "@angular/forms";
import {NgxGoogleAnalyticsModule} from "@dustfoundation/ngx-google-analytics";
import {ApiService} from "../../../shared/services/api.service";
import {StorageService} from "../../../shared/services/storage.service";
import {TranslateIonicTextsDirective} from "../../../shared/directives/translate-ionic-texts.directive";
import {ImgFallbackDirective} from "../../../img-fallback.directive";
import {FormatDistancePipe} from "../../../shared/pipes/format-distance.pipe";
import {KiloFormatterPipe} from "../../../shared/pipes/kilo-formatter-pipe";
import {SecondsFormatterPipe} from "../../../shared/pipes/seconds-formatter-pipe";
import {catchError, finalize} from "rxjs";
import {ChildFormsService} from "../child-forms.service";
import {AppDataService} from "../../../shared/services/app-data.service";

@Component({
  selector: 'app-profile-children-create',
  templateUrl: 'children-create-page.component.html',
  styleUrls: ['children-create-page.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    IfIsBrowserDirective,
    NgIf,
    CardComponent,
    NgTemplateOutlet,
    NgForOf,
    AsyncPipe,
    JsonPipe,
    RouterModule,
    DatePipe,
    NgOptimizedImage,
    FormsModule,
    NgxGoogleAnalyticsModule,
    ReactiveFormsModule,
    TranslateIonicTextsDirective,
    ImgFallbackDirective,
    FormatDistancePipe,
    KiloFormatterPipe,
    SecondsFormatterPipe,
  ],
})
export class ChildrenCreatePageComponent implements OnInit {
  public isReady: boolean = false;
  public formLoading: boolean = false;
  nowTime = new Date().toISOString();
  public spouses: {
    id: number,
    full_name: string,
  }[] = [];
  updateForm = this.formBuilder.group({
    name: ['', [Validators.required]],
    gender: ['MALE', [Validators.required]],
    mother_id: ['', []],
    dob: ['', [Validators.required]],
    phone: ['', []],
    national_id: ['', []],
    marital_status: ['', [Validators.required]],
    health_status: ['', [Validators.required]],
    educational_status: ['', [Validators.required]],
    user_region_id: ['', [Validators.required]],
  });

  constructor(@Inject(DOCUMENT) private document: Document,
              @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
              public profileService: ProfileService,
              public apiService: ApiService,
              public childFormsService: ChildFormsService,
              public formBuilder: FormBuilder,
              public toastController: ToastController,
              public router: Router,
              public appDataService: AppDataService,
              public storageService: StorageService,
  ) {

  }

  ngOnInit(): void {
    if (this.isServer) {
      console.log('is server')
    } else {
      this.browserSetup();
    }
    this.fetchChildrenData()
  }

  fetchChildrenData(): void {
    this.childFormsService.getSpouses().subscribe((data) => {
      this.spouses = data;
      this.isReady = true;
    })
  }

  browserSetup() {

  }

  store() {
    if (!this.updateForm.valid)
      return this.updateForm.markAllAsTouched();
    this.formLoading = true;
    this.childFormsService.store({
      name: this.updateForm.controls.name.value,
      gender: this.updateForm.controls.gender.value,
      mother_id: this.updateForm.controls.mother_id.value,
      dob: this.updateForm.controls.dob.value,
      phone: this.updateForm.controls.phone.value,
      national_id: this.updateForm.controls.national_id.value,
      marital_status: this.updateForm.controls.marital_status.value,
      health_status: this.updateForm.controls.health_status.value,
      user_region_id: this.updateForm.controls.user_region_id.value,
      educational_status: this.updateForm.controls.educational_status.value,
    }).pipe(
      catchError(({error}) => {
        this.toastController.create({
          message: error?.message || 'خطأ !',
          position: 'top',
          color: 'danger',
          cssClass: 'custom-toast',
          buttons: [{
            side: 'end',
            text: 'إلغاء',
            role: 'cancel',
          }],
        }).then((toast) => {
          toast.present();
          setTimeout(() => toast.dismiss(), 5000);
        });
        throw new Error(JSON.stringify(error))
      }),
      finalize(() => this.formLoading = false)
    ).subscribe((res: any) => {
      if (res.success === true) {
        this.toastController.create({
          message: 'تم إضافة الطلب بنجاح',
          position: 'top',
          color: 'success',
          cssClass: 'custom-toast',
          buttons: [{
            side: 'end',
            text: 'إلغاء',
            role: 'cancel',
          }],
        }).then((toast) => {
          toast.present();
          this.router.navigate(['/me/children']);
          setTimeout(() => toast.dismiss(), 5000);
        });
      } else {
        this.toastController.create({
          message: res.message || 'خطأ !',
          position: 'top',
          color: 'danger',
          cssClass: 'custom-toast',
          buttons: [{
            side: 'end',
            text: 'إلغاء',
            role: 'cancel',
          }],
        }).then((toast) => {
          toast.present();
          setTimeout(() => toast.dismiss(), 5000);
        });
      }
    })
  }

  failCallbackImage(event: any) {
    this.appDataService.data$.subscribe(data => {
      event.target.src = (data!['iconPath']) ||  '/assets/icon/logo.svg'
    })
  }
}
