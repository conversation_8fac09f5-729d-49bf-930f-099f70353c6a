import {ChangeDetector<PERSON>ef, Component, inject, OnInit} from '@angular/core';
import {
  Alert<PERSON>ontroller,
  InfiniteScrollCustomEvent,
  IonicModule,
  ModalController,
  ToastController
} from "@ionic/angular";
import {HeaderComponent} from "../../../layout/header/header.component";
import {DatePipe, DOCUMENT, NgForOf, NgIf, NgOptimizedImage} from "@angular/common";
import {FormsModule} from "@angular/forms";
import {RouterLink} from "@angular/router";
import {ImgFallbackDirective} from "../../../img-fallback.directive";
import {ApiService} from "../../../shared/services/api.service";
import {Pagination} from "../../../shared/models";
import {catchError, throwError} from "rxjs";
import {QrCodeScannerComponent} from "../../../shared/components/qr-code-scanner/qr-code-scanner.component";
import {IS_SERVER_PLATFORM} from "../../../shared/IS_SERVER_PLATFORM.token";

@Component({
  selector: 'app-friends',
  templateUrl: 'friends-page.component.html',
  styleUrls: ['friends-page.component.scss'],
  standalone: true,
  imports: [
    HeaderComponent,
    IonicModule,
    NgIf,
    NgForOf,
    FormsModule,
    RouterLink,
    ImgFallbackDirective,
    NgOptimizedImage,
    DatePipe,
  ],
})
export class FriendsPageComponent implements OnInit {
  selectedSegment: 'friends' | 'requests' | 'sent' = 'friends';
  private apiService = inject(ApiService);
  private cdr = inject(ChangeDetectorRef);
  modalCtrl = inject(ModalController);
  alertCtrl = inject(AlertController);
  toastCtrl = inject(ToastController);
  isServer = inject(IS_SERVER_PLATFORM);
  document = inject(DOCUMENT);
  friends: any[] = [];
  friendsLoaded = false;
  friendsPagination: null | Pagination = null;
  sentFriendRequests: any[] = [];
  sentFriendRequestsLoaded = false;
  sentFriendRequestsPagination: null | Pagination = null;
  receivedFriendRequests: any[] = [];
  receivedFriendRequestsLoaded = false;
  receivedFriendRequestsPagination: null | Pagination = null;
  friendsFamilyUserID = '';
  statistics: any = {
    total_friends: 0,
    total_sent_requests: 0,
    total_received_requests: 0,
  }

  constructor() {

  }

  ngOnInit(): void {
    this.getFriends({}, undefined).then(() => {
    });
    this.apiService.getFriendStatistics().subscribe((response) => {
      this.statistics = response;
    });
    if (!this.isServer) {
      this.browserSetup();
    }
  }

  browserSetup() {
  }

  segmentChanged($event: any) {
    if (this.selectedSegment === 'requests' && !this.receivedFriendRequestsLoaded) {
      this.getReceivedFriendRequests({}, undefined).then(() => {
      });
    } else if (this.selectedSegment === 'sent' && !this.sentFriendRequestsLoaded) {
      this.getSentFriendRequests({}, undefined).then(() => {
      });
    }
  }

  async removeFriend(friend: any) {
    let alert = await this.alertCtrl.create({
      header: 'حذف القريب',
      message: `هل تريد حقاً حذف ${friend.name} من قائمة الأقارب؟`,
      buttons: [
        {
          text: 'إلغاء',
          role: 'cancel'
        }, {
          text: 'حذف',
          role: 'destructive',
          handler: () => {
            this.apiService.deleteFriendship(friend.id)
              .pipe(catchError(async (error) => {
                this.toastCtrl.create({
                  message: 'حدث خطأ أثناء حذف القريب',
                  duration: 2000,
                  color: 'danger'
                }).then(toast => toast.present())
                throw error;
              }))
              .subscribe((res) => {
                this.successToast('تم حذف الصداقة بنجاح');
                if (this.statistics.total_sent_requests > 0)
                  this.statistics.total_sent_requests--;
                this.friends = this.friends.filter((f) => f.id !== friend.id);
                this.cdr.detectChanges();
              });
          }
        }]
    });
    alert.present();
  }

  async deleteFriendRequest(request: any) {
    let alert = await this.alertCtrl.create({
      header: 'حذف الطلب المرسل',
      message: `هل تريد حقاً حذف الطلب المرسل لـ ${request.recipient.name} ؟`,
      buttons: [
        {
          text: 'إلغاء',
          role: 'cancel'
        }, {
          text: 'حذف',
          role: 'destructive',
          handler: () => {
            this.apiService.deleteFriendRequest(request.id)
              .pipe(catchError(async (error) => {
                this.toastCtrl.create({
                  message: 'حدث خطأ أثناء حذف الطلب',
                  duration: 2000,
                  color: 'danger'
                }).then(toast => toast.present());
                throw error;
              }))
              .subscribe((res) => {
                this.successToast('تم حذف طلب الصداقة بنجاح');
                if (this.statistics.total_sent_requests > 0)
                  this.statistics.total_sent_requests--;
                this.sentFriendRequests = this.sentFriendRequests.filter((r) => r.id !== request.id);
                this.cdr.detectChanges();
              });
          }
        }]
    });
    alert.present();
  }

  async acceptRequest(request: any) {
    let alert = await this.alertCtrl.create({
      header: 'قبول الطلب',
      message: `هل تريد حقاً قبول طلب ${request.sender.name} ؟`,
      buttons: [
        {
          text: 'إلغاء',
          role: 'cancel'
        }, {
          text: 'قبول',
          role: 'confirm',
          handler: () => {
            this.apiService.updateFriendRequest(request.id, {action: 'accept'})
              .pipe(catchError(async (error) => {
                this.toastCtrl.create({
                  message: 'حدث خطأ أثناء قبول الطلب',
                  duration: 2000,
                  color: 'danger'
                }).then(toast => toast.present());
                throw error;
              }))
              .subscribe((res) => {
                this.successToast('تم قبول طلب الصداقة بنجاح');
                if (this.statistics.total_received_requests > 0)
                  this.statistics.total_received_requests--;
                this.receivedFriendRequests = this.receivedFriendRequests.filter((r) => r.id !== request.id);
                this.handleFriendsRefresh(null);
                this.cdr.detectChanges();
              });
          }
        }]
    });
    alert.present();
  }

  async rejectRequest(request: any) {
    let alert = await this.alertCtrl.create({
      header: 'رفض الطلب',
      message: `هل تريد حقاً رفض طلب ${request.sender.name} ؟`,
      buttons: [
        {
          text: 'إلغاء',
          role: 'cancel'
        }, {
          text: 'رفض',
          role: 'destructive',
          handler: () => {
            this.apiService.updateFriendRequest(request.id, {action: 'reject'})
              .pipe(catchError(async (error) => {
                this.toastCtrl.create({
                  message: 'حدث خطأ أثناء رفض الطلب',
                  duration: 2000,
                  color: 'danger'
                }).then(toast => toast.present());
                throw error;
              }))
              .subscribe((res) => {
                this.successToast('تم رفض طلب الصداقة بنجاح');
                if (this.statistics.total_received_requests > 0)
                  this.statistics.total_received_requests--;
                this.receivedFriendRequests = this.receivedFriendRequests.filter((r) => r.id !== request.id);
                this.cdr.detectChanges();
              });
          }
        }]
    });
    alert.present();
  }

  async cancelAddFriendModal() {
    await this.modalCtrl.dismiss();
  }

  async confirmAddFriendModal(family_user_id: any) {
    this.alertCtrl.create({
      header: 'إرسال طلب صداقة',
      message: `هل تريد إرسال طلب صداقة إلى ${this.friendsFamilyUserID}؟`,
      buttons: [
        {
          text: 'إلغاء',
          role: 'cancel',
          handler: () => {
            this.friendsFamilyUserID = '';
          }
        }, {
          text: 'إرسال',
          role: 'ok',
          handler: () => {
            this.apiService.sendFriendRequest({
              family_user_id
            })
              .pipe(catchError(async (error) => {
                this.toastCtrl.create({
                  message: 'حدث خطأ أثناء إرسال طلب الصداقة',
                  duration: 2000,
                  color: 'danger'
                }).then(toast => toast.present());
                this.friendsFamilyUserID = '';
                throw error;
              }))
              .subscribe((res) => {
                this.successToast('تم إرسال طلب الصداقة بنجاح');
                this.handleSentFriendRequestsRefresh(null);
                this.friendsFamilyUserID = '';
              });
          }
        }]
    }).then(alert => alert.present())
  }

  async successToast(message: string) {
    let toast = await this.toastCtrl.create({
      message: message,
      duration: 2000,
      color: 'success',

    });
    await this.modalCtrl.dismiss();
    await toast.present()
  }

  async scanQRCode() {
    const modal = await this.modalCtrl.create({
      component: QrCodeScannerComponent,
    });
    await modal.present();
    const {data, role} = await modal.onWillDismiss();
    if (data && 'qrCode' in data && data['qrCode'])
      this.apiService.sendFriendRequest({
        family_user_id: data['qrCode']
      })
        .pipe(catchError(async (error) => {
          this.toastCtrl.create({
            message: 'حدث خطأ أثناء إرسال طلب الصداقة',
            duration: 2000,
            color: 'danger'
          }).then(toast => toast.present());
          this.friendsFamilyUserID = '';
          throw error;
        }))
        .subscribe((res) => {
          this.successToast('تم إرسال طلب الصداقة بنجاح');
          this.handleSentFriendRequestsRefresh(null);
          this.friendsFamilyUserID = '';
        });
  }

  handleFriendsRefresh(event: any) {
    this.getFriends({}, undefined, true).then(() => {
      event.target.complete();
    });
  }

  // call api to get more data of timelineActivities, handle infinite scroll and pull to refresh
  getFriends(params: any, next_page?: string, refresh: boolean = false): Promise<void> {
    this.friendsLoaded = false;
    return new Promise((resolve) => {
      this.apiService.getFriendships(params, next_page).subscribe((response) => {
        if (next_page) {
          this.friends = this.friends.concat(response.data);
        } else {
          this.friends = response.data;
        }
        this.friendsPagination = response.pagination;
        this.friendsLoaded = true;
        this.statistics.total_friends = response.pagination.total;
        this.cdr.detectChanges();
        // remove duplicates from members array
        this.friends = this.friends.filter((friend, index, self) => index === self.findIndex((x) => (x.id === friend.id)));
        resolve();
      });
    });
  }

  onFriendsIonInfinite(event: InfiniteScrollCustomEvent) {
    if (this.friendsPagination?.next_url && this.friends.length < this.friendsPagination.total) {
      this.getFriends({}, this.friendsPagination.next_url).then(() => {
        event.target.complete();
      });
    } else {
      event.target.complete();
    }
  }

  handleReceivedFriendRequestsRefresh(event: any) {
    this.getReceivedFriendRequests({}, undefined, true).then(() => {
      event.target.complete();
    });
  }

  // call api to get more data of timelineActivities, handle infinite scroll and pull to refresh
  getReceivedFriendRequests(params: any, next_page?: string, refresh: boolean = false): Promise<void> {
    this.receivedFriendRequestsLoaded = false;
    return new Promise((resolve) => {
      this.apiService.getFriendRequests({
        ...params,
        type: 'received'
      }, next_page).subscribe((response) => {
        if (next_page) {
          this.receivedFriendRequests = this.receivedFriendRequests.concat(response.data);
        } else {
          this.receivedFriendRequests = response.data;
        }
        this.receivedFriendRequestsPagination = response.pagination;
        this.receivedFriendRequestsLoaded = true;
        this.statistics.total_received_requests = response.pagination.total;
        this.cdr.detectChanges();
        // remove duplicates from members array
        this.friends = this.friends.filter((friend, index, self) => index === self.findIndex((x) => (x.id === friend.id)));
        resolve();
      });
    });
  }

  onReceivedFriendRequestsIonInfinite(event: InfiniteScrollCustomEvent) {
    if (this.receivedFriendRequestsPagination?.next_url && this.friends.length < this.receivedFriendRequestsPagination.total) {
      this.getReceivedFriendRequests({}, this.receivedFriendRequestsPagination.next_url).then(() => {
        event.target.complete();
      });
    } else {
      event.target.complete();
    }
  }

  handleSentFriendRequestsRefresh(event: any) {
    this.getSentFriendRequests({}, undefined, true).then(() => {
      event.target.complete();
    });
  }

  // call api to get more data of timelineActivities, handle infinite scroll and pull to refresh
  getSentFriendRequests(params: any, next_page?: string, refresh: boolean = false): Promise<void> {
    this.sentFriendRequestsLoaded = false;
    return new Promise((resolve) => {
      this.apiService.getFriendRequests({
        ...params,
        type: 'sent'
      }, next_page).subscribe((response) => {
        if (next_page) {
          this.sentFriendRequests = this.sentFriendRequests.concat(response.data);
        } else {
          this.sentFriendRequests = response.data;
        }
        this.sentFriendRequestsPagination = response.pagination;
        this.sentFriendRequestsLoaded = true;
        this.statistics.total_sent_requests = response.pagination.total;
        this.cdr.detectChanges();
        // remove duplicates from members array
        this.friends = this.friends.filter((friend, index, self) => index === self.findIndex((x) => (x.id === friend.id)));
        resolve();
      });
    });
  }

  onSentFriendRequestsIonInfinite(event: InfiniteScrollCustomEvent) {
    if (this.sentFriendRequestsPagination?.next_url && this.friends.length < this.sentFriendRequestsPagination.total) {
      this.getSentFriendRequests({}, this.sentFriendRequestsPagination.next_url).then(() => {
        event.target.complete();
      });
    } else {
      event.target.complete();
    }
  }
}
