<div class="ion-page">
  <app-header [hideProfileIcon]="false" [showBackButton]="true" backTo="/settings"
              [leftButtonTemplate]="leftButton"></app-header>
  <ng-template #leftButton>
    <ion-button fill="clear"
                id="open-add-friend-modal"
                class="ion-justify-content-start"
                style="position: absolute; left: -10px;">
      <ion-icon name="person-add-outline" slot="icon-only" color="secondary"></ion-icon>

    </ion-button>
  </ng-template>
  <ion-content>
    <ion-segment [(ngModel)]="selectedSegment" (ngModelChange)="segmentChanged($event)">
      <ion-segment-button value="friends">
        <ion-label>الأقارب</ion-label>
      </ion-segment-button>
      <ion-segment-button value="requests">
        <ion-badge *ngIf="statistics.total_received_requests > 0"
                   style="position: absolute; inset-inline-end: 0; inset-block-end: 4px;">{{ statistics.total_received_requests }}
        </ion-badge>
        <ion-label style="width: 100%">الطلبات</ion-label>
      </ion-segment-button>
      <ion-segment-button value="sent">
        <ion-badge *ngIf="statistics.total_sent_requests > 0"
                   style="position: absolute; inset-inline-end: 0; inset-block-end: 4px;">{{ statistics.total_sent_requests }}
        </ion-badge>
        <ion-label style="width: 100%">المرسلة</ion-label>
      </ion-segment-button>
    </ion-segment>
    <ng-container *ngIf="selectedSegment === 'friends'">
      <ion-refresher slot="fixed" style="margin-top: 10px;" (ionRefresh)="handleFriendsRefresh($event)">
        <ion-refresher-content></ion-refresher-content>
      </ion-refresher>
      <ion-list class="ion-margin-vertical" style="user-select: none;">
        <ion-item *ngFor="let friend of friends">
          <ion-avatar aria-hidden="true" slot="start">
            <img width="120" height="120"
                 [ngSrc]="friend?.profile_photo_url ?? ('/assets/d3-images/' + (friend.gender ?? 'male').toLowerCase() + '-icon.svg')"
                 [appImgFallback]="('/assets/d3-images/' + (friend.gender ?? 'male').toLowerCase() + '-icon.svg')">
          </ion-avatar>
          <ion-label>
            <h4>{{ friend.full_name }}</h4>
            <p>{{ friend.family_user_id }}</p>
          </ion-label>
          <ion-button fill="clear" slot="end" (click)="removeFriend(friend)">
            <ion-icon slot="icon-only" color="danger" name="person-remove"></ion-icon>
          </ion-button>
        </ion-item>
        <ng-container *ngIf="friendsLoaded; else loading"></ng-container>
        <ion-infinite-scroll
          *ngIf="friendsPagination?.next_url"
          #infiniteScroll (ionInfinite)="onFriendsIonInfinite($event)">
          <ion-infinite-scroll-content></ion-infinite-scroll-content>
        </ion-infinite-scroll>
      </ion-list>
    </ng-container>
    <ng-container *ngIf="selectedSegment === 'requests'">
      <ion-refresher slot="fixed" style="margin-top: 10px;" (ionRefresh)="handleReceivedFriendRequestsRefresh($event)">
        <ion-refresher-content></ion-refresher-content>
      </ion-refresher>
      <ion-list class="ion-margin-vertical" style="user-select: none;">
        <ion-item *ngFor="let request of receivedFriendRequests">
          <ion-avatar aria-hidden="true" slot="start">
            <img width="120" height="120"
                 [ngSrc]="request.sender.profile_photo_url ?? ('/assets/d3-images/' + (request.sender.gender ?? 'male').toLowerCase() + '-icon.svg')"
                 [appImgFallback]="('/assets/d3-images/' + (request.sender.gender ?? 'male').toLowerCase() + '-icon.svg')">
          </ion-avatar>
          <ion-label>
            <h4>{{ request.sender.full_name }}</h4>
            <p>{{ request.sender.family_user_id }}</p>
          </ion-label>
          <!--        <p style="position: absolute; inset-block-end: -14px; inset-inline-end: 8px;">{{ request.added_at | date:'fullDate'  }}</p>-->
          <ion-button fill="clear" slot="end" color="secondary" (click)="acceptRequest(request)">
            <ion-icon name="checkmark-circle" slot="end"></ion-icon>
          </ion-button>
          <ion-button fill="clear" slot="end" color="danger" (click)="rejectRequest(request)">
            <ion-icon slot="icon-only" color="danger" name="close-circle"></ion-icon>
          </ion-button>
        </ion-item>
        <ng-container *ngIf="receivedFriendRequestsLoaded; else loading"></ng-container>
        <ion-infinite-scroll
          *ngIf="receivedFriendRequestsPagination?.next_url"
          #infiniteScroll (ionInfinite)="onReceivedFriendRequestsIonInfinite($event)">
          <ion-infinite-scroll-content></ion-infinite-scroll-content>
        </ion-infinite-scroll>
      </ion-list>
    </ng-container>
    <ng-container *ngIf="selectedSegment === 'sent'">
      <ion-refresher slot="fixed" style="margin-top: 10px;" (ionRefresh)="handleSentFriendRequestsRefresh($event)">
        <ion-refresher-content></ion-refresher-content>
      </ion-refresher>
      <ion-list class="ion-margin-vertical" style="user-select: none;">
        <ion-item *ngFor="let request of sentFriendRequests">
          <ion-avatar aria-hidden="true" slot="start">
            <img width="120" height="120"
                 [ngSrc]="request.recipient.profile_photo_url ?? ('/assets/d3-images/' + (request.recipient.gender ?? 'male').toLowerCase() + '-icon.svg')"
                 [appImgFallback]="('/assets/d3-images/' + (request.recipient.gender ?? 'male').toLowerCase() + '-icon.svg')">
          </ion-avatar>
          <ion-label>
            <h4>{{ request.recipient.full_name }}</h4>
            <p>{{ request.recipient.family_user_id }}</p>
          </ion-label>
          <p
            style="position: absolute; inset-block-end: -14px; inset-inline-end: 8px;">{{ request.created_at | date:'fullDate' }}</p>
          <ion-button fill="clear" slot="end" color="danger" (click)="deleteFriendRequest(request)">
            <ion-icon slot="icon-only" color="danger" name="close-circle"></ion-icon>
          </ion-button>
        </ion-item>
        <ng-container *ngIf="sentFriendRequestsLoaded; else loading"></ng-container>
        <ion-infinite-scroll
          *ngIf="sentFriendRequestsPagination?.next_url"
          #infiniteScroll (ionInfinite)="onSentFriendRequestsIonInfinite($event)">
          <ion-infinite-scroll-content></ion-infinite-scroll-content>
        </ion-infinite-scroll>
      </ion-list>
    </ng-container>
    <ion-modal trigger="open-add-friend-modal" [initialBreakpoint]="0.90" [breakpoints]="[0,0.9]">
      <ng-template>
        <ion-header>
          <ion-toolbar>
            <ion-buttons slot="start">
              <ion-button (click)="cancelAddFriendModal()">إلغاء</ion-button>
            </ion-buttons>
            <ion-title>إضافة قريب</ion-title>
            <ion-buttons slot="end">
              <ion-button (click)="confirmAddFriendModal(friendsFamilyUserID)" [strong]="true">أضف</ion-button>
            </ion-buttons>
          </ion-toolbar>
        </ion-header>
        <ion-content class="ion-padding">
          <ion-icon name="letter-message" color="secondary"
                    style="font-size: 180px; text-align: center; width: 100%;"></ion-icon>

          <ion-item>
            <ion-input
              label="أضف الرقم التعريفي للقريب"
              labelPlacement="stacked"
              pattern="[0-9]*"
              maxlength="5"
              inputmode="numeric"
              placeholder="12345"
              [(ngModel)]="friendsFamilyUserID"
            ></ion-input>
          </ion-item>
          <div class="separator">أو</div>

          <!--        QR CODE Scan-->
          <ion-button fill="clear" expand="block" (click)="scanQRCode()" color="secondary">امسح رمز الـ QR</ion-button>
        </ion-content>
      </ng-template>
    </ion-modal>
  </ion-content>
  <ng-template #loading>
    <ion-item *ngFor="let one of [].constructor(3)" class="ion-margin-vertical">
      <ion-thumbnail slot="start">
        <ion-skeleton-text [animated]="true"></ion-skeleton-text>
      </ion-thumbnail>
      <ion-label>
        <h3>
          <ion-skeleton-text [animated]="true" style="width: 80%;"></ion-skeleton-text>
        </h3>
      </ion-label>
    </ion-item>
  </ng-template>
</div>
