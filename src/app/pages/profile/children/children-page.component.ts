import {Component, Inject, OnInit} from '@angular/core';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DatePipe,
  DOCUMENT,
  JsonPipe,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ptimizedImage,
  NgTemplateOutlet
} from "@angular/common";
import {IonicModule} from "@ionic/angular";
import {HttpClientModule} from "@angular/common/http";
import {RouterModule} from "@angular/router";
import {HeaderComponent} from "../../../layout/header/header.component";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {CardComponent} from "../../../shared/components/card/card.component";
import {IS_SERVER_PLATFORM} from "../../../shared/IS_SERVER_PLATFORM.token";
import {ProfileService} from "../../../shared/services/profile.service";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {NgxGoogleAnalyticsModule} from "@dustfoundation/ngx-google-analytics";
import {ApiService} from "../../../shared/services/api.service";
import {StorageService} from "../../../shared/services/storage.service";
import {TranslateIonicTextsDirective} from "../../../shared/directives/translate-ionic-texts.directive";
import {ImgFallbackDirective} from "../../../img-fallback.directive";
import {FormatDistancePipe} from "../../../shared/pipes/format-distance.pipe";
import {KiloFormatterPipe} from "../../../shared/pipes/kilo-formatter-pipe";
import {SecondsFormatterPipe} from "../../../shared/pipes/seconds-formatter-pipe";
import {BehaviorSubject, catchError, finalize, map, of, zip} from "rxjs";
import {ChildFormsService} from "../child-forms.service";
import {FeaturesService} from "../../../shared/services/features.service";
import {AppDataService} from "../../../shared/services/app-data.service";

@Component({
  selector: 'app-profile-children',
  templateUrl: 'children-page.component.html',
  styleUrls: ['children-page.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    IfIsBrowserDirective,
    NgIf,
    CardComponent,
    NgTemplateOutlet,
    NgForOf,
    AsyncPipe,
    JsonPipe,
    RouterModule,
    DatePipe,
    NgOptimizedImage,
    FormsModule,
    NgxGoogleAnalyticsModule,
    ReactiveFormsModule,
    TranslateIonicTextsDirective,
    ImgFallbackDirective,
    FormatDistancePipe,
    KiloFormatterPipe,
    SecondsFormatterPipe,
  ],
})
export class ChildrenPageComponent implements OnInit {
  public isReady: boolean = false;
  errorMessage: string | null = null;
  nowTime = new Date().toISOString();
  private _children$ = new BehaviorSubject<{
    name: string,
    family_user_id?: string,
    profile_photo_url?: string,
    isForm: boolean,
  }[]>([]);
  children$ = this._children$.asObservable()

  constructor(@Inject(DOCUMENT) private document: Document,
              @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
              public profileService: ProfileService,
              public apiService: ApiService,
              public childFormsService: ChildFormsService,
              public storageService: StorageService,
              public appDataService: AppDataService,
              public featuresService: FeaturesService,
  ) {

  }

  ngOnInit(): void {
    if (this.isServer) {
      console.log('is server')
    } else {
      this.browserSetup();
    }
    this.profileService.$user.subscribe(user => {
      if (user && user.gender) {
        this.fetchChildrenData(user.gender)
      }
    });
  }

  fetchChildrenData(gender: string): void {
    zip(
      this.profileService.getChildren(),
      gender === 'MALE' ? this.childFormsService.getForms() : of([])
    ).pipe(
      catchError((res: any) => {
        return [];
      }),
      map(([children, forms]: [any, any]) => {
        this._children$.next([
          ...children.map((d: any) => {
            return {
              name: d.name,
              family_user_id: d.family_user_id,
              profile_photo_url: d.profile_photo_url,
              isForm: false,
            }
          }),
          ...forms.map((d: any) => {
            return {
              name: d.name,
              isForm: true,
            }
          })
        ]);
      }),
      finalize(() => this.isReady = true)
    ).subscribe();
  }

  browserSetup() {

  }

  failCallbackImage(event: any) {
    this.appDataService.data$.subscribe(data => {
      event.target.src = (data!['iconPath']) ||  '/assets/icon/logo.svg'
    })
  }
}
