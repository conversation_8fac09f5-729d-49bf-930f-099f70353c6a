<app-header [hideProfileIcon]="false" [showBackButton]="true" backTo="/settings"></app-header>
<ion-content>
  <ion-grid [fixed]="true">
    <ion-row>
      <ion-col size="12">
        <div style="overflow-y: scroll;overflow-x: hidden;max-width: 370px;margin: auto;">
          <ng-container *ngIf="children$ | async as children;">
            <ion-spinner *ngIf="!isReady" style="margin: auto; width: 100%;height: 40px;"></ion-spinner>
            <ng-container *ngIf="(profileService.$user|async)?.gender === 'MALE' && (featuresService.features$ | async)!['ChildForms'] === true">
              <ion-item [title]="'جديد'" button lines="full" [routerLink]="'/me/children/create'"
                        *ngIf="(profileService.$user|async)?.gender === 'MALE'">
                <ion-avatar slot="start">
                  <ion-img [src]="((appDataService.data$ | async)!['iconPath']) || '/assets/icon/logo.svg'"></ion-img>
                </ion-avatar>
                <ion-label>
                  <h4 class="ion-text-wrap" style="line-height: 1.7 !important;">إضافة ابن</h4>
                </ion-label>
              </ion-item>
              <br>
            </ng-container>
            <ion-item *ngIf="children.length === 0">
              <ion-text color="danger">لا يوجد لديك أبناء مضافين في المشجرة..</ion-text>
            </ion-item>
            <ion-item *ngFor="let child of children; let i = index"
                      [title]="child.name" button lines="full">
              <ion-avatar slot="start">
                <ion-img [src]="child.profile_photo_url || ((appDataService.data$ | async)!['iconPath']) || '/assets/icon/logo.svg'"
                         (ionError)="failCallbackImage($event)"></ion-img>
              </ion-avatar>
              <ion-label>
                <h4 class="ion-text-wrap" style="line-height: 1.7 !important;">
                  {{ child.name }}
                  &nbsp;
                  <ion-text color="danger" *ngIf="child.isForm === true">بانتظار الموافقة</ion-text>
                  <ion-text color="primary" *ngIf="child.family_user_id">#{{ child.family_user_id }}</ion-text>
                </h4>
              </ion-label>
            </ion-item>
          </ng-container>
        </div>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
