import {ChangeDete<PERSON><PERSON><PERSON>, <PERSON>mpo<PERSON>, <PERSON>ement<PERSON><PERSON>, Inject, OnInit, ViewChild} from '@angular/core';
import {
  <PERSON>ync<PERSON><PERSON><PERSON>,
  DatePipe,
  DOCUMENT,
  JsonPipe,
  NgForOf,
  NgI<PERSON>,
  NgOptimizedImage,
  NgTemplateOutlet
} from "@angular/common";
import {ActionSheetController, IonicModule, ModalController, NavController, PopoverController} from "@ionic/angular";
import {HttpClientModule} from "@angular/common/http";
import {RouterModule} from "@angular/router";
import {HeaderComponent} from "../../layout/header/header.component";
import {IfIsBrowserDirective} from "../../shared/IfIsBrowser.directive";
import {CardComponent} from "../../shared/components/card/card.component";
import {IS_SERVER_PLATFORM} from "../../shared/IS_SERVER_PLATFORM.token";
import {ApiService} from "../../shared/services/api.service";
import {catchError, take} from "rxjs";
import {StorageService} from "../../shared/services/storage.service";
import {ProfileService} from "../../shared/services/profile.service";
import {FormsModule} from "@angular/forms";
import html2canvas from "html2canvas";
import {NgxGoogleAnalyticsModule} from "@dustfoundation/ngx-google-analytics";
import {ProfileImageUpload} from "./profile-image-upload";
import {ImgFallbackDirective} from "../../img-fallback.directive";
import {FeaturesService} from "../../shared/services/features.service";

@Component({
  selector: 'app-profile',
  templateUrl: 'profile-page.component.html',
  styleUrls: ['profile-page.component.scss',
    'profile-page.components.less'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    IfIsBrowserDirective,
    NgIf,
    CardComponent,
    NgTemplateOutlet,
    NgForOf,
    AsyncPipe,
    JsonPipe,
    RouterModule,
    DatePipe,
    NgOptimizedImage,
    FormsModule,
    NgxGoogleAnalyticsModule,
    ImgFallbackDirective,
  ],
})
export class ProfilePageComponentPage extends ProfileImageUpload implements OnInit {
  loadingLogout = false;
  familyGraphFullscreen = false;
  @ViewChild('fileInput', {static: false}) fileInput!: ElementRef;

  constructor(@Inject(DOCUMENT) private document: Document,
              @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
              private navCtrl: NavController,
              private cdr: ChangeDetectorRef,
              private popoverController: PopoverController,
              private featuresService: FeaturesService,
              _apiService: ApiService,
              _storageService: StorageService,
              _profileService: ProfileService,
              _modalCtrl: ModalController,
              _actionSheetCtrl: ActionSheetController,
  ) {
    super(_apiService, _storageService, _profileService, _modalCtrl, _actionSheetCtrl);
  }


  ngOnInit(): void {
    this.apiService.me().subscribe((res) => {
      this.profileService.setUser(res);
    })

    if (this.isServer) {
      console.log('is server')
    }

    if (!this.isServer) {
      this.browserSetup();
    }
  }

  browserSetup() {

  }

  logout() {
    this.loadingLogout = true;
    this.apiService.logout().pipe(
      catchError((err) => {
        this.loadingLogout = false;
        this.storageService.removeItem('access_token');
        this.storageService.removeItem('expires_at');
        this.storageService.removeItem('user');
        this.profileService.removeUser();
        this.featuresService.loadFeatures()
        this.navCtrl.navigateRoot('/home').then();
        return err;
      })
    ).subscribe(
      (res) => {
        this.loadingLogout = false;
        this.storageService.removeItem('access_token');
        this.storageService.removeItem('expires_at');
        this.storageService.removeItem('user');
        this.profileService.removeUser();
        this.featuresService.loadFeatures()
        this.navCtrl.navigateRoot('/home').then();
      }
    )
  }


  fullscreenClicked() {
    this.familyGraphFullscreen = !this.familyGraphFullscreen;
    if (this.familyGraphFullscreen) {
      // setTimeout(
      //   () => {
      //     this.drawTree()
      //   }, 1000
      // )
    }
  }

  generateCanvas(width: number, height: number) {
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    const context = canvas.getContext('2d');
    return {canvas, context};
  }

  dismissPopover() {
    this.popoverController.dismiss();
  }

  override getFileInput(): ElementRef<any> {
    return this.fileInput;
  }
}
