.terms-container {
  max-width: 800px;
  margin: 0 auto;

  .header-section {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--ion-color-light-shade);

    .title {
      font-size: 2rem;
      font-weight: bold;
      color: var(--ion-color-primary);
      margin-bottom: 0.5rem;
    }

    .last-updated {
      font-size: 0.9rem;
      color: var(--ion-color-medium);
    }
  }

  .terms-content {
    margin-bottom: 2rem;

    .section {
      margin-bottom: 2rem;
      padding: 1.5rem;
      background: var(--ion-color-light);
      border-radius: 12px;
      transition: transform 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      }

      .section-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--ion-color-primary);
        margin-bottom: 1rem;
      }

      .section-content {
        font-size: 1.05rem;
        line-height: 1.8;
        color: var(--ion-color-dark);
        margin: 0;
        text-align: justify;
        direction: rtl;
      }
    }
  }

  .agreement-section {
    margin-bottom: 2rem;

    ion-card {
      background: var(--ion-color-primary-tint);
      margin: 0;

      ion-card-content {
        padding: 1.5rem;

        .agreement-text {
          font-size: 1.1rem;
          color: var(--ion-color-primary-shade);
          text-align: center;
          margin: 0;
          font-weight: 500;
        }
      }
    }
  }

  .actions-section {
    text-align: center;
    margin-bottom: 2rem;

    ion-button {
      --border-radius: 8px;
      height: 48px;
      font-size: 1rem;
      max-width: 400px;
      margin: 0 auto;
    }
  }
}

// Dark mode styles
@media (prefers-color-scheme: dark) {
  .terms-container {
    .header-section {
      border-bottom-color: var(--ion-color-dark-shade);
    }

    .terms-content {
      .section {
        background: var(--ion-color-step-100);

        .section-content {
          color: var(--ion-color-light);
        }
      }
    }

    .agreement-section {
      ion-card {
        background: var(--ion-color-step-150);

        .agreement-text {
          color: var(--ion-color-light);
        }
      }
    }
  }
}

// Mobile responsiveness
@media (max-width: 768px) {
  .terms-container {
    .header-section {
      .title {
        font-size: 1.5rem;
      }
    }

    .terms-content {
      .section {
        padding: 1rem;

        .section-title {
          font-size: 1.1rem;
        }

        .section-content {
          font-size: 1rem;
        }
      }
    }

    .agreement-section {
      ion-card-content {
        padding: 1rem;

        .agreement-text {
          font-size: 1rem;
        }
      }
    }
  }
}

// RTL Support
:host-context([dir="rtl"]) {
  .terms-container {
    .terms-content {
      .section {
        .section-content {
          text-align: right;
        }
      }
    }
  }
}