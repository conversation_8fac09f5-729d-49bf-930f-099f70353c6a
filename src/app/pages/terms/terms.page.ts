import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { HeaderComponent } from '../../layout/header/header.component';
import { AppDataService } from '../../shared/services/app-data.service';
import { environment } from '../../../environments/environment';

interface TenantTermsContent {
  altwijry: {
    title: string;
    lastUpdated: string;
    sections: Array<{
      title: string;
      content: string;
    }>;
  };
}

@Component({
  selector: 'app-terms',
  templateUrl: './terms.page.html',
  styleUrls: ['./terms.page.scss'],
  standalone: true,
  imports: [
    CommonModule,
    IonicModule,
    HeaderComponent
  ]
})
export class TermsPage {
  currentTenant: string;
  termsContent: any;

  private tenantContent: TenantTermsContent = {
    altwijry: {
      title: 'الشروط والأحكام',
      lastUpdated: '2024-01-01',
      sections: [
        {
          title: '1. المقدمة',
          content: 'مرحبًا بكم في تطبيق صندوق عائلة التويجري. باستخدامك لهذا التطبيق، فإنك توافق على الالتزام بهذه الشروط والأحكام.'
        },
        {
          title: '2. استخدام التطبيق',
          content: 'يحق لأفراد العائلة المسجلين فقط استخدام هذا التطبيق. يجب عليك الحفاظ على سرية معلومات حسابك وعدم مشاركتها مع الآخرين.'
        },
        {
          title: '3. الخصوصية',
          content: 'نحن ملتزمون بحماية خصوصيتك. جميع المعلومات الشخصية التي نجمعها تُستخدم فقط لتحسين خدماتنا وتسهيل التواصل بين أفراد العائلة.'
        },
        {
          title: '4. المحتوى',
          content: 'يجب أن يكون المحتوى المنشور مناسبًا ومحترمًا. نحتفظ بالحق في إزالة أي محتوى غير لائق أو مخالف لقيم العائلة.'
        },
        {
          title: '5. المعاملات المالية',
          content: 'جميع المعاملات المالية التي تتم عبر التطبيق آمنة ومحمية. يتم معالجة المدفوعات عبر بوابات دفع موثوقة.'
        },
        {
          title: '6. حقوق الملكية الفكرية',
          content: 'جميع المحتويات في هذا التطبيق، بما في ذلك النصوص والصور والتصاميم، محمية بحقوق الملكية الفكرية.'
        },
        {
          title: '7. المسؤولية',
          content: 'نسعى لتوفير خدمة موثوقة، ولكننا لا نضمن عدم حدوث انقطاعات أو أخطاء. استخدامك للتطبيق يكون على مسؤوليتك الخاصة.'
        },
        {
          title: '8. التعديلات',
          content: 'نحتفظ بالحق في تعديل هذه الشروط والأحكام في أي وقت. سيتم إشعار المستخدمين بأي تغييرات جوهرية.'
        },
        {
          title: '9. الاتصال',
          content: 'لأي استفسارات حول هذه الشروط والأحكام، يرجى التواصل معنا عبر البريد الإلكتروني: <EMAIL>'
        }
      ]
    }
  };

  constructor(
    protected appDataService: AppDataService
  ) {
    this.currentTenant = this.detectCurrentTenant();
    this.termsContent = this.tenantContent[this.currentTenant as keyof TenantTermsContent];
  }

  private detectCurrentTenant(): string {
    const baseUrl = environment.baseUrl.toLowerCase();
    if (baseUrl.includes('altwijry') || baseUrl.includes('altuwaijri')) {
      return 'altwijry';
    } else if (baseUrl.includes('alhumaid')) {
      return 'alhumaid';
    } else if (baseUrl.includes('algarawi')) {
      return 'algarawi';
    }
    return 'alhumaid'; // default
  }
}
