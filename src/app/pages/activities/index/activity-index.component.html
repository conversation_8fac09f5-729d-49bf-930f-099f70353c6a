<app-header [hideProfileIcon]="false" [showBackButton]="true" backTo="/home"></app-header>
<ion-content>
  <ion-grid [fixed]="true">
    <ion-row>
      <ion-col size="12">
        <ng-container *ngTemplateOutlet="activities"></ng-container>
      </ion-col>
    </ion-row>
    <ion-infinite-scroll (ionInfinite)="onIonInfinite($event)" *ngIf="activitiesNextPageUrl">
      <ion-infinite-scroll-content></ion-infinite-scroll-content>
    </ion-infinite-scroll>
  </ion-grid>
</ion-content>

<ng-template #activities>
  <div style="overflow-y: scroll; overflow-x: hidden; max-width: 370px; margin: auto;">
    <ng-container *ngIf="activities$ | async as activities;">
      <ion-spinner *ngIf="isLoading && activities.length === 0" style="margin: auto; width: 100%;height: 40px;"></ion-spinner>
      <ion-item *ngIf="!isLoading && activities.length === 0">
        <ion-text color="danger">لا يوجد أنشطة في الوقت الحالي</ion-text>
      </ion-item>
      <ion-item *ngFor="let oneActivity of activities; let i = index"
                [title]="oneActivity.title" button
                lines="full"
                [routerLink]="'/activities/' + oneActivity.id">
        <ion-avatar slot="start">
          <ion-img [src]="oneActivity.cover_thumb_url || ((appDataService.data$ | async)!['iconPath']) || '/assets/icon/logo.svg'"
                   (ionError)="failCallbackImage($event)"></ion-img>
        </ion-avatar>
        <ion-label>
          <h4 class="ion-text-wrap" style="line-height: 1.7 !important;">{{oneActivity.title}}</h4>
          <p class="">{{oneActivity.created_at | date:'longDate'}}</p>
        </ion-label>
      </ion-item>
    </ng-container>
  </div>
</ng-template>
