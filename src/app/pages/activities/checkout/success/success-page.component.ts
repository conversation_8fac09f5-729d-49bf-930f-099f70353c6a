import { Component, Inject, OnInit, Input, TransferState } from '@angular/core';
import {AsyncPipe, DatePipe, DOCUMENT, JsonPipe, <PERSON><PERSON>or<PERSON><PERSON>, <PERSON><PERSON>f, NgTemplateOutlet} from "@angular/common";
import {IonicModule} from "@ionic/angular";

import {HttpClientModule} from "@angular/common/http";

import {ActivatedRoute, RouterModule} from "@angular/router";
import {IfIsBrowserDirective} from "../../../../shared/IfIsBrowser.directive";
import {HeaderComponent} from "../../../../layout/header/header.component";
import {CardComponent} from "../../../../shared/components/card/card.component";
import {ApiService} from "../../../../shared/services/api.service";
import {IS_SERVER_PLATFORM} from "../../../../shared/IS_SERVER_PLATFORM.token";
import {take} from "rxjs";

@Component({
  selector: 'app-checkout-success',
  templateUrl: 'success-page.component.html',
  styleUrls: ['success-page.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    IfIsBrowserDirective,
    NgIf,
    CardComponent,
    NgTemplateOutlet,
    NgForOf,
    AsyncPipe,
    JsonPipe,
    RouterModule,
    DatePipe
  ],
})
export class ActivitiesCheckoutSuccessPage implements OnInit {
  @Input('id') activityId?: number;
  @Input() transactionUUID?: string;

  constructor(@Inject(DOCUMENT) private document: Document,
              @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
              private apiService: ApiService,
              private transferState: TransferState,
              private activatedRoute: ActivatedRoute,
  ) {

  }

  ngOnInit(): void {
    if (this.isServer) {
      console.log('is server')
    }

    if (!this.isServer) {
      this.browserSetup();
    }
  }

  browserSetup() {
  }
}
