import { Component, Inject, OnInit, Input, TransferState } from '@angular/core';
import {AsyncPipe, DatePipe, DOCUMENT, JsonPipe, Ng<PERSON>orO<PERSON>, <PERSON><PERSON>f, NgTemplateOutlet} from "@angular/common";
import {IonicModule, NavController} from "@ionic/angular";

import {HttpClientModule} from "@angular/common/http";

import {ActivatedRoute, RouterModule} from "@angular/router";
import {IfIsBrowserDirective} from "../../../../shared/IfIsBrowser.directive";
import {HeaderComponent} from "../../../../layout/header/header.component";
import {CardComponent} from "../../../../shared/components/card/card.component";
import {ApiService} from "../../../../shared/services/api.service";
import {IS_SERVER_PLATFORM} from "../../../../shared/IS_SERVER_PLATFORM.token";
import {take} from "rxjs";

@Component({
  selector: 'app-checkout-failure',
  templateUrl: 'failure-page.component.html',
  styleUrls: ['failure-page.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    IfIsBrowserDirective,
    NgIf,
    CardComponent,
    NgTemplateOutlet,
    NgForOf,
    AsyncPipe,
    JsonPipe,
    RouterModule,
    DatePipe
  ],
})
export class ActivitiesCheckoutFailurePage implements OnInit {
  @Input('id') activityId?: number;
  @Input() transactionUUID?: string;

  constructor(@Inject(DOCUMENT) private document: Document,
              @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
              private apiService: ApiService,
              private transferState: TransferState,
              private activatedRoute: ActivatedRoute,
              private navCtrl: NavController) {

  }

  ngOnInit(): void {
    if (this.isServer) {
      console.log('is server')
    }

    if (!this.isServer) {
      this.browserSetup();
    }
  }

  browserSetup() {
  }

  goBack() {
    this.navCtrl.navigateBack(`activities/${this.activityId}`);
  }
}
