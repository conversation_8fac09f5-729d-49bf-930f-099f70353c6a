import {Component, Inject, Input, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild} from '@angular/core';
import {HeaderComponent} from "../../../layout/header/header.component";
import {AlertController, IonicModule, IonModal, ModalController, NavController, ToastController} from "@ionic/angular";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {HttpClientModule} from "@angular/common/http";
import {Activity} from "../../../shared/models";
import {ApiService} from "../../../shared/services/api.service";
import {catchError, finalize, take, tap} from "rxjs";
import {NavigationExtras, Router, RouterModule} from '@angular/router';
import {AsyncPipe, DatePipe, DecimalPipe, DOCUMENT, NgForOf, NgIf} from "@angular/common";
import {OverlayEventDetail} from "@ionic/core/components";
import {environment} from "../../../../environments/environment";
import {RenderBlockPipe} from "../../../shared/pipes/render-block.pipe";
import {CardComponent} from "../../../shared/components/card/card.component";
import {FormatDistancePipe} from "../../../shared/pipes/format-distance.pipe";
import {OneCardComponent} from "../../eid-cards/one-card/one-card.component";
import {ProfileService} from "../../../shared/services/profile.service";
import {FeaturesService} from "../../../shared/services/features.service";
import {IS_SERVER_PLATFORM} from "../../../shared/IS_SERVER_PLATFORM.token";
import {GenericService} from "../../../shared/services/generic.service";
import {activityTermsAndConditionsComponent} from "../terms-and-condition/terms-and-conditions.component";
import {NotificationService} from "../../../shared/services/notification.service";
import {Browser} from '@capacitor/browser';
import {AppDataService} from "../../../shared/services/app-data.service";

declare var myFatoorahAP: any;
declare var myFatoorah: any;

@Component({
  selector: 'app-activity-details',
  templateUrl: './activity-details.component.html',
  styleUrls: ['./activity-details.component.scss'],
  standalone: true,
  imports: [
    AsyncPipe,
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    IfIsBrowserDirective,
    NgForOf,
    NgIf,
    RenderBlockPipe,
    DatePipe,
    CardComponent,
    FormatDistancePipe,
    OneCardComponent,
    DecimalPipe,
    RouterModule
  ]
})
export class ActivityDetailsComponent implements OnInit, OnDestroy {
  @ViewChild('payModal') modal!: IonModal;
  @Input() activity?: Activity;
  @Input('id') activityId?: number;
  formLoading = false;
  env = environment;
  showPaymentsSpinner: boolean = false
  creditCardProcessing: boolean = false
  transactionUUID = '';
  myFatoorahAppleSessionId = '';
  myFatoorahCreditCardSessionId = '';
  public childrenAlertButtons = [
    {text: 'إلغاء', role: 'cancel'},
    {
      text: 'تأكيد', role: 'confirm',
      handler: (data: any) => {
        if (data.length === 0) {
          // Show an error message if no inputs are checked
          this.toastController.create({
            message: 'يجب اختيار ابن واحد على الأقل.',
            duration: 2000,
            color: 'danger'
          }).then(toast => toast.present());
          return false; // Prevent the alert from dismissing
        }
        return true; // Allow the alert to dismiss
      }
    },
  ];
  public childrenAlertInputs = [];
  private channelName!: string
  private isCapacitorApp = false

  constructor(private apiService: ApiService,
              @Inject(DOCUMENT) private document: Document,
              @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
              public profileService: ProfileService,
              public featuresService: FeaturesService,
              private navCtrl: NavController,
              private modalCtrl: ModalController,
              public genericService: GenericService,
              private alertController: AlertController,
              private toastController: ToastController,
              @Inject('Window') private window: Window,
              private notificationService: NotificationService,
              public appDataService: AppDataService,
              private router: Router) {
    this.isCapacitorApp = this.genericService.isCapacitorApp()
  }

  get canAppliedChildren() {
    return this.activity?.children?.filter((child: any) => child.can_apply || child.dob_empty)
  }

  get pendingChildrenRequest() {
    return this.activity?.children?.filter((child: any) => child.has_request && !child.is_member)
  }

  loadActivityData() {
    if (this.activityId)
      this.getActivityById(this.activityId).subscribe({
        error: (error) => {
          if (error.status === 404)
            this.router.navigate(['/activities'])
        }
      });
  }

  getActivityById(id: number) {
    return this.apiService.checkAndGetData(
      this.apiService.getDynamicStateKey(`activity-details-${this.activityId}`),
      this.apiService.getActivityById(this.activityId ?? 0),
      []
    ).pipe(take(1), tap((data: Activity) => {
      this.activity = data;
      this.childrenAlertInputs = this.activity?.children?.map(function (child: any) {
        return {
          label: child.name + (function (child: any): string {
            if (child.is_member)
              return ' (مشارك)';
            if (child.has_request)
              return ' (مسجل)';
            if (child.dob_empty)
              return ' (تاريخ الميلاد مفقود)';
            if (!child.can_apply)
              return ' (لم تنطبق الشروط)';
            return '';
          })(child),
          type: 'checkbox',
          value: child.id,
          checked: false,
          disabled: !child.can_apply,
        }
      })
    }))
  }

  ngOnInit() {
    if (!this.isServer) {
      this.browserSetup()
    }
    this.loadActivityData()
  }

  ngOnDestroy(): void {
    if (this.channelName && this.notificationService.Echo) {
      this.notificationService.Echo.leave(this.channelName)
      //this.notificationService.Echo.disconnect()
    }
  }

  browserSetup() {
  }

  failCallbackImage(event: any) {
    this.appDataService.data$.subscribe(data => {
      event.target.src = (data!['iconPath']) ||  '/assets/icon/logo.svg'
    })
  }

  sendApplyRequest() {
    if (this.activity) {
      this.formLoading = true;
      this.apiService.applyActivityRequestById(this.activity.id)
        .pipe(finalize(() => this.formLoading = false))
        .subscribe(async (res) => {
          if (res.success === true) {
            if (res.action)
              switch (res.action) {
                case 'pay':
                  if (!this.isServer) {
                    if (this.isCapacitorApp) {
                      const uuid = res['uuid'];
                      await Browser.open({
                        url: `${environment.appURL}/pay?uuid=${uuid}`,
                        presentationStyle: 'popover',
                      });
                      this.formLoading = false
                      this.channelName = `ProductTransaction.${uuid}`
                      if (this.notificationService.Echo)
                        this.notificationService.Echo.channel(this.channelName).listen('PaymentTransactionStatusUpdated', (data: any) => {
                          let navigationExtras: NavigationExtras = {
                            queryParams: {
                              transactionUUID: uuid
                            }
                          };
                          try {
                            if (data['status'] === 'SUCCESS')
                              this.navCtrl.navigateForward(`/activities/${this.activity?.id}/checkout/success`, navigationExtras).then()
                            else
                              this.navCtrl.navigateForward(`/activities/${this.activity?.id}/checkout/failure`, navigationExtras).then()
                          } catch {
                            this.navCtrl.navigateForward(`/activities/${this.activity?.id}/checkout/failure`, navigationExtras).then()
                          }
                          try {
                            Browser.close()
                          } catch {
                          }
                          if (this.channelName)
                            this.notificationService.Echo.leave(this.channelName)
                          this.showPaymentsSpinner = false;
                        });
                    } else
                      this.modal.present().then(() => {
                        this.transactionUUID = res['uuid'];
                        this.myFatoorahAppleSessionId = res['apple_session_id'];
                        this.myFatoorahCreditCardSessionId = res['session_id'];
                        this.initiateApplePayPayment();
                        this.initiateCreditCardPayment();
                        this.showPaymentsSpinner = false;
                        this.formLoading = false
                      })
                  } else {
                    // todo alert
                  }
                  break;
              }
            else
              this.loadActivityData()
          }
        })
    }
  }

  async applyRequest() {
    if (this.activity) {
      if (!this.activity.terms_and_conditions_required) {
        this.sendApplyRequest()
      } else {
        const modal = await this.modalCtrl.create({
          component: activityTermsAndConditionsComponent,
          componentProps: {
            activity: this.activity
          }
        });
        await modal.present();
        const {data, role} = await modal.onWillDismiss();
        if (role === 'confirm')
          this.sendApplyRequest()
      }
    }
  }

  onWillDismiss(event: Event) {
    const ev = event as CustomEvent<OverlayEventDetail<string>>;
    if (ev.detail.role === 'confirm') {

    }
  }

  cancel() {
    this.transactionUUID = '';
    this.myFatoorahAppleSessionId = '';
    this.myFatoorahCreditCardSessionId = '';
    this.showPaymentsSpinner = false;
    this.modal.dismiss(null, 'cancel');
  }

  payWithCreditCard() {
    this.creditCardProcessing = true;

    (this.window as any).myFatoorah.submit().then(
      (response: { sessionId: string, cardBrand: string }) => {
        // In case of success
        // Here you need to pass session id to you backend here
        const sessionId = response.sessionId;
        const cardBrand = response.cardBrand; //cardBrand will be one of the following values: Master, Visa, Mada, Amex
        this.modal.dismiss().then();
        let navigationExtras: NavigationExtras = {
          queryParams: {
            transactionUUID: this.transactionUUID
          }
        };
        this.apiService.getInvoiceFromPaymentSessionId(this.transactionUUID, sessionId).subscribe((res: any) => {
          if (res['success'] === true)
            window.location = res['invoice_url'];
          else
            this.navCtrl.navigateForward(`/activities/${this.activity?.id}/checkout/failure`, navigationExtras).then()
        });
        this.creditCardProcessing = false;
      }
    ).catch(
      (error: any) => {
        // In case of errors
        console.error(error);
        this.creditCardProcessing = false;
      }
    );
  }

  confirmChildrenSelect(data: any) {
    if (this.activity) {
      this.formLoading = true;
      this.apiService.applyActivityRequestById(this.activity.id, data)
        .pipe(
          finalize(() => this.formLoading = false),
          catchError(({error}) => {
            this.loadActivityData()
            let errorMessage = error.message || 'خطأ في التسجيل .';
            this.toastController.create({
              header: 'خطأ',
              message: errorMessage,
              position: 'bottom',
              color: 'danger',
              buttons: [{
                side: 'end',
                text: 'x',
                role: 'cancel',
              }]
            }).then((toast) => {
              toast.present();
            });
            throw new Error(JSON.stringify(error))
          })
        )
        .subscribe(async (res) => {
          if (res.success === true) {
            if (res.action)
              switch (res.action) {
                case 'pay':
                  if (!this.isServer) {
                    if (this.isCapacitorApp) {
                      const uuid = res['uuid'];
                      await Browser.open({
                        url: `${environment.appURL}/pay?uuid=${uuid}`,
                        presentationStyle: 'popover',
                      });
                      this.formLoading = false
                      this.channelName = `ProductTransaction.${uuid}`
                      if (this.notificationService.Echo)
                        this.notificationService.Echo.channel(this.channelName).listen('PaymentTransactionStatusUpdated', (data: any) => {
                          let navigationExtras: NavigationExtras = {
                            queryParams: {
                              transactionUUID: uuid
                            }
                          };
                          try {
                            if (data['status'] === 'SUCCESS')
                              this.navCtrl.navigateForward(`/activities/${this.activity?.id}/checkout/success`, navigationExtras).then()
                            else
                              this.navCtrl.navigateForward(`/activities/${this.activity?.id}/checkout/failure`, navigationExtras).then()
                          } catch {
                            this.navCtrl.navigateForward(`/activities/${this.activity?.id}/checkout/failure`, navigationExtras).then()
                          }
                          try {
                            Browser.close()
                          } catch {
                          }
                          if (this.channelName)
                            this.notificationService.Echo.leave(this.channelName)
                          this.showPaymentsSpinner = false;
                        });
                    } else
                      this.modal.present().then(() => {
                        this.transactionUUID = res['uuid'];
                        this.myFatoorahAppleSessionId = res['apple_session_id'];
                        this.myFatoorahCreditCardSessionId = res['session_id'];
                        this.initiateApplePayPayment();
                        this.initiateCreditCardPayment();
                        this.showPaymentsSpinner = false;
                        this.formLoading = false
                      })
                  } else {
                    // todo alert
                  }
                  break;
              }
            else
              this.loadActivityData()
          }
        })
    }
  }

  async childrenApplyRequestModal() {
    const alert = await this.alertController.create({
      header: 'اختر الأبناء',
      subHeader: 'حدد الأبناء الذين تنطبق عليهم الشروط.',
      cssClass: 'children-popup',
      buttons: this.childrenAlertButtons,
      inputs: this.childrenAlertInputs,
      backdropDismiss: false,
    })
    await alert.present();
    const {data, role} = await alert.onWillDismiss();
    if (role === 'confirm') {
      const values = data.values;
      const confirm = await this.alertController.create({
        header: 'متأكد؟',
        message: 'تأكيد تسجيل "' + values.length + '" الابناء بالبرنامج ' + (this.activity?.request_paid ? (
          'بمبلغ ' + (this.activity?.request_price * values.length) + ' ر.س؟'
        ) : '؟'),
        buttons: [
          {
            text: 'إلغاء',
            role: 'cancel',
            cssClass: 'secondary'
          }, {
            text: 'تاكيد',
            handler: () => {
              this.confirmChildrenSelect(values);
            }
          }
        ]
      });
      await confirm.present();
    }
  }

  async childrenApplyRequest() {
    if (this.activity) {
      if (!this.activity.terms_and_conditions_required) {
        await this.childrenApplyRequestModal()
      } else {
        const modal = await this.modalCtrl.create({
          component: activityTermsAndConditionsComponent,
          componentProps: {
            activity: this.activity
          }
        });
        await modal.present();
        const {data, role} = await modal.onWillDismiss();
        if (role === 'confirm')
          await this.childrenApplyRequestModal()
      }
    }
  }

  initiateApplePayPayment() {
    this.showPaymentsSpinner = true;

    const config = {
      sessionId: this.myFatoorahAppleSessionId, // Here you add the "SessionId" you receive from InitiateSession Endpoint.
      countryCode: environment.myfatoorah.countryCode,
      currencyCode: environment.myfatoorah.currencyCode,
      amount: this.activity?.request_price,
      cardViewId: "card-applepay",
      Language: "ar",
      callback: ((response: any) => {
        this.showPaymentsSpinner = true;
        this.apiService.getInvoiceFromPaymentSessionId(this.transactionUUID, response.sessionId).subscribe((res: any) => {
          this.modal.dismiss().then();
          let navigationExtras: NavigationExtras = {
            queryParams: {
              transactionUUID: this.transactionUUID
            }
          };
          if (res['success'] === true)
            this.navCtrl.navigateForward(`/activities/${this.activity?.id}/checkout/success`, navigationExtras).then()
          else
            this.navCtrl.navigateForward(`/activities/${this.activity?.id}/checkout/failure`, navigationExtras).then()
          this.showPaymentsSpinner = false;
        });
      }),
      sessionStarted: ((sessionStarted: any) => {
        this.showPaymentsSpinner = true;
      }),
      sessionCanceled: ((sessionCanceled: any) => {
        // this.showPaymentsSpinner = false;
      }),
    };
    myFatoorahAP.init(config);
  }

  initiateCreditCardPayment() {
    this.showPaymentsSpinner = true;
    const config = {
      sessionId: this.myFatoorahCreditCardSessionId, // Here you add the "SessionId" you receive from InitiateSession Endpoint.
      countryCode: environment.myfatoorah.countryCode,
      currencyCode: environment.myfatoorah.currencyCode,
      amount: this.activity?.request_price,
      cardViewId: "credit-card",
      Language: "ar",
      callback: ((response: any) => {
      }),
      sessionStarted: ((sessionStarted: any) => {
        this.showPaymentsSpinner = true;
      }),
      sessionCanceled: ((sessionCanceled: any) => {
        // this.showPaymentsSpinner = false;
      }),
    };

    myFatoorah.init(config);
  }
}
