<div class="ion-page">
  <app-header [hideProfileIcon]="false" [showBackButton]="true" backTo="/activities"></app-header>
  <ion-content>
    <ion-spinner *ngIf="!activity" style="margin: auto; width: 100%;height: 40px;"></ion-spinner>
    <ng-container *ngIf="activity">
      <ion-grid [fixed]="true" class="ion-align-items-center ion-margin-bottom">
        <ion-card mode="ios" style="box-shadow: unset; border:solid 1px var(--ion-color-secondary, #113665); max-width: 370px; margin: auto">
          <ion-card-header>
            <ion-card-title>{{ activity.title }}</ion-card-title>
            <ion-text color="primary">
              <h6>
                <ion-row>
                  <ion-col class="ion-margin-start ion-no-padding">
                    {{ activity.start_at | date:'longDate' }}
                  </ion-col>
                  <ion-col size="auto" class="ion-no-padding"> ←</ion-col>
                  <ion-col class="ion-margin-end ion-no-padding" style="text-align: left">
                    {{ activity.end_at | date:'longDate' }}
                  </ion-col>
                </ion-row>
              </h6>
            </ion-text>
          </ion-card-header>
          <ion-card-content>
            <ion-row class="ion-justify-content-center ion-align-items-center">
              <ion-col class="ion-justify-content-center ion-align-items-center">
                <div *ngIf="activity.description" style="display: inline-block"
                     [innerHTML]="activity.description"></div>
                <ion-img [src]="activity.cover_url || ((appDataService.data$ | async)!['iconPath']) || '/assets/icon/logo.svg'" style="margin-top: 10px;"
                         (ionError)="failCallbackImage($event)"></ion-img>
              </ion-col>
            </ion-row>
            <ion-col class="ion-justify-content-center ion-align-items-center"
                     *ngIf="activity?.accepting_requests && activity.can_apply">
              <ion-button color="primary" expand="block" mode="ios" style="color: white;"
                          (click)="applyRequest()" [disabled]="formLoading">
                <ion-spinner *ngIf="formLoading"></ion-spinner>

                <ng-container *ngIf="!formLoading">
                  <ng-container>
                    <span>سجّل اهتمامك</span>
                  </ng-container>
                  <span *ngIf="activity?.request_paid">
                    &nbsp;
                    ( {{ (activity?.request_price | number)?.toString() + ' ر.س' }} )
                  </span>
                </ng-container>
              </ion-button>
            </ion-col>
            <ion-col class="ion-justify-content-center ion-align-items-center"
                     *ngIf="!(profileService.$user | async)?.dob && (!activity.can_apply && activity?.accepting_requests)">
              <ion-button color="primary" expand="block" mode="ios" style="color: white;"
                          routerLink="/me/edit" [queryParams]="{returnURL: '/activities/' + activity?.id}">
                <ion-spinner *ngIf="formLoading"></ion-spinner>
                <span *ngIf="!formLoading">يرجى إدخال تاريخ الميلاد للتسجيل في البرنامج</span>
              </ion-button>
            </ion-col>
            @if (activity?.children && childrenAlertInputs.length > 0) {
              @if (canAppliedChildren.length > 0) {
                <ion-col class="ion-justify-content-center ion-align-items-center">
                  <ion-button color="primary" expand="block" mode="ios" style="color: white;"
                              (click)="childrenApplyRequest()" [disabled]="formLoading">
                    <ion-spinner *ngIf="formLoading"></ion-spinner>

                    <ng-container *ngIf="!formLoading">
                      <ng-container>
                        <span>تسجّيل الأبناء</span>
                      </ng-container>
                      <span *ngIf="activity?.request_paid">
                    &nbsp;
                    ( {{ (activity?.request_price | number)?.toString() + ' ر.س' }} / {{ 'للإبن' }} )
                  </span>
                    </ng-container>
                  </ion-button>
                </ion-col>
              } @else if (pendingChildrenRequest.length > 0) {
                <ion-col class="ion-justify-content-center ion-align-items-center ion-margin-top">
                  <ion-text color="secondary" class="center" style="font-size: 14px;">
                    <b>شكرا لك، تم تسجيل كل الأبناء، سنتواصل معك قريبا..</b>
                  </ion-text>
                </ion-col>
              }
            }
          </ion-card-content>
        </ion-card>
      </ion-grid>
      <div class="ion-margin-bottom"
           *ngIf="(featuresService.features$ | async)!['ActivitySupportAmount'] === true">
        <app-card title="إحصائيات" [showExpandButton]="false" style="height: 100%" [scrollable]="false">
          <ion-item>
            <ion-label>قيمة البرنامج</ion-label>
            <ion-text color="primary">{{ activity.support_amount | number }} ر.س</ion-text>
          </ion-item>
          <ion-item>
            <ion-label>قيمة الدعم</ion-label>
            <ion-text color="primary">{{ activity.sponsors_support_amount | number }} ر.س</ion-text>
          </ion-item>
          <ion-item>
            <ion-label>المتبقي</ion-label>
            <ion-text color="primary">{{ activity.remaining | number }} ر.س</ion-text>
          </ion-item>
        </app-card>
      </div>
      <div class="ion-margin-bottom" *ngIf="(activity?.sponsors ?? []).length > 0">
        <app-card title="الرعاة" [showExpandButton]="false" style="height: 100%" [scrollable]="false">
          <ng-container *ngFor="let sponsor of activity?.sponsors; let i= index">
            <ion-item>
              <ion-img [src]="sponsor?.photo_url || ((appDataService.data$ | async)!['iconPath']) || '/assets/icon/logo.svg'"
                       style="margin-top: 10px;width: 32px;height: 32px;"
                       (ionError)="failCallbackImage($event)"></ion-img>
              &nbsp;
              {{ sponsor.title }}
            </ion-item>
          </ng-container>
        </app-card>
      </div>
      <div class="ion-margin-bottom">
        <app-card title="المشاركين" [showExpandButton]="false" style="height: 100%" [scrollable]="false">
          <ng-container *ngIf="(activity?.hide_members_list && activity?.members?.length === 0); else membersTemplate">
            <ng-container *ngIf="profileService.$user | async as user">
              <ion-row *ngIf="(activity?.current_member_roles ?? []).length > 0">
                <ion-col class="ion-text-center" style="font-size: 18px;">
                  <ion-icon name="altwijry-checked" color="success" class="success-icon"></ion-icon>
                  &nbsp;
                  <span>
                    {{ user?.gender === 'MALE' ? 'ابن' : 'ابنة' }}
                    العم
                    {{ user?.name }}
                    ،أنت
                    {{ user?.gender === 'MALE' ? 'مشترك' : 'مشتركة' }}
                    في
                    {{ activity?.title }}،
                    {{ (activity?.current_member_roles ?? []).length > 1 ? 'في أدوار' : 'في دور' }}
                    {{ (activity?.current_member_roles ?? []).join(' | ') }}
                  </span>
                </ion-col>
              </ion-row>
              <ion-col class="ion-justify-content-center ion-align-items-center"
                       *ngIf="activity?.current_user_request">
                <ion-text color="secondary" class="center" style="font-size: 18px;">
                  <b>شكرا لك، تم تسجيل اهتمامك، سنتواصل معك قريبا..</b>
                </ion-text>
              </ion-col>
              <ion-row *ngIf="!activity?.current_user_request && (activity?.current_member_roles ?? []).length === 0">
                <ion-col class="ion-text-center" style="font-size: 18px;">
                  <ion-icon name="altwijry-cancel" color="danger" class="failure-icon"></ion-icon>
                  &nbsp;
                  <span>
                    {{ user?.gender === 'MALE' ? 'ابن' : 'ابنة' }}
                    العم
                    {{ user?.name }}
                    ،أنت غير
                    {{ user?.gender === 'MALE' ? 'مشترك' : 'مشتركة' }}
                    في
                    {{ activity?.title }}
                  </span>
                </ion-col>
              </ion-row>
            </ng-container>
          </ng-container>
          <ng-template #membersTemplate>
            <ng-container *ngIf="(activity?.members ?? []).length > 0; else emptyMembersTemplate">
              <ng-container *ngFor="let member of activity?.members; let i= index">
                <ion-item>
                  <ion-label>{{ member.user }}</ion-label>
                  <ion-text color="primary">{{ member.role }}</ion-text>
                </ion-item>
              </ng-container>
            </ng-container>
          </ng-template>
          <ng-template #emptyMembersTemplate>
            <div class="ion-text-center">
              <ion-text color="danger">لا يوجد مشاركين بالبرنامج</ion-text>
            </div>
          </ng-template>
        </app-card>
      </div>
    </ng-container>
    <ion-modal *ifIsBrowser
               #payModal
               (willDismiss)="onWillDismiss($event)"
               mode="ios"
               [initialBreakpoint]="0.75"
               [breakpoints]="[0.75]"
               (ionModalDidDismiss)="cancel()">
      <ng-template>
        <ion-header>
          <ion-toolbar>
            <ion-buttons slot="start">
              <ion-button (click)="cancel()">ألغ</ion-button>
            </ion-buttons>
            <ion-title color="primary">الدفع</ion-title>
          </ion-toolbar>
        </ion-header>
        <ion-content class="ion-padding">
          <ion-spinner *ngIf="showPaymentsSpinner"
                       style="margin: auto; width: 100%;height: 40px;"></ion-spinner>
          <div id="card-applepay" class="ion-margin-top" [hidden]="showPaymentsSpinner"></div>
          <div class="altwijry-delimiter" *ngIf="!creditCardProcessing"></div>
          <div id="credit-card" [hidden]="showPaymentsSpinner"></div>

          <ion-button *ngIf="myFatoorahCreditCardSessionId"
                      color="secondary"
                      expand="block"
                      class="ion-margin-top"
                      [hidden]="showPaymentsSpinner"
                      (click)="payWithCreditCard()">
            <ion-spinner *ngIf="creditCardProcessing"></ion-spinner>
            <span *ngIf="!creditCardProcessing" [hidden]="showPaymentsSpinner">ادفع</span>
          </ion-button>
        </ion-content>
      </ng-template>
    </ion-modal>
  </ion-content>
</div>
