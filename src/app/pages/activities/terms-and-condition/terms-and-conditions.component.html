<ion-content>
  <ion-grid [fixed]="true" class="ion-align-items-center">
    <ion-card mode="ios" style="box-shadow: unset; border:solid 1px var(--ion-color-primary, #113665); margin: auto">
      <ion-card-content>
        <ion-spinner *ngIf="!activity; else terms"
                     style="margin: auto; width: 100%;height: 40px;"></ion-spinner>
        <ng-template #terms>
          <ion-row class="ion-justify-content-center ion-align-items-center"
                   *ngFor="let oneBlock of activity?.terms_and_conditions?.blocks; let i = index">
            <ion-col class="ion-justify-content-center ion-align-items-center">
              <pre *ngIf="oneBlock.type === 'code'"> {{ oneBlock.data.code }}</pre>
              <ion-item-divider *ngIf="oneBlock.type === 'delimiter'"></ion-item-divider>
              <div *ngIf="oneBlock.type !== 'code' && oneBlock.type !== 'delimiter'" style="display: inline-block"
                   [innerHTML]="oneBlock | renderBlock"></div>
            </ion-col>
          </ion-row>
        </ng-template>
      </ion-card-content>
    </ion-card>
    <ion-row class="ion-justify-content-center">
      <ion-col size="12" class="ion-justify-content-center ion-text-center ion-align-items-center"
               style="display: flex">
        <ion-button color="secondary" expand="block" mode="ios" style="color: white;min-width: 100%;"
                    (click)="confirm()" [disabled]="isLoading">
          <ion-spinner *ngIf="isLoading"></ion-spinner>

          <ng-container *ngIf="!isLoading">
            <span>الموافقة على الشروط والتسجيل</span>
            <span *ngIf="activity?.request_paid">
              &nbsp;
              ( {{ (activity?.request_price | number)?.toString() + ' ر.س' }} )
            </span>
          </ng-container>
        </ion-button>
      </ion-col>
      <ion-col size="12" class="ion-justify-content-center ion-text-center ion-align-items-center"
               style="display: flex">
        <ion-button color="primary" expand="block" mode="ios" style="color: white;min-width: 100%;"
                    (click)="cancel()" [disabled]="isLoading">
          <ion-spinner *ngIf="isLoading"></ion-spinner>
          <ng-container *ngIf="!isLoading">
            <span>الرجوع</span>
          </ng-container>
        </ion-button>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
