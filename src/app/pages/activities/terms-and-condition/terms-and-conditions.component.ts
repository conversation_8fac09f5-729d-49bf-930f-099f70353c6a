import {IonicModule, ModalController} from "@ionic/angular";
import {Component, Input} from "@angular/core";
import {FormsModule} from "@angular/forms";
import {AsyncPipe, DatePipe, CurrencyPipe, DecimalPipe, NgIf, <PERSON><PERSON><PERSON>, NgOptimizedImage} from "@angular/common";
import {RouterLink} from "@angular/router";
import {RenderBlockPipe} from "../../../shared/pipes/render-block.pipe";
import {Activity} from "../../../shared/models";

@Component({
  selector: 'app-activity-terms-and-conditions',
  templateUrl: 'terms-and-conditions.component.html',
  styleUrls: ['terms-and-conditions.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    FormsModule,
    AsyncPipe,
    DatePipe,
    CurrencyPipe,
    DecimalPipe,
    NgIf,
    NgFor,
    NgOptimizedImage,
    RouterLink,
    RenderBlockPipe,
  ],
})
export class activityTermsAndConditionsComponent {
  public isLoading = false;
  activity?: Activity;
  protected readonly JSON = JSON;

  constructor(
    protected modalCtrl: ModalController,
  ) {
  }

  confirm() {
    this.isLoading = true;
    this.modalCtrl.dismiss(true, 'confirm');
  }

  cancel() {
    this.isLoading = true;
    this.modalCtrl.dismiss();
  }
}
