import {Component} from '@angular/core';
import {CommonModule} from '@angular/common';
import {IonicModule} from '@ionic/angular';
import {HeaderComponent} from '../../layout/header/header.component';
import {AppDataService} from '../../shared/services/app-data.service';
import {environment} from '../../../environments/environment';
import {Router} from "@angular/router";

interface TenantAboutContent {
  altwijry: {
    title: string;
    subtitle: string;
    description: string;
    mission: string;
    vision: string;
    values: string[];
  };
  alhumaid: {
    title: string;
    subtitle: string;
    description: string;
    mission: string;
    vision: string;
    values: string[];
  };
  algarawi: {
    title: string;
    subtitle: string;
    description: string;
    mission: string;
    vision: string;
    values: string[];
  };
}

@Component({
  selector: 'app-about',
  templateUrl: './about.page.html',
  styleUrls: ['./about.page.scss'],
  standalone: true,
  imports: [
    CommonModule,
    IonicModule,
    HeaderComponent
  ]
})
export class AboutPage {
  currentTenant: string;
  aboutContent: any;

  private tenantContent: TenantAboutContent = {
    altwijry: {
      title: 'صندوق عائلة عبدالله راجح التويجري',
      subtitle: 'مؤسسة تجارية برقم السجل 7042410089',
      description: `صندوق عبدالله راجح التويجري هو مؤسسة تجارية اجتماعية، تُعنى بتقديم منتجات وخدمات تجمع بين الطابع الأسري والتجاري، وتسهم في تعزيز التكافل العائلي وتنمية الموارد المالية واستثمارها بطرق مستدامة، عبر مشاريع متنوعة تشمل التجارة الإلكترونية والمبادرات التنموية.`,
      mission: 'توفير منتجات وخدمات ذات طابع عائلي وتجاري تسهم في دعم أفراد العائلة وتنمية مواردها.',
      vision: 'أن نكون نموذجًا رياديًا في الجمع بين العمل التجاري والتنمية العائلية المستدامة.',
      values: [
        'الاستدامة المالية',
        'الجودة والمصداقية',
        'الخدمة المجتمعية',
        'الشفافية والتكامل'
      ]
    },
    alhumaid: {
      title: '',
      subtitle: '',
      description: '',
      mission: '',
      vision: '',
      values: []
    },
    algarawi: {
      title: '',
      subtitle: '',
      description: '',
      mission: '',
      vision: '',
      values: []
    }
  };

  constructor(
    protected appDataService: AppDataService,
    protected router: Router
  ) {
    this.currentTenant = this.detectCurrentTenant();
    this.aboutContent = this.tenantContent[this.currentTenant as keyof TenantAboutContent];
    if (!this.aboutContent) {
      console.warn(`No content found for tenant: ${this.currentTenant}`); // redirect to home
      this.router.navigate(['/home']);
    }
  }

  private detectCurrentTenant(): string {
    const baseUrl = environment.baseUrl.toLowerCase();
    if (baseUrl.includes('altwijry') || baseUrl.includes('altuwaijri')) {
      return 'altwijry';
    } else if (baseUrl.includes('alhumaid')) {
      return 'alhumaid';
    } else if (baseUrl.includes('algarawi')) {
      return 'algarawi';
    }
    return 'alhumaid'; // default
  }
}
