<app-header title="من نحن" [showBackButton]="true" [hideProfileIcon]="true"></app-header>

<ion-content class="ion-padding">
  <div class="about-container">
    <div class="header-section">
      <h1 class="title">{{ aboutContent.title }}</h1>
      <p class="subtitle">{{ aboutContent.subtitle }}</p>
    </div>

    <div class="content-section">
      <div class="description-card">
        <p class="description">{{ aboutContent.description }}</p>
      </div>

      <div class="mission-vision-section">
        <div class="card mission-card">
          <ion-icon name="flag-outline" size="large"></ion-icon>
          <h2>رسالتنا</h2>
          <p>{{ aboutContent.mission }}</p>
        </div>

        <div class="card vision-card">
          <ion-icon name="eye-outline" size="large"></ion-icon>
          <h2>رؤيتنا</h2>
          <p>{{ aboutContent.vision }}</p>
        </div>
      </div>

      <div class="values-section">
        <h2 class="section-title">قيمنا</h2>
        <div class="values-grid">
          <div class="value-item" *ngFor="let value of aboutContent.values">
            <ion-icon name="checkmark-circle" color="primary"></ion-icon>
            <span>{{ value }}</span>
          </div>
        </div>
      </div>

      <div class="contact-section">
        <h2 class="section-title">تواصل معنا</h2>
        <div class="contact-buttons">
          <ion-button expand="block" fill="clear" [href]="'mailto:info@' + currentTenant + '.com'">
            <ion-icon name="mail-outline" slot="start"></ion-icon>
            البريد الإلكتروني
          </ion-button>
        </div>
      </div>
    </div>
  </div>
</ion-content>
