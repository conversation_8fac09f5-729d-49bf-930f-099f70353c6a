.about-container {
  max-width: 800px;
  margin: 0 auto;

  .header-section {
    text-align: center;
    margin-bottom: 2rem;

    .title {
      font-size: 2rem;
      font-weight: bold;
      color: var(--ion-color-primary);
      margin-bottom: 0.5rem;
    }

    .subtitle {
      font-size: 1.2rem;
      color: var(--ion-color-medium);
    }
  }

  .content-section {
    .description-card {
      background: var(--ion-color-light);
      padding: 1.5rem;
      border-radius: 12px;
      margin-bottom: 2rem;

      .description {
        font-size: 1.1rem;
        line-height: 1.8;
        color: var(--ion-color-dark);
        margin: 0;
      }
    }

    .mission-vision-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;

      .card {
        background: var(--ion-color-light);
        padding: 1.5rem;
        border-radius: 12px;
        text-align: center;
        transition: transform 0.3s ease;

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        ion-icon {
          color: var(--ion-color-primary);
          margin-bottom: 1rem;
        }

        h2 {
          font-size: 1.3rem;
          color: var(--ion-color-primary);
          margin-bottom: 0.5rem;
        }

        p {
          font-size: 1rem;
          line-height: 1.6;
          color: var(--ion-color-dark);
          margin: 0;
        }
      }
    }

    .values-section {
      margin-bottom: 2rem;

      .section-title {
        font-size: 1.5rem;
        color: var(--ion-color-primary);
        text-align: center;
        margin-bottom: 1.5rem;
      }

      .values-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;

        .value-item {
          display: flex;
          align-items: center;
          background: var(--ion-color-light);
          padding: 1rem;
          border-radius: 8px;

          ion-icon {
            margin-inline-end: 0.75rem;
            font-size: 1.5rem;
          }

          span {
            font-size: 1.1rem;
            color: var(--ion-color-dark);
          }
        }
      }
    }

    .contact-section {
      text-align: center;

      .section-title {
        font-size: 1.5rem;
        color: var(--ion-color-primary);
        margin-bottom: 1.5rem;
      }

      .contact-buttons {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        max-width: 400px;
        margin: 0 auto;

        ion-button {
          --border-radius: 8px;
          height: 48px;
          font-size: 1rem;
        }
      }
    }
  }
}

// Dark mode styles
@media (prefers-color-scheme: dark) {
  .about-container {
    .content-section {
      .description-card,
      .mission-vision-section .card,
      .values-section .value-item {
        background: var(--ion-color-step-100);
      }
    }
  }
}

// Mobile responsiveness
@media (max-width: 768px) {
  .about-container {
    .header-section {
      .title {
        font-size: 1.5rem;
      }

      .subtitle {
        font-size: 1rem;
      }
    }

    .content-section {
      .mission-vision-section {
        grid-template-columns: 1fr;
      }

      .values-section {
        .values-grid {
          grid-template-columns: 1fr;
        }
      }
    }
  }
}