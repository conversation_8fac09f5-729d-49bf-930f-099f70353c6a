import { ChangeDetectorRef, Component, Inject, OnInit, TransferState } from '@angular/core';
import {AsyncPipe, DatePipe, DOCUMENT, <PERSON>sonPipe, <PERSON><PERSON>orO<PERSON>, <PERSON><PERSON>f, NgTemplateOutlet} from "@angular/common";
import {InfiniteScrollCustomEvent, IonicModule} from "@ionic/angular";
import {HeaderComponent} from "../../../layout/header/header.component";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {IS_SERVER_PLATFORM} from "../../../shared/IS_SERVER_PLATFORM.token";
import {CardComponent} from "../../../shared/components/card/card.component";
import {ApiService} from "../../../shared/services/api.service";
import {HttpClient, HttpClientModule} from "@angular/common/http";

import {BehaviorSubject, finalize, of, take, tap} from "rxjs";
import {RouterModule} from "@angular/router";
import {WorkoutProgram} from "../../../shared/models";


@Component({
  selector: 'app-workout-programs-index',
  templateUrl: 'workout-program-index.component.html',
  styleUrls: ['workout-program-index.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    IfIsBrowserDirective,
    NgIf,
    CardComponent,
    NgTemplateOutlet,
    NgForOf,
    AsyncPipe,
    JsonPipe,
    RouterModule,
    DatePipe
  ],
})
export class WorkoutProgramIndexComponent implements OnInit {
  isLoading = false;
  workoutProgramsPage = 0;
  workoutProgramsNextPageUrl = '';
  private _workoutPrograms$ = new BehaviorSubject<WorkoutProgram[]>([]);
  workoutPrograms$ = this._workoutPrograms$.asObservable()


  constructor(@Inject(DOCUMENT) private document: Document,
              @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
              private apiService: ApiService,
              private transferState: TransferState,
              private http: HttpClient,
              private cdr: ChangeDetectorRef
  ) {

  }

  ngOnInit(): void {
    if (this.isServer) {
      console.log('is server')
    }
    this.getWorkoutPrograms().pipe(take(1)).subscribe();

    if (!this.isServer) {
      this.browserSetup();
    }
  }

  browserSetup() {
  }

  getWorkoutPrograms() {
    if (this.isLoading)
      return of();
    this.isLoading = true;
    this.workoutProgramsPage++;
    return this.apiService.checkAndGetData(
      this.apiService.getDynamicStateKey(`workoutPrograms-${this.workoutProgramsPage}`),
      this.apiService.getWorkoutPrograms(this.workoutProgramsNextPageUrl),
      []
    ).pipe(take(1), finalize(() => this.isLoading = false), tap((data) => {
      this.workoutProgramsNextPageUrl = data.pagination?.next_url;
      this._workoutPrograms$.next([...this._workoutPrograms$.value, ...data.data].sort(
        (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      ) ?? []);
      this.cdr.detectChanges();
    }));
  }

  onIonInfinite(ev: any) {
    this.getWorkoutPrograms().pipe(take(1)).subscribe();
    setTimeout(() => {
      (ev as InfiniteScrollCustomEvent).target.complete().then();
    }, 500);
  }
}
