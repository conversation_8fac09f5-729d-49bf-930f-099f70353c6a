<app-header [hideProfileIcon]="false" [showBackButton]="true"></app-header>
<ion-content>
  <ion-grid [fixed]="true">
    <app-card title="مسابقات الأنشطة" [showExpandButton]="false">
      <ng-container *ngTemplateOutlet="workoutPrograms"></ng-container>
    </app-card>
    <ion-infinite-scroll (ionInfinite)="onIonInfinite($event)" *ngIf="workoutProgramsNextPageUrl">
      <ion-infinite-scroll-content></ion-infinite-scroll-content>
    </ion-infinite-scroll>
    <ion-modal [initialBreakpoint]="0.25" [breakpoints]="[0, 0.25, 0.5, 0.75]" trigger="open-modal" *ifIsBrowser>
      <ng-template>
        <ion-content>
          <ion-list>
            <ion-item lines="none">
              <ion-avatar slot="start">
                <ion-img src="https://i.pravatar.cc/300?u=b"></ion-img>
              </ion-avatar>
            </ion-item>
          </ion-list>
        </ion-content>
      </ng-template>
    </ion-modal>
  </ion-grid>
</ion-content>


<ng-template #workoutPrograms>
  <div style=" overflow-y: scroll;
  overflow-x: hidden;">
    <ng-container *ngIf="workoutPrograms$ | async as workoutPrograms;">
      <ion-spinner *ngIf="workoutPrograms.length === 0" style="margin: auto; width: 100%;height: 40px;"></ion-spinner>
      <ion-item *ngFor="let oneProgram of workoutPrograms; let i = index"
                [routerLink]="'/workout-programs/' + oneProgram.id"
                [title]="oneProgram.title">
        <!--<ion-avatar slot="start" [routerLink]="'/workout-programs/'+ oneProgram.id">
          <ion-img [src]="((appDataService.data$ | async)!['iconPath']) || '/assets/icon/logo.svg'"></ion-img>
        </ion-avatar>-->
        <!--<ion-label [routerLink]="'workout-programs/'+ oneProgram.id">
          <h4 class="ion-text-wrap" style="line-height: 1.7 !important;">{{ oneProgram.title }}</h4>
          <p class="">{{oneProgram.created_at | date:'longDate'}}</p>
        </ion-label>-->
        <ion-row>
          <ion-col size="24" class="ion-no-padding">
            <ion-label [routerLink]="'/workout-programs/' + oneProgram.id">
              <h4 class="ion-text-wrap" style="line-height: 1.7 !important;">
                {{ oneProgram.title }}
              </h4>
            </ion-label>
          </ion-col>
          <ion-col class="ion-no-padding">
            {{oneProgram.start_at | date:'longDate'}}
          </ion-col>
          <ion-col size="auto" class="ion-no-padding">{{ ' ← ' }}</ion-col>
          <ion-col class="ion-no-padding" style="text-align: left">
            {{oneProgram.end_at | date:'longDate'}}
          </ion-col>
        </ion-row>
      </ion-item>
    </ng-container>
  </div>
</ng-template>
