import {ChangeDetectorRef, Component, CUSTOM_ELEMENTS_SCHEMA, inject, Input, OnInit} from "@angular/core";
import {InfiniteScrollCustomEvent, IonicModule} from "@ionic/angular";
import {AsyncPipe, DatePipe, DecimalPipe, NgForOf, NgIf, NgOptimizedImage} from "@angular/common";
import {KiloFormatterPipe} from "../../../shared/pipes/kilo-formatter-pipe";
import {ApiService} from "../../../shared/services/api.service";
import {Pagination, WorkoutProgram} from "../../../shared/models";
import {WorkoutSummaryComponent} from "./workout-summary/workout-summary.component";
import {ProfileService} from "src/app/shared/services/profile.service";
import {ImgFallbackDirective} from "../../../img-fallback.directive";
import {StorageService} from "../../../shared/services/storage.service";
import {map} from "rxjs";
import {RouterLink} from "@angular/router";

@Component({
  selector: 'app-workout-timeline',
  templateUrl: './workout-timeline.component.html',
  styleUrls: ['./workout-timeline.component.scss'],
  standalone: true,
  imports: [
    DatePipe,
    IonicModule,
    KiloFormatterPipe,
    NgForOf,
    NgIf,
    NgOptimizedImage,
    WorkoutSummaryComponent,
    AsyncPipe,
    ImgFallbackDirective,
    RouterLink,

  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class WorkoutTimelineComponent implements OnInit {
  @Input() workoutProgramId?: number;
  timelineActivities: any[] = [];
  private apiService = inject(ApiService);
  loaded = false;
  pagination: null | Pagination = null;
  profileService = inject(ProfileService);
  storageService = inject(StorageService);
  friendAnnouncement = true;
  rolesCount$ = this.profileService.$user.pipe(
    map(user => user?.roles ? Object.keys(user.roles as object).length : 0),
    // Add a default value to prevent null
    map(count => count ?? 0)
  );
  constructor(private cdr: ChangeDetectorRef) {
  }

  ngOnInit(): void {
    this.loadFriendAnnouncement();
    this.getTimelineActivities({}, undefined).then(() => {
    });
  }

  loadFriendAnnouncement() {
    this.storageService.getItem('friend_announcement').then((value) => {
      if (!value) {
        this.friendAnnouncement = true;
      } else {
        this.friendAnnouncement = value == 'true';
      }
    })
    this.cdr.detectChanges();
  }

  handleRefresh(event: any) {
    this.getTimelineActivities({
      last_id: this.timelineActivities.length > 0 ? this.timelineActivities[0].id : null
    }, undefined, true).then(() => {
      event.target.complete();
    });
  }

  // call api to get more data of timelineActivities, handle infinite scroll and pull to refresh
  getTimelineActivities(params: any, next_page?: string, refresh: boolean = false): Promise<void> {
    this.loaded = false;
    return new Promise((resolve) => {
      if (this.workoutProgramId)
        this.apiService.getWorkoutProgramTimeline(this.workoutProgramId, params, next_page).subscribe((response) => {
          if (next_page || refresh) {
            //this.timelineActivities.unshift(...response.data);
            this.timelineActivities = this.timelineActivities.concat(response.data);
          } else {
            this.timelineActivities = response.data;
          }
          if (!refresh)
            this.pagination = response.pagination;
          this.loaded = true;
          this.cdr.detectChanges();
          // remove duplicates from members array
          this.timelineActivities = this.timelineActivities
            .filter((activity, index, self) => index === self.findIndex((x) => (x.id === activity.id)));
          resolve();
        });
    });
  }

  onIonInfinite(event: InfiniteScrollCustomEvent) {
    if (this.pagination?.next_url && this.timelineActivities.length < this.pagination.total) {
      this.getTimelineActivities({}, this.pagination.next_url).then(() => {
        event.target.complete();
      });
    } else {
      event.target.complete();
    }
  }


  removeFriendAnnouncement() {
    this.storageService.setItem('friend_announcement', 'false');
    this.friendAnnouncement = false;
    this.cdr.detectChanges();
  }
}
