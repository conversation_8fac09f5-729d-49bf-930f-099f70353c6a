<ion-refresher slot="fixed" style="margin-top: 10px;" (ionRefresh)="handleRefresh($event)">
  <ion-refresher-content></ion-refresher-content>
</ion-refresher>
<ion-list class="activities-list ion-margin-top">
  <ion-item
    class="ion-padding-vertical"
    lines="none"
    style="width: 100%">
    <ion-avatar slot="start" class="ion-no-margin ion-flex ion-justify-content-center ion-align-items-center">
      <ion-icon name="alert-circle-outline" color="warning" size="meduim"></ion-icon>
    </ion-avatar>
    <h5 class="ion-no-margin" style="line-height: 1.5;"> هذه الصفحة تظهر نشاط أسرتك وأقاربك
      <br>
      (الوالدان، والأعمام، والخوال، وزوجتك، وأخوتك وأخواتك وأبنائهم)</h5>
  </ion-item>
  <ion-item
    class="ion-margin-bottom"
    lines="none"
    style="width: 100%"
    routerLink="/me/friends"
    detail="false"
    *ngIf="friendAnnouncement">
    <ion-avatar slot="start" class="ion-no-margin ion-flex ion-justify-content-center ion-align-items-center">
      <ion-icon name="alert-circle-outline" color="warning" size="meduim"></ion-icon>
    </ion-avatar>
    <h5 class="ion-no-margin">تابع نشاط أقاربك الآن</h5>
    <ion-avatar
      (click)="removeFriendAnnouncement()"
      slot="end" class="ion-no-margin ion-flex ion-justify-content-end ion-align-items-center">
      <ion-icon name="close-circle" color="light" size="meduim"></ion-icon>
    </ion-avatar>
  </ion-item>
  <ion-item *ngFor="let activity of timelineActivities"
            class="ion-margin-bottom" lines="none" style="width: 100%">
    <app-workout-summary [user]="activity.user" [stravaActivity]="activity"></app-workout-summary>
  </ion-item>

  <ng-container *ngIf="loaded; else loading"></ng-container>
  <ion-infinite-scroll
    *ngIf="pagination?.next_url"
    #infiniteScroll (ionInfinite)="onIonInfinite($event)">
    <ion-infinite-scroll-content></ion-infinite-scroll-content>
  </ion-infinite-scroll>
</ion-list>


<ng-template #loading>
  <ion-item *ngFor="let one of [].constructor(3)" class="ion-margin-vertical">
    <ion-thumbnail slot="start">
      <ion-skeleton-text [animated]="true"></ion-skeleton-text>
    </ion-thumbnail>
    <ion-label>
      <h3>
        <ion-skeleton-text [animated]="true" style="width: 80%;"></ion-skeleton-text>
      </h3>
    </ion-label>
  </ion-item>
</ng-template>
