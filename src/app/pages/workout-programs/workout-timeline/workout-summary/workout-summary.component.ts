import {Component, Input, OnInit} from '@angular/core';
import {IonicModule} from "@ionic/angular";
import {DatePipe, NgIf, DecimalPipe, NgOptimizedImage} from "@angular/common";
import {KiloFormatterPipe} from "../../../../shared/pipes/kilo-formatter-pipe";
import {ImgFallbackDirective} from "../../../../img-fallback.directive";
import {SecondsFormatterPipe} from "../../../../shared/pipes/seconds-formatter-pipe";
import {AppLauncher} from "@capacitor/app-launcher";

@Component({
  selector: 'app-workout-summary',
  templateUrl: './workout-summary.component.html',
  styleUrls: ['./workout-summary.component.scss'],
  imports: [
    NgIf,
    DatePipe,
    IonicModule,
    DecimalPipe,
    NgOptimizedImage,
    KiloFormatterPipe,
    ImgFallbackDirective,
    SecondsFormatterPipe,
  ],
  standalone: true
})
export class WorkoutSummaryComponent implements OnInit {
  @Input() stravaActivity?: any;
  @Input() user?: any;
  @Input() routeImage? = '/assets/images/workout-tutorial/workout_map.svg';
  @Input() hideUser = false


  constructor() {
  }

  ngOnInit() {
  }

  openActivityInStrava() {
    let url = `https://www.strava.com/activities/${this.stravaActivity.strava_id}`;
    AppLauncher.openUrl({url: url})
  }
}
