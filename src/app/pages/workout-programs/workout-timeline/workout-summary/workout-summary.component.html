<div [class.ion-align-items-start]="!hideUser" [class.ion-justify-content-center]="hideUser" class="ion-flex-column  " style="width: 100%;" *ngIf="stravaActivity">
  <div class="ion-flex ion-align-items-center ion-padding-top">
    <ion-avatar *ngIf="!hideUser" slot="start" style="width: 40px; height: 40px;">
      <img alt="Avatar" width="30" height="30"
           ngSrc="{{ (user ?? stravaActivity.user)!.profile_photo_url ?? ('/assets/d3-images/' + ((user ?? stravaActivity.user)!.gender ?? 'male').toLowerCase() + '-icon.svg') }}"
           [appImgFallback]="('/assets/d3-images/' + ((user ?? stravaActivity.user)!.gender ?? 'male').toLowerCase() + '-icon.svg')">
    </ion-avatar>
    <div [class.ion-margin-start]="!hideUser" class="ion-flex-column ion-align-items-start">
      <ion-label *ngIf="!hideUser">{{ (user ?? stravaActivity.user)!.full_name }}</ion-label>
      <!--      <ion-note style="font-size: 8px">{{  }}</ion-note>-->
      <p class="ion-no-margin"
         [class.font-bold]="hideUser"
         style="font-size: {{hideUser ? '12px': '10px'}} !important;">{{ stravaActivity.start_date_local | date: 'EEEE, MMMM d, y, h:mm a' }}</p>

    </div>
  </div>
  <div class="ion-flex ion-justify-content-center ion-margin-vertical" style="width: 100%">
    @if (stravaActivity.distance) {
      <div class="ion-flex-column">
        <span class="key-metric">المسافة</span>
        <h5 class="ion-no-margin">{{ stravaActivity.distance | kiloFormatter }}</h5>
      </div>
      <div class="divider"></div>
    }
    <!--@if (stravaActivity.calories) {
      <div class="ion-flex-column">
        <span class="key-metric">السعرات</span>
        <h5 class="ion-no-margin">{{ stravaActivity.calories | number }}</h5>
      </div>
      <div class="divider"></div>
    }-->
    @if (stravaActivity.moving_time) {
      <div class="ion-flex-column">
        <span class="key-metric">المدة</span>
        <h5 class="ion-no-margin">{{ stravaActivity.moving_time | secondsFormatter }}</h5>
      </div>
      <div class="divider"></div>
    }
    @if (stravaActivity.distance && stravaActivity.moving_time) {
      <div class="ion-flex-column">
        <span class="key-metric">متوسط السرعة</span>
        <h5 class="ion-no-margin">{{ (stravaActivity.distance / stravaActivity.moving_time * 3.6) | number: "1.0-1" }} ك.م/س</h5>
      </div>
    }
  </div>

  <img style="border-radius: 10px; max-height: 150px; width: 100%; object-fit: cover;"
       class="ion-margin-bottom" (click)="openActivityInStrava()"
       [src]="stravaActivity.polyline_image_url || stravaActivity.summary_polyline_image_url || routeImage"
       [appImgFallback]="routeImage">
</div>
