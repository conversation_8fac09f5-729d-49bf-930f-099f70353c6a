<div class="stats-container" *ngIf="activitySummary">
  <div class="stat-item">

    <div class="stat-content">
      <ion-text color="light"><p class="stat-label">المسافة المقطوعة</p></ion-text>
      <ion-text color="secondary"><h4 class="stat-value">{{ activitySummary.totalDistance | kiloFormatter }}</h4></ion-text>

    </div>
    <ion-icon name="two-points-route" class="icon"></ion-icon>

  </div>

  <div class="divider"></div>

  <div class="stat-item">

    <div class="stat-content">
      <ion-text color="light"><p class="stat-label">إجمالي الوقت</p></ion-text>
      <ion-text color="secondary"><h4 class="stat-value">{{ activitySummary.totalMinutes | secondsFormatter }}</h4></ion-text>
    </div>
    <ion-icon name="speed-watch" class="icon"></ion-icon>

  </div>

</div>
