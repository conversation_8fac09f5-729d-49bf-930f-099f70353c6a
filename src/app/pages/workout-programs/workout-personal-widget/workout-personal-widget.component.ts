import {Component, CUSTOM_ELEMENTS_SCHEMA, Input, OnInit} from "@angular/core";
import {IonicModule} from "@ionic/angular";
import {HeaderComponent} from "../../../layout/header/header.component";
import {HttpClientModule} from "@angular/common/http";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {DatePipe, DecimalPipe, NgForOf, NgIf} from "@angular/common";
import {RenderBlockPipe} from "../../../shared/pipes/render-block.pipe";
import {CardComponent} from "../../../shared/components/card/card.component";
import {FormatDistancePipe} from "../../../shared/pipes/format-distance.pipe";
import {OneCardComponent} from "../../eid-cards/one-card/one-card.component";
import {nl2brPipe} from "../../../nl2br.pipe";
import {TruncatePipe} from "../../../truncate.pipe";
import {RouterLink} from "@angular/router";
import {KiloFormatterPipe} from "../../../shared/pipes/kilo-formatter-pipe";
import {SecondsFormatterPipe} from "../../../shared/pipes/seconds-formatter-pipe";

@Component({
  selector: 'app-workout-personal-widget',
  templateUrl: './workout-personal-widget.component.html',
  styleUrls: ['./workout-personal-widget.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    IfIsBrowserDirective,
    NgForOf,
    NgIf,
    RenderBlockPipe,
    DatePipe,
    CardComponent,
    FormatDistancePipe,
    OneCardComponent,
    DecimalPipe,
    nl2brPipe,
    TruncatePipe,
    RouterLink,
    KiloFormatterPipe,
    SecondsFormatterPipe,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class WorkoutPersonalWidgetComponent implements OnInit {

  @Input() activitySummary: any; // Daily target minutes

  ngOnInit(): void {

  }

}
