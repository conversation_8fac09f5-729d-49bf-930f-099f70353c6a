import {
  Component, inject, Input,

} from "@angular/core";
import {IonicModule, ModalController} from "@ionic/angular";
import {AsyncPipe, NgForOf, NgIf, NgOptimizedImage} from "@angular/common";
import {ImgFallbackDirective} from "../../../img-fallback.directive";
import {NgxSplideModule} from "ngx-splide";
import {nl2brPipe} from "../../../nl2br.pipe";
import {Router, RouterLink} from "@angular/router";
import {StravaService} from "../../../shared/services/strava.service";
import {AppDataService} from "../../../shared/services/app-data.service";

@Component({
  selector: 'app-workout-tutorial',
  templateUrl: './workout-tutorial.component.html',
  styleUrls: ['./workout-tutorial.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    NgForOf,
    NgIf,
    ImgFallbackDirective,
    NgOptimizedImage,
    NgxSplideModule,
    nl2brPipe,
    RouterLink,
    AsyncPipe
  ],
})
export class WorkoutTutorialComponent {
  router = inject(Router);
  stravaAuthURL: string | null = null;
  stravaService = inject(StravaService);
  isUserLinkedStrava = false;
  slides = [
    {
      id: 'welcome',
      image: 'assets/images/workout-tutorial/man-running.svg',
      title: 'شارك معنا في كلنا نمشي!',
      description: 'شارك في تحدي <b>كلنا نمشي</b> اليومي' +
        '\n' +
        'وابدأ رحلتك نحو حياة أكثر نشاطاً',
    },
    {
      id: 'weekdays',
      image: 'assets/images/workout-tutorial/days_v1.svg',
      title: 'كل أسبوع جولة',
      description: 'لكل أسبوع جولة من هدفين' +
        '<br>' +
        '<b>الأول:</b> المشي خمسة أيام بالأسبوع (لا يلزم تتابع الأيام)'
        + '<br>' +
        '<b>الثاني:</b> الحد الأدنى لوقت المشي يتغير أسبوعيًا (١٠ دقائق في الأسبوع الأول، ٢٠ دقيقة في الأسبوع الثاني، ٣٠ دقيقة في الأسبوع الثالث، ٤٠ دقيقة في الأسبوع الأخير)'

    },
    {
      id: 'continuity',
      image: 'assets/images/workout-tutorial/man-walking.svg',
      title: 'الاستمرارية',
      description: 'نحن نؤمن بأن الاستمرارية هي <b>السر!</b>' +
        '\n' +
        'في هذه المبادرة، نركز على تعزيز عادة المشي اليومية. كل دقيقة تُحتسب.',
    },
  ]
  modalCtrl = inject(ModalController);
  appData = inject(AppDataService);
  // @ts-ignore
  protected workoutProgramId: number;

  constructor() {
    this.stravaService.athlete().subscribe(({athlete}) => {
      this.isUserLinkedStrava = !!athlete;
      if (athlete) return; // user already linked strava

      this.slides = [...this.slides, {
        id: 'link-strava',
        image: 'assets/images/workout-tutorial/strava-appstore.svg',
        title: 'ربط تطبيق سترافا',
        description: 'يجب ربط تطبيق سترافا بحسابك هنا في تطبيق التويجري.\n' +
          ' بداية ونهاية كل نشاط يجب أن تكون عبر تطبيق <b>سترافا </b>' +
          'لضمان تحديث بياناتك في تطبيقنا.',
      },]
      try {
        this.stravaAuthURL = this.stravaService.getAuthorizationUrl([
          'read',
          'activity:read',
        ]);
      } catch (e) {
        this.stravaAuthURL = null;
      }
    })
  }
}
