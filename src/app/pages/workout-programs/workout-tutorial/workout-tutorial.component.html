<splide [options]="{direction: 'rtl', autoplay:false, arrows:false}"
        style="height: 100%" class="ion-padding-bottom">
  <splide-slide *ngFor="let slide of slides; let slideIndex = index">
    <div class="ion-flex-column ion-justify-content-center ion-text-center ion-margin-top">

      <img class="ion-margin-top" priority [ngSrc]="slide.image" [alt]="slide.title" width="500" height="350"/>
      <ion-text color="secondary">
        <h1 style="margin: 0">{{ slide.title }}</h1>
      </ion-text>
      <ion-text>
        <h4 class="ion-padding ion-text-justify font primary-font" style="max-width: 500px;"
            [innerHTML]="slide.description | nl2br"></h4>

        <ion-button expand="block" color="primary"
                    *ngIf="isUserLinkedStrava && slide.id === 'continuity'"
                    [routerLink]="workoutProgramId ? ('workout-programs/' + workoutProgramId) : '#'"
                    (click)="modalCtrl.dismiss()">
          ابدا الآن
        </ion-button>

        <ion-button expand="block" color="primary"
                    *ngIf="!isUserLinkedStrava && slide.id === 'link-strava'"
                    [href]="stravaAuthURL" (click)="modalCtrl.dismiss()">
          اربط حسابك في سترافا
        </ion-button>

        <ng-container *ngIf="(appData.data$ | async) as appData">

          <ion-button
            *ngIf="!isUserLinkedStrava && slideIndex === slides.length - 1 && slide.id !== 'link-strava'"
            expand="block" color="primary"
            (click)="modalCtrl.dismiss()"
            [routerLink]="['workout-programs',  $any(appData['activeWorkoutProgram']).id]"
            [queryParams]="{ showTutorial: true }">
            حسنا
          </ion-button>
        </ng-container>

      </ion-text>
    </div>
  </splide-slide>
</splide>
