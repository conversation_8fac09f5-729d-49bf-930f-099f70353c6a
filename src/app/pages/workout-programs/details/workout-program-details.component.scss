:host {
  // Light mode variables
  --primary-color: #2e7d32;
  --secondary-color: #7d7d7d;
  --background-light: #f8f8f8;
  --text-primary: #000000;
  --text-secondary: #7d7d7d;
  --background-card: #ffffff;
  --border-color: #e0e0e0;
  --progress-bg: #f4f4f4;
  --tooltip-bg: rgba(0, 0, 0, 0.8);
  --tooltip-text: #ffffff;
  --progress-incomplete: #e0e0e0;
  --circle-bg: #75A08F;

  @media (prefers-color-scheme: dark) {
    // Dark mode variables
    --primary-color: #4caf50;
    --secondary-color: #9e9e9e;
    --background-light: #1a1a1a;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --background-card: #2d2d2d;
    --border-color: #404040;
    --progress-bg: #404040;
    --tooltip-bg: rgba(255, 255, 255, 0.8);
    --tooltip-text: #000000;
    --progress-incomplete: #404040;
    --circle-bg: #557b6e;
  }
}

ion-header {
  ion-toolbar {
    --background: var(--background-light);
  }
}


ion-list.stats-list {
  background: transparent;
  padding: 0 1rem;

  ion-item {
    --background: white;
    --padding-start: 1rem;
    --padding-end: 1rem;
    --padding-top: 0.75rem;
    --padding-bottom: 0.75rem;
    --border-radius: 12px;
    /*margin-bottom: 0.5rem;*/

    ion-icon {
      color: var(--primary-color);
      font-size: 1.5rem;
      margin-inline-end: 1rem;
    }

    .stat-value {
      font-size: 1.2rem;
      font-weight: bold;
      color: var(--primary-color);
    }

    .stat-label {
      font-size: 0.9rem;
      color: var(--secondary-color);
      margin-inline-start: 0.5rem;
    }
  }
}



// WeekChart
.workout-card {
  background: var(--ion-color-light);
  border-radius: 25px;
  padding: 0 20px 20px 20px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
  border: 2px solid rgba(158, 186, 177, 0.7);


  .weekly-chart {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    height: 100%;
    position: relative;

    // Target line
    /*&::before {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      // Calculate the position from the bottom
      // If targetMinutes is 15, and max height is 180px (progress-container height)
      // then we position it at: (15/15) * 180px = 180px from bottom
      bottom: calc(((var(--target-minutes) / var(--max-minutes)) * 180px) + 4.3rem);
      border-top: 2px dashed #e0e0e0;
      z-index: 1;
    }*/

    .day-column {
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;
      height: 100%;
      position: relative;
      padding: 0 0.25rem;
      z-index: 1;

      &.today {
        .day-label {
          color: var(--ion-color-primary);
          font-weight: bold;
        }
      }

      .progress-container {
        height: 100%;
        display: flex;
        align-items: flex-end;
        width: 30px; // Increased to accommodate circle
        position: relative;
        z-index: 2;

        .circular-progress {
          width: 100%;
          height: 30px; // Fixed height for circle
          position: relative;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-4px);
          }

          svg {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg); // Start from top

            .progress-circle-bg {
              fill: none;
              stroke: #75A08F;
            }

            .progress-circle {
              fill: none;
              transition: stroke-dashoffset 0.3s ease;

              &.incomplete {
                stroke: #e0e0e0;
              }

              &.animate {
                animation: progress-pulse 2s infinite;
              }
            }
          }

          .check-mark {
            position: absolute;
            font-size: 1.1rem !important;
            top: 53%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
          }

          .value-label {
            position: absolute;
            width: 100%;
            top: -25px;
            left: 0;
            text-align: center;
            font-size: 0.875rem;
            color: var(--primary-color);
            font-weight: 500;
            opacity: 0;
            transition: opacity 0.2s ease;
          }
        }

        .vertical-progress {
          width: 100%;
          height: 100%;
          background: #f4f4f4;
          border-radius: 16px;
          overflow: hidden;
          position: relative;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-4px);
          }

          .progress-fill {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            background: var(--ion-color-success);
            border-radius: 16px;
            transition: height 0.3s ease;

            &.incomplete {
              background: #e0e0e0;
            }

            &.animate {
              animation: progress-pulse 2s infinite;
            }
          }

          .value-label {
            position: absolute;
            width: 100%;
            top: -25px;
            left: 0;
            text-align: center;
            font-size: 0.875rem;
            color: var(--primary-color);
            font-weight: 500;
            opacity: 0;
            transition: opacity 0.2s ease;
          }
        }

        &:hover .value-label {
          opacity: 1;
        }
      }

      .day-label {
        text-align: center;
        margin-top: 1rem;
        font-size: 0.875rem;
        color: var(--secondary-color);
        transition: color 0.3s ease;
        transform: rotate(-90deg);
      }
    }
  }

  .progress-tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.875rem;
    top: -3.5rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 12;
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.2s ease;
    pointer-events: none;

    &.visible {
      opacity: 1;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: -6px;
      left: 50%;
      transform: translateX(-50%);
      border-width: 6px 6px 0 6px;
      border-style: solid;
      border-color: rgba(0, 0, 0, 0.8) transparent transparent transparent;
    }
  }

  @keyframes progress-pulse {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0.6;
    }
    100% {
      opacity: 1;
    }
  }

  .goal-progress {
    display: flex;
    align-items: center;
    justify-content: start;
  }

  .goal-content {
    display: flex;
    align-items: center;
  }


  .icon-section {
    font-size: 3rem !important;
    margin-left: 0.5rem;
  }

}

// auth-state.component.scss & strava-state.component.scss

.auth-card, .strava-card {
  margin: 0;
  box-shadow: none;
  border-radius: 12px;
}

.auth-card {
  --background: rgba(239, 83, 80, 0.1);
  border: 1px solid rgba(239, 83, 80, 0.2);
}

.strava-card {
  --background: rgba(251, 140, 0, 0.1);
  border: 1px solid rgba(251, 140, 0, 0.2);
}

.alert-container {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.alert-icon {
  font-size: 1.5rem;
  padding-top: 0.25rem;
}

.auth-card .alert-icon {
  color: var(--ion-color-danger);
}

.strava-card .alert-icon {
  color: var(--ion-color-warning);
}

.alert-content {
  flex: 1;

  h2 {
    margin: 0 0 0.5rem;
    font-size: 1.1rem;
    font-weight: 600;
  }

  p {
    margin: 0;
    font-size: 0.9rem;
    color: var(--ion-color-medium);
  }
}

.auth-card .alert-content {
  h2 {
    color: var(--ion-color-danger);
  }
}

.strava-card .alert-content {
  h2 {
    color: var(--ion-color-warning);
  }
}

ion-button {
  margin-top: 1rem;
  --border-radius: 8px;
  height: 48px;

  .strava-icon {
    width: 24px;
    height: 24px;
    margin-right: 8px;
  }
}

ion-item::part(detail-icon) {
  display: none;
}

