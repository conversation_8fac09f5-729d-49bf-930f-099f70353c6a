<app-header [leftButtonTemplate]="selectedSegment === TABS.communityStats && false ? leftButton: null"
            [hideProfileIcon]="false" [showBackButton]="true" backTo="/home"></app-header>
<ng-template #leftButton>
  <ion-button fill="clear"
              routerLink="/me/friends"
              class="ion-justify-content-start"
              style="position: absolute; left: -10px;">
    <ion-icon name="person-add-outline" slot="icon-only" color="secondary"></ion-icon>

  </ion-button>
</ng-template>

<ion-content>
  <ion-grid [fixed]="true" class="ion-align-items-center" style="max-width: 370px;">
    <ion-segment [value]="selectedSegment" [swipeGesture]="true"
                 (ionChange)="segmentValueChanged($event)">
      <ion-segment-button [value]="TABS.yourStats" (click)="segmentChanged(TABS.yourStats)"
                          [class.active]="selectedSegment === TABS.yourStats">
        <ion-label>إحصائياتك</ion-label>
      </ion-segment-button>
      <ion-segment-button [value]="TABS.communityStats" (click)="segmentChanged(TABS.communityStats)"
                          [class.active]="selectedSegment === TABS.communityStats">
        <ion-label>نشاط الأسرة والأقارب</ion-label>
      </ion-segment-button>
    </ion-segment>

    <ion-spinner class="ion-margin-top" *ngIf="!workoutProgram"
                 style="margin: auto; width: 100%;height: 40px;"></ion-spinner>

    <ng-container *ngIf="workoutProgram && selectedSegment === TABS.yourStats">
      <ion-text color="medium" class="ion-text-center ion-no-margin">
        <h2>مبادرة</h2>
      </ion-text>

      <ion-text color="secondary" class="ion-text-center ion-no-margin">
        <h1 style="font-weight: bolder" class="ion-no-margin">{{ workoutProgram?.title }}</h1>
      </ion-text>
      <ion-text color="dark" class="ion-text-center">
        <p>{{ currentDate | date: 'fullDate' }}</p>
      </ion-text>

      <app-today-counter-widget class="ion-margin-bottom"></app-today-counter-widget>

      <ion-card *ngIf="workoutProgram?.user_data!.authed && !workoutProgram?.user_data!.strava_linked"
                class="strava-card">
        <ion-card-content>
          <div class="alert-container">
            <ion-icon name="link-outline" class="alert-icon"></ion-icon>
            <div class="alert-content">
              <h2>ربط حسابك مع Strava</h2>
              <p>لتتبع نشاطاتك الرياضية، يجب ربط حسابك مع Strava</p>
            </div>
          </div>
          <ion-button expand="block" color="warning" [href]="stravaAuthURL">
            <!--<img src="assets/strava-logo.svg" alt="Strava" class="strava-icon">-->
            <span>اربط مع Strava</span>
            <ion-icon name="arrow-forward" slot="end"></ion-icon>
          </ion-button>
        </ion-card-content>
      </ion-card>

      @if (workoutProgram?.user_data!.authed && workoutProgram?.user_data!.strava_linked) {
        <div style="margin-top: 40px;">
          <ion-text color="secondary">
            <h3 style="font-weight: bolder">إحصائياتك لـ  {{ workoutProgram?.user_data?.current_challenge?.title }}</h3>
          </ion-text>

          <app-workout-personal-widget [activitySummary]="activitySummary"></app-workout-personal-widget>

          <div class="workout-card ion-margin-top" *ngIf="days && days.length > 0">
            <div class="goal-progress">
              <div class="goal-content">
                <div class="icon-section">
                  <!--<ion-icon name="hourglass-outline"></ion-icon>-->
                  <ion-icon name="eclipse"></ion-icon>
                </div>
                <ion-text color="secondary">
                  @if (getCurrentDay() !== -1) {
                    <b> <span style="font-size: 1.5rem;">{{ getCurrentDay() | number: "1.0-1" }}</span></b>
                    <span style="font-size: 1rem;"> دقيقة</span><span> (مجموع نشاط اليوم)</span>
                  } @else {
                    <b> <span style="font-size: 1.5rem;">ابدأ نشاطك اليوم..</span></b>
                  }
                  <br style="display: block; content: ''; margin-top: -17px;">
                  <ion-text color="medium">
                    <b>
                     هدف {{  workoutProgram?.user_data?.current_challenge?.title  }}: {{ targetMinutes | number: "1.0-1" }} {{ tafkeetMinutes }} يوميا
                    </b>
                  </ion-text>
                </ion-text>
              </div>
            </div>

            <div class="weekly-chart"
                 [attr.data-target]="targetMinutes"
                 [style.--target-minutes]="targetMinutes"
                 [attr.data-max]="Math.max(maxReachedMinutes, targetMinutes)"
                 [style.--max-minutes]="Math.max(maxReachedMinutes, targetMinutes)">
              <ng-container *ngFor="let day of days; let i = index">
                <div class="day-column"
                     [class.today]="day.isToday"
                     (mouseenter)="hoveredDay = i"
                     (mouseleave)="hoveredDay = null">
                  <div class="progress-container">
                    <div class="progress-tooltip" *ngIf="day.value > 0" [class.visible]="hoveredDay === i">
                      {{ day.value | number: "1.0-1" }} دقيقة من {{ targetMinutes | number: "1.0-1" }}
                    </div>
                    <div class="circular-progress">
                      <svg viewBox="0 0 40 40">
                        <!-- Background circle -->
                        <circle class="progress-circle-bg"
                                cx="20"
                                cy="20"
                                r="16"
                                stroke-width="6"/>
                        <!-- Progress circle -->
                        <circle class="progress-circle"
                                [class.incomplete]="!day.completed"
                                [class.animate]="day.isToday"
                                cx="20"
                                cy="20"
                                r="16"
                                stroke-width="6"
                                [style.stroke]="getProgressColor(day)"
                                [style.strokeDasharray]="2 * Math.PI * 16"
                                [style.strokeDashoffset]="2 * Math.PI * 16 * (1 - calculateDayProgress(day)/100)"/>
                      </svg>
                      <div class="check-mark" *ngIf="day.completed">
                        <ion-icon name="altwijry-checked" color="success" class="success-icon"></ion-icon>
                      </div>
                      <div class="value-label" *ngIf="day.value > 0">
                        {{ day.value }}د
                      </div>
                    </div>
                  </div>
                  <div class="day-label" [class.today]="day.isToday">
                    {{ day.label }}
                  </div>
                </div>
              </ng-container>
            </div>
          </div>

          <ng-container *ngIf="activities && activities.length > 0">
            <ion-text color="secondary">
              <h3 style="font-weight: bolder">الأنشطة الأخيرة</h3>
            </ion-text>
            <div class="workout-card" style="max-height: 450px; overflow: auto; padding: 0;">
              <ion-list class="activities-list">
                <ion-item *ngFor="let activity of activities">
                  <app-workout-summary [stravaActivity]="activity" [user]="(profileService.$user | async)"
                  [hideUser]="true"
                  />
                  <ion-row *ngIf="false" class="ion-align-items-end" style="width: 100%;">
                    <ion-col size="auto">
                      <ion-icon name="strava-map" class="strava-map" style="font-size: 4rem;"
                                *ngIf="!activity.polyline_image_url && !activity.summary_polyline_image_url"></ion-icon>
                      <img class="strava-map" style="font-size: 4rem;"
                           *ngIf="activity.polyline_image_url || activity.summary_polyline_image_url"
                           [src]="activity.polyline_image_url || activity.summary_polyline_image_url">
                    </ion-col>
                    <ion-col>
                      <div class="activity-content">
                        <div style="text-align: end;" *ngIf="activity.city">
                          <span class="location">
                            {{ activity.city }}
                          </span>
                          <ion-icon name="location-outline"></ion-icon>
                        </div>
                        <div>
                          <h4 *ngIf="activity.distance"
                              style="font-weight: bolder">{{ activity.distance  | kiloFormatter }}
                            في  {{ activity.moving_time | secondsFormatter }}
                          </h4>
                        </div>
                        <div style="text-align: end">
                          {{ activity.date + ' ' + activity.time | date: 'dd/MM/yyyy hh:mm a' }}
                        </div>
                      </div>
                    </ion-col>
                  </ion-row>
                </ion-item>
              </ion-list>
            </div>
          </ng-container>
        </div>
      }
      <app-elite-widget *ngIf="false"></app-elite-widget>
      <!--  END OF yourStats Segments body    -->
    </ng-container>
    <ng-container *ngIf="workoutProgram && selectedSegment === TABS.communityStats ">
      <app-workout-timeline [workoutProgramId]="workoutProgram?.id"></app-workout-timeline>
    </ng-container>
  </ion-grid>
</ion-content>
