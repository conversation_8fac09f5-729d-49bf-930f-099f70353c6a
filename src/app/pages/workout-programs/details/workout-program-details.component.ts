import {
  booleanAttribute,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  inject,
  Input,
  OnInit
} from '@angular/core';
import {HeaderComponent} from "../../../layout/header/header.component";
import {IonicModule, ModalController} from "@ionic/angular";
import {WorkoutProgram} from "../../../shared/models";
import {ApiService} from "../../../shared/services/api.service";
import {tap} from "rxjs";
import {Router, RouterLink} from '@angular/router';
import {AsyncPipe, DatePipe, DecimalPipe, NgForOf, NgIf} from "@angular/common";
import {environment} from "../../../../environments/environment";
import {RenderBlockPipe} from "../../../shared/pipes/render-block.pipe";
import {CardComponent} from "../../../shared/components/card/card.component";
import {FormatDistancePipe} from "../../../shared/pipes/format-distance.pipe";
import {OneCardComponent} from "../../eid-cards/one-card/one-card.component";
import {nl2brPipe} from "../../../nl2br.pipe";
import {TruncatePipe} from "../../../truncate.pipe";
import {KiloFormatterPipe} from "../../../shared/pipes/kilo-formatter-pipe";
import {TodayCounterWidgetComponent} from "../today-counter-widget/today-counter-widget.component";
import {format} from "date-fns";
import {EliteWidgetComponent} from "../elite-widget/elite-widget.component";
import {WorkoutPersonalWidgetComponent} from "../workout-personal-widget/workout-personal-widget.component";
import {WorkoutTimelineComponent} from "../workout-timeline/workout-timeline.component";
import {WorkoutTutorialComponent} from "../workout-tutorial/workout-tutorial.component";
import {StravaService} from "../../../shared/services/strava.service";
import {WorkoutSummaryComponent} from "../workout-timeline/workout-summary/workout-summary.component";
import {ProfileService} from "../../../shared/services/profile.service";
import {SecondsFormatterPipe} from "../../../shared/pipes/seconds-formatter-pipe";

@Component({
  selector: 'app-workout-program-details',
  templateUrl: './workout-program-details.component.html',
  styleUrls: ['./workout-program-details.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    NgForOf,
    NgIf,
    RenderBlockPipe,
    DatePipe,
    CardComponent,
    FormatDistancePipe,
    OneCardComponent,
    DecimalPipe,
    nl2brPipe,
    TruncatePipe,
    RouterLink,
    KiloFormatterPipe,
    TodayCounterWidgetComponent,
    EliteWidgetComponent,
    WorkoutPersonalWidgetComponent,
    WorkoutTimelineComponent,
    WorkoutSummaryComponent,
    AsyncPipe,
    SecondsFormatterPipe,
  ]
})
export class WorkoutProgramDetailsComponent implements OnInit {
  @Input() workoutProgram?: WorkoutProgram;
  @Input({transform: booleanAttribute}) hideTutorial = false;

  workoutProgramId?: number;
  env = environment;
  targetMinutes = 5; // Daily target minutes
  targetDays = 0; // Daily target minutes
  tafkeetMinutes = "دقيقة";
  tafkeetDays = "يوم";
  TABS = {
    communityStats: 'communityStats',
    yourStats: 'yourStats',
  }
  selectedSegment = this.TABS.yourStats;

  maxReachedMinutes = 10; // Daily target minutes

  hoveredDay: number | null = null;
  activitySummary!: ActivitySummary;

  days: DayActivity[] = [];

  activities: any[] = [];

  cdr = inject(ChangeDetectorRef);
  modalCtrl = inject(ModalController);
  stravaAuthURL: string | null = null;
  private stravaService = inject(StravaService);
  profileService = inject(ProfileService);
  currentDate = new Date();

  constructor(
    private apiService: ApiService,
    private router: Router
  ) {
    this.workoutProgramId = Number(this.router.url.split('/')[2]);
    if (this.workoutProgramId) {
      this.getWorkoutProgramById(this.workoutProgramId).subscribe();
    } else {
      this.router.navigate(['/workout-programs']);
    }

    try {
      this.stravaAuthURL = this.stravaService.getAuthorizationUrl([
        'read',
        'activity:read',
      ]);
    } catch (e) {
      this.stravaAuthURL = null;
    }
  }

  ngOnInit() {

  }

  getWorkoutProgramById(id: number) {
    return this.apiService.getWorkoutProgramById(this.workoutProgramId ?? 0).pipe(
      tap((data) => {
        this.workoutProgram = data;
        if (this.workoutProgram.user_data) {
          if (this.workoutProgram.user_data.current_challenge)
            this.targetMinutes = this.workoutProgram.user_data.current_challenge.daily_challenge;
          this.targetDays = this.workoutProgram.user_data.current_challenge.commitment_days;
          if (this.targetMinutes <= 10 && this.targetMinutes > 1) this.tafkeetMinutes = "دقائق";
          if (this.targetDays === 1)
            this.tafkeetDays = "يوم واحد";
          else if (this.targetDays === 2)
            this.tafkeetDays = "يومان";
          else if (this.targetDays <= 10)
            this.tafkeetDays = "أيام";
          if (this.workoutProgram.user_data.week_chart) {
            this.days = this.workoutProgram.user_data.week_chart
            this.maxReachedMinutes = Math.max(...this.workoutProgram.user_data.week_chart.map(x => x.value))

          }
          if (this.workoutProgram.user_data.summary)
            this.activitySummary = this.workoutProgram.user_data.summary
          if (this.workoutProgram.user_data.activities)
            this.activities = this.workoutProgram.user_data.activities
        }


        if (this.workoutProgram?.user_data!.authed && !this.workoutProgram?.user_data!.strava_linked) {
          // open tutorial modal (app-workout-tutorial) here
          if (this.hideTutorial) return;
          this.modalCtrl.create({
            component: WorkoutTutorialComponent,
            breakpoints: [0, 1],
            initialBreakpoint: 1,
            componentProps: {}
          }).then(modal => {
            modal.present();
          });
          this.cdr.detectChanges();

        }


        this.cdr.detectChanges();
      })
    )

  }

  calculateDayProgress(day: DayActivity): number {
    const value = day.value;
    const progress = (value / Math.max(this.targetMinutes, this.maxReachedMinutes)) * 100;
    return Math.min(progress, 100); // Cap at 100%
  }

  getProgressColor(day: DayActivity): string {
    //if (day.warning) return '#FFA726';
    if (day.warning) return '#DBA852';
    //if (day.completed && day.value >= this.targetMinutes) return '#1C6A4D';
    if (day.completed || day.value > 0) return '#1C6A4D';
    return '#EF5350';
  }

  getCurrentDay() {
    const dateFnsToday = format(new Date(), "yyyy-MM-dd");
    const isToday = this.days.find(day => day.date === dateFnsToday);

    if (isToday) {
      return isToday.value;
    } else {
      // return max from day.value from this.days
      return -1
    }
  }

  isToday(day: DayActivity): boolean {
    // get current date of the users with the same timezone
    const dateFnsToday = format(new Date(), "yyyy-MM-dd");
    return day.date === dateFnsToday;
  }

  protected readonly Math = Math;


  segmentChanged(value: string) {
    this.selectedSegment = value;
    this.cdr.detectChanges();
  }

  segmentValueChanged(event: any) {
    this.segmentChanged(event.detail.value);
  }

  linkWithStrava() {

  }
}

interface DayActivity {
  label: string;
  date: string;
  value: number;
  isToday: boolean;
  completed: boolean;
  warning: boolean;
}

interface ActivitySummary {
  totalMinutes: string | undefined | null;
  totalDistance: string | undefined | null;
}
