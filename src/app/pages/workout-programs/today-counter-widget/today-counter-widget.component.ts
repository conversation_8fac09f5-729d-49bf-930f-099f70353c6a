import {
  ChangeDetectorRef,
  Component,
  CUSTOM_ELEMENTS_SCHEMA, EventEmitter,
  inject,
  Input,
  OnDestroy,
  OnInit,
  Output
} from "@angular/core";
import {IonicModule} from "@ionic/angular";
import {interval, Subscription} from "rxjs";
import {startOfTomorrow, differenceInMilliseconds, differenceInSeconds} from 'date-fns';
import {log} from "node:util";

@Component({
  selector: 'app-today-counter-widget',
  templateUrl: './today-counter-widget.component.html',
  styleUrls: ['./today-counter-widget.component.scss'],
  standalone: true,
  imports: [
    IonicModule
  ],
})
export class TodayCounterWidgetComponent implements OnInit, OnDestroy {
  countdownTime: string = '';
  progressPercentage: number = 0;
  private subscription: Subscription | null = null;

  constructor(private cdr: ChangeDetectorRef) {
  }

  ngOnInit() {
    this.startCountdown();
  }

  startCountdown() {
    const updateCountdown = () => {
      const now = new Date();
      const tomorrow = startOfTomorrow();

      // Get total milliseconds until tomorrow
      const remainingMs = differenceInMilliseconds(tomorrow, now);
      const remainingSeconds = differenceInSeconds(tomorrow, now);

      // Update countdown display
      this.countdownTime = this.formatCountdown(remainingSeconds);

      // Calculate progress (24 hours in milliseconds)
      const fullDay = 24 * 60 * 60 * 1000;
      this.progressPercentage = ((fullDay - remainingMs) / fullDay);
      this.progressPercentage = Math.min(100, Math.max(0, this.progressPercentage));


      this.cdr.detectChanges();

      // Restart at midnight
      if (remainingMs <= 0) {
        this.startCountdown();
      }
    };

    // Update immediately
    updateCountdown();

    // Clean up any existing subscription
    if (this.subscription) {
      this.subscription.unsubscribe();
    }

    // Update every second
    this.subscription = interval(1000).subscribe(updateCountdown);
  }

  private formatCountdown(totalSeconds: number): string {
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
