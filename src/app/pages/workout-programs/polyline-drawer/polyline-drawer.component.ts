import {
  ChangeDetectorRef,
  Component,
  CUSTOM_ELEMENTS_SCHEMA, EventEmitter,
  inject,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
  Output
} from "@angular/core";
import {IonicModule} from "@ionic/angular";
import {interval, Subscription} from "rxjs";
import {startOfTomorrow, differenceInMilliseconds, differenceInSeconds} from 'date-fns';
import {log} from "node:util";
import {NgForOf, NgIf, NgOptimizedImage} from "@angular/common";
import {ImgFallbackDirective} from "../../../img-fallback.directive";

@Component({
  selector: 'app-polyline-drawer',
  templateUrl: './polyline-drawer.component.html',
  styleUrls: ['./polyline-drawer.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    NgForOf,
    NgIf,
    ImgFallbackDirective,
    NgOptimizedImage
  ],
})
export class PolylineDrawerComponent implements OnInit {
  @Input() encodedPolyline = ''


  ngOnInit(): void {

  }
}
