import {
  ChangeDetectorRef,
  Component,
  CUSTOM_ELEMENTS_SCHEMA, EventEmitter,
  inject,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
  Output
} from "@angular/core";
import {IonicModule} from "@ionic/angular";
import {interval, Subscription} from "rxjs";
import {startOfTomorrow, differenceInMilliseconds, differenceInSeconds} from 'date-fns';
import {log} from "node:util";
import {NgForOf, NgIf, NgOptimizedImage} from "@angular/common";
import {ImgFallbackDirective} from "../../../img-fallback.directive";

@Component({
  selector: 'app-elite-widget',
  templateUrl: './elite-widget.component.html',
  styleUrls: ['./elite-widget.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    NgForOf,
    NgIf,
    ImgFallbackDirective,
    NgOptimizedImage
  ],
})
export class EliteWidgetComponent implements OnInit {
  selectedSegment: any = 1;
  rounds = [{
    name: "الجولة الأولى",
    id: 1,
  },
    {
      name: "الجولة الثانية",
      id: 2,
    },
    {
      name: "الجولة الثالثة",
      id: 3,
    }
  ]
  members = [
    {
      user: {
      avatar: null,
      full_name: "عاصم بن محمد بن حمود",
      gender: "male",
    }
  },    {
      user: {
      avatar: null,
      full_name: "عاصم بن محمد بن حمود",
      gender: "male",
    }
  }, {
      user: {
        avatar: null,
        full_name: "عاصم بن محمد بن حمود",
        gender: "male",
      }
    },


  ]
  pagination= {
    page: 1,
    limit: 10,
    total: 0,
    next_url: null,
  };
  loaded = true;


  ngOnInit(): void {
    this.selectedSegment = this.rounds[0].id;
  }

  segmentChanged(segmentValue: any) {
    this.selectedSegment = segmentValue;
  }

  openProfile(user:any) {

  }

  onIonInfinite($event: any) {

  }
}
