<ion-text color="secondary">
  <h3 style="font-weight: bolder">النخبة</h3>
</ion-text>
<ion-text>
  <p>
    نهاية كل أسبوع تنتهي جولة. وسيتم ذكر أسماء من أكمل هدف الجولة ليكونوا مع النخبة ويتم اختيارهم بالسحب على جوائز نهاية المبادرة.
  </p>
</ion-text>

<ion-segment [value]="selectedSegment">
  <ion-segment-button *ngFor="let oneRound of rounds" [value]="oneRound.id" (click)="segmentChanged(oneRound.id)" [class.active]="selectedSegment === oneRound.id">
    <ion-label>{{ oneRound.name }}</ion-label>
  </ion-segment-button>

</ion-segment>

<ion-list class="ion-margin-top">
  <ion-item *ngFor="let user of members" [button]="true" [detail]="false" (click)="openProfile(user.user)">
    <ion-avatar aria-hidden="true" slot="start">
      <img width="120" height="120"
           [ngSrc]="user.user.avatar ?? ('/assets/d3-images/' + (user.user.gender ?? 'male').toLowerCase() + '-icon.svg')"
           [appImgFallback]="('/assets/d3-images/' + (user.user.gender ?? 'male').toLowerCase() + '-icon.svg')">
    </ion-avatar>
    <ion-label>{{ user.user.full_name }}</ion-label>
  </ion-item>
  <ng-container *ngIf="loaded; else loading"></ng-container>
  <ion-infinite-scroll
    *ngIf="pagination?.next_url"
    #infiniteScroll (ionInfinite)="onIonInfinite($event)">
    <ion-infinite-scroll-content></ion-infinite-scroll-content>
  </ion-infinite-scroll>
</ion-list>


<ng-template #loading>
  <ion-item *ngFor="let one of [].constructor(3)">
    <ion-thumbnail slot="start">
      <ion-skeleton-text [animated]="true"></ion-skeleton-text>
    </ion-thumbnail>
    <ion-label>
      <h3>
        <ion-skeleton-text [animated]="true" style="width: 80%;"></ion-skeleton-text>
      </h3>
    </ion-label>
  </ion-item>
</ng-template>
