import {ChangeDetectorRef, Component, inject, Inject, OnInit} from '@angular/core';
import {IonicModule} from "@ionic/angular";
import {HeaderComponent} from "../../layout/header/header.component";
import {AsyncPip<PERSON>, DecimalPipe, <PERSON>ForOf, Ng<PERSON>f, NgOptimizedImage, NgTemplateOutlet} from "@angular/common";
import {FormsModule} from "@angular/forms";
import {StatisticsService} from "../../shared/services/statistics.service";
import {AppDataService} from "../../shared/services/app-data.service";
import {ThemeService} from "../../shared/services/theme.service";

@Component({
  selector: 'app-stats',
  templateUrl: 'stats.component.html',
  styleUrls: ['stats.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    NgTemplateOutlet,
    NgForOf,
    FormsModule,
    NgOptimizedImage,
    DecimalPipe,
    AsyncPipe,
    NgIf,
  ],
})
export class StatsComponent implements OnInit {
  selectedSegment = 1;
  cdr = inject(ChangeDetectorRef);
  currentData: any[] = [];
  colorScheme = 'light'

  constructor(
    public statisticsService: StatisticsService,
    public appDataService: AppDataService,
    public themeService: ThemeService,
  ) {
    //
  }

  ngOnInit(): void {
    this.themeService.isDark.subscribe(res => {
      this.colorScheme = res ? 'dark' : 'light'
    })
    this.statisticsService.$dataLoaded.subscribe((loaded) => {
      if (loaded)
        this.statisticsService.$data.subscribe((data) => {
          if (data.length > 0) {
            this.selectedSegment = data[0].id;
            this.updateCurrentData(data[0].data);
          }
        });
    });
  }

  activeListUpdated(event: any, data: any[]) {
    this.selectedSegment = event;
    this.updateCurrentData(data);
    this.cdr.detectChanges();
  }

  private updateCurrentData(data: any[]) {
    this.currentData = (data ?? []).sort((a, b) => b.count - a.count);
  }
}
