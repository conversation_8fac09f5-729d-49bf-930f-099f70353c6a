<app-header [hideProfileIcon]="false" [showBackButton]="true"></app-header>

<ion-content>
  <ng-container *ngIf="statisticsService.$dataLoaded | async as loaded;">
    <ng-container *ngIf="loaded">
      <ng-container *ngIf="statisticsService.$data | async as statisticsData;">
        <ion-segment [(ngModel)]="selectedSegment" color="primary">
          <ion-segment-button *ngFor="let oneStateType of statisticsData"
                              (click)="activeListUpdated(oneStateType.id, oneStateType.data)"
                              [value]="oneStateType.id">
            {{ oneStateType.name }}
          </ion-segment-button>
        </ion-segment>
        <ion-grid [fixed]="true">
          <ion-row>
            <ion-col size="12">
              <ng-template [ngTemplateOutlet]="data"/>
            </ion-col>
          </ion-row>
        </ion-grid>
      </ng-container>
    </ng-container>
    <ng-container *ngIf="!loaded">
      <ng-container *ngTemplateOutlet="loading"></ng-container>
    </ng-container>
  </ng-container>
</ion-content>

<ng-template #data>
  <ion-list class="ion-margin-bottom">
    <ion-item *ngFor="let item of currentData" [button]="true" [detail]="false">
      <ion-avatar aria-hidden="true" slot="start">
        <img width="120" height="120"
             [src]="(colorScheme === 'dark' ? ((appDataService.data$ | async)!['logoPathDark']) : ((appDataService.data$ | async)!['logoPathLight'])) || ((appDataService.data$ | async)!['iconPath']) || '/assets/icon/logo.svg'"
             alt="الشعار">
      </ion-avatar>
      <ion-label>{{ item.name }}</ion-label>
      <ion-note slot="end">{{ item.count | number: '1.0' }}</ion-note>
    </ion-item>
  </ion-list>
</ng-template>

<ng-template #loading>
  <ion-item *ngFor="let one of [].constructor(3)" class="ion-margin-vertical">
    <ion-thumbnail slot="start">
      <ion-skeleton-text [animated]="true"></ion-skeleton-text>
    </ion-thumbnail>
    <ion-label>
      <h3>
        <ion-skeleton-text [animated]="true" style="width: 80%;"></ion-skeleton-text>
      </h3>
    </ion-label>
  </ion-item>
</ng-template>
