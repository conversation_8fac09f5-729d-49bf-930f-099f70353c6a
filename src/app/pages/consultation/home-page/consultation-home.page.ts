import {ChangeDetector<PERSON><PERSON>, Component, make<PERSON><PERSON><PERSON><PERSON>, OnInit} from '@angular/core';
import {InfiniteScrollCustomEvent, IonicModule} from "@ionic/angular";
import {HeaderComponent} from "../../../layout/header/header.component";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {ConsultationService} from "../../../shared/services/consultation.service";
import {BehaviorSubject, finalize, of, take, tap} from "rxjs";
import {Consultation} from "../../../shared/models";
import {StorageService} from "../../../shared/services/storage.service";
import {CardComponent} from "../../../shared/components/card/card.component";
import {AsyncPipe, DatePipe, NgForOf, NgIf, NgTemplateOutlet} from "@angular/common";
import {RouterLink} from "@angular/router";

@Component({
  selector: 'consultation-home-page',
  templateUrl: 'consultation-home.page.html',
  styleUrls: ['consultation-home.page.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    IfIsBrowserDirective,
    CardComponent,
    NgIf,
    NgTemplateOutlet,
    AsyncPipe,
    DatePipe,
    NgForOf,
    RouterLink
  ],
})
export class ConsultationHomePage implements OnInit {
  isLoading = false;
  private _data$ = new BehaviorSubject<Consultation[]>([]);
  data$ = this._data$.asObservable();
  isReady: boolean = false;
  currentPage = 0;
  nextPageUrl = '';

  constructor(
    private consultationService: ConsultationService,
    private cdr: ChangeDetectorRef,
  ) {

  }

  getData() {
    if (this.isLoading)
      return of();
    this.isLoading = true;
    this.currentPage++;
    return this.consultationService.checkAndGetData(
      makeStateKey(`news-${this.currentPage}`),
      this.consultationService.index(this.nextPageUrl),
      []
    ).pipe(take(1), finalize(() => this.isLoading = false), tap((data) => {
      this.isReady = true;
      this.nextPageUrl = data.pagination?.next_url;
      this._data$.next([...this._data$.value, ...data.data] ?? []);
      this.cdr.detectChanges();
    }));
  }

  ngOnInit(): void {
    this.getData().pipe(take(1)).subscribe();
  }

  onIonInfinite(ev: any) {
    this.getData().pipe(take(1)).subscribe();
    setTimeout(() => {
      (ev as InfiniteScrollCustomEvent).target.complete().then();
    }, 500);
  }
}
