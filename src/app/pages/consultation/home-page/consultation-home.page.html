<app-header [hideProfileIcon]="false" [showBackButton]="true"></app-header>
<ion-content>
  <ion-grid [fixed]="true">
    <app-card title="الإستشارات" [showExpandButton]="false" [scrollable]="false">
      <ng-container *ngTemplateOutlet="consultations"></ng-container>
    </app-card>
    <ion-infinite-scroll (ionInfinite)="onIonInfinite($event)" *ngIf="nextPageUrl">
      <ion-infinite-scroll-content loadingText="Please wait..." loadingSpinner="bubbles"></ion-infinite-scroll-content>
    </ion-infinite-scroll>
  </ion-grid>
</ion-content>

<ng-template #consultations>
  <div style="overflow-y: scroll; overflow-x: hidden;">
    <ng-container *ngIf="data$ | async as consultations;">
      <ion-spinner *ngIf="!isReady" style="margin: auto; width: 100%;height: 40px;"></ion-spinner>
      <ion-button color="primary" expand="block" mode="ios" style="color: white;width: 100%;height: 3rem;"
                  class="ion-margin-top ion-margin-bottom" routerLink="/consultations/create">
        إستشارة جديدة
      </ion-button>
      <hr>
      <ion-list>
        <ion-item *ngFor="let consultation of consultations; let i = index"
                  style="border-bottom: 1px dotted var(--ion-color-primary);"
                  lines="full" button>
          <ion-avatar slot="start">
            <ion-img src="/assets/icon/logo.svg"></ion-img>
          </ion-avatar>
          <ion-label>
            <h4 style="line-height: 1.7 !important;">
              {{consultation.major}}
              <ion-chip [color]="consultation.status.color" *ngIf="consultation.status"
                        style="float:left;">{{consultation.status.title}}</ion-chip>
            </h4>
            <p class="">{{consultation.created_at | date:'longDate'}}</p>
          </ion-label>
        </ion-item>
      </ion-list>
    </ng-container>
  </div>
</ng-template>
