import {<PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChildren} from '@angular/core';
import {IonicModule, ToastController} from "@ionic/angular";
import {HeaderComponent} from "../../../layout/header/header.component";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {ConsultationService} from "../../../shared/services/consultation.service";
import {catchError} from "rxjs";
import {CardComponent} from "../../../shared/components/card/card.component";
import {AsyncPipe, CurrencyPipe, DatePipe, NgForOf, NgIf, NgTemplateOutlet} from "@angular/common";
import {RouterLink} from "@angular/router";
import {FormBuilder, FormsModule, ReactiveFormsModule, Validators} from "@angular/forms";
import {TranslateIonicTextsDirective} from "../../../shared/directives/translate-ionic-texts.directive";
import {CategoriesService} from "../../../shared/services/categories.service";

@Component({
  selector: 'consultation-create-page',
  templateUrl: 'consultation-create.page.html',
  styleUrls: ['consultation-create.page.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    IfIsBrowserDirective,
    CardComponent,
    NgIf,
    NgTemplateOutlet,
    AsyncPipe,
    DatePipe,
    NgForOf,
    RouterLink,
    CurrencyPipe,
    ReactiveFormsModule,
    FormsModule,
    TranslateIonicTextsDirective,
  ],
})
export class ConsultationCreatePage implements OnInit {
  @ViewChildren("checkboxes") checkboxes: QueryList<ElementRef> = new QueryList<ElementRef>();
  errorMessage: string | null = null;
  public formLoading: boolean = false;
  public isReady: boolean = true;
  nowTime = new Date().toISOString();
  createForm = this.formBuilder.group({
    category_id: ['', [
      Validators.required,
    ]],
    major: ['', [
      Validators.required,
      Validators.maxLength(191)
    ]],
    details: ['', [
      Validators.required,
      Validators.maxLength(5120)
    ]],
    services: this.formBuilder.array([])
  });

  constructor(
    private consultationService: ConsultationService,
    public toastController: ToastController,
    private formBuilder: FormBuilder,
    public categoriesService: CategoriesService,
  ) {

  }

  getData() {
  }

  ngOnInit(): void {
  }

  ionViewWillEnter() {
    // load data...
    this.refreshForm();
  }

  refreshForm() {
    this.createForm.patchValue({
      category_id: '',
      major: '',
      details: '',
    });
  }

  store() {
    this.formLoading = true
    this.errorMessage = null
    this.consultationService.store({
      category_id: this.createForm.controls.category_id.value,
      major: this.createForm.controls.major.value,
      details: this.createForm.controls.details.value,
    }).pipe(
      catchError(({error}) => {
        this.formLoading = false

        if (Object.keys(error.errors).length === 1)
          this.errorMessage = error.message;
        else
          this.errorMessage = 'خطأ في بيانات الإستشارة.';
        throw new Error(JSON.stringify(error))
      })
    ).subscribe((res) => {
      this.formLoading = false;
      this.createForm.reset();
      this.checkboxes.forEach((element) => {
        if ('checked' in element)
          element.checked = false;
      });
      this.toastController.create({
        header: 'تم إرسال الإستشارة بنجاح',
        message: 'جاري مراجعة الإستشارة من قبل المسؤلين !',
        position: 'top',
        buttons: [{
          side: 'end',
          text: 'x',
          role: 'cancel',
        }]
      }).then((toast) => {
        toast.present();
      });
    })
  }
}
