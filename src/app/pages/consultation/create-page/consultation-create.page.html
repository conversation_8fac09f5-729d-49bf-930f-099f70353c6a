<app-header [hideProfileIcon]="false" [showBackButton]="true"></app-header>
<ion-content>
  <ion-grid [fixed]="true">
    <app-card title="إضافة إستشارة جديدة" [showExpandButton]="false" [scrollable]="false">
      <ion-spinner *ngIf="!isReady" style="margin: auto; width: 100%;height: 40px;"></ion-spinner>

      <div class="" *ngIf="isReady" [formGroup]="createForm">
        <ion-item class="ion-margin-horizontal ion-margin-top">
          <ion-select placeholder="نوع الإستشارة" formControlName="category_id" TranslateIonicTexts>
            <ion-select-option *ngFor="let category$ of (categoriesService.$consultationCategories | async); let i= index"
                               [value]="category$.id.toString()">{{ category$.title }}</ion-select-option>
            <!--<ion-select-option value="LEGAL">استشارة قانونية</ion-select-option>
            <ion-select-option value="ACADEMIC">استشارة أكاديمية</ion-select-option>
            <ion-select-option value="FINANCIAL">استشارة مالية</ion-select-option>
            <ion-select-option value="MEDICAL">استشارة طبية</ion-select-option>-->
          </ion-select>
        </ion-item>

        <ion-item class="ion-margin-horizontal ion-margin-top">
          <ion-input label="العنوان" formControlName="major"></ion-input>
        </ion-item>

        <ion-item class="ion-margin-top ion-margin-bottom ion-margin-horizontal">
          <ion-textarea label="التفاصيل" labelPlacement="floating" formControlName="details"></ion-textarea>
        </ion-item>

        <ion-text color="danger" class="ion-text-center ion-margin-top" *ngIf="errorMessage">
          <h2 style="font-size: 1.3rem">{{ errorMessage }}</h2>
        </ion-text>

        <ion-button color="primary" expand="block" mode="ios" style="color: white;"
                    class="ion-margin-top ion-margin-horizontal"
                    (click)="store()" [disabled]="formLoading">
          <ion-spinner *ngIf="formLoading"></ion-spinner>
          <span *ngIf="!formLoading">
            إضافة جديد
          </span>
        </ion-button>
      </div>
    </app-card>
  </ion-grid>
</ion-content>
