import { ChangeDetectorRef, Component, Inject, OnInit, TransferState } from '@angular/core';
import {AsyncPipe, DatePipe, DOCUMENT, <PERSON>sonPipe, <PERSON><PERSON>orO<PERSON>, <PERSON><PERSON><PERSON>, NgTemplateOutlet} from "@angular/common";
import {InfiniteScrollCustomEvent, IonicModule} from "@ionic/angular";
import {HeaderComponent} from "../../../layout/header/header.component";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {IS_SERVER_PLATFORM} from "../../../shared/IS_SERVER_PLATFORM.token";
import {CardComponent} from "../../../shared/components/card/card.component";
import {ApiService} from "../../../shared/services/api.service";
import {HttpClient, HttpClientModule} from "@angular/common/http";

import {BehaviorSubject, take, tap, finalize, of} from "rxjs";
import {RouterModule} from "@angular/router";
import {QuranCompetition} from "../../../shared/models";


@Component({
  selector: 'app-quran-competitions-index',
  templateUrl: 'quran-competition-index.component.html',
  styleUrls: ['quran-competition-index.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    IfIsBrowserDirective,
    NgIf,
    CardComponent,
    NgTemplateOutlet,
    NgForOf,
    AsyncPipe,
    JsonPipe,
    RouterModule,
    DatePipe
  ],
})
export class QuranCompetitionIndexComponent implements OnInit {
  isLoading = false;
  quranCompetitionsPage = 0;
  quranCompetitionsNextPageUrl = '';
  private _quranCompetitions$ = new BehaviorSubject<QuranCompetition[]>([]);
  quranCompetitions$ = this._quranCompetitions$.asObservable()

  constructor(@Inject(DOCUMENT) private document: Document,
              @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
              private apiService: ApiService,
              private transferState: TransferState,
              private http: HttpClient,
              private cdr: ChangeDetectorRef
  ) {
  }

  ngOnInit(): void {
    if (!this.isServer) {
      this.browserSetup();
    }
    this.getQuranCompetitions().pipe(take(1)).subscribe();
  }

  browserSetup() {

  }

  getQuranCompetitions() {
    if (this.isLoading)
      return of();
    this.isLoading = true;
    this.quranCompetitionsPage++;
    return this.apiService.checkAndGetData(
      this.apiService.getDynamicStateKey(`quranCompetitions-${this.quranCompetitionsPage}`),
      this.apiService.getQuranCompetitions(this.quranCompetitionsNextPageUrl),
      []
    ).pipe(take(1), finalize(() => this.isLoading = false), tap((data) => {
      this.quranCompetitionsNextPageUrl = data.pagination?.next_url;
      this._quranCompetitions$.next([...this._quranCompetitions$.value, ...data.data].sort(
        (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      ) ?? []);
      this.cdr.detectChanges();
    }));
  }

  onIonInfinite(ev: any) {
    this.getQuranCompetitions().pipe(take(1)).subscribe();
    setTimeout(() => {
      (ev as InfiniteScrollCustomEvent).target.complete().then();
    }, 500);
  }
}
