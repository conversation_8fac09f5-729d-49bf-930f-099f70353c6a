<app-header [hideProfileIcon]="false" [showBackButton]="true" backTo="/home"></app-header>
<ion-content>
  <ion-grid [fixed]="true">
    <ion-row>
      <ion-col size="12">
        <ng-container *ngTemplateOutlet="quranCompetitions"></ng-container>
      </ion-col>
    </ion-row>
    <ion-infinite-scroll (ionInfinite)="onIonInfinite($event)" *ngIf="quranCompetitionsNextPageUrl">
      <ion-infinite-scroll-content></ion-infinite-scroll-content>
    </ion-infinite-scroll>
  </ion-grid>
</ion-content>

<ng-template #quranCompetitions>
  <div style="overflow-y: scroll; overflow-x: hidden; max-width: 370px; margin: auto;">
    <ng-container *ngIf="quranCompetitions$ | async as quranCompetitions;">
      <ion-spinner *ngIf="quranCompetitions.length === 0" style="margin: auto; width: 100%;height: 40px;"></ion-spinner>
      <ion-item *ngFor="let oneQuranCompetition of quranCompetitions; let i = index"
                [disabled]="oneQuranCompetition.closed"
                [routerLink]="'/quran-competitions/' + oneQuranCompetition.id"
                [title]="oneQuranCompetition.title">
        <ion-avatar slot="start" [routerLink]="'/quran-competitions/'+ oneQuranCompetition.id">
          <ion-img [src]="'/assets/images/icons/quran_competition.svg'"></ion-img>
        </ion-avatar>
        <!--<ion-label [routerLink]="'quran-competitions/'+ oneQuranCompetition.id">
          <h4 class="ion-text-wrap" style="line-height: 1.7 !important;">{{ oneQuranCompetition.title }}</h4>
          <p class="">{{oneQuranCompetition.created_at | date:'longDate'}}</p>
        </ion-label>-->
        <ion-row>
          <ion-col size="24" class="ion-no-padding">
            <ion-label [routerLink]="'/quran-competitions/' + oneQuranCompetition.id">
              <h4 class="ion-text-wrap" style="line-height: 1.7 !important;">
                {{ oneQuranCompetition.title }}
              </h4>
            </ion-label>
          </ion-col>
          <ion-col class="ion-no-padding">
            <ion-text color="primary" *ngIf="oneQuranCompetition.closed">{{ 'منتهية' }}</ion-text>
            <ion-text color="success" *ngIf="!oneQuranCompetition.closed && oneQuranCompetition.accept_users">{{ 'التسجيل مفتوح' }}</ion-text>
            <ion-text color="danger" *ngIf="!oneQuranCompetition.closed && !oneQuranCompetition.accept_users">{{ 'التسجيل مغلق' }}</ion-text>
          </ion-col>
          <!--
          <ion-col size="auto" class="ion-no-padding">{{ ' ← ' }}</ion-col>
          <ion-col class="ion-no-padding" style="text-align: left">
            {{oneQuranCompetition.end_at | date:'longDate'}}
          </ion-col>-->
        </ion-row>
      </ion-item>
    </ng-container>
  </div>
</ng-template>
