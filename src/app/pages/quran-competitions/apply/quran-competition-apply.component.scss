#container {
  text-align: center;

  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}

#container strong {
  font-size: 20px;
  line-height: 26px;
}

#container p {
  font-size: 16px;
  line-height: 22px;

  color: #8c8c8c;

  margin: 0;
}

#container a {
  text-decoration: none;
}

.swiper {
  width: 100%;
  max-width: 600px;
  height: 100%;
  min-height: 470px;
  margin-block-start: 16px;
  margin-block-end: 16px;
}

ion-icon {
  font-size: 40px;
}

.buttons-wrapper {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-content: center;
  justify-content: center;
  align-items: center;
  gap: 15px;
  max-width: 550px;
  margin: auto;
  margin-block-end: 32px;

  .page-button {
    cursor: pointer;
    background: var(--ion-color-light, #fff);
    width: 175px;
    height: 113px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
}

::ng-deep {
  .swiper-pagination {
    bottom: 0 !important;
  }

  h1, h2, h3, h4, h5, h6 {
    line-height: 1.35 !important;
  }
}

cdk-virtual-scroll-viewport {
  height: 100%;
  width: 100%;
}

.buy-button {
  margin-top: 0;
  margin-bottom: 0;
  position: fixed;
  /*bottom: 25px;*/
  left: 0;
  right: 0;
  top: 80%;
}

swiper-container {
  --swiper-pagination-bullet-inactive-color: #DBA852;
}

swiper-slide {
  border-radius: 18px;
  font-size: 22px;
  font-weight: bold;
  //width: calc(100% - 50px) !important;
  //margin-left: 5px;
  height: 470px;
  border: 1px solid rgba(219, 168, 82, 0.6);
}

/*swiper-slide:nth-child(1n) {
  border: 1px solid rgb(206, 17, 17);
}

swiper-slide:nth-child(2n) {
  border: 1px solid rgb(0, 140, 255);
}

swiper-slide:nth-child(3n) {
  border: 1px solid rgb(10, 184, 111);
}

swiper-slide:nth-child(4n) {
  border: 1px solid rgb(211, 122, 7);
}*/
