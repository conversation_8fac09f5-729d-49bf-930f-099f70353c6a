<app-header [hideProfileIcon]="false" [showBackButton]="true"
            [backTo]="quranCompetition?.id ? ('/quran-competitions/' + quranCompetition?.id) : '/quran-competitions'"></app-header>
<ion-content>
  <ng-container *ngIf="quranCompetition; else spinner">
    <app-card [title]="quranCompetition.title" [showExpandButton]="false" [scrollable]="false" style="height: 100%;"
              *ngIf="quranCompetition">
      <ion-card-content>
        <ion-list [inset]="true" [formGroup]="form">
          <ion-item detail="false">
            <ion-label>الاسم</ion-label>
            <ion-note>{{ (profileService.$user |async)?.full_name }}</ion-note>
          </ion-item>

          <ion-item style="font-size: 0.8rem">
            <ion-icon slot="start" *ngIf="form.touched && form.controls.challenge_id.invalid"
                      name="alert-circle-outline" color="danger" style="font-size: 32px;"></ion-icon>
            <ion-select label="عدد الأجزاء" formControlName="challenge_id" TranslateIonicTexts>
              <ion-select-option *ngFor="let challenge$ of quranCompetition?.challenges; let i= index"
                                 [value]="challenge$.id.toString()">{{ challenge$.chapters }}</ion-select-option>
            </ion-select>
          </ion-item>
        </ion-list>

        <ion-button color="primary" expand="block" mode="ios" style="color: white;"
                    (click)="applyRequest()" [disabled]="formLoading">
          <ion-spinner *ngIf="formLoading"></ion-spinner>
          <span *ngIf="!formLoading">
            قدم طلبك
          </span>
        </ion-button>
      </ion-card-content>
    </app-card>
  </ng-container>
</ion-content>

<ng-template #spinner>
  <div class="ion-flex ion-justify-content-center">
    <ion-spinner size="large" name="crescent" style="width: 70px; height: 70px"></ion-spinner>
  </div>
</ng-template>
