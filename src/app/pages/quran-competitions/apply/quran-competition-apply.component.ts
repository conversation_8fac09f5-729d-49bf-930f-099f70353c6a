import {Component, Inject, OnInit, ViewChild, CUSTOM_ELEMENTS_SCHEMA, Input} from '@angular/core';
import {
  AsyncPipe, CurrencyPipe,
  DatePipe,
  DecimalPipe,
  DOCUMENT,
  JsonPipe,
  NgForOf,
  NgIf,
  NgOptimizedImage,
  NgTemplateOutlet
} from "@angular/common";
import {IonicModule, IonModal, NavController, ToastController} from "@ionic/angular";
import {HeaderComponent} from "../../../layout/header/header.component";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {IS_SERVER_PLATFORM} from "../../../shared/IS_SERVER_PLATFORM.token";
import {CardComponent} from "../../../shared/components/card/card.component";
import {ApiService} from "../../../shared/services/api.service";
import {HttpClientModule} from "@angular/common/http";
import {catchError, Observable, tap, take, throwError, finalize} from "rxjs";
import {ActivatedRoute, NavigationExtras, RouterModule, Router} from "@angular/router";
import {QuranCompetition} from "../../../shared/models";
import {RenderBlockPipe} from "../../../shared/pipes/render-block.pipe";
import {SanitizeHtmlPipe} from "../../../shared/pipes/sanitize-html.pipe";
import {FormBuilder, FormsModule, ReactiveFormsModule, Validators} from "@angular/forms";
import {FormatDistancePipe} from "../../../shared/pipes/format-distance.pipe";
import {GenericService} from "../../../shared/services/generic.service";
import {ProfileService} from "../../../shared/services/profile.service";
import {TranslateIonicTextsDirective} from "../../../shared/directives/translate-ionic-texts.directive";

@Component({
  selector: 'app-quran-competition-apply',
  templateUrl: 'quran-competition-apply.component.html',
  styleUrls: ['quran-competition-apply.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    IfIsBrowserDirective,
    NgIf,
    CardComponent,
    NgTemplateOutlet,
    NgForOf,
    AsyncPipe,
    JsonPipe,
    RouterModule,
    DatePipe,
    DecimalPipe,
    RenderBlockPipe,
    SanitizeHtmlPipe,
    NgOptimizedImage,
    FormsModule,
    ReactiveFormsModule,
    CurrencyPipe,
    FormatDistancePipe,
    TranslateIonicTextsDirective,
  ],
})
export class QuranCompetitionApplyComponent implements OnInit {
  @Input() quranCompetition?: QuranCompetition;
  @Input('id') quranCompetitionId?: number;
  formLoading = false;
  form = this.formBuilder.group({
    challenge_id: ['', [Validators.required]],
  });

  constructor(@Inject(DOCUMENT) private document: Document,
              private activatedRoute: ActivatedRoute,
              @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
              public profileService: ProfileService,
              public formBuilder: FormBuilder,
              private apiService: ApiService,
              private toastController: ToastController,
              private router: Router,
              private navCtrl: NavController,
              private genericService: GenericService,
              @Inject('Window') private window: Window,
  ) {
  }

  ngOnInit(): void {
    if (!this.isServer) {
      this.browserSetup();
    }
    if (this.quranCompetitionId)
      this.getQuranCompetitionById(this.quranCompetitionId).subscribe();
  }

  getQuranCompetitionById(id: number) {
    return this.apiService.checkAndGetData(
      this.apiService.getDynamicStateKey(`quran-competition-details-${id}`),
      this.apiService.getQuranCompetitionById(id ?? 0, {action: 'apply'}),
      []
    ).pipe(take(1), tap((data: QuranCompetition) => {
      if (data.closed)
        this.router.navigate([`/quran-competitions`])
      else if (!data.accept_users || data.user_reach_max || (data.user_has_request && data.user_request_status))
        this.router.navigate([`/quran-competitions/${data.id}`])
      else {
        this.quranCompetition = data;
        /*if (data.challenges && data.challenges.length > 0)
          this.form.patchValue({challenge_id: data.challenges[0].id.toString()})*/
      }
    }))
  }

  browserSetup() {
  }

  applyRequest() {
    if (!this.form.valid)
      return this.form.markAllAsTouched();
    if (this.quranCompetition) {
      this.formLoading = true;
      this.apiService
        .applyForQuranCompetitionById(this.quranCompetition.id, {
          challenge_id: this.form.controls.challenge_id.value
        })
        .pipe(
          finalize(() => this.formLoading = false),
          catchError(({error}) => {
            let errorMessage = error.message || 'خطأ لا يمكن تقديم طلبك في الوقت الحالي .'
            this.toastController.create({
              message: errorMessage,
              position: 'bottom',
              color: 'danger',
              buttons: [{
                side: 'end',
                text: 'x',
                role: 'cancel',
              }]
            }).then((toast) => {
              toast.present()
            })
            throw new Error(JSON.stringify(error))
          })
        )
        .subscribe((res) => {
          if (res.success === true) {
            this.toastController.create({
              message: 'تم تقديم طلبك بنجاح.',
              position: 'bottom',
              color: 'success',
              buttons: [{
                side: 'end',
                text: 'إلغاء',
                role: 'cancel',
              }],
            }).then((toast) => {
              toast.present()
              this.form.patchValue({challenge_id: null});
              this.form.markAsUntouched();
              this.router.navigate([`/quran-competitions/${this.quranCompetition?.id}`])
              setTimeout(() => toast.dismiss(), 5000)
            })
          } else if (res.error || res.message) {
            this.toastController.create({
              message: res.error || res.message,
              position: 'bottom',
              color: 'danger',
              buttons: [{
                side: 'end',
                text: 'إلغاء',
                role: 'cancel',
              }],
            }).then((toast) => {
              toast.present()
              setTimeout(() => toast.dismiss(), 5000)
            })
          }
        })
    }
  }
}
