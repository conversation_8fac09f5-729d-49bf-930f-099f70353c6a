import { ChangeDetectorRef, Component, Inject, OnInit, Input, TransferState } from '@angular/core';
import {AsyncPipe, DatePipe, DOCUMENT, <PERSON>sonPipe, <PERSON>ForOf, NgIf, NgTemplateOutlet} from "@angular/common";
import {InfiniteScrollCustomEvent, IonicModule} from "@ionic/angular";
import {HeaderComponent} from "../../../layout/header/header.component";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {IS_SERVER_PLATFORM} from "../../../shared/IS_SERVER_PLATFORM.token";
import {CardComponent} from "../../../shared/components/card/card.component";
import {ApiService} from "../../../shared/services/api.service";
import {HttpClient, HttpClientModule} from "@angular/common/http";

import {BehaviorSubject, take, tap, finalize} from "rxjs";
import {RouterModule, Router, ActivatedRoute} from "@angular/router";
import {QuranCompetition} from "../../../shared/models";


@Component({
  selector: 'app-quran-competitions-details',
  templateUrl: 'quran-competition-details.component.html',
  styleUrls: ['quran-competition-details.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    IfIsBrowserDirective,
    NgIf,
    CardComponent,
    NgTemplateOutlet,
    NgForOf,
    AsyncPipe,
    JsonPipe,
    RouterModule,
    DatePipe
  ],
})
export class QuranCompetitionDetailsComponent implements OnInit {
  @Input() quranCompetition?: QuranCompetition;
  @Input('id') quranCompetitionId?: number;

  constructor(@Inject(DOCUMENT) private document: Document,
              @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
              private apiService: ApiService,
              private transferState: TransferState,
              private http: HttpClient,
              private router: Router,
              private route: ActivatedRoute,
              private cdr: ChangeDetectorRef
  ) {
    route.params.subscribe(val => {
      if (this.quranCompetitionId)
        this.getQuranCompetitionById(this.quranCompetitionId).subscribe();
    });
  }

  ngOnInit(): void {
    if (!this.isServer) {
      this.browserSetup();
    }
    if (this.quranCompetitionId)
      this.getQuranCompetitionById(this.quranCompetitionId).subscribe();
  }

  getQuranCompetitionById(id: number) {
    return this.apiService.checkAndGetData(
      this.apiService.getDynamicStateKey(`quran-competition-details-${id}`),
      this.apiService.getQuranCompetitionById(id ?? 0),
      []
    ).pipe(take(1), tap((data: QuranCompetition) => {
      if (data.closed)
        this.router.navigate([`/quran-competitions`])
      else
        this.quranCompetition = data;
    }))
  }

  browserSetup() {

  }
}
