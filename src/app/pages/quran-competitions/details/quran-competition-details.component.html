<app-header [hideProfileIcon]="false" [showBackButton]="true" backTo="/quran-competitions"></app-header>
<ion-content>
  <ion-grid [fixed]="true" *ngIf="quranCompetition; else spinner">
    <app-card [title]="quranCompetition.title" [showExpandButton]="false" [scrollable]="false" style="height: 100%;">
      <div class="ion-flex ion-justify-content-center">
        <ion-img src="/assets/images/quran_competition_brochure.jpeg"/>
      </div>
      <div class="ion-text-center" *ngIf="quranCompetition?.user_has_request">
        <ion-text color="primary"
                  *ngIf="!quranCompetition?.user_request_status">
          <h4>تم تقديم الطلب بنجاح .</h4>
        </ion-text>
        <ion-text color="success"
                  *ngIf="quranCompetition?.user_request_status === 'APPROVED'">
          <h4>تم قبول طلبك .</h4>
        </ion-text>
        <ion-text color="danger"
                  *ngIf="quranCompetition?.user_request_status === 'DECLINED'">
          <h4>تم رفض طلبك !</h4>
        </ion-text>
      </div>
      <ion-button color="primary" expand="block" mode="ios"
                  routerLink="/quran-competitions/{{ quranCompetition?.id }}/apply"
                  *ngIf="quranCompetition?.accept_users && !quranCompetition?.user_reach_max && (!quranCompetition?.user_has_request || !quranCompetition?.user_request_status)">
        <span *ngIf="!quranCompetition?.user_has_request">تقدم للمشاركة</span>
        <span *ngIf="quranCompetition?.user_has_request">تعديل طلبك</span>
      </ion-button>
      <ion-text color="danger" *ngIf="quranCompetition?.user_reach_max">
        <h4>لقد اتممت ثلاثون جزءًا لا يمكنك المشاركة .</h4>
      </ion-text>
    </app-card>
  </ion-grid>
</ion-content>

<ng-template #spinner>
  <div class="ion-flex ion-justify-content-center">
    <ion-spinner size="large" name="crescent" style="width: 70px; height: 70px"></ion-spinner>
  </div>
</ng-template>
