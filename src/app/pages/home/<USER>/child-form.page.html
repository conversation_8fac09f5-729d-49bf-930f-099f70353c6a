<ion-modal #modal
           trigger="child-data-modal"
           (ionModalDidPresent)="loadData(true)"
           [initialBreakpoint]="0.3" [breakpoints]="[0, 0.3, 0.5, 0.75]">
  <ng-template>
    <ion-header>
      <ion-toolbar>
        <ng-container *ngIf="!selectedChild">
          <ion-buttons slot="start">
            <ion-button color="medium" fill="clear" (click)="dismiss()">
              <ion-icon slot="icon-only" name="close-circle-outline"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ng-container>
        <ng-container *ngIf="selectedChild">
          <ion-buttons slot="start">
            <ion-button color="medium" fill="clear" (click)="cancel()" [disabled]="formLoading"
                        style="rotate: 180deg; padding: 0; margin: 0;">
              <ion-icon slot="icon-only" name="altwijry-back-button"></ion-icon>
            </ion-button>
          </ion-buttons>
          <ion-title color="light">
            <ion-text color="primary">
              استكمال بيانات
              {{ selectedChild!.gender === 'MALE' ? 'الابن' : 'الابنة' }}
              {{ selectedChild!.name }}
            </ion-text>
          </ion-title>
          <ion-buttons slot="end">
            <ion-button color="primary" expand="block" mode="ios" style="color: white;"
                        (click)="store()" [disabled]="formLoading">
              <ion-spinner *ngIf="formLoading"></ion-spinner>
              <span *ngIf="!formLoading">حفظ</span>
            </ion-button>
          </ion-buttons>
        </ng-container>
      </ion-toolbar>
    </ion-header>
    <ion-content>
      <!--<ion-searchbar placeholder="Search" (click)="modal.setCurrentBreakpoint(0.75)"></ion-searchbar>-->
      <ion-list class="ion-margin" [inset]="true" [formGroup]="updateForm">
        <ng-container *ngIf="!childDataLoading; else loading">
          @if (selectedChild) {
            <ion-item detail="false">
              <ion-label>الاسم</ion-label>
              <ion-note>{{ selectedChild.name }}</ion-note>
            </ion-item>

            <ion-item detail="false">
              <ion-label>الرقم التعريفي</ion-label>
              <ion-note>{{ selectedChild.family_user_id }}</ion-note>
            </ion-item>

            @if (selectedChild.dob) {
              <ion-item detail="false">
                <ion-label>تاريخ الميلاد (ميلادي)</ion-label>
                <ion-note>{{ (selectedChild.dob) | date: 'yyyy-MM-dd' }}</ion-note>
              </ion-item>
            } @else {
              <ion-item lines="none">
                <div>تاريخ الميلاد</div>
              </ion-item>
              <ion-item [formGroupName]="'dobGroup'" title="تاريخ الميلاد">
                <ion-input label="اليوم" type="number" formControlName="day"></ion-input>
                <ion-input label="الشهر" type="number" formControlName="month"></ion-input>
                <ion-input label="السنة" type="number" formControlName="year"></ion-input>
              </ion-item>
            }

            @if (selectedChild.marital_status) {
              <ion-item detail="false">
                <ion-label>الحالة الإجتماعية</ion-label>
                <ion-note>{{
                    {
                      SINGLE: 'أعزب',
                      MARRIED: 'متزوج',
                      DIVORCED: 'مطلق',
                      WIDOWER: 'أرمل'
                    }[selectedChild.marital_status] ?? '!'
                  }}
                </ion-note>
              </ion-item>
            } @else {
              <ion-item class="" style="font-size: 0.8rem">
                <ion-icon slot="start" *ngIf="updateForm.touched && updateForm.get('marital_status')!.invalid"
                          name="alert-circle-outline" color="danger"></ion-icon>
                <ion-select label="الحالة الإجتماعية" formControlName="marital_status" TranslateIonicTexts>
                  <ion-select-option value="SINGLE">أعزب</ion-select-option>
                  <ion-select-option value="MARRIED">متزوج</ion-select-option>
                  <ion-select-option value="DIVORCED">مطلق</ion-select-option>
                  <ion-select-option value="WIDOWER">أرمل</ion-select-option>
                </ion-select>
              </ion-item>
            }

            @if (selectedChild.health_status) {
              <ion-item detail="false">
                <ion-label>الحالة الصحية</ion-label>
                <ion-note>{{ selectedChild.health_status ?? '!' }}</ion-note>
              </ion-item>
            } @else {
              <ion-item style="font-size: 0.8rem">
                <ion-icon slot="start" *ngIf="updateForm.touched && updateForm.get('health_status')!.invalid"
                          name="alert-circle-outline" color="danger"></ion-icon>
                <ion-select label="الحالة الصحية" formControlName="health_status" TranslateIonicTexts>
                  <ion-select-option value="سليم">سليم</ion-select-option>
                  <ion-select-option value="مريض بمرض مزمن">مريض بمرض مزمن</ion-select-option>
                  <ion-select-option value="معاق">معاق</ion-select-option>
                </ion-select>
              </ion-item>
            }

            @if (selectedChild.educational_status) {
              <ion-item detail="false">
                <ion-label>التعليم</ion-label>
                <ion-note>{{ selectedChild.educational_status ?? '!' }}</ion-note>
              </ion-item>
            } @else {
              <ion-item style="font-size: 0.8rem">
                <ion-icon slot="start" *ngIf="updateForm.touched && updateForm.get('educational_status')!.invalid"
                          name="alert-circle-outline" color="danger"></ion-icon>
                <ion-select label="التعليم" formControlName="educational_status" TranslateIonicTexts>
                  <ion-select-option value="طفل">طفل</ion-select-option>
                  <ion-select-option value="ابتدائي">ابتدائي</ion-select-option>
                  <ion-select-option value="متوسط">متوسط</ion-select-option>
                  <ion-select-option value="دبلوم">دبلوم</ion-select-option>
                  <ion-select-option value="ثانوي">ثانوي</ion-select-option>
                  <ion-select-option value="جامعي">جامعي</ion-select-option>
                  <ion-select-option value="بكالوريوس">بكالوريوس</ion-select-option>
                  <ion-select-option value="دكتوراه">دكتوراه</ion-select-option>
                  <ion-select-option value="ماجستير">ماجستير</ion-select-option>
                </ion-select>
              </ion-item>
            }

            @if (selectedChild.user_region_id) {
              <ion-item detail="false">
                <ion-label>مكان الإقامة</ion-label>
                <ion-note>{{ selectedChild.user_region!.title_ar ?? '!' }}</ion-note>
              </ion-item>
            } @else {
              <ion-item style="font-size: 0.8rem">
                <ion-icon slot="start" *ngIf="updateForm.touched && updateForm.get('user_region_id')!.invalid"
                          name="alert-circle-outline" color="danger"></ion-icon>
                <ion-select label="مكان الإقامة" formControlName="user_region_id" TranslateIonicTexts>
                  <ion-select-option *ngFor="let region$ of (profileService.$regions | async); let i= index"
                                     [value]="region$.id.toString()">{{ region$.title_ar }}
                  </ion-select-option>
                </ion-select>
              </ion-item>
            }
          } @else {
            <ion-item *ngFor="let child of childData"
                      style="cursor: pointer;" (click)="selectChild(child)">
              <ion-avatar slot="start">
                <ion-img
                  src="{{ child.profile_photo_url ?? ('/assets/d3-images/' + (child.gender ?? 'male').toLowerCase() + '-icon.svg') }}"></ion-img>
              </ion-avatar>
              <ion-label>
                <h2>{{ child.name }}</h2>
                <p>{{ child.family_user_id }}</p>
              </ion-label>
            </ion-item>
          }
        </ng-container>
      </ion-list>
    </ion-content>
  </ng-template>
  <ng-template #loading>
    <ion-item *ngFor="let one of [].constructor(3)">
      <ion-thumbnail slot="start">
        <ion-skeleton-text [animated]="true"></ion-skeleton-text>
      </ion-thumbnail>
      <ion-label>
        <h3>
          <ion-skeleton-text [animated]="true" style="width: 80%;"></ion-skeleton-text>
        </h3>
      </ion-label>
    </ion-item>
  </ng-template>
</ion-modal>
