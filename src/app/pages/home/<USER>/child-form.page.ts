import {Component, CUSTOM_ELEMENTS_SCHEMA, Inject, OnChanges, OnInit, ViewChild} from '@angular/core';
import {<PERSON>ync<PERSON><PERSON><PERSON>, DatePipe, JsonPipe, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>Outlet} from "@angular/common";
import {IonicModule, ModalController, ToastController} from "@ionic/angular";
import {HeaderComponent} from "../../../layout/header/header.component";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {IS_SERVER_PLATFORM} from "../../../shared/IS_SERVER_PLATFORM.token";
import {CardComponent} from "../../../shared/components/card/card.component";
import {RouterModule} from "@angular/router";
import {animate, state, style, transition, trigger} from '@angular/animations';
import {ProfileService} from "../../../shared/services/profile.service";
import {catchError, finalize} from "rxjs";
import {FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators} from "@angular/forms";
import {TranslateIonicTextsDirective} from "../../../shared/directives/translate-ionic-texts.directive";
import {format, isValid, parseISO, subYears} from "date-fns";

// @ts-ignore
import {hijriToJulian, julianToGregorian} from '../../../shared/helpers/hijri';

@Component({
  selector: 'app-child-form',
  templateUrl: 'child-form.page.html',
  styleUrls: ['child-form.page.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    IfIsBrowserDirective,
    NgIf,
    NgFor,
    CardComponent,
    NgTemplateOutlet,
    NgForOf,
    AsyncPipe,
    JsonPipe,
    RouterModule,
    DatePipe,
    NgClass,
    FormsModule,
    ReactiveFormsModule,
    TranslateIonicTextsDirective,
  ],
  animations: [
    trigger('slideDown', [
      state('void', style({
        transform: 'translateY(-100%)',
        opacity: 0
      })),
      state('*', style({
        transform: 'translateY(0)',
        opacity: 1
      })),
      transition('void <=> *', [
        animate('0.5s ease-in-out')
      ]),
    ])
  ],
})
export class ChildFormPage implements OnInit, OnChanges {
  childDataLoading: boolean = true;
  formLoading: boolean = false;
  childData: ChildData[] = [];
  selectedChild?: ChildData;
  public updateForm: any = this.formBuilder.group({});
  dateError: string = '';
  age = 0;
  nowTime: Date = new Date();
  @ViewChild('modal') modal: any;
  protected readonly window = window;

  constructor(
    @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
    public profileService: ProfileService,
    public modalCtrl: ModalController,
    public toastController: ToastController,
    public formBuilder: FormBuilder,
  ) {
  }

  ngOnInit(): void {
    if (this.isServer) {
      console.log('is server')
    }
  }

  ngOnChanges() {
    //
  }

  loadData(initialLoad: boolean = false) {
    this.childDataLoading = true;
    this.formLoading = false;
    this.selectedChild = undefined;
    this.profileService.getUnfilledChildren()
      .pipe(
        catchError(async (err) => {
          this.modalCtrl.dismiss();
          (await this.toastController.create({
            message: 'خطأ أثناء تحميل البيانات يمكنك المحاولة لاحقاً!',
            duration: 5000,
            position: 'bottom',
            icon: 'alert-circle-outline',
            color: 'warning',
          })).present()
          return err;
        })
      )
      .subscribe(async (res) => {
        if (!res.length) {
          this.profileService.loadUser();
          this.modalCtrl.dismiss();
          await (async () => {
            const toast = await this.toastController.create({
              message: 'تم استكمال جميع البيانات بنجاح',
              duration: 5000,
              position: 'bottom',
              icon: 'alert-circle-outline',
              color: 'success',
            });
            await toast.present();
          })();
        }
        this.childData = res;
        this.childDataLoading = false
        if (initialLoad) {
          let currentBreakpoint = await this.modal.getCurrentBreakpoint()
          if (currentBreakpoint < 0.5)
            this.modal.setCurrentBreakpoint(0.5);
        }
      });
  }

  dismiss() {
    this.modalCtrl.dismiss();
  }

  cancel() {
    this.selectedChild = undefined;
    this.updateForm = this.formBuilder.group({});
  }

  async store() {
    if (this.selectedChild)
      if (this.updateForm.valid) {
        const childData = this.updateForm.value;
        this.formLoading = true;
        this.profileService.updateUnfilledChild({
          user_id: this.selectedChild.id,
          ...childData
        })
          .pipe(
            finalize(() => this.formLoading = false),
            catchError(async ({error}) => {
              this.formLoading = false
              let errorMessage = '';
              if ((error.errors && Object.keys(error.errors).length === 1) || error.message)
                errorMessage = error.message;
              else
                errorMessage = 'خطأ أثناء تعديل البيانات .';
              (await this.toastController.create({
                message: errorMessage,
                duration: 5000,
                position: 'bottom',
                icon: 'alert-circle-outline',
                color: 'warning',
              })).present()
              throw new Error(JSON.stringify(error))
            })
          )
          .subscribe(async (res: any) => {
            if (this.selectedChild) {
              let childPrefix = this.selectedChild.gender === 'MALE' ? 'الابن' : 'الابنة';
              (await this.toastController.create({
                message: `تم تعديل بيانات ${childPrefix} بنجاح`,
                duration: 5000,
                position: 'bottom',
                icon: 'alert-circle-outline',
                color: 'success',
              })).present()
            }
            this.loadData();
          })
      } else if (this.dateError) {
        (await this.toastController.create({
          message: this.dateError,
          duration: 5000,
          position: 'bottom',
          icon: 'alert-circle-outline',
          color: 'danger',
        })).present()
      }
  }

  selectChild(child: any) {
    this.selectedChild = child;
    this.modal.setCurrentBreakpoint(0.75);
    this.dateError = '';
    this.updateForm = this.formBuilder.group({});
    if (!child.dob) {
      this.updateForm.addControl('dob', this.formBuilder.control(null, [Validators.required]));
      this.updateForm.addControl('dobGroup', this.formBuilder.group({
        day: ['', [Validators.required, Validators.min(1), Validators.max(31)]],
        month: ['', [Validators.required, Validators.min(1), Validators.max(12)]],
        year: ['', [Validators.required, Validators.min(1300), Validators.max(this.nowTime.getFullYear())]],
      }));
      this.updateForm.get('dobGroup')?.valueChanges.subscribe(() => {
        this.calculateDate();
      });
    }
    if (!child.health_status)
      this.updateForm.addControl('health_status', this.formBuilder.control(null, [Validators.required]));
    if (!child.educational_status)
      this.updateForm.addControl('educational_status', this.formBuilder.control(null, [Validators.required]));
    if (!child.marital_status)
      this.updateForm.addControl('marital_status', this.formBuilder.control(null, [Validators.required]));
    /*if (!child.national_id)
      this.updateForm.addControl('national_id', this.formBuilder.control(child.national_id, [Validators.required]));*/
    if (!child.user_region_id)
      this.updateForm.addControl('user_region_id', this.formBuilder.control(null, [Validators.required]));
  }

  calculateDate() {
    const dobGroup = this.updateForm.get('dobGroup') as FormGroup;

    if (dobGroup.valid) {
      const {day, month, year} = dobGroup.value;
      const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      const date = parseISO(dateStr);

      if (!isValid(date)) {
        this.dateError = 'قم بإدخال تاريخ الميلاد بشكل صحيح رجاء';
        return;
      }
      let gregorianDate = date;

      if (this.isHijriDate(+year)) {
        // convert and handle Hijri to Gregorian if needed
        const hijriToGregorian = this.handleHijriToGregorianConversion(day, month, year);
        gregorianDate = new Date(hijriToGregorian.year, hijriToGregorian.month - 1, hijriToGregorian.day);
      }

      const maxDate = subYears(gregorianDate, 100);
      if (gregorianDate < maxDate && year > 1900) {
        this.dateError = 'لا يمكن أن يكون تاريخ الميلاد أكبر من 100 سنة';
        return;
      }

      this.dateError = '';
      this.updateForm.controls.dob.setValue(format(gregorianDate, 'yyyy-MM-dd'));
      this.calculateAge();

    } else {
      this.dateError = 'قم بإدخال تاريخ الميلاد بشكل صحيح رجاء';
    }
  }

  handleHijriToGregorianConversion(day: string, month: string, year: string) {
    const hijriToJulianDate = hijriToJulian(+year, +month, +day);
    return julianToGregorian(hijriToJulianDate) as { year: number, month: number, day: number };
  }

  isHijriDate(year: number): boolean {
    return year > 1300 && year < 1600;
  }

  calculateAge(): void {
    const dob = this.updateForm.controls.dob;
    if (!dob.value) {
      this.dateError = 'قم بإدخال تاريخ الميلاد بشكل صحيح رجاء';
      return;
    }
    const date = parseISO(dob.value);
    if (!isValid(date)) {
      this.dateError = 'قم بإدخال تاريخ الميلاد بشكل صحيح رجاء';
      return;
    }


    const birthDateObj = new Date(date);
    const currentDate = new Date();

    if (birthDateObj > currentDate) {
      this.age = 0;
      return;
    }

    this.age = currentDate.getFullYear() - birthDateObj.getFullYear();

    const monthDifference = currentDate.getMonth() - birthDateObj.getMonth();
    if (monthDifference < 0 || (monthDifference === 0 && currentDate.getDate() < birthDateObj.getDate())) {
      this.age--;
    }
  }
}

export interface ChildData {
  id: number,
  name: string,
  gender: string,
  family_user_id: string,
  profile_photo_url: string,
  dob: string,
  health_status: string,
  educational_status: string,
  marital_status: string,
  national_id: string,
  user_region_id: number,
  user_region: { id: number, title_ar: string },

  [key: string]: string | number | any | boolean
}
