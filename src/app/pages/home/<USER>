<app-header [hideProfileIcon]="false"></app-header>
<ion-content>
  <ion-grid [fixed]="true">
    @if (supportedVersion) {
      <div [@slideDown] class="ion-margin-top"
           *ngIf="versionChecked && appHasNewVersion">
        <ion-text color="danger">
          <h2 class="text-secondary" style="width: 350px; margin: auto;">
            يوجد تحديث جديد للتطبيق
            <a (click)="openStore()"
               href="javascript:void(0)">
              @if (storeLoading) {
                <ion-spinner></ion-spinner>
              } @else {
                حدث الآن
              }
            </a>
          </h2>
        </ion-text>
      </div>
      <ion-text color="secondary" *ngIf="!(profileService.$user |async)?.id">
        <h2 class="text-secondary" style="width: 350px; margin: auto;">
          أهلا
          <ion-text color="primary">
            <strong>
              زائرنا
            </strong>
          </ion-text>
          الكريم
          <ion-icon slot="center" name="hand" style="height: 28px;vertical-align: middle;"></ion-icon>
        </h2>
        <ion-text color="secondary" routerLink="/login" class="ion-margin-top" style="cursor:pointer;">
          <h6 class="text-medium" style="width: 350px; margin: auto;">
            لتسجيل الدخول اضغط هنا </h6>
        </ion-text>
      </ion-text>
      <ng-container *ngIf="profileService.$user | async as user">
        <ion-text color="secondary">
          <h2 class="text-secondary" style="width: 350px; margin: auto;">
            أهلا
            {{ (profileService.$user | async)?.gender === 'MALE' ? 'ابن' : 'ابنة' }}
            العم
          </h2>
        </ion-text>
        <ion-text color="primary" *ngIf="user">
          <h2 class="text-secondary" style="width: 350px; margin: auto;">
            <strong>
              {{ user.name }}
            </strong>
            <ion-icon slot="center" name="hand" style="height: 28px;vertical-align: middle;"></ion-icon>
          </h2>
        </ion-text>
        <div [@slideDown] class="ion-margin-top"
             [style.display]="((profileService.$user | async)?.unfilled_children || false) ? 'block' : 'none'">
          <ion-text color="secondary" class="ion-text-center">
            <h2 class="text-secondary" style="width: 350px; margin: auto;">
              <a id="child-data-modal" href="javascript:void(0)">أكمل بيانات أبنائك الآن</a>
            </h2>
          </ion-text>
        </div>
      </ng-container>
      <app-app-banner/>
      <div class="buttons-wrapper" style="margin-top: 30px">
        <ng-container *ngIf="featuresService.features$ | async as features">
          <ng-container *ngFor="let onePage of pages; let i= index">
            <ng-container
              *ngIf="((onePage.feature && features) ? (features[onePage.feature] || false) : false) || (onePage.feature === null)">
              <a style="color: var(--ion-color-light-contrast);text-decoration: none;" class="page-button"
                 [href]="onePage.href" [target]="onePage.target" *ngIf="onePage.href">
                <ion-icon name="{{onePage.icon}}" *ngIf="onePage.icon" slot="icon-only"
                          [class.heartbeat]="onePage.animated"></ion-icon>
                <img [src]="onePage.image" *ngIf="onePage.image" [style]="{width: '50px', height: '50px'}"
                     [class.heartbeat]="onePage.animated"/>
                <h4 style="font-size: 15px; margin: 8px 0;">
                  {{ onePage.title }}
                </h4>
              </a>
              <div class="page-button" [routerLink]="onePage.url" *ngIf="onePage.url" [style]="onePage.style">
                <ion-icon *ngIf="onePage.feature === 'Strava'" name="help-circle-outline" slot="icon-only"
                          (click)="openWorkoutTutorial($event)"
                          style="position:absolute; top:0; left: 0;transform: scaleX(-1);"></ion-icon>
                <ion-icon name="{{onePage.icon}}" *ngIf="onePage.icon" slot="icon-only"
                          [class.heartbeat]="onePage.animated"></ion-icon>
                <img [src]="onePage.image" *ngIf="onePage.image" [style]="{width: '50px', height: '50px'}"
                     [class.heartbeat]="onePage.animated"/>
                <h4 style="font-size: 15px; margin: 8px 0;">
                  {{ onePage.title }}
                </h4>
              </div>
            </ng-container>
          </ng-container>
        </ng-container>
      </div>
    } @else if (versionChecked) {
      <ion-text color="secondary">
        <h2 class="text-secondary" style="width: 350px; margin: auto;">
          يرجى تحديث التطبيق للإستفادة من كافة المميزات
        </h2>
        <img src="assets/images/update-app.svg" alt="حدّث التطبيق">

        <ion-button color="secondary" expand="block" mode="ios" class="ion-margin-top"
                    (click)="openStore()" *ngIf="iosAppUrl">
          <ion-spinner *ngIf="storeLoading"></ion-spinner>
          <span *ngIf="!storeLoading">
            تحديث التطبيق
          </span>
        </ion-button>
      </ion-text>
    }
  </ion-grid>
  <app-child-form/>
</ion-content>

<ng-container *ngIf="showFooter">
  <ng-container *ngIf="(appDataService.data$ | async) as data">
    <ng-container *ngIf="(data['name'] + '').includes('التويجري')">
      <h6 class="ion-text-center">
        صندوق عائلة عبد الله بن راجح التويجري | س.ت: 1131345546
      </h6>
      <div style="display: flex; justify-content: center; gap: 10px">
        <a routerLink="/policy" style="color: var(--ion-color-primary); font-size: 15px; display: inline">سياسة الخصوصية</a>
        <a routerLink="/terms" style="color: var(--ion-color-primary); font-size: 15px; display: inline">الشروط والأحكام</a>
        <a routerLink="/about" style="color: var(--ion-color-primary); font-size: 15px; display: inline">عنا</a>
        <a href="tel:+966537116656" style="color: var(--ion-color-primary); font-size: 15px; display: inline">تواصل معنا</a>
      </div>
      <h6 class="ion-text-center">
        جميع الحقوق محفوظة &copy; 2025
      </h6>

    </ng-container>
  </ng-container>
</ng-container>

