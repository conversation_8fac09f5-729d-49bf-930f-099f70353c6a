:host {
  max-width: var(--page-width);
  margin: auto;
}

#container {
  text-align: center;
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}

#container strong {
  font-size: 20px;
  line-height: 26px;
}

#container p {
  font-size: 16px;
  line-height: 22px;
  color: #8c8c8c;
  margin: 0;
}

#container a {
  text-decoration: none;
}

.swiper {
  width: 100%;
  max-width: 600px;
  height: 100%;
  min-height: 270px;
  margin-block-start: 16px;
  margin-block-end: 16px;
}

ion-icon {
  font-size: 40px;
}

.buttons-wrapper {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-content: center;
  justify-content: center;
  align-items: center;
  gap: 15px;
  max-width: 550px;
  margin: auto;
  margin-block-end: 40px;

  .page-button {
    cursor: pointer;
    background: var(--ion-color-light, #fff);
    width: 150px;
    height: 113px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
}

::ng-deep {
  .swiper-pagination {
    bottom: 0 !important;
  }
}

::ng-deep {
  h1, h2, h3, h4, h5, h6 {
    line-height: 1.35 !important;
  }
}

cdk-virtual-scroll-viewport {
  height: 100%;
  width: 100%;
}

// media query for desktop
@media (min-width: 490px) {
  .buttons-wrapper {
    .page-button {
      width: 165px;
    }
  }
}

ion-col {
  padding: unset;
}
