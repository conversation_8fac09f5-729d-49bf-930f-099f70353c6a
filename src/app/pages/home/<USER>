import {Component, CUSTOM_ELEMENTS_SCHEMA, Inject, OnChanges, OnDestroy, OnInit} from '@angular/core';
import {
  Async<PERSON>ipe,
  DOCUMENT,
  NgForOf,
  NgIf,
} from "@angular/common";
import {IonicModule, ModalController, Platform} from "@ionic/angular";
import {HeaderComponent} from "../../layout/header/header.component";
import {IS_SERVER_PLATFORM} from "../../shared/IS_SERVER_PLATFORM.token";
import {RouterModule} from "@angular/router";
import {ProfileService} from "../../shared/services/profile.service";
import {FeaturesService} from "../../shared/services/features.service";
import {ThemeService} from "../../shared/services/theme.service";
import {AppDataService} from "../../shared/services/app-data.service";
import {animate, state, style, transition, trigger} from '@angular/animations';
import {ChildFormPage} from "./child-form/child-form.page";
import * as semver from 'semver';
import {App} from '@capacitor/app';
import {AppBannerComponent} from "../../shared/components/app-banner/app-banner.component";
import {take} from 'rxjs';
import {WorkoutTutorialComponent} from "../workout-programs/workout-tutorial/workout-tutorial.component";
import {GenericService} from "../../shared/services/generic.service";

@Component({
  selector: 'app-home',
  templateUrl: 'home.page.html',
  styleUrls: ['home.page.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    NgIf,
    NgForOf,
    AsyncPipe,
    RouterModule,
    ChildFormPage,
    AppBannerComponent,
  ],
  animations: [
    trigger('slideDown', [
      state('void', style({
        transform: 'translateY(-100%)',
        opacity: 0
      })),
      state('*', style({
        transform: 'translateY(0)',
        opacity: 1
      })),
      transition('void <=> *', [
        animate('0.5s ease-in-out')
      ]),
    ])
  ],
})
export class HomePage implements OnInit, OnDestroy, OnChanges {
  // Typically referenced to your ion-router-outlet
  awardUserIcon: string = 'assets/images/icons/award-users.svg';
  activeWorkoutProgram: any;

  // todo load pages with api
  pages: any[] = [
    {
      title: 'بطاقة التهنئة',
      icon: 'congrats-arabic',
      url: '/cards',
      feature: 'Cards',
    },
    {
      title: 'وقفك صلة',
      icon: 'wqfk-icon',
      target: '_blank',
      href: 'https://altwijristore.com/products/wqfk5',
      feature: 'WQFK',
    },
    {
      title: 'العضويات',
      url: '/members',
      icon: 'altwijry-badge',
      feature: 'Memberships',
      animated: true,
    },
    {
      title: 'المشجرة الإلكترونية',
      url: '/family-graph/me',
      icon: 'altwijry-branches',
      feature: 'FamilyGraph',
    }, {
      title: 'إحصائيات',
      url: '/stats',
      icon: 'custom-stats',
      feature: 'Statistics',
    },
    {
      title: 'المشتركون',
      url: '/members/list',
      icon: 'altwijry-active-members',
      feature: 'ActiveMembers',
    },
    {
      title: 'جائزة التميز',
      url: '/user-awards',
      image: this.awardUserIcon,
      feature: 'Awards',
    },
    {
      title: 'المتجر الإلكتروني',
      url: '/store',
      icon: 'altwijry-commerce',
      feature: 'Store',
    },
    {
      title: 'المسابقة القرآنية',
      url: '/quran-competitions',
      icon: 'custom-quran-competition',
      feature: 'QuranCompetition',
    },
    {
      title: 'البرامج والأنشطة',
      url: '/activities',
      icon: 'altwijry-ideas',
      feature: 'Activities',
    },
    {
      title: 'المناسبات',
      url: '/events',
      icon: 'altwijry-ideas',
      feature: 'Events',
    },
    {
      title: 'الشركات',
      url: '/companies',
      icon: 'altwijry-companies',
      feature: 'Companies',
    },
    {
      title: 'المؤلفات',
      url: '/literatures',
      icon: 'altwijry-book',
      feature: 'Literatures',
    },
    {
      title: 'الوثائق',
      url: '/documents',
      icon: 'altwijry-document',
      feature: 'Documents',
    },
    {
      title: 'حجز القاعة',
      url: '/hall-reservation',
      icon: 'altwijry-hall',
      feature: 'HallReservation',
    },
    {
      title: 'الاستشارات',
      url: '/consultations',
      icon: 'altwijry-survey',
      feature: 'Consultations',
    },
    {
      title: 'الهيكل الإداري',
      url: '/family-graph/management-crew',
      icon: 'altwijry-org-chart',
      feature: 'ManagementCrew',
    },
    {
      title: 'أهم الأخبار',
      url: '/news',
      icon: 'altwijry-news',
      feature: 'News',
    },
    {
      title: 'الحسابات البنكية',
      url: '/accounts',
      icon: 'alrajhi',
      feature: 'BankAccounts',
    },
    {
      title: 'العروض والتخفيضات',
      url: '/store-promotions',
      icon: 'altwijry-offers',
      feature: 'Promotions',
    },
  ]
  isDark = false;
  versionChecked = false;
  supportedVersion = false;
  appHasNewVersion = false;
  storeLoading = false;
  showFooter = false;
  iosAppUrl!: string;
  androidAppUrl!: string;

  constructor(
    @Inject(DOCUMENT) private document: Document,
    @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
    private modalCtrl: ModalController,
    public featuresService: FeaturesService,
    public profileService: ProfileService,
    public themeService: ThemeService,
    public appDataService: AppDataService,
    public platform: Platform,
    private genericService: GenericService,
  ) {
    this.themeService.isDark.subscribe(isDark => {
      this.isDark = isDark;
      // change icon color based on theme
      this.pages = this.pages.map(page => {
        if (!page.icon || !page.icon.includes('alrajhi')) return page;
        return {
          ...page,
          icon: isDark ? page.icon + '-dark' : page.icon.replace('-dark', '')
        }
      })
    })
  }

  ngOnDestroy(): void {
    this.versionChecked = false;
  }

  ngOnInit(): void {
    this.versionChecked = false;
    this.supportedVersion = false;
    this.appHasNewVersion = false;
    if (this.isServer) {
      console.log('is server')
    }
    this.appDataService.data$
      .pipe(take(1))
      .subscribe(data => {
        if (!data) return;
        if (data['awardUserIcon'] && typeof data['awardUserIcon'] === 'string') {
          this.awardUserIcon = data['awardUserIcon'];
          this.pages = this.pages.map(page => {
            if (page.feature === 'Awards') {
              return {
                ...page,
                image: this.awardUserIcon
              }
            }
            return page;
          });
        }
        if (this.genericService.isCapacitorApp()) {
          if (this.genericService.isIosApp()) {
            if (data['iosMinVersion'] || data['iosCurrentVersion'])
              App.getInfo().then(res => {
                let appVersion = res.version
                let minVersion = data['iosMinVersion'] as string;
                let currentVersion = data['iosCurrentVersion'] as string;
                this.supportedVersion = semver.gte(appVersion, minVersion);
                this.appHasNewVersion = !this.supportedVersion || semver.gt(currentVersion, appVersion);
                this.versionChecked = true;
              })
          } else if (this.genericService.isAndroidApp()) {
            if (data['androidMinVersion'] || data['androidCurrentVersion'])
              App.getInfo().then(res => {
                let appVersion = res.version
                let minVersion = data['androidMinVersion'] as string;
                let currentVersion = data['androidCurrentVersion'] as string;
                this.supportedVersion = semver.gte(appVersion, minVersion);
                this.appHasNewVersion = !this.supportedVersion || semver.gt(currentVersion, appVersion);
                this.versionChecked = true;
              })
          }
          if (data['iosAppUrl'])
            this.iosAppUrl = data['iosAppUrl'] as string;
          if (data['androidAppUrl'])
            this.androidAppUrl = data['androidAppUrl'] as string;
        } else {
          this.showFooter = true;
        }
        if (!this.versionChecked) {
          this.versionChecked = true;
          this.supportedVersion = true;
          this.appHasNewVersion = false;
        }
        if ('activeWorkoutProgram' in data) {
          if (data['activeWorkoutProgram']) {
            this.activeWorkoutProgram = data['activeWorkoutProgram'] as any;
            this.pages = [
              {
                title: this.activeWorkoutProgram.title,
                url: '/workout-programs/' + this.activeWorkoutProgram.id,
                icon: 'strava-walk',
                feature: 'Strava',
                style: {
                  background: '#1C6A4D',
                  color: 'white',
                  position: 'relative'
                },
              },
              ...this.pages,
            ];
          }
        }
      });
  }

  ngOnChanges() {
    this.browserSetup();
  }

  browserSetup() {
  }

  openStore() {
    if (this.storeLoading) return;
    if (this.genericService.isCapacitorApp()) {
      this.storeLoading = true;
      if (this.platform.is('ios') && this.iosAppUrl) {
        window.open(this.iosAppUrl, '_system');
      } else if (this.platform.is('android') && this.androidAppUrl) {
        window.open(this.androidAppUrl, '_system');
      }
      this.storeLoading = false;
    }
  }

  openWorkoutTutorial(event: any) {
    event.preventDefault();
    event.stopPropagation();

    // open tutorial modal (app-workout-tutorial) here
    this.modalCtrl.create({
      component: WorkoutTutorialComponent,
      breakpoints: [0, 1],
      initialBreakpoint: 1,
      componentProps: {
        workoutProgramId: this.activeWorkoutProgram.id,
      }
    }).then(modal => {
      modal.present();
    });
  }

}
