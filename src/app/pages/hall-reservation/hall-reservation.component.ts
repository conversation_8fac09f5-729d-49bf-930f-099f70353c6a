import {ChangeDetectorRef, Component, make<PERSON>tate<PERSON><PERSON>, OnInit} from '@angular/core';
import {
  InfiniteScrollCustomEvent,
  IonicModule,
  AlertController,
  ToastController,
  PopoverController
} from "@ionic/angular";
import {HeaderComponent} from "../../layout/header/header.component";
import {IfIsBrowserDirective} from "../../shared/IfIsBrowser.directive";
import {ReservationService} from "../../shared/services/reservation.service";
import {BehaviorSubject, take, tap, finalize, catchError} from "rxjs";
import {Reservation} from "../../shared/models";
import {StorageService} from "../../shared/services/storage.service";
import {CardComponent} from "../../shared/components/card/card.component";
import {AsyncPipe, DatePipe, NgForOf, NgIf, NgOptimizedImage, NgTemplateOutlet} from "@angular/common";
import {RouterLink} from "@angular/router";
import {FormsModule} from "@angular/forms";
import {NgxSplideModule} from "ngx-splide";

@Component({
  selector: 'hall-reservation',
  templateUrl: 'hall-reservation.component.html',
  styleUrls: ['hall-reservation.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    IfIsBrowserDirective,
    CardComponent,
    NgIf,
    NgTemplateOutlet,
    AsyncPipe,
    DatePipe,
    NgForOf,
    RouterLink,
    FormsModule,
    NgOptimizedImage,
    NgxSplideModule
  ],
})
export class HallReservation implements OnInit {
  private _data$ = new BehaviorSubject<Reservation[]>([]);
  data$ = this._data$.asObservable();
  isReady: boolean = false;
  formLoading: boolean = false;
  currentPage = 0;
  nextPageUrl = '';
  alertButtons = (id: number | string) => [
    {
      text: 'ألغ الحجز',
      role: 'destructive',
      cssClass: 'danger',
      handler: () => {
        this.delete(id);
      }
    },
    {
      text: 'رجوع',
      role: 'cancel',
      cssClass: 'secondary',
    }];
  ads: any[] = [
    {
      image: '/assets/images/store-ads/ALWAQF5_1.jpg',
      link: '14',
      order: 1
    },
    {
      image: '/assets/images/store-ads/ALWAQF5_1.jpg',
      link: '14',
      order: 1
    }
  ];

  constructor(private storageService: StorageService,
              private reservationService: ReservationService,
              private alertController: AlertController,
              private popoverController: PopoverController,
              private toastController: ToastController,
              private cdr: ChangeDetectorRef,) {

  }

  getData() {
    this.currentPage++;
    return this.reservationService.checkAndGetData(
      makeStateKey(`news-${this.currentPage}`),
      this.reservationService.index(this.nextPageUrl),
      []
    ).pipe(take(1), tap((data) => {
      this.isReady = true;
      this.nextPageUrl = data.pagination?.next_url;
      this._data$.next([...this._data$.value, ...data.data] ?? []);
      this.cdr.detectChanges();
    }));
  }

  ngOnInit(): void {
    this.getData().pipe(take(1)).subscribe();
  }

  onIonInfinite(ev: InfiniteScrollCustomEvent) {
    this.getData().pipe(take(1)).subscribe(() => {
      ev.target.complete().then();
    });
  }

  delete(id: any) {
    this.formLoading = true;
    this.reservationService.delete(id)
      .pipe(
        finalize(() => this.formLoading = false),
        catchError(({error}) => {
          let errorMessage = error.message || 'خطأ !';
          this.toastController.create({
            header: 'خطأ',
            message: errorMessage,
            position: 'top',
            color: 'danger',
            buttons: [{
              side: 'end',
              text: 'x',
              role: 'cancel',
            }]
          }).then((toast) => {
            toast.present();
            setTimeout(() => toast.dismiss(), 5000);
          });
          throw new Error(JSON.stringify(error))
        })
      )
      .subscribe((res) => {
        this._data$.value.map((r) => {
          if (r.id === id)
            r.status = 'CANCELLED';
          return r;
        });
        this.popoverController.dismiss();
      });
  }
}
