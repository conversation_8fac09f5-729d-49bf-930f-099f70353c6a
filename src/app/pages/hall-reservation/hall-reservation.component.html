<app-header [hideProfileIcon]="true" [showBackButton]="true" backTo="home" [leftButtonTemplate]="customLeftButton">
</app-header>
<ng-template #customLeftButton>
  <ion-button fill="clear" class="ion-justify-content-start"
              color="primary"
              style="position: absolute; left: -10px; font-weight: bold" routerLink="/hall-reservation/create">
    حجز جديد
  </ion-button>
</ng-template>
<ion-content [fullscreen]="true">
  <splide [options]="{direction: 'rtl', autoplay:true, arrows:false}" *ngIf="false">
    <splide-slide *ngFor="let slide of ads">
      <img class="ads" [ngSrc]="slide.image" alt="إعلان" width="500" height="150" priority/>
    </splide-slide>
  </splide>
  <ng-container *ngTemplateOutlet="reservations"></ng-container>
  <ion-infinite-scroll (ionInfinite)="onIonInfinite($event)" *ngIf="nextPageUrl">
    <ion-infinite-scroll-content loadingText="جار التحميل" loadingSpinner="bubbles"></ion-infinite-scroll-content>
  </ion-infinite-scroll>
</ion-content>

<ng-template #reservations>
  <div style="overflow-y: scroll; overflow-x: hidden;">
    <ng-container *ngIf="data$ | async as reservations;">
      <ion-spinner *ngIf="!isReady" style="margin: auto; width: 100%;height: 40px;"></ion-spinner>
      <ion-list lines="full" *ngIf="reservations.length > 0">
        <ion-item *ngFor="let reservation of reservations; let i = index">
          <ion-icon *ngIf="reservation.status === 'CONFIRMED'" slot="start" name="checkmark-circle"
                    color="primary"></ion-icon>
          <ion-icon *ngIf="reservation.status === 'COMPLETED'" slot="start" name="checkmark-circle"
                    color="secondary"></ion-icon>
          <ion-icon *ngIf="reservation.status === 'PENDING'" slot="start" name="time-outline"
                    color="primary"></ion-icon>
          <ion-icon *ngIf="reservation.status === 'DECLINED'" slot="start" name="close-circle"
                    color="danger"></ion-icon>
          <ion-icon *ngIf="reservation.status === 'CANCELLED'" slot="start" name="close-circle"
                    color="primary"></ion-icon>
          <ion-label>
            <h4 style="line-height: 1.7 !important;">
              <span>حجز قاعة ال{{ reservation.event_type_locale }}</span>
              <ion-chip *ngIf="false" [color]="{
                          PENDING: 'tertiary',
                          CONFIRMED: 'primary',
                          DECLINED: 'danger',
                          CANCELLED: 'danger',
                          COMPLETED: 'success'
                        }[reservation.status] ?? 'secondary'"
                        style="position: absolute; bottom: 2px; inset-inline-end: 0;">
                <span style="font-size: 12px;">
                {{ reservation.status_locale }}</span>
              </ion-chip>
              <ion-button
                *ngIf="reservation.status === 'PENDING'"
                fill="clear"
                color="primary"
                id="open-cancel-popover_{{reservation.id}}"
                style="position: absolute; top: 2px; inset-inline-end: 0;">
                <ion-icon name="ellipsis-vertical" slot="icon-only"></ion-icon>
              </ion-button>
              <ion-popover *ngIf="reservations.length > 0 && reservation.status === 'PENDING'"
                           trigger="open-cancel-popover_{{reservation.id}}" triggerAction="click">
                <ng-template>
                  <ion-content class="ion-no-padding">
                    <ion-button fill="clear" color="danger" id="cancelConfirmation_{{reservation.id}}"
                                [disabled]="formLoading">
                      <ng-container slot="start">
                        <ion-icon name="close-circle" *ngIf="!formLoading"></ion-icon>
                        <ion-spinner color="danger" *ngIf="formLoading" size="small"></ion-spinner>
                      </ng-container>
                      <span class="ion-align-self-baseline">إلغاء الحجز</span>
                    </ion-button>
                    <ion-alert
                      trigger="cancelConfirmation_{{reservation.id}}"
                      header="متأكد؟"
                      message="هل أنت متأكد من إلغاء حجز {{reservation.event_type_locale}}؟"
                      [buttons]="alertButtons(reservation.id)"
                    ></ion-alert>
                  </ion-content>
                </ng-template>
              </ion-popover>
            </h4>
            <ion-row>
              <ion-col>
                <p> {{ reservation.start_at | date:'longDate' }} |
                  {{ reservation.start_at | date:'shortTime' }} - {{ reservation.end_at | date:'shortTime' }}
                </p>
              </ion-col>
            </ion-row>
          </ion-label>
        </ion-item>
      </ion-list>
      <ng-container *ngIf="reservations.length === 0 && isReady">
        <ion-item class="">
          <ion-label>
            <h4 class="ion-text-center">ليس لديك حجوزات للقاعة بعد!</h4>

            <ion-button style="width:100%; margin: auto" fill="clear" color="primary"
                        routerLink="/hall-reservation/create">احجز الآن
            </ion-button>

          </ion-label>
        </ion-item>
      </ng-container>
    </ng-container>
  </div>
</ng-template>
