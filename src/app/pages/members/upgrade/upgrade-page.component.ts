import {<PERSON>mpo<PERSON>, Inject, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild} from '@angular/core';
import {
  <PERSON>ync<PERSON><PERSON><PERSON>, CurrencyPipe,
  DatePipe,
  DecimalPipe,
  DOCUMENT,
  JsonPipe,
  NgForOf,
  NgIf,
  NgOptimizedImage,
  NgTemplateOutlet
} from "@angular/common";
import {IonicModule, IonModal, NavController} from "@ionic/angular";
import {HeaderComponent} from "../../../layout/header/header.component";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {IS_SERVER_PLATFORM} from "../../../shared/IS_SERVER_PLATFORM.token";
import {CardComponent} from "../../../shared/components/card/card.component";
import {ApiService} from "../../../shared/services/api.service";
import {HttpClientModule} from "@angular/common/http";
import {catchError, Observable, throwError} from "rxjs";
import {ActivatedRoute, NavigationExtras, RouterModule} from "@angular/router";
import {Package} from "../../../shared/models";
import {OverlayEventDetail} from "@ionic/core/components";
import {RenderBlockPipe} from "../../../shared/pipes/render-block.pipe";
import {SanitizeHtmlPipe} from "../../../shared/pipes/sanitize-html.pipe";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {FormatDistancePipe} from "../../../shared/pipes/format-distance.pipe";
import {GenericService} from "../../../shared/services/generic.service";
import {Browser} from '@capacitor/browser';
import {environment} from "../../../../environments/environment";
import {NotificationService} from "../../../shared/services/notification.service";

declare var myFatoorahAP: any;
declare var myFatoorah: any;

@Component({
  selector: 'subscribe-page',
  templateUrl: 'upgrade-page.component.html',
  styleUrls: ['upgrade-page.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    IfIsBrowserDirective,
    NgIf,
    CardComponent,
    NgTemplateOutlet,
    NgForOf,
    AsyncPipe,
    JsonPipe,
    RouterModule,
    DatePipe,
    DecimalPipe,
    RenderBlockPipe,
    SanitizeHtmlPipe,
    NgOptimizedImage,
    FormsModule,
    ReactiveFormsModule,
    CurrencyPipe,
    FormatDistancePipe,
  ],
})
export class UpgradePage implements OnInit, OnDestroy {
  @ViewChild('payModal') modal!: IonModal;
  packageId: number
  amount: number = 0
  package$: Observable<Package> | null = null
  formLoading: boolean = false
  showPaymentsSpinner: boolean = false
  creditCardProcessing: boolean = false
  transactionUUID = '';
  myFatoorahAppleSessionId = '';
  myFatoorahCreditCardSessionId = '';
  private channelName!: string
  private isCapacitorApp = false

  // Gift mode properties
  isGiftMode: boolean = false
  giftRecipientId: string = ''
  giftRecipientName: string = ''

  constructor(
    @Inject(DOCUMENT) private document: Document,
    private activatedRoute: ActivatedRoute,
    @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
    private apiService: ApiService,
    private navCtrl: NavController,
    private genericService: GenericService,
    private notificationService: NotificationService,
    @Inject('Window') private window: Window,
  ) {
    this.packageId = this.activatedRoute.snapshot.params['id']
    this.isCapacitorApp = this.genericService.isCapacitorApp()

    // Check for gift mode parameters
    this.activatedRoute.queryParams.subscribe(params => {
      this.isGiftMode = params['gift'] === 'true'
      this.giftRecipientId = params['recipient'] || ''
      this.giftRecipientName = params['recipientName'] || ''
    })
  }

  ngOnDestroy(): void {
    if (this.channelName && this.notificationService.Echo) {
      this.notificationService.Echo.leave(this.channelName)
      //this.notificationService.Echo.disconnect()
    }
  }

  ngOnInit(): void {
    if (this.packageId) {
      this.package$ = this.apiService.getUpgradePackage(this.packageId)
      this.package$.subscribe((_package) => {
        this.amount = _package.product.price
      })
    }
    if (!this.isServer) {
      this.browserSetup()
    }
  }

  browserSetup() {
  }

  subscribe() {
    this.formLoading = true
    const giftRecipient = this.isGiftMode ? this.giftRecipientId : undefined
    this.apiService.getPackageUpgradeSessionsId(this.packageId, giftRecipient)
      .pipe(catchError((err) => {
        this.formLoading = false
        return throwError(err)
      }))
      .subscribe(async (res: any) => {
        if (this.isCapacitorApp) {
          const uuid = res['uuid'];
          await Browser.open({
            url: `${environment.appURL}/pay?uuid=${uuid}`,
            presentationStyle: 'popover',
          });
          this.formLoading = false
          this.channelName = `ProductTransaction.${uuid}`
          if (this.notificationService.Echo)
            this.notificationService.Echo.channel(this.channelName).listen('PaymentTransactionStatusUpdated', (data: any) => {
              let navigationExtras: NavigationExtras = {
                queryParams: {
                  packageId: this.packageId,
                  transactionUUID: this.transactionUUID,
                  ...(this.isGiftMode ? { gift: 'true', recipient: this.giftRecipientId, recipientName: this.giftRecipientName } : {})
                }
              }
              try {
                if (data['status'] === 'SUCCESS')
                  this.navCtrl.navigateForward('/members/checkout/success', navigationExtras).then()
                else
                  this.navCtrl.navigateForward('/members/checkout/failure', navigationExtras).then()
              } catch {
                this.navCtrl.navigateForward('/members/checkout/failure', navigationExtras).then()
              }
              try {
                Browser.close()
              } catch {
              }
              if (this.channelName)
                this.notificationService.Echo.leave(this.channelName)
            });
        } else {
          this.transactionUUID = res['uuid'];
          this.myFatoorahAppleSessionId = res['apple_session_id'];
          this.myFatoorahCreditCardSessionId = res['session_id'];
          this.initiateApplePayPayment();
          this.initiateCreditCardPayment();
          this.showPaymentsSpinner = false;
          this.formLoading = false
        }
      })
    if (!this.isCapacitorApp)
      this.modal.present().then()
  }

  confirm() {
    this.modal.dismiss({}, 'confirm');
  }

  onWillDismiss(event: Event) {
    const ev = event as CustomEvent<OverlayEventDetail<string>>;
    if (ev.detail.role === 'confirm') {

    }
  }

  cancel() {
    this.transactionUUID = '';
    this.myFatoorahAppleSessionId = '';
    this.myFatoorahCreditCardSessionId = '';
    this.showPaymentsSpinner = false;
    this.modal.dismiss(null, 'cancel');
  }

  private initiateApplePayPayment() {
    this.showPaymentsSpinner = true;
    const config = {
      sessionId: this.myFatoorahAppleSessionId, // Here you add the "SessionId" you receive from InitiateSession Endpoint.
      countryCode: "SAU",
      currencyCode: "SAR",
      amount: this.amount,
      cardViewId: "card-applepay",
      Language: "ar",
      callback: ((response: any) => {
        this.showPaymentsSpinner = true;
        this.apiService.getInvoiceFromPaymentSessionId(this.transactionUUID, response.sessionId).subscribe((res: any) => {
          this.modal.dismiss().then();
          let navigationExtras: NavigationExtras = {
            queryParams: {
              productId: this.packageId,
              transactionUUID: this.transactionUUID,
              ...(this.isGiftMode ? { gift: 'true', recipient: this.giftRecipientId, recipientName: this.giftRecipientName } : {})
            }
          };
          if (res['success'] === true)
            this.navCtrl.navigateForward('/members/checkout/success', navigationExtras).then()
          else
            this.navCtrl.navigateForward('/members/checkout/failure', navigationExtras).then()
          this.showPaymentsSpinner = false;
        });
      }),
      sessionStarted: ((sessionStarted: any) => {
        this.showPaymentsSpinner = true;

      }),
      sessionCanceled: ((sessionCanceled: any) => {
        // this.showPaymentsSpinner = false;
      }),
    };
    myFatoorahAP.init(config);
  }

  private initiateCreditCardPayment() {
    this.showPaymentsSpinner = true;
    const config = {
      sessionId: this.myFatoorahCreditCardSessionId, // Here you add the "SessionId" you receive from InitiateSession Endpoint.
      countryCode: "SAU",
      currencyCode: "SAR",
      amount: this.amount,
      cardViewId: "credit-card",
      Language: "ar",
      callback: ((response: any) => {
      }),
      sessionStarted: ((sessionStarted: any) => {
        this.showPaymentsSpinner = true;
      }),
      sessionCanceled: ((sessionCanceled: any) => {
        // this.showPaymentsSpinner = false;
      }),
    };

    myFatoorah.init(config);
  }

  payWithCreditCard() {
    this.creditCardProcessing = true;

    (this.window as any).myFatoorah.submit().then(
      (response: { sessionId: string, cardBrand: string }) => {
        // In case of success
        // Here you need to pass session id to you backend here
        const sessionId = response.sessionId;
        const cardBrand = response.cardBrand; //cardBrand will be one of the following values: Master, Visa, Mada, Amex
        this.modal.dismiss().then();
        let navigationExtras: NavigationExtras = {
          queryParams: {
            productId: this.packageId,
            transactionUUID: this.transactionUUID,
            ...(this.isGiftMode ? { gift: 'true', recipient: this.giftRecipientId, recipientName: this.giftRecipientName } : {})
          }
        };
        this.apiService.getInvoiceFromPaymentSessionId(this.transactionUUID, sessionId).subscribe((res: any) => {
          if (res['success'] === true)
            window.location = res['invoice_url'];
          else
            this.navCtrl.navigateForward('/members/checkout/failure', navigationExtras).then()
        });
        this.creditCardProcessing = false;
      }
    ).catch(
      (error: any) => {
        // In case of errors
        console.error(error);
        this.creditCardProcessing = false;
      }
    );
  }
}
