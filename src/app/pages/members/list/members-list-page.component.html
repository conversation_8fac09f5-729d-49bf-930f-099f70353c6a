<app-header [hideProfileIcon]="false" [showBackButton]="true" backTo="/"></app-header>
<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large" class="ion-margin-vertical" style="">المشتركون</ion-title>
    </ion-toolbar>
  </ion-header>
    <ng-container *ngIf="packages$ | async as packages">
  <ion-segment [(ngModel)]="selectedSegment" (ngModelChange)="activeListUpdated($event)"
               class="ion-margin-bottom" color="primary">

    <ion-segment-button *ngFor="let _package of packages.data.slice().reverse()" [value]="_package.id">
      <!--<ion-img style="width: 20px;margin-top: 8px;"
               [src]="'/assets/images/icons/membership-' + _package.slug + '.svg'"></ion-img>-->
      <ion-icon style="width: 20px;margin-top: 8px;"
                name="membership-{{_package.slug}}">
      </ion-icon>
      {{ _package.title.replace('عضو', '').trim() }}
      <b style="">({{ _package.members_count | number }})</b>
    </ion-segment-button>

  </ion-segment>
      </ng-container>
  <!--<ion-searchbar placeholder="ابحث بالاسم أو بالرقم التعريفي"
                 class="ion-no-padding" [debounce]="500"
                 (ionInput)="searchUser($event)"></ion-searchbar>-->
    <ion-list class="ion-margin-bottom">
      <ion-item *ngFor="let user of members" [button]="true" [detail]="false" (click)="openProfile(user.user)">
        <ion-avatar aria-hidden="true" slot="start">
          @let fallbackURL = ('/assets/d3-images/' + (user.user.gender ?? 'male').toLowerCase() + '-icon.svg') ;
          @let fallbackIcon = ((user.user.gender ?? 'male').toLowerCase() + '-icon') ;
          @if (user.user.avatar) {
            <img
              [src]="user.user.avatar ?? fallbackURL"
              [appImgFallback]="fallbackURL"
              disabled="avatarUpdating"
              width="120" height="120">
          } @else {
            <ion-icon slot="start" [name]="fallbackIcon" style="font-size: 114px;"></ion-icon>
          }
        </ion-avatar>
        <ion-label>{{ user.user.full_name }}</ion-label>
      </ion-item>
      <ng-container *ngIf="loaded; else loading"></ng-container>
      <ion-infinite-scroll
        *ngIf="pagination?.next_url"
                           #infiniteScroll (ionInfinite)="onIonInfinite($event)">
        <ion-infinite-scroll-content></ion-infinite-scroll-content>
      </ion-infinite-scroll>
    </ion-list>


  <ng-template #loading>
      <ion-item *ngFor="let one of [].constructor(3)">
        <ion-thumbnail slot="start">
          <ion-skeleton-text [animated]="true"></ion-skeleton-text>
        </ion-thumbnail>
        <ion-label>
          <h3>
            <ion-skeleton-text [animated]="true" style="width: 80%;"></ion-skeleton-text>
          </h3>
        </ion-label>
      </ion-item>
  </ng-template>

</ion-content>
