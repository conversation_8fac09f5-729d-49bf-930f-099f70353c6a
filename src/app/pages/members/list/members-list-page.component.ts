import {ChangeDete<PERSON><PERSON><PERSON>, <PERSON>mpo<PERSON>, CUSTOM_ELEMENTS_SCHEMA, Inject, OnInit, ViewChild,} from '@angular/core';
import {
  AsyncPipe,
  DatePipe,
  DecimalPipe,
  DOCUMENT,
  JsonPipe,
  NgForOf,
  <PERSON>I<PERSON>,
  <PERSON><PERSON>ptimizedImage,
  NgTemplateOutlet
} from "@angular/common";
import {InfiniteScrollCustomEvent, IonicModule, IonInfiniteScroll, ModalController} from "@ionic/angular";
import {HeaderComponent} from "../../../layout/header/header.component";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {CardComponent} from "../../../shared/components/card/card.component";
import {ApiService} from "../../../shared/services/api.service";
import {RouterModule} from "@angular/router";
import {FormsModule} from "@angular/forms";
import {UserProfilePage} from "../../family-graph/user-profile/user-profile-page.component";
import {ProfileService} from "../../../shared/services/profile.service";
import {ImgFallbackDirective} from "../../../img-fallback.directive";
import {Package, Pagination} from "../../../shared/models";

@Component({
  selector: 'members-list-page',
  templateUrl: 'members-list-page.component.html',
  styleUrls: ['members-list-page.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    IfIsBrowserDirective,
    NgIf,
    CardComponent,
    NgTemplateOutlet,
    NgForOf,
    AsyncPipe,
    JsonPipe,
    RouterModule,
    DatePipe,
    DecimalPipe,
    NgOptimizedImage,
    FormsModule,
    ImgFallbackDirective,
  ],
})
export class MembersListPage implements OnInit {
  packages$ = this.apiService.getPackages();
  selectedSegment = 2 // Initially select first segment
  members: any[] = []; // Array to store members list
  currentPage = 1; // Current page for pagination
  pagination: null |  Pagination= null;
  searchTerm = ''; // Search term for filtering
  loaded = false;
  @ViewChild('infiniteScroll') infiniteScroll!: IonInfiniteScroll;

  constructor(@Inject(DOCUMENT) private document: Document,
              public apiService: ApiService,
              private modalCtrl: ModalController,
              private cdr: ChangeDetectorRef,
              public profileService: ProfileService,
  ) {

  }

  ngOnInit(): void {
    this.getMembers(this.selectedSegment);

  }

  onIonInfinite(event: InfiniteScrollCustomEvent) {
    if (this.pagination?.next_url && this.members.length < this.pagination.total) {
      this.getMembers(this.selectedSegment, this.pagination.next_url).then(() => {
        event.target.complete();
      });
    } else {
      event.target.complete();
    }
  }

  async openProfile(userInfo: any) {
    const modal = await this.modalCtrl.create({
      component: UserProfilePage,
      componentProps: {
        'user': userInfo,
        'currentUser': this.profileService.$user,
      }
    });
    await modal.present();
  }


  // Function to fetch members based on package_id
  getMembers(package_id: number, next_page?: string): Promise<void> {
    this.loaded = false;
    return new Promise((resolve) => {
      this.apiService.getMembersList({package_id, search: this.searchTerm}, next_page).subscribe((response) => {
        if (next_page) {
          this.members = this.members.concat(response.data);
        } else {
          this.members = response.data;
        }
        this.pagination = response.pagination;
        this.loaded = true;
        // remove duplicates from members array
        this.members = this.members.filter((member, index, self) =>
          index === self.findIndex((m) => (m.id === member.id))
        );
        resolve();
      });
    });
  }

  activeListUpdated(event: any) {
    this.selectedSegment = event;
    this.members = []; // Clear members list
    this.pagination = null; // Reset pagination
    this.getMembers(this.selectedSegment);
  }
}
