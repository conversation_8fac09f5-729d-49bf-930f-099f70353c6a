import { ChangeDetectorRef, Component, Inject, OnInit, TransferState } from '@angular/core';
import {Async<PERSON>ip<PERSON>, DatePipe, DecimalPipe, DOCUMENT, JsonPipe, NgForOf, <PERSON><PERSON><PERSON>, NgTemplateOutlet} from "@angular/common";
import {IonicModule} from "@ionic/angular";

import {HttpClient, HttpClientModule} from "@angular/common/http";

import {ActivatedRoute, Router, RouterModule} from "@angular/router";
import {IfIsBrowserDirective} from "../../../../shared/IfIsBrowser.directive";
import {HeaderComponent} from "../../../../layout/header/header.component";
import {CardComponent} from "../../../../shared/components/card/card.component";
import {ApiService} from "../../../../shared/services/api.service";
import {IS_SERVER_PLATFORM} from "../../../../shared/IS_SERVER_PLATFORM.token";
import {take, catchError, of} from "rxjs";
import {Package, TransactionDetails, TransactionMetadata, ActualTransactionResponse} from "../../../../shared/models";


@Component({
  selector: 'app-checkout-success',
  templateUrl: 'success-page.component.html',
  styleUrls: ['success-page.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    IfIsBrowserDirective,
    NgIf,
    CardComponent,
    NgTemplateOutlet,
    NgForOf,
    AsyncPipe,
    JsonPipe,
    RouterModule,
    DatePipe,
    DecimalPipe
  ],
})
export class MembershipCheckoutSuccessPage implements OnInit {
  // URL Parameters (fallback data)
  packageId = -1;
  transactionUUID = '';
  productId = -1;
  isGiftMode = false; // From URL parameters
  giftRecipientId = ''; // From URL parameters
  giftRecipientName = ''; // From URL parameters

  // API Transaction Data (authoritative)
  actualTransactionData: ActualTransactionResponse | null = null;
  transactionDetails: TransactionDetails | null = null;
  transactionMetadata: TransactionMetadata | null = null;

  // Package information
  packageDetails: Package | null = null;
  packageLoading = true;

  // API data state
  transactionDataLoading = false;
  transactionDataLoaded = false;
  transactionDataError = '';
  apiDataAvailable = false;

  // UI state
  showDetails = false;
  animationComplete = false;

  constructor(@Inject(DOCUMENT) private document: Document,
              @Inject(IS_SERVER_PLATFORM) public isServer: boolean,
              private apiService: ApiService,
              private transferState: TransferState,
              private activatedRoute: ActivatedRoute,
              private router: Router
  ) {

  }

  ngOnInit(): void {
    console.log('Success page component initialized');

    if (this.isServer) {
      console.log('Running on server side')
    } else {
      console.log('Running on client side, calling browserSetup...');
      this.browserSetup();
    }
  }

  browserSetup() {
    // Parse query parameters from URL
    this.activatedRoute.queryParams.pipe(take(1)).subscribe(params => {
      this.packageId = parseInt(params['packageId']) || -1;
      this.productId = parseInt(params['productId']) || -1;
      this.transactionUUID = params['uuid'] || '';
      this.isGiftMode = params['gift'] === '1';
      this.giftRecipientId = params['recipient'] || '';
      this.giftRecipientName = decodeURIComponent(params['recipientName'] || '');

      console.log('Success page initialized with URL parameters:', {
        packageId: this.packageId,
        productId: this.productId,
        transactionUUID: this.transactionUUID, // URL param is 'transactionUUID'
        isGiftMode: this.isGiftMode,
        recipientId: this.giftRecipientId,
        recipientName: this.giftRecipientName
      });

      // If we have a transaction UUID, fetch complete data from API
      if (this.transactionUUID) {
        console.log('Transaction UUID found, calling fetchTransactionDetails...');
        this.fetchTransactionDetails();
      } else {
        console.log('No transaction UUID, falling back to package loading...');
        // Fallback to package loading with URL parameters
        this.loadPackageDetails();
      }

      // Start success animation
      this.startSuccessAnimation();
    });
  }

  fetchTransactionDetails() {
    console.log('fetchTransactionDetails called with UUID:', this.transactionUUID);

    if (!this.transactionUUID) {
      console.error('No transaction UUID provided');
      this.transactionDataError = 'معرف المعاملة غير متوفر';
      this.loadPackageDetails(); // Fallback to URL parameters
      return;
    }

    console.log('Starting API call for transaction details...');
    this.transactionDataLoading = true;
    this.transactionDataError = '';

    console.log('Making API request to:', `/transactions/${this.transactionUUID}/details`);

    this.apiService.getTransactionDetails(this.transactionUUID).pipe(
      take(1),
      catchError(error => {
        console.error('Error fetching transaction details:', error);
        this.transactionDataLoading = false;
        this.transactionDataError = 'فشل في تحميل تفاصيل المعاملة';

        // Fallback to URL parameters and package loading
        this.loadPackageDetails();
        return of(null);
      })
    ).subscribe(response => {
      console.log('API response received:', response);
      this.transactionDataLoading = false;

      if (response && response.success && response.data) {
        const data: ActualTransactionResponse = response.data;
        console.log('Transaction data:', data);

        // Store the raw API response
        this.actualTransactionData = data;

        // Map the actual API response to component properties
        this.transactionUUID = data.uuid || this.transactionUUID;
        this.packageId = data.packageId || this.packageId;
        this.productId = data.productId || this.productId;

        // Update gift-related properties from API
        this.isGiftMode = data.is_gift || data.gift || this.isGiftMode;
        this.giftRecipientId = data.recipient || this.giftRecipientId;
        this.giftRecipientName = data.recipientName || this.giftRecipientName;

        // Store transaction details in a format compatible with helper methods
        this.transactionDetails = {
          transaction_uuid: data.uuid,
          transaction_type: this.isGiftMode ? 'gift' : 'regular',
          transaction_status: data.status,
          payment_status: data.status,
          transaction_amount: data.amount,
          transaction_date: data.confirmed_at || new Date().toISOString(),
          package_id: data.packageId,
          product_id: data.productId
        };

        // Create metadata object for gift transactions
        if (this.isGiftMode) {
          this.transactionMetadata = {
            gift_sub_type: data.giftSubType as 'new_membership' | 'upgrade',
            recipient_family_user_id: data.recipient,
            recipient_name: data.recipientName,
            gift_message: data.giftMessage || undefined
          };
        }

        this.apiDataAvailable = true;
        this.transactionDataLoaded = true;

        // Load package details if we have packageId
        if (this.packageId > 0) {
          this.loadPackageDetails();
        } else {
          this.packageLoading = false;
        }

        console.log('Transaction details loaded successfully:', {
          uuid: data.uuid,
          packageId: data.packageId,
          productId: data.productId,
          amount: data.amount,
          status: data.status,
          isGift: this.isGiftMode,
          recipient: data.recipient,
          recipientName: data.recipientName,
          giftSubType: data.giftSubType
        });

      } else {
        console.error('Invalid response format:', response);
        this.transactionDataError = 'تنسيق الاستجابة غير صحيح';
        this.loadPackageDetails(); // Fallback
      }
    });
  }

  loadPackageDetails() {
    console.log('loadPackageDetails called with packageId:', this.packageId);

    if (this.packageId <= 0) {
      console.log('No valid packageId, skipping package loading');
      this.packageLoading = false;
      return;
    }

    console.log('Loading packages from API...');
    this.apiService.getPackages().pipe(
      take(1),
      catchError(error => {
        console.error('Error loading packages:', error);
        this.packageLoading = false;
        return of({ data: [] });
      })
    ).subscribe(response => {
      console.log('Packages response:', response);
      const packages = response.data || [];
      this.packageDetails = packages.find(pkg => pkg.id === this.packageId) || null;
      this.packageLoading = false;

      console.log('Found package details:', this.packageDetails);
    });
  }

  startSuccessAnimation() {
    // Delay to show success animation
    setTimeout(() => {
      this.animationComplete = true;
    }, 1500);
  }

  toggleDetails() {
    this.showDetails = !this.showDetails;
  }

  navigateToHome() {
    this.router.navigate(['/home']);
  }

  navigateToMemberships() {
    this.router.navigate(['/members']);
  }

  // Generic helper methods that work with any transaction type

  getTransactionType(): string {
    // Use API data if available, otherwise fall back to URL parameters
    if (this.apiDataAvailable && this.transactionDetails) {
      return this.transactionDetails.transaction_type;
    }
    return this.isGiftMode ? 'gift' : 'regular';
  }

  getSuccessTitle(): string {
    if (this.transactionDataLoading) {
      return 'جاري التحقق من تفاصيل المعاملة...';
    }

    if (this.transactionDataError) {
      return 'تمت العملية بنجاح!';
    }

    const transactionType = this.getTransactionType();

    switch (transactionType) {
      case 'gift':
        // Get gift sub-type from API data or infer from URL parameters
        let giftSubType = 'new_membership';
        if (this.apiDataAvailable && this.transactionMetadata?.gift_sub_type) {
          giftSubType = this.transactionMetadata.gift_sub_type;
        } else {
          // Fallback logic for URL parameters
          giftSubType = (this.productId > 0 && this.packageId > 0) ? 'upgrade' : 'new_membership';
        }
        return giftSubType === 'upgrade' ? 'تم إهداء الترقية بنجاح!' : 'تم إهداء العضوية بنجاح!';

      case 'subscription':
        return 'تم تفعيل الاشتراك بنجاح!';

      case 'donation':
        return 'تم التبرع بنجاح!';

      case 'refund':
        return 'تم استرداد المبلغ بنجاح!';

      case 'upgrade':
        return 'تم ترقية العضوية بنجاح!';

      default:
        return 'تمت العملية بنجاح!';
    }
  }

  getSuccessMessage(): string {
    if (this.transactionDataLoading) {
      return 'يتم الآن تحميل تفاصيل المعاملة من الخادم...';
    }

    if (this.transactionDataError) {
      return this.transactionDataError + ' - تم الاعتماد على البيانات الأساسية';
    }

    const transactionType = this.getTransactionType();

    switch (transactionType) {
      case 'gift':
        // Use API data if available, otherwise fall back to URL parameters
        const recipientName = (this.apiDataAvailable && this.transactionMetadata?.recipient_name) || this.giftRecipientName;
        const recipientId = (this.apiDataAvailable && this.transactionMetadata?.recipient_family_user_id) || this.giftRecipientId;
        const recipientDisplay = recipientName || `المستخدم ${recipientId}`;

        // Get gift sub-type from API data or infer from URL parameters
        let giftSubType = 'new_membership';
        if (this.apiDataAvailable && this.transactionMetadata?.gift_sub_type) {
          giftSubType = this.transactionMetadata.gift_sub_type;
        } else {
          // Fallback logic for URL parameters
          giftSubType = (this.productId > 0 && this.packageId > 0) ? 'upgrade' : 'new_membership';
        }

        if (giftSubType === 'upgrade') {
          return `تم إهداء ترقية العضوية إلى ${recipientDisplay} بنجاح`;
        }
        return `تم إهداء العضوية إلى ${recipientDisplay} بنجاح`;

      case 'subscription':
        const billingCycle = this.transactionMetadata?.billing_cycle || 'yearly';
        return `تم تفعيل الاشتراك ${billingCycle === 'monthly' ? 'الشهري' : 'السنوي'} بنجاح`;

      case 'donation':
        const causeName = this.transactionMetadata?.cause_name;
        return causeName ? `تم التبرع لـ ${causeName} بنجاح` : 'تم التبرع بنجاح';

      case 'refund':
        return 'تم معالجة طلب الاسترداد بنجاح';

      case 'upgrade':
        const fromPackage = this.transactionMetadata?.from_package_title;
        const toPackage = this.transactionMetadata?.to_package_title;
        if (fromPackage && toPackage) {
          return `تم ترقية العضوية من ${fromPackage} إلى ${toPackage}`;
        }
        return 'تم ترقية العضوية بنجاح';

      default:
        return 'تم تفعيل عضويتك بنجاح';
    }
  }

  getSuccessSubMessage(): string {
    if (this.transactionDataLoading) {
      return 'الرجاء الانتظار...';
    }

    const transactionType = this.getTransactionType();
    const transactionStatus = this.transactionDetails?.transaction_status;

    switch (transactionType) {
      case 'gift':
        if (this.apiDataAvailable && transactionStatus) {
          return `حالة المعاملة: ${transactionStatus} - سيتم إشعار المستلم قريباً`;
        }
        return 'سيتم إشعار المستلم بالهدية قريباً';

      case 'subscription':
        const autoRenew = this.transactionMetadata?.auto_renew;
        return autoRenew ? 'سيتم تجديد الاشتراك تلقائياً' : 'يمكنك تجديد الاشتراك يدوياً';

      case 'donation':
        return 'شكراً لك على مساهمتك الكريمة';

      case 'refund':
        return 'سيتم إرجاع المبلغ خلال 3-5 أيام عمل';

      case 'upgrade':
        const effectiveDate = this.transactionMetadata?.upgrade_effective_date;
        return effectiveDate ? `ستصبح الترقية فعالة في ${effectiveDate}` : 'الترقية فعالة الآن';

      default:
        return 'يمكنك الآن الاستفادة من جميع مزايا العضوية';
    }
  }

  getDataSource(): string {
    return this.apiDataAvailable ? 'البيانات محملة من الخادم' : 'البيانات من معاملات URL';
  }

  // Transaction type specific helper methods

  isGiftTransaction(): boolean {
    return this.getTransactionType() === 'gift';
  }

  isSubscriptionTransaction(): boolean {
    return this.getTransactionType() === 'subscription';
  }

  isDonationTransaction(): boolean {
    return this.getTransactionType() === 'donation';
  }

  isRefundTransaction(): boolean {
    return this.getTransactionType() === 'refund';
  }

  isUpgradeTransaction(): boolean {
    return this.getTransactionType() === 'upgrade';
  }

  getTransactionAmount(): number {
    // Use actual API data if available
    if (this.apiDataAvailable && this.actualTransactionData?.amount) {
      return this.actualTransactionData.amount;
    }
    // Fallback to package price if available
    if (this.packageDetails?.product?.price) {
      return this.packageDetails.product.price;
    }
    return 0;
  }

  getTransactionDate(): Date {
    // Use actual API data if available
    if (this.apiDataAvailable && this.actualTransactionData?.confirmed_at) {
      return new Date(this.actualTransactionData.confirmed_at);
    }
    return new Date();
  }

  getTransactionStatus(): string {
    // Use actual API data if available
    if (this.apiDataAvailable && this.actualTransactionData?.status) {
      return this.actualTransactionData.status;
    }
    return '';
  }

  getGiftMessage(): string {
    // Get gift message from actual API data
    if (this.apiDataAvailable && this.actualTransactionData?.giftMessage) {
      return this.actualTransactionData.giftMessage;
    }
    return '';
  }

  getGiftSubType(): string {
    // Get gift sub-type from actual API data
    if (this.apiDataAvailable && this.actualTransactionData?.giftSubType) {
      return this.actualTransactionData.giftSubType;
    }
    // Fallback logic for URL parameters
    return (this.productId > 0 && this.packageId > 0) ? 'upgrade' : 'new_membership';
  }

  // Debug method to test component state
  debugComponentState() {
    console.log('=== Component State Debug ===');
    console.log('URL Parameters:');
    console.log('  transactionUUID:', this.transactionUUID);
    console.log('  packageId:', this.packageId);
    console.log('  productId:', this.productId);
    console.log('  isGiftMode:', this.isGiftMode);
    console.log('  giftRecipientId:', this.giftRecipientId);
    console.log('  giftRecipientName:', this.giftRecipientName);
    console.log('');
    console.log('API State:');
    console.log('  transactionDataLoading:', this.transactionDataLoading);
    console.log('  transactionDataLoaded:', this.transactionDataLoaded);
    console.log('  apiDataAvailable:', this.apiDataAvailable);
    console.log('  transactionDataError:', this.transactionDataError);
    console.log('');
    console.log('API Data:');
    console.log('  actualTransactionData:', this.actualTransactionData);
    console.log('  transactionDetails:', this.transactionDetails);
    console.log('  transactionMetadata:', this.transactionMetadata);
    console.log('  packageDetails:', this.packageDetails);
    console.log('');
    console.log('Helper Methods:');
    console.log('  getTransactionType():', this.getTransactionType());
    console.log('  getTransactionAmount():', this.getTransactionAmount());
    console.log('  getTransactionStatus():', this.getTransactionStatus());
    console.log('  getGiftSubType():', this.getGiftSubType());
    console.log('  getGiftMessage():', this.getGiftMessage());
    console.log('============================');
  }
}
