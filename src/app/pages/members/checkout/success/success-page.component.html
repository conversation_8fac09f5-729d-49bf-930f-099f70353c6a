<app-header [hideProfileIcon]="false" [showBackButton]="true" backTo="/home"></app-header>
<ion-content>
  <ion-grid [fixed]="true">
    <app-card title="" [showExpandButton]="false" [scrollable]="false">

      <!-- Success Animation Section -->
      <div class="success-animation-container">
        <ion-row>
          <ion-col class="ion-text-center">
            <div class="success-icon-wrapper" [class.animate]="animationComplete">
              <ion-icon name="checkmark-circle" color="success" class="success-icon"></ion-icon>
              <div class="success-ripple" [class.animate]="animationComplete"></div>
            </div>
          </ion-col>
        </ion-row>

        <!-- Success Message -->
        <ion-row class="ion-margin-top">
          <ion-col class="ion-text-center">
            <div class="success-content" [class.animate]="animationComplete">
              <h2 class="success-title">{{ getSuccessTitle() }}</h2>
              <p class="success-message">{{ getSuccessMessage() }}</p>
              <!--<p class="success-sub-message">{{ getSuccessSubMessage() }}</p>-->

              <!-- Data Source Indicator (for debugging) -->
              <!--<p *ngIf="transactionUUID && getDataSource()" class="data-source-indicator">
                <ion-icon name="information-circle" color="medium"></ion-icon>
                {{ getDataSource() }}
              </p>-->

              <!-- Debug Button (temporary) -->
              <!--<ion-button *ngIf="!isServer" fill="clear" size="small" (click)="debugComponentState()">
                <ion-icon name="bug" slot="start"></ion-icon>
                Debug State
              </ion-button>-->

              <!-- Debug Info (temporary) -->
              <!--<div *ngIf="!isServer" class="debug-info" style="font-size: 12px; color: #666; margin-top: 10px;">
                <p>Transaction UUID: {{ transactionUUID || 'None' }}</p>
                <p>Package ID: {{ packageId || 'None' }}</p>
                <p>Gift Mode: {{ isGiftMode ? 'Yes' : 'No' }}</p>
                <p>API Loading: {{ transactionDataLoading ? 'Yes' : 'No' }}</p>
                <p>API Available: {{ apiDataAvailable ? 'Yes' : 'No' }}</p>
                <p>Error: {{ transactionDataError || 'None' }}</p>
              </div>-->

              <!-- Loading Indicator for Transaction Data -->
              <div *ngIf="transactionDataLoading" class="transaction-loading-indicator">
                <ion-spinner name="dots" color="primary"></ion-spinner>
                <p>جاري تحميل تفاصيل المعاملة...</p>
              </div>
            </div>
          </ion-col>
        </ion-row>
      </div>

      <!-- Gift Transaction Details -->
      <div *ngIf="isGiftTransaction() && animationComplete && !transactionDataLoading" class="gift-details-section ion-margin-top">
        <ion-card class="gift-summary-card">
          <ion-card-header>
            <ion-card-title>
              <ion-icon name="gift" color="primary"></ion-icon>
              تفاصيل الهدية
              <ion-badge *ngIf="apiDataAvailable" color="success" class="api-badge">محقق</ion-badge>
              <ion-badge *ngIf="!apiDataAvailable" color="warning" class="api-badge">أساسي</ion-badge>
            </ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <ion-item lines="none">
              <ion-icon name="person" slot="start" color="medium"></ion-icon>
              <ion-label>
                <h3>المستلم</h3>
                <p>{{ (transactionMetadata?.recipient_name || giftRecipientName) || 'مستخدم ' + (transactionMetadata?.recipient_family_user_id || giftRecipientId) }}</p>
                <p class="recipient-id">الرقم التعريفي: {{ transactionMetadata?.recipient_family_user_id || giftRecipientId }}</p>
              </ion-label>
            </ion-item>

            <ion-item lines="none" *ngIf="packageDetails">
              <ion-icon name="card" slot="start" color="medium"></ion-icon>
              <ion-label>
                <h3>نوع الهدية</h3>
                <p>{{ packageDetails.title }}</p>
                <p class="package-price">{{ getTransactionAmount() | number }} ر.س</p>
              </ion-label>
            </ion-item>

            <!--<ion-item lines="none">
              <ion-icon name="time" slot="start" color="medium"></ion-icon>
              <ion-label>
                <h3>تاريخ الإهداء</h3>
                <p>{{ getTransactionDate() | date:'dd/MM/yyyy - HH:mm' }}</p>
              </ion-label>
            </ion-item>-->

            <!-- Gift Message (if available from API) -->
            <ion-item lines="none" *ngIf="getGiftMessage() && apiDataAvailable">
              <ion-icon name="chatbubble" slot="start" color="medium"></ion-icon>
              <ion-label>
                <h3>رسالة الهدية</h3>
                <p>{{ getGiftMessage() }}</p>
              </ion-label>
            </ion-item>

            <!-- Transaction Status (if available from API) -->
            <!--<ion-item lines="none" *ngIf="getTransactionStatus() && apiDataAvailable">
              <ion-icon name="checkmark-circle" slot="start" color="success"></ion-icon>
              <ion-label>
                <h3>حالة المعاملة</h3>
                <p>{{ getTransactionStatus() }}</p>
              </ion-label>
            </ion-item>-->
          </ion-card-content>
        </ion-card>

        <!-- API Error Notice -->
        <ion-card *ngIf="transactionDataError && !apiDataAvailable" class="error-notice-card">
          <ion-card-content>
            <ion-item lines="none">
              <ion-icon name="warning" slot="start" color="warning"></ion-icon>
              <ion-label>
                <h3>تنبيه</h3>
                <p>{{ transactionDataError }}</p>
                <p class="error-fallback">تم عرض البيانات الأساسية من معاملات URL</p>
              </ion-label>
            </ion-item>
          </ion-card-content>
        </ion-card>
      </div>

      <!-- Regular Transaction Details -->
      <div *ngIf="!isGiftTransaction() && animationComplete && packageDetails" class="transaction-details-section ion-margin-top">
        <ion-card class="transaction-summary-card">
          <ion-card-header>
            <ion-card-title>
              <ion-icon name="receipt" color="primary"></ion-icon>
              تفاصيل العضوية
            </ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <ion-item lines="none">
              <ion-icon [name]="'membership-' + packageDetails.slug" slot="start" color="primary"></ion-icon>
              <ion-label>
                <h3>{{ packageDetails.title }}</h3>
                <p>{{ packageDetails.product?.price | number }} ر.س/سنة</p>
              </ion-label>
            </ion-item>

            <ion-item lines="none">
              <ion-icon name="time" slot="start" color="medium"></ion-icon>
              <ion-label>
                <h3>تاريخ التفعيل</h3>
                <p>{{ getTransactionDate() | date:'dd/MM/yyyy - HH:mm' }}</p>
              </ion-label>
            </ion-item>
          </ion-card-content>
        </ion-card>
      </div>

      <!-- Action Buttons -->
      <div *ngIf="animationComplete" class="action-buttons ion-margin-top">
        <ion-button expand="block" color="primary" (click)="navigateToHome()" class="primary-action">
          <ion-icon name="home" slot="start"></ion-icon>
          العودة للرئيسية
        </ion-button>

        <ion-button expand="block" fill="outline" color="primary" (click)="navigateToMemberships()" class="secondary-action ion-margin-top">
          <ion-icon name="card" slot="start"></ion-icon>
          {{ isGiftTransaction() ? 'إهداء عضوية أخرى' : 'عرض العضويات' }}
        </ion-button>
      </div>

    </app-card>
  </ion-grid>
</ion-content>

