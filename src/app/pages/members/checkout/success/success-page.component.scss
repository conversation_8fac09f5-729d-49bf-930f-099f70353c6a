
// Success Animation Styles
.success-animation-container {
  padding: 20px 0;
}

.success-icon-wrapper {
  position: relative;
  display: inline-block;

  .success-icon {
    font-size: 120px;
    opacity: 0;
    transform: scale(0.5);
    transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);

    &.animate {
      opacity: 1;
      transform: scale(1);
    }
  }

  .success-ripple {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 120px;
    height: 120px;
    border: 3px solid var(--ion-color-success);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;

    &.animate {
      animation: ripple 1s ease-out 0.3s forwards;
    }
  }

  &.animate .success-icon {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes ripple {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.8;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}

// Success Content Styles
.success-content {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s ease-out 0.6s;

  &.animate {
    opacity: 1;
    transform: translateY(0);
  }

  .success-title {
    color: var(--ion-color-success);
    font-weight: 600;
    margin-bottom: 12px;
    font-size: 1.5rem;
  }

  .success-message {
    font-size: 1.1rem;
    color: var(--ion-color-dark);
    margin-bottom: 8px;
    font-weight: 500;
  }

  .success-sub-message {
    color: var(--ion-color-medium);
    font-size: 0.95rem;
    margin-bottom: 0;
  }

  .data-source-indicator {
    font-size: 0.8rem;
    color: var(--ion-color-medium);
    margin-top: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
  }

  .transaction-loading-indicator {
    margin-top: 16px;

    ion-spinner {
      margin-bottom: 8px;
    }

    p {
      font-size: 0.9rem;
      color: var(--ion-color-medium);
      margin: 0;
    }
  }
}

// Gift Details Styles
.gift-details-section {
  animation: slideInUp 0.6s ease-out;

  .gift-summary-card {
    border: 1px solid var(--ion-color-primary-tint);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);

    ion-card-title {
      color: var(--ion-color-primary);
      font-size: 1.2rem;
      display: flex;
      align-items: center;
      gap: 8px;

      .api-badge {
        margin-left: auto;
        font-size: 0.7rem;
        padding: 2px 6px;
      }
    }

    ion-item {
      --padding-start: 0;
      --inner-padding-end: 0;

      ion-label {
        h3 {
          color: var(--ion-color-dark);
          font-weight: 600;
          margin-bottom: 4px;
        }

        p {
          color: var(--ion-color-medium);
          margin: 2px 0;
        }

        .recipient-id {
          font-size: 0.9rem;
          color: var(--ion-color-medium);
        }

        .package-price {
          color: var(--ion-color-primary);
          font-weight: 600;
        }
      }
    }
  }
}

// Error Notice Card
.error-notice-card {
  border: 1px solid var(--ion-color-warning);
  border-radius: 8px;
  margin-top: 16px;

  ion-card-content {
    padding: 12px;
  }

  ion-item {
    --padding-start: 0;
    --inner-padding-end: 0;

    ion-label {
      h3 {
        color: var(--ion-color-warning);
        font-weight: 600;
        margin-bottom: 4px;
      }

      p {
        color: var(--ion-color-medium);
        margin: 2px 0;
        font-size: 0.9rem;
      }

      .error-fallback {
        color: var(--ion-color-dark);
        font-weight: 500;
        font-size: 0.85rem;
      }
    }
  }
}

// Transaction Details Styles
.transaction-details-section {
  animation: slideInUp 0.6s ease-out;

  .transaction-summary-card {
    border: 1px solid var(--ion-color-light);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);

    ion-card-title {
      color: var(--ion-color-primary);
      font-size: 1.2rem;
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
}

// Transaction Details Toggle
.transaction-details-toggle {
  text-align: center;

  .details-toggle-btn {
    --color: var(--ion-color-medium);
    font-size: 0.9rem;
  }

  .transaction-details {
    margin-top: 16px;
    animation: slideInUp 0.3s ease-out;

    ion-list {
      background: var(--ion-color-light);
      border-radius: 8px;
      padding: 8px;
    }

    ion-item {
      --background: transparent;
      --padding-start: 12px;
      --inner-padding-end: 12px;

      ion-label {
        h3 {
          font-size: 0.9rem;
          color: var(--ion-color-dark);
          font-weight: 600;
        }

        p {
          font-size: 0.85rem;
          color: var(--ion-color-medium);
          font-family: monospace;
        }
      }
    }
  }
}

// Action Buttons
.action-buttons {
  padding: 20px 0;

  .primary-action {
    --border-radius: 12px;
    height: 48px;
    font-weight: 600;
    font-size: 1rem;
  }

  .secondary-action {
    --border-radius: 12px;
    height: 44px;
    font-weight: 500;
    --border-width: 2px;
  }
}

// Animations
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .success-icon-wrapper .success-icon {
    font-size: 100px;
  }

  .success-content .success-title {
    font-size: 1.3rem;
  }

  .success-content .success-message {
    font-size: 1rem;
  }
}
