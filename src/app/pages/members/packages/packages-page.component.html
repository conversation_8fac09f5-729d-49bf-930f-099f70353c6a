<app-header [hideProfileIcon]="false" [showBackButton]="true" backTo="home"></app-header>
<ion-content>
  <ion-grid [fixed]="true">
    <app-card title="العضويات" [showExpandButton]="false" [scrollable]="false" style="height: 100%;">
      <!-- Loading State -->
      <div *ngIf="isDataLoading" class="loading-container">
        <ion-spinner style="margin: auto; width: 100%; height: 40px;"></ion-spinner>
        <ion-text color="medium">
          <p style="text-align: center; margin-top: 16px;">جاري تحميل البيانات...</p>
        </ion-text>
      </div>

      <!-- Auto-Gift Loading Overlay -->
      <div *ngIf="autoGiftMode && autoGiftProcessing"
           class="auto-gift-loading-overlay"
           role="status"
           aria-live="polite"
           [attr.aria-label]="getAutoGiftLoadingMessage()">

        <div class="overlay-backdrop"></div>

        <div class="loading-content">
          <div class="loading-card">
            <div class="loading-header">
              <ion-icon name="gift" class="gift-icon"></ion-icon>
              <h2>إعداد الهدية</h2>
            </div>

            <div class="loading-body">
              <ion-spinner name="dots" class="main-spinner"></ion-spinner>

              <div class="recipient-info">
                <p class="recipient-label">المستلم:</p>
                <p class="recipient-name" *ngIf="autoGiftRecipientName; else recipientIdTemplate">
                  {{ autoGiftRecipientName }}
                  <span class="recipient-id">({{ autoGiftRecipientId }})</span>
                </p>
                <ng-template #recipientIdTemplate>
                  <p class="recipient-name">مستخدم {{ autoGiftRecipientId }}</p>
                </ng-template>
              </div>

              <div class="loading-message">
                <p class="status-message">{{ getAutoGiftLoadingMessage() }}</p>
                <div class="progress-dots">
                  <span class="dot" [class.active]="autoGiftStep >= 1"></span>
                  <span class="dot" [class.active]="autoGiftStep >= 2"></span>
                  <span class="dot" [class.active]="autoGiftStep >= 3"></span>
                </div>
              </div>
            </div>

            <div class="loading-footer">
              <ion-button fill="clear"
                         color="medium"
                         size="small"
                         (click)="cancelAutoGiftFlow()"
                         class="cancel-button">
                <ion-icon name="close" slot="start"></ion-icon>
                إلغاء
              </ion-button>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content - Only show when data is loaded -->
      <ng-container *ngIf="!isDataLoading && { user: user$ | async, packages: packages$ | async } as pageData"
                    [ngTemplateOutlet]="mainContent"
                    [ngTemplateOutletContext]="{ pageData: pageData }">
      </ng-container>
    </app-card>
  </ion-grid>
  <ion-modal *ifIsBrowser
             #payModal
             (willDismiss)="onWillDismiss($event)"
             mode="ios"
             [initialBreakpoint]="0.75"
             [breakpoints]="[0.75]"
             (ionModalDidDismiss)="cancel()">
    <ng-template>
      <ion-header>
        <ion-toolbar>
          <ion-buttons slot="start">
            <ion-button (click)="cancel()">ألغ</ion-button>
          </ion-buttons>
          <ion-title color="primary">الدفع</ion-title>
        </ion-toolbar>
      </ion-header>
      <ion-content class="ion-padding">
        <ion-spinner *ngIf="showPaymentsSpinner"
                     style="margin: auto; width: 100%;height: 40px;"></ion-spinner>
        <div id="card-applepay" class="ion-margin-top" [hidden]="showPaymentsSpinner"></div>
        <div class="altwijry-delimiter" *ngIf="!creditCardProcessing"></div>
        <div id="credit-card" [hidden]="showPaymentsSpinner"></div>

        <ion-button *ngIf="myFatoorahCreditCardSessionId"
                    color="secondary"
                    expand="block"
                    class="ion-margin-top"
                    [hidden]="showPaymentsSpinner"
                    (click)="payWithCreditCard()">
          <ion-spinner *ngIf="creditCardProcessing"></ion-spinner>
          <span *ngIf="!creditCardProcessing" [hidden]="showPaymentsSpinner">ادفع</span>
        </ion-button>
      </ion-content>
    </ng-template>
  </ion-modal>
</ion-content>

<!-- Main Content Template -->
<ng-template #mainContent let-pageData="pageData">
  <div *ngIf="pageData.user && pageData.packages"
       class="packages-content"
       style="animation: fadeIn 0.3s ease-in-out;">

    <!-- Gift Options - Available to All Users -->
    <ion-row class="ion-margin-bottom">
      <ion-col size="12">
        <ion-label>
          <h3 style="text-align: center; margin-bottom: 16px;">إهداء العضويات</h3>
          <p style="text-align: center; color: var(--ion-color-medium);">
            أهدي عضوية لأحد أفراد العائلة
            <!--أهدي عضوية أو ترقية لأحد أفراد العائلة-->
          </p>
        </ion-label>
      </ion-col>
    </ion-row>

    <ion-row class="ion-margin-bottom">
      <ion-col size="12">
        <ion-button color="primary" expand="block" fill="outline"
                    (click)="openGiftModalForNewMembershipSimple()"
                    class="ion-margin-start ion-margin-end">
          <ion-icon name="gift" slot="start"></ion-icon>
          إهداء عضوية جديدة
        </ion-button>
      </ion-col>
      <!--<ion-col size="6">
        <ion-button color="secondary" expand="block" fill="outline"
                    (click)="openGiftModalForUpgradeSimple()"
                    class="ion-margin-start ion-margin-end">
          <ion-icon name="gift" slot="start"></ion-icon>
          إهداء ترقية
        </ion-button>
      </ion-col>-->
    </ion-row>

    <!-- Separator -->
    <ion-row class="ion-margin-bottom">
      <ion-col size="12">
        <div class="separator"><span>أو</span></div>
      </ion-col>
    </ion-row>

    <!-- User Membership Status and Package Options -->
      <ng-container style="height: 100%;"
                    *ngIf="pageData.user !== null && pageData.user.membership !== undefined && !upgradePackage$">
        <ion-row>
          <ion-col>
            <ion-avatar style="margin: auto;width: 90px; height: 90px;">
              @let fallbackURL = ('/assets/d3-images/' + (pageData.user.gender ?? 'male').toLowerCase() + '-icon.svg') ;
              @let fallbackIcon = ((pageData.user.gender ?? 'male').toLowerCase() + '-icon') ;
              @if (pageData.user.profile_photo_url) {
                <img
                  [src]="pageData.user.profile_photo_url ?? fallbackURL"
                  [appImgFallback]="fallbackURL"
                  width="120" height="120">
              } @else {
                <ion-icon slot="start" [name]="fallbackIcon" style="font-size: 114px;"></ion-icon>
              }
            </ion-avatar>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col class="ion-no-padding">
            <ion-label class="ion-text-wrap ion-text-center">
              <ion-text color="secondary">
                <h5
                  style="line-height: 1.7; font-weight: bolder; letter-spacing: 4px">{{pageData.user.family_user_id}}</h5>
              </ion-text>
              <h4 style="line-height: 1.7; font-weight: bolder">{{pageData.user.full_name}}</h4>
            </ion-label>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col size="auto" class="ion-no-padding">
            <ion-label>
              <h4 class="ion-text-wrap" style="line-height: 1.7 !important;">
                {{pageData.user.membership.package.title}}
              </h4>
            </ion-label>
          </ion-col>
          <ion-col size="auto" class="ion-no-padding">
            <div style="margin: 8px 4px 0 0;">
              <ion-icon [name]="'membership-' + pageData.user.membership.package.slug" style="font-size: 16px;"></ion-icon>
            </div>
            <!--<ion-img *ngIf="pageData" style="width: 18px;margin: 8px 4px 0 0;"
                     [src]="'/assets/images/icons/membership-'+pageData.user.membership.package.slug+'.svg'"></ion-img>-->
          </ion-col>
          <ion-col class="ion-margin-start ion-no-padding">
            {{pageData.user.membership.start_at | date:'longDate'}}
          </ion-col>
          <ion-col size="auto" class="ion-no-padding"> ←</ion-col>
          <ion-col class="ion-margin-end ion-no-padding" style="text-align: left">
            {{pageData.user.membership.expired_at | date:'longDate'}}
          </ion-col>
        </ion-row>
        <ion-button *ngIf="(pageData.user.membership?.package?.upgradable_packages_id ?? []).length > 0"
                    color="secondary" expand="block" (click)="upgradePackage$ = true"
                    class="ion-margin-top ion-margin-start ion-margin-end">
          ترقية العضوية الحالية
        </ion-button>
      </ng-container>

      <ion-card-content *ngIf="upgradePackage$">
        <ion-button *ngIf="false" expand="block" color="secondary" fill="outline" class="cancel-button ion-margin"
                    (click)="upgradePackage$ = false">
          رجوع
        </ion-button>
        <ng-container
          *ngIf="getUpgradablePackages(pageData.packages ?? [], (pageData.user?.membership?.package?.upgradable_packages_id ?? [])).length > 0">
          <div
            dir="rtl"
            class="ion-margin-top"
            style="border-radius: var(--border-radius); border: var(--ion-color-dark) solid 1px;"
            *ngFor="let package$ of getUpgradablePackages(pageData.packages ?? [], (pageData.user?.membership?.package?.upgradable_packages_id ?? [])); let i = index">
            <ion-row class="ion-margin-bottom">
              <ion-col size="12" class="ion-no-padding">
                <div style="width: 80px;margin: auto;" *ngIf="package$.slug">
                  <ion-icon [name]="'membership-' + package$.slug" style="font-size: 80px;"></ion-icon>
                </div>
                <!--<ion-img *ngIf="package$.slug" style="width: 80px;margin: auto;"
                         [src]="'/assets/images/icons/membership-'+package$.slug+'.svg'"></ion-img>-->
              </ion-col>
              <ion-col size="12" class="ion-no-padding">
                <ion-label class="ion-text-center">
                  <h3>
                    <b>
                      {{package$.title}}
                    </b>
                  </h3>
                </ion-label>
              </ion-col>
              <ion-col size="12">
                <ion-label class="ion-text-center">
                  <h2>
                    <b>
                      {{package$.product.price | number}}
                    </b>
                  </h2>
                  <h4>ريال/سنة</h4>
                  <h2 class="ion-margin-top">
                    تكلفة الترقية :
                    {{Math.ceil(package$.product.price - (pageData.user?.membership?.actual_balance ?? -1)) | number}}
                    ر.س
                  </h2>
                </ion-label>
              </ion-col>
              <ion-col size="12">
                <ul class="ion-no-padding ion-no-margin">
                  <ng-container *ngFor="let feature$ of package$.features; let i = index">
                    <h5 style="margin: 0; padding-top: 3px">
                      <!--<img priority ngSrc="/assets/images/icons/check.svg" style="vertical-align: middle;" width="20" height="20">-->
                      <span style="vertical-align: middle;">
                        <ion-icon name="check" style="font-size: 20px;"></ion-icon>
                      </span>
                      {{ feature$ }}
                    </h5>
                  </ng-container>
                </ul>
              </ion-col>
            </ion-row>

            <ion-button color="primary" class="ion-margin"
                        expand="block"
                        (click)="onUpgradeClick(package$.id)">
              اختر
            </ion-button>
            <!--<ion-button color="secondary" class="ion-margin"
                        expand="block" fill="outline"
                        (click)="onGiftUpgradeClick(package$.id)">
              <ion-icon name="gift" slot="start"></ion-icon>
              إهداء ترقية
            </ion-button>-->
          </div>
        </ng-container>
      </ion-card-content>

      <ng-container style="height: 100%;" *ngIf="pageData.user === null || pageData.user.membership === undefined">
        <ion-card-content *ngIf="pageData.user?.membership === undefined">

          <ng-container
            *ngIf="(pageData.packages ?? []).length > 0">
            <div
              class="ion-margin-top"
              style="border-radius: var(--border-radius); border: var(--ion-color-dark) solid 1px;"
              *ngFor="let package$ of pageData.packages; let i = index">
              <ion-row class="ion-margin-bottom">
                <ion-col size="12" class="ion-no-padding">
                  <!--<ion-img *ngIf="package$.slug" style="width: 80px;margin: auto;"
                           [src]="'/assets/images/icons/membership-'+package$.slug+'.svg'"></ion-img>-->
                  <div style="width: 80px;margin: auto;" *ngIf="package$.slug">
                    <ion-icon [name]="'membership-' + package$.slug" style="font-size: 80px;"></ion-icon>
                  </div>
                </ion-col>
                <ion-col size="12" class="ion-no-padding">
                  <ion-label class="ion-text-center">
                    <h3>
                      <b>
                        {{package$.title}}
                      </b>
                    </h3>
                  </ion-label>
                </ion-col>
                <ion-col size="12">
                  <ion-label class="ion-text-center">
                    <h2>
                      <b>
                        {{package$.product.price | number}}
                      </b>
                    </h2>
                    <h4>ريال/سنة</h4>
                  </ion-label>
                </ion-col>
                <ion-col size="12">
                  <ul class="ion-no-padding ion-no-margin">
                    <ng-container *ngFor="let feature$ of package$.features; let i = index">
                      <h5 style="margin: 0; padding-top: 3px">
                        <!--<img priority ngSrc="/assets/images/icons/check.svg" style="vertical-align: middle;" width="20" height="20">-->
                        <span style="vertical-align: middle;">
                          <ion-icon name="check" style="font-size: 20px;"></ion-icon>
                        </span>
                        {{ feature$ }}
                      </h5>
                    </ng-container>
                  </ul>
                </ion-col>
              </ion-row>
              <ion-button color="primary" class="ion-margin"
                          expand="block"
                          (click)="onSubscribeClick(package$.id)">
                اشترك الآن
              </ion-button>
              <!--<ion-button color="secondary" class="ion-margin"
                          expand="block" fill="outline"
                          (click)="onGiftSubscribeClick(package$.id)">
                <ion-icon name="gift" slot="start"></ion-icon>
                إهداء عضوية
              </ion-button>-->
            </div>
          </ng-container>
        </ion-card-content>
      </ng-container>
  </div>
</ng-template>

<!-- Step 1: Recipient Validation Modal -->
<ion-modal [isOpen]="recipientModalOpen" (didDismiss)="closeRecipientModal()">
  <ng-template>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-button (click)="cancelGiftFlow()">إلغاء</ion-button>
        </ion-buttons>
        <ion-title>الخطوة 1: تحديد مستلم الهدية</ion-title>
        <ion-buttons slot="end">
          <ion-button (click)="confirmRecipient()" [strong]="true"
                      [disabled]="!giftRecipientUser || giftRecipientMembershipError != ''">التالي</ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>
    <ion-content class="ion-padding">
      <ion-icon name="gift" color="secondary"
                style="font-size: 120px; text-align: center; width: 100%; margin-bottom: 20px;"></ion-icon>

      <div class="ion-text-center ion-margin-bottom">
        <h2>{{ giftType === 'upgrade' ? 'إهداء ترقية عضوية' : 'إهداء عضوية جديدة' }}</h2>
        <p color="medium">أدخل الرقم التعريفي للشخص المراد إهداؤه</p>
      </div>

      <ion-item>
        <ion-input
          label="أضف الرقم التعريفي للقريب"
          labelPlacement="stacked"
          pattern="[0-9]*"
          maxlength="5"
          inputmode="numeric"
          placeholder="اضغط Enter للبحث أو انقر على زر البحث"
          [formControl]="giftRecipientControl"
          (keypress)="onRecipientInputKeyPress($event)">
        </ion-input>
        <ion-button
          fill="clear"
          slot="end"
          (click)="searchGiftRecipient()"
          [disabled]="!isInputValidForSearch() || giftRecipientValidating"
          color="primary">
          <ion-spinner *ngIf="giftRecipientValidating" name="dots"></ion-spinner>
          <ion-icon *ngIf="!giftRecipientValidating" name="search" slot="icon-only"></ion-icon>
        </ion-button>
      </ion-item>

      <!-- Input validation hint -->
      <div *ngIf="giftRecipientControl.value && getInputValidationHint()" class="ion-padding-start ion-padding-end">
        <ion-text color="medium">
          <small>{{ getInputValidationHint() }}</small>
        </ion-text>
      </div>

      <!-- Validation feedback -->
      <div class="ion-padding-top">
        <ion-spinner *ngIf="giftRecipientValidating" name="dots" class="ion-margin-bottom"></ion-spinner>

        <!-- Error messages -->
        <ion-text color="danger" *ngIf="giftRecipientError">
          <p><ion-icon name="alert-circle" style="vertical-align: middle;"></ion-icon> {{ giftRecipientError }}</p>
        </ion-text>

        <ion-text color="warning" *ngIf="giftRecipientMembershipError">
          <p><ion-icon name="warning" style="vertical-align: middle;"></ion-icon> {{ giftRecipientMembershipError }}</p>
        </ion-text>

        <!-- Success - User profile display -->
        <div *ngIf="giftRecipientUser && !giftRecipientError && !giftRecipientMembershipError" class="ion-margin-top">
          <ion-item lines="none" class="recipient-profile">
            <ion-avatar slot="start">
              @let fallbackURL = ('/assets/d3-images/' + (giftRecipientUser.gender ?? 'male').toLowerCase() + '-icon.svg') ;
              @let fallbackIcon = ((giftRecipientUser.gender ?? 'male').toLowerCase() + '-icon') ;
              @if (giftRecipientUser.profile_photo_url) {
                <img
                  [src]="giftRecipientUser.profile_photo_url ?? fallbackURL"
                  [appImgFallback]="fallbackURL"
                  width="36" height="36">
              } @else {
                <ion-icon slot="start" [name]="fallbackIcon" style="font-size: 114px;"></ion-icon>
              }
            </ion-avatar>
            <ion-label>
              <h3>{{ giftRecipientUser.full_name }}</h3>
              <p>{{ giftRecipientUser.family_user_id }}</p>
            </ion-label>
            <ion-icon name="checkmark-circle" color="success" slot="end"></ion-icon>
          </ion-item>
        </div>
      </div>
    </ion-content>
  </ng-template>
</ion-modal>

<!-- Step 2: Package Selection Modal -->
<ion-modal [isOpen]="packageSelectionModalOpen" (didDismiss)="closePackageSelectionModal()">
  <ng-template>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-button (click)="backToRecipientValidation()">رجوع</ion-button>
        </ion-buttons>
        <ion-title>الخطوة 2: اختيار العضوية</ion-title>
        <ion-buttons slot="end">
          <ion-button (click)="confirmPackageSelection()" [strong]="true"
                      [disabled]="!selectedPackageForGift">التالي</ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <!-- Recipient Summary -->
      <div *ngIf="giftRecipientUser" class="recipient-summary ion-margin-bottom">
        <ion-card>
          <ion-card-header>
            <ion-card-subtitle>مستلم الهدية</ion-card-subtitle>
          </ion-card-header>
          <ion-card-content>
            <ion-item lines="none">
              <ion-avatar slot="start">
                @let fallbackURL = ('/assets/d3-images/' + (giftRecipientUser.gender ?? 'male').toLowerCase() + '-icon.svg') ;
                @let fallbackIcon = ((giftRecipientUser.gender ?? 'male').toLowerCase() + '-icon') ;
                @if (giftRecipientUser.profile_photo_url) {
                  <img
                    [src]="giftRecipientUser.profile_photo_url ?? fallbackURL"
                    [appImgFallback]="fallbackURL"
                    width="36" height="36">
                } @else {
                  <ion-icon slot="start" [name]="fallbackIcon" style="font-size: 114px;"></ion-icon>
                }
              </ion-avatar>
              <ion-label>
                <h3>{{ giftRecipientUser.full_name || 'مستخدم ' + giftRecipientUser.family_user_id }}</h3>
                <p>الرقم التعريفي: {{ giftRecipientUser.family_user_id }}</p>
              </ion-label>
            </ion-item>
          </ion-card-content>
        </ion-card>
      </div>

      <!-- Package Selection -->
      <div class="package-selection">
        <ion-label>
          <h3>اختر العضوية المراد إهداؤها</h3>
          <p color="medium">{{ giftType === 'upgrade' ? 'العضويات المتاحة للترقية' : 'جميع العضويات المتاحة' }}</p>
        </ion-label>

        <!-- Loading state for packages -->
        <div *ngIf="!availableGiftPackages || availableGiftPackages.length === 0" class="packages-loading">
          <ion-spinner style="margin: auto; width: 100%; height: 40px;"></ion-spinner>
          <ion-text color="medium">
            <p style="text-align: center; margin-top: 16px;">جاري تحميل العضويات...</p>
          </ion-text>
        </div>

        <!-- Package selection when packages are available -->
        <div *ngIf="availableGiftPackages && availableGiftPackages.length > 0">
          <ion-radio-group [(ngModel)]="selectedPackageForGift">
            <div *ngFor="let package$ of availableGiftPackages; trackBy: trackByPackageId; let i = index"
                 class="package-option ion-margin-bottom">
              <ion-card [class.selected]="selectedPackageForGift?.id === package$.id"
                        (click)="selectedPackageForGift = package$"
                        class="clickable-card">
                <ion-card-content>
                  <ion-item lines="none" class="package-item">
                    <ion-radio slot="start" [value]="package$" class="hidden-radio"></ion-radio>
                    <div style="width: 60px; margin-right: 16px;" *ngIf="package$?.slug">
                      <ion-icon [name]="'membership-' + package$.slug" style="font-size: 60px;"></ion-icon>
                    </div>
                    <ion-label>
                      <h2>{{ package$?.title || 'عضوية' }}</h2>
                      <h3>{{ package$?.product?.price | number }} ر.س/سنة</h3>
                      <div class="features-preview" *ngIf="package$?.features">
                        <p *ngFor="let feature of package$.features?.slice(0, 2); trackBy: trackByFeature" class="feature-item">
                          <ion-icon name="checkmark" color="success"></ion-icon>
                          {{ feature }}
                        </p>
                        <p *ngIf="(package$.features ?? []).length > 2" class="more-features">
                          +{{ package$.features.length - 2 }} ميزة أخرى
                        </p>
                      </div>
                    </ion-label>
                    <!-- Selection indicator -->
                    <ion-icon *ngIf="selectedPackageForGift?.id === package$.id"
                              name="checkmark-circle"
                              color="primary"
                              slot="end"
                              style="font-size: 24px;"></ion-icon>
                  </ion-item>
                </ion-card-content>
              </ion-card>
            </div>
          </ion-radio-group>
        </div>
      </div>
    </ion-content>
  </ng-template>
</ion-modal>

<!-- Step 3: Payment Modal -->
<ion-modal [isOpen]="paymentModalOpen" (didDismiss)="closePaymentModal()">
  <ng-template>
    <ion-header>
      <ion-toolbar>
        <ion-title>
          {{ paymentMode === 'gift' ? 'إتمام الدفع - هدية' :
             paymentMode === 'upgrade' ? 'إتمام الدفع - ترقية' : 'إتمام الدفع' }}
        </ion-title>
        <ion-buttons slot="end">
          <ion-button (click)="closePaymentModal()">
            <ion-icon name="close"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <div *ngIf="selectedPackageForPayment" class="payment-details">
        <!-- Package Information -->
        <ion-card>
          <ion-card-header>
            <ion-card-title>{{ selectedPackageForPayment.title }}</ion-card-title>
            <ion-card-subtitle>
              {{ paymentMode === 'gift' ?
                 (giftType === 'upgrade' ? 'هدية ترقية إلى: ' : 'هدية عضوية جديدة إلى: ') +
                 (giftRecipientUser?.full_name || 'مستخدم ' + giftRecipientUser?.family_user_id) :
                 paymentMode === 'upgrade' ? 'ترقية العضوية' : 'عضوية جديدة' }}
            </ion-card-subtitle>
          </ion-card-header>
          <ion-card-content>
            <ion-row>
              <ion-col size="6">
                <ion-text color="medium">
                  <p>السعر:</p>
                </ion-text>
              </ion-col>
              <ion-col size="6" class="ion-text-end">
                <ion-text color="primary">
                  <h3>{{ selectedPackageForPayment.product.price }} ر.س</h3>
                </ion-text>
              </ion-col>
            </ion-row>

            <!-- Gift recipient info -->
            <div *ngIf="paymentMode === 'gift' && giftRecipientUser" class="gift-info ion-margin-top">
              <ion-item lines="none">
                <ion-icon name="gift" slot="start" color="primary"></ion-icon>
                <ion-label>
                  <h3>المستلم</h3>
                  <p>{{ giftRecipientUser.full_name || 'مستخدم ' + giftRecipientUser.family_user_id }}</p>
                  <p>الرقم التعريفي: {{ giftRecipientUser.family_user_id }}</p>
                </ion-label>
              </ion-item>

              <!-- Gift message input -->
              <ion-item class="ion-margin-top gift-message-item" lines="none">
                <ion-textarea
                  label="رسالة الهدية (اختيارية)"
                  labelPlacement="stacked"
                  placeholder="اكتب رسالة شخصية مع هديتك..."
                  rows="3"
                  maxlength="200"
                  [formControl]="giftMessageControl"
                  counter="true"
                  fill="outline"
                  mode="md">
                </ion-textarea>
              </ion-item>

              <!-- Character count helper -->
              <div class="ion-padding-start ion-padding-end" *ngIf="giftMessageControl.value">
                <ion-text color="medium">
                  <small>{{ giftMessageControl.value?.length || 0 }}/200 حرف</small>
                </ion-text>
              </div>
            </div>
          </ion-card-content>
        </ion-card>

        <!-- Payment Actions -->
        <div class="payment-actions ion-margin-top">
          <ion-button
            expand="block"
            color="primary"
            (click)="processPayment()"
            [disabled]="paymentLoading">
            <ion-spinner *ngIf="paymentLoading" name="dots" slot="start"></ion-spinner>
            <ion-icon *ngIf="!paymentLoading" name="card" slot="start"></ion-icon>
            {{ paymentLoading ? 'جاري المعالجة...' :
               paymentMode === 'gift' ? 'ادفع كهدية' : 'ادفع الآن' }}
          </ion-button>

          <ion-button
            expand="block"
            fill="outline"
            color="medium"
            (click)="closePaymentModal()"
            [disabled]="paymentLoading"
            class="ion-margin-top">
            إلغاء
          </ion-button>
        </div>
      </div>
    </ion-content>
  </ng-template>
</ion-modal>
