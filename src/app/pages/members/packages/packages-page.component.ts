import {AfterView<PERSON>nit, Component, CUSTOM_ELEMENTS_SCHEMA, Inject, On<PERSON><PERSON>roy, OnInit, ViewChild} from '@angular/core';
import {AsyncPipe, DatePipe, DecimalPipe, DOCUMENT, NgForOf, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>plateOutlet} from "@angular/common";
import {
  AlertController,
  IonicModule,
  IonicSlides,
  IonModal,
  ModalController,
  NavController,
  ToastController
} from "@ionic/angular";
import {HeaderComponent} from "../../../layout/header/header.component";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {IS_SERVER_PLATFORM} from "../../../shared/IS_SERVER_PLATFORM.token";
import {CardComponent} from "../../../shared/components/card/card.component";
import {ApiService} from "../../../shared/services/api.service";
import {HttpClientModule} from "@angular/common/http";
import {catchError, map, Observable, of, take, tap, throwError} from "rxjs";
import {ActivatedRoute, NavigationExtras, Router, RouterModule} from "@angular/router";
import {Package, User} from "../../../shared/models";
import {FormControl, FormsModule, ReactiveFormsModule} from "@angular/forms";
import {GenericService} from "../../../shared/services/generic.service";

import {NgxSplideModule} from "ngx-splide";
import {ImgFallbackDirective} from "../../../img-fallback.directive";
import {Browser} from "@capacitor/browser";
import {environment} from "../../../../environments/environment";
import {NotificationService} from "../../../shared/services/notification.service";
import {OverlayEventDetail} from "@ionic/core/components";

declare var myFatoorahAP: any;
declare var myFatoorah: any;

@Component({
  selector: 'packages-page',
  templateUrl: 'packages-page.component.html',
  styleUrls: ['packages-page.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    IfIsBrowserDirective,
    NgIf,
    CardComponent,
    NgTemplateOutlet,
    NgForOf,
    AsyncPipe,
    RouterModule,
    DatePipe,
    DecimalPipe,
    NgxSplideModule,
    ImgFallbackDirective,
    ReactiveFormsModule,
    FormsModule
  ],
})
export class PackagesPage implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('payModal') modal!: IonModal;
  packages$: null | Observable<Package[]> = null
  user$: null | Observable<User | {
    membership: undefined,
    family_user_id: undefined,
    full_name: undefined,
    profile_photo_url: undefined
  }> = null
  upgradePackage$: boolean = false
  swiperModules = [IonicSlides]

  // Loading states
  userDataLoaded: boolean = false
  packagesDataLoaded: boolean = false
  isDataLoading: boolean = true

  // Payment modal properties
  paymentModalOpen: boolean = false
  selectedPackageForPayment: Package | null = null
  paymentMode: 'subscribe' | 'upgrade' | 'gift' = 'subscribe'
  paymentLoading: boolean = false

  // Payment session properties (matching existing implementation)
  transactionUUID: string = ''
  myFatoorahAppleSessionId: string = ''
  myFatoorahCreditCardSessionId: string = ''
  showPaymentsSpinner: boolean = false
  creditCardProcessing: boolean = false
  private isCapacitorApp: boolean = false

  // Gift wizard flow properties
  giftType: 'new_membership' | 'upgrade' | null = null // Track the type of gift being processed

  // Step 1: Recipient Validation
  recipientModalOpen: boolean = false
  giftRecipientControl = new FormControl('')
  giftRecipientUser: User | null = null
  giftRecipientValidating: boolean = false
  giftRecipientError: string = ''
  giftRecipientMembershipError: string = ''

  // Gift message
  giftMessageControl = new FormControl('')

  // Step 2: Package Selection
  packageSelectionModalOpen: boolean = false
  selectedPackageForGift: Package | null = null
  availableGiftPackages: Package[] = []

  // Auto-gift functionality
  autoGiftMode: boolean = false
  autoGiftRecipientId: string = ''
  autoGiftRecipientName: string = ''
  autoGiftProcessing: boolean = false
  autoGiftStep: number = 0 // 1: Validating, 2: Loading packages, 3: Opening modal
  autoGiftCancelled: boolean = false // Flag to track if auto-gift was cancelled
  private channelName!: string

  // Make Math available in template
  Math = Math

  constructor(
    @Inject(DOCUMENT) private document: Document,
    @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
    private apiService: ApiService,
    private modalCtrl: ModalController,
    private alertCtrl: AlertController,
    private toastCtrl: ToastController,
    private router: Router,
    private navCtrl: NavController,
    private genericService: GenericService,
    @Inject('Window') private window: Window,
    private notificationService: NotificationService,
    private activatedRoute: ActivatedRoute,
  ) {
    this.isCapacitorApp = this.genericService.isCapacitorApp()
  }

  ngOnDestroy(): void {
    this.modalCtrl.dismiss();
  }

  ngOnInit(): void {
    this.getUserData()
    this.getPackages()
    if (!this.isServer)
      this.browserSetup()
  }

  ngAfterViewInit() {

  }

  browserSetup() {
    // Check for auto-gift query parameters
    this.activatedRoute.queryParams.pipe(take(1)).subscribe(params => {
      const recipientId = params['recipientId'];
      const recipientName = params['recipientName'];

      console.log('Browser setup - Query params:', {recipientId, recipientName});

      if (recipientId) {
        this.autoGiftMode = true;
        this.autoGiftRecipientId = recipientId;

        // Store the recipient name if provided (URL-decoded)
        if (recipientName) {
          this.autoGiftRecipientName = decodeURIComponent(recipientName);
          console.log('Auto-gift mode activated for recipient:', recipientId, 'Name:', this.autoGiftRecipientName);
        } else {
          this.autoGiftRecipientName = '';
          console.log('Auto-gift mode activated for recipient:', recipientId, '(no name provided)');
        }

        // Wait for user data and packages to load before starting auto-gift flow
        this.initializeAutoGiftFlow();
      }
    });
  }

  initializeAutoGiftFlow() {
    // Wait for both user data and packages to be loaded
    const checkDataLoaded = () => {
      if (this.userDataLoaded && this.packagesDataLoaded && !this.autoGiftProcessing) {
        console.log('Data loaded, starting auto-gift flow...');
        this.autoGiftProcessing = true;
        this.startAutoGiftFlow();
      } else if (!this.autoGiftProcessing) {
        // Check again after a short delay
        setTimeout(checkDataLoaded, 500);
      }
    };

    checkDataLoaded();
  }

  startAutoGiftFlow() {
    console.log('Starting auto-gift flow for recipient:', this.autoGiftRecipientId,
      this.autoGiftRecipientName ? `(${this.autoGiftRecipientName})` : '(no name)');

    // Validate recipient ID format before proceeding
    if (!this.autoGiftRecipientId || !/^\d{1,5}$/.test(this.autoGiftRecipientId)) {
      console.error('Invalid recipient ID format:', this.autoGiftRecipientId);
      this.handleAutoGiftValidationFailure();
      return;
    }

    // Start the loading overlay
    this.autoGiftProcessing = true;
    this.autoGiftStep = 1; // Step 1: Validating recipient

    // Set up the gift recipient control with the provided ID
    this.giftRecipientControl.setValue(this.autoGiftRecipientId);

    // Clear any existing errors
    this.giftRecipientError = '';
    this.giftRecipientMembershipError = '';

    // Start recipient validation
    this.searchGiftRecipient().then((success) => {
      // Check if auto-gift was cancelled while validation was in progress
      if (this.autoGiftCancelled) {
        console.log('Auto-gift validation completed but flow was cancelled, ignoring result');
        return;
      }

      if (success) {
        console.log('Auto-gift recipient validation successful');
        this.proceedToAutoGiftPackageSelection();
      } else {
        console.log('Auto-gift recipient validation failed:', {
          error: this.giftRecipientError,
          membershipError: this.giftRecipientMembershipError
        });
        this.handleAutoGiftValidationFailure();
      }
    }).catch((error) => {
      // Check if auto-gift was cancelled
      if (this.autoGiftCancelled) {
        console.log('Auto-gift validation error but flow was cancelled, ignoring error');
        return;
      }

      console.error('Auto-gift validation error:', error);
      this.handleAutoGiftValidationFailure();
    });
  }

  proceedToAutoGiftPackageSelection() {
    // Check if auto-gift was cancelled
    if (this.autoGiftCancelled) {
      console.log('Auto-gift package selection cancelled, aborting');
      return;
    }

    // Step 2: Loading packages
    this.autoGiftStep = 2;

    // Set default gift type
    this.determineAutoGiftType();

    // Load available packages
    this.loadAvailablePackages();

    // Step 3: Opening modal with better error handling
    setTimeout(() => {
      // Check cancellation again before proceeding
      if (this.autoGiftCancelled) {
        console.log('Auto-gift cancelled during package loading, aborting');
        return;
      }
      this.autoGiftStep = 3;
    }, 1000);

    // Open package selection modal with defensive programming
    setTimeout(() => {
      // Final cancellation check before opening modal
      if (this.autoGiftCancelled) {
        console.log('Auto-gift cancelled before opening modal, aborting');
        return;
      }

      // Use safe modal opening method
      this.safelyOpenPackageModal();
    }, 1500);
  }

  async determineAutoGiftType() {
    // Simplified: Default to new_membership for auto-gift flow
    // Gift type can be determined later in the flow or by user selection
    this.giftType = 'new_membership';
    console.log('Auto-gift type set to: new_membership (default)');
  }

  handleAutoGiftValidationFailure() {
    this.autoGiftProcessing = false;
    this.autoGiftStep = 0;

    // Show error message
    this.showAutoGiftError();

    // Pre-populate the gift recipient field for manual correction
    this.giftRecipientControl.setValue(this.autoGiftRecipientId);

    // Open the recipient modal for manual correction
    this.recipientModalOpen = true;
  }

  async showAutoGiftError() {
    let errorMessage = 'فشل في التحقق من صحة المستلم المحدد';

    if (this.giftRecipientError) {
      errorMessage = this.giftRecipientError;
    } else if (this.giftRecipientMembershipError) {
      errorMessage = this.giftRecipientMembershipError;
    } else if (!this.autoGiftRecipientId || !/^\d{1,5}$/.test(this.autoGiftRecipientId)) {
      errorMessage = 'الرقم التعريفي المحدد غير صحيح';
    }

    const toast = await this.toastCtrl.create({
      message: `خطأ في الإهداء التلقائي: ${errorMessage}`,
      duration: 5000,
      color: 'danger',
      position: 'top',
      buttons: [
        {
          text: 'إغلاق',
          role: 'cancel'
        }
      ]
    });

    await toast.present();
  }

  getUserData() {
    this.userDataLoaded = false
    this.updateLoadingState()

    this.user$ = this.apiService.me().pipe(
      tap(() => {
        this.userDataLoaded = true
        this.updateLoadingState()
      }),
      catchError(({error}) => {
        this.userDataLoaded = true
        this.updateLoadingState()
        return of({
          membership: undefined, family_user_id: undefined, full_name: undefined, profile_photo_url: undefined
        })
      })
    )

    // Subscribe to trigger the loading state updates
    this.user$.subscribe()
  }

  getPackages() {
    this.packagesDataLoaded = false
    this.updateLoadingState()

    this.packages$ = this.apiService.getPackages().pipe(
      map(data => data.data),
      tap(() => {
        this.packagesDataLoaded = true
        this.updateLoadingState()
      }),
      catchError(error => {
        this.packagesDataLoaded = true
        this.updateLoadingState()
        return of([])
      })
    )

    // Subscribe to trigger the loading state updates
    this.packages$.subscribe()
  }

  private updateLoadingState() {
    this.isDataLoading = !this.userDataLoaded || !this.packagesDataLoaded
  }

  getUpgradablePackages(packages: Package[], upgradable_packages_id: number[]) {
    return packages.filter(p => upgradable_packages_id.indexOf(p.id) >= 0)
  }

  // Gift wizard methods

  // Step 1: Start recipient validation
  startGiftFlow(giftType: 'new_membership' | 'upgrade') {
    this.giftType = giftType
    this.resetGiftWizard() // Full reset when starting new gift flow
    this.recipientModalOpen = true
  }

  // Step 1: Close recipient modal (preserve data for potential return)
  closeRecipientModal() {
    this.recipientModalOpen = false
    // Don't reset recipient data - user might return to this step
  }

  // Complete cancellation of gift flow
  cancelGiftFlow() {
    this.resetGiftWizard() // Full reset when user cancels entire flow
    this.resetAutoGiftState() // Reset auto-gift state
  }

  resetAutoGiftState() {
    this.autoGiftMode = false
    this.autoGiftRecipientId = ''
    this.autoGiftRecipientName = ''
    this.autoGiftProcessing = false
    this.autoGiftStep = 0
    this.autoGiftCancelled = false
  }

  getAutoGiftLoadingMessage(): string {
    switch (this.autoGiftStep) {
      case 1:
        return 'جاري التحقق من صحة المستلم...';
      case 2:
        return 'جاري تحميل العضويات المتاحة...';
      case 3:
        return 'جاري فتح نافذة اختيار العضوية...';
      default:
        return 'جاري إعداد الهدية...';
    }
  }

  async cancelAutoGiftFlow() {
    console.log('Auto-gift flow cancellation requested by user');

    // Show confirmation dialog for better UX
    const alert = await this.alertCtrl.create({
      header: 'إلغاء عملية الإهداء',
      message: 'هل أنت متأكد من إلغاء عملية إعداد الهدية؟',
      buttons: [
        {
          text: 'لا، متابعة',
          role: 'cancel',
          cssClass: 'secondary'
        },
        {
          text: 'نعم، إلغاء',
          cssClass: 'danger',
          handler: () => {
            this.performCancellation();
          }
        }
      ]
    });

    await alert.present();
  }

  private async performCancellation() {
    console.log('Auto-gift flow cancelled by user');

    // 1. Set cancellation flag to prevent any pending operations from continuing
    this.autoGiftCancelled = true;

    // 2. Stop any ongoing API calls by resetting the validation state
    this.giftRecipientValidating = false;

    // 3. Reset all auto-gift state
    this.resetAutoGiftState();

    // 4. Clear form controls and error messages
    this.giftRecipientControl.setValue('');
    this.giftRecipientError = '';
    this.giftRecipientMembershipError = '';
    this.giftRecipientUser = null;

    // 5. Close any open modals
    this.recipientModalOpen = false;
    this.packageSelectionModalOpen = false;

    // 6. Reset gift wizard state completely
    this.resetGiftWizard();

    // 7. Provide user feedback
    await this.showCancellationToast();

    // 8. Optional: Remove query parameters from URL to clean up browser history
    this.cleanupUrlParameters();
  }

  private async showCancellationToast() {
    const toast = await this.toastCtrl.create({
      message: 'تم إلغاء عملية الإهداء',
      duration: 2500,
      color: 'medium',
      position: 'bottom',
      icon: 'checkmark-circle',
      buttons: [
        {
          text: 'إغلاق',
          role: 'cancel'
        }
      ]
    });

    await toast.present();
  }

  private cleanupUrlParameters() {
    // Remove auto-gift related query parameters from URL
    const currentUrl = this.router.url.split('?')[0]; // Get base URL without query params
    this.router.navigate([currentUrl], {
      replaceUrl: true,
      queryParams: {}
    }).then(() => {
      console.log('URL parameters cleaned up after auto-gift cancellation');
    }).catch(error => {
      console.error('Error cleaning up URL parameters:', error);
    });
  }

  // Safely open package selection modal with error handling
  private safelyOpenPackageModal() {
    try {
      // Ensure we're in a stable state
      if (this.autoGiftCancelled) {
        console.log('Auto-gift cancelled, not opening modal');
        return;
      }

      // Double-check packages are available
      if (!this.availableGiftPackages || this.availableGiftPackages.length === 0) {
        console.warn('No packages available for modal');
        this.handleAutoGiftValidationFailure();
        return;
      }

      // Reset any selected package to prevent conflicts
      this.selectedPackageForGift = null;

      // Open modal
      this.packageSelectionModalOpen = true;
      this.autoGiftProcessing = false;

      console.log('Package selection modal opened safely', {
        packagesCount: this.availableGiftPackages.length,
        giftType: this.giftType,
        recipientId: this.autoGiftRecipientId
      });

    } catch (error) {
      console.error('Error opening package selection modal:', error);
      this.handleAutoGiftValidationFailure();
    }
  }

  // TrackBy function for better Angular change detection performance
  trackByPackageId(index: number, package$: Package): number {
    return package$?.id || index;
  }

  // TrackBy function for features
  trackByFeature(index: number, feature: string): string {
    return feature || index.toString();
  }

  // Step 1 → Step 2: Proceed to package selection
  proceedToPackageSelection() {
    if (!this.giftRecipientUser) {
      this.giftRecipientError = 'يرجى التحقق من المستلم أولاً'
      return
    }

    console.log('Proceeding to package selection...', {
      giftType: this.giftType,
      recipientUser: this.giftRecipientUser
    })

    this.recipientModalOpen = false

    // Load packages and then open modal
    // Load packages and then open modal
    this.loadAvailablePackages()

    // Small delay to ensure packages are loaded before opening modal
    setTimeout(() => {
      console.log('Available packages loaded:', this.availableGiftPackages.length)
      this.packageSelectionModalOpen = true
    }, 100)
  }

  // Step 2: Close package selection modal (preserve recipient data)
  closePackageSelectionModal() {
    this.packageSelectionModalOpen = false
    this.selectedPackageForGift = null
    this.availableGiftPackages = []
    // Keep giftRecipientUser intact - user might return to this step
  }

  // Step 2: Go back to recipient validation
  backToRecipientValidation() {
    this.packageSelectionModalOpen = false
    this.selectedPackageForGift = null
    this.availableGiftPackages = []
    this.recipientModalOpen = true
    // Preserve giftRecipientUser data
  }

  // Step 2 → Step 3: Proceed to payment
  proceedToPayment() {
    if (!this.selectedPackageForGift) {
      return
    }

    this.packageSelectionModalOpen = false
    this.openPaymentModal(this.selectedPackageForGift.id, 'gift')
  }

  resetGiftRecipient() {
    this.giftRecipientControl.setValue('')
    this.giftRecipientUser = null
    this.giftRecipientError = ''
    this.giftRecipientMembershipError = ''
    this.giftMessageControl.setValue('')
  }

  // Only clear input field and errors, preserve validated user data
  clearGiftRecipientInput() {
    this.giftRecipientControl.setValue('')
    this.giftRecipientError = ''
    this.giftRecipientMembershipError = ''
    // Keep giftRecipientUser intact for wizard flow
  }

  resetGiftWizard() {
    this.resetGiftRecipient() // Full reset when completely exiting gift flow
    this.selectedPackageForGift = null
    this.availableGiftPackages = []
    this.recipientModalOpen = false
    this.packageSelectionModalOpen = false
    this.giftType = null
  }

  // Load available packages based on gift type
  loadAvailablePackages() {
    console.log('Loading available packages...', {giftType: this.giftType})

    if (!this.packages$) {
      console.error('Packages observable is null')
      this.availableGiftPackages = []
      return
    }

    this.packages$.pipe(take(1)).subscribe({
      next: (packages) => {
        console.log('Packages received:', packages?.length || 0)

        if (!packages || packages.length === 0) {
          console.warn('No packages available')
          this.availableGiftPackages = []
          return
        }

        if (this.giftType === 'upgrade') {
          // For upgrades, we need to get upgradeable packages
          // This would require the recipient's current membership info
          // For now, show all packages (this should be enhanced with recipient's membership data)
          this.availableGiftPackages = packages
        } else {
          // For new memberships, show all available packages
          this.availableGiftPackages = packages
        }

        console.log('Available gift packages set:', this.availableGiftPackages.length)
      },
      error: (error) => {
        console.error('Error loading packages:', error)
        this.availableGiftPackages = []
      }
    })
  }

  searchGiftRecipient(): Promise<boolean> {
    return new Promise((resolve) => {
      const value = this.giftRecipientControl.value?.trim()

      // Reset states
      this.giftRecipientUser = null
      this.giftRecipientError = ''
      this.giftRecipientMembershipError = ''

      if (!value) {
        this.giftRecipientError = 'يرجى إدخال الرقم التعريفي'
        resolve(false)
        return
      }

      if (value.length < 3) {
        this.giftRecipientError = 'يجب أن يكون الرقم التعريفي مكون من 3 أرقام على الأقل'
        resolve(false)
        return
      }

      // Validate input format (5 digits max, numeric only)
      if (!/^\d{1,5}$/.test(value)) {
        this.giftRecipientError = 'يجب أن يكون الرقم التعريفي مكون من أرقام فقط (حد أقصى 5 أرقام)'
        resolve(false)
        return
      }

      // Check for self-gifting (prevent users from gifting to themselves)
      // @ts-ignore
      this.user$.pipe(take(1)).subscribe(currentUser => {
        if (currentUser && currentUser.family_user_id === value) {
          this.giftRecipientError = 'لا يمكنك إهداء عضوية لنفسك'
          resolve(false)
          return
        }

        this.performUserSearch(value, resolve)
      })
    })
  }

  private performUserSearch(value: string, resolve?: (success: boolean) => void) {
    this.giftRecipientValidating = true

    // Use secure gift eligibility check
    this.apiService.checkGiftEligibility(value).pipe(
      catchError(error => {
        this.giftRecipientValidating = false
        // Generic error message for security (prevents user enumeration)
        this.giftRecipientError = 'المستخدم غير صالح للإهداء'
        if (resolve) resolve(false)
        return of(null)
      })
    ).subscribe(response => {
      this.giftRecipientValidating = false

      // Check if auto-gift was cancelled while API call was in progress
      if (this.autoGiftCancelled) {
        console.log('User search completed but auto-gift was cancelled, ignoring result');
        if (resolve) resolve(false);
        return;
      }

      if (response && response.success) {
        const eligibility = response.data

        if (eligibility.user_exists && eligibility.can_receive_gift) {
          // User is eligible for gifts - create a minimal user object for display
          // Use provided recipient name from auto-gift flow if available
          const displayName = this.autoGiftMode && this.autoGiftRecipientName
            ? this.autoGiftRecipientName
            : `مستخدم ${value}`; // Generic name for privacy

          this.giftRecipientUser = {
            family_user_id: value,
            full_name: displayName,
            profile_photo_url: eligibility.profile_photo_url,
            gender: eligibility.gender ?? 'male' // Default for icon fallback
          } as any
          this.giftRecipientError = ''
          this.giftRecipientMembershipError = ''
          if (resolve) resolve(true)
        } else {
          // User exists but cannot receive gifts (has active membership)
          this.giftRecipientMembershipError = 'المستخدم غير صالح للإهداء'
          this.giftRecipientUser = null
          if (resolve) resolve(false)
        }
      } else {
        if (resolve) resolve(false)
      }
    })
  }

  onRecipientInputKeyPress(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      event.preventDefault()
      this.searchGiftRecipient().then(success => {
        console.log('Manual recipient search result:', success)
      })
    }
  }

  // Helper method to check if input is valid for search
  isInputValidForSearch(): boolean {
    const value = this.giftRecipientControl.value?.trim()
    return !!(value && value.length >= 3 && /^\d{1,5}$/.test(value))
  }

  // Helper method to get input validation message
  getInputValidationHint(): string {
    const value = this.giftRecipientControl.value?.trim()
    if (!value) return ''
    if (value.length < 3) return 'أدخل 3 أرقام على الأقل'
    if (!/^\d{1,5}$/.test(value)) return 'أرقام فقط (حد أقصى 5 أرقام)'
    return ''
  }


  // Step 1: Confirm recipient and proceed to next step
  async confirmRecipient() {
    console.log('Confirming recipient...', {
      hasRecipientUser: !!this.giftRecipientUser,
      hasSelectedPackage: !!this.selectedPackageForGift,
      giftType: this.giftType
    })

    if (!this.giftRecipientUser) {
      this.giftRecipientError = 'يرجى اختيار المستلم'
      return
    }

    if (this.giftRecipientMembershipError) {
      // Show error toast for membership conflict
      const toast = await this.toastCtrl.create({
        message: this.giftRecipientMembershipError,
        duration: 3000,
        color: 'danger',
        position: 'top'
      })
      await toast.present()
      return
    }

    // If we already have a selected package (from specific package gift buttons),
    // proceed directly to payment
    if (this.selectedPackageForGift) {
      console.log('Package already selected, going to payment:', this.selectedPackageForGift.id)
      this.recipientModalOpen = false
      this.openPaymentModal(this.selectedPackageForGift.id, 'gift')
    } else {
      // Otherwise, proceed to package selection step
      console.log('No package selected, going to package selection')
      this.proceedToPackageSelection()
    }
  }

  // Step 2: Confirm package selection and proceed to payment
  confirmPackageSelection() {
    if (!this.selectedPackageForGift) {
      return
    }
    this.proceedToPayment()
  }

  // Helper method to check if a package is upgradeable (for current user context)
  isPackageUpgradeable(packageId: number): boolean {
    // This would need to be implemented based on current user's membership
    // For now, return true to show all packages in upgrade mode
    return true
  }

  onSubscribeClick(packageId: number) {
    this.openPaymentModal(packageId, 'subscribe')
  }

  onUpgradeClick(packageId: number) {
    this.openPaymentModal(packageId, 'upgrade')
  }

  // Payment Modal Methods
  openPaymentModal(packageId: number, mode: 'subscribe' | 'upgrade' | 'gift') {
    // Find the selected package
    this.packages$?.pipe(take(1)).subscribe(packages => {
      const selectedPackage = packages?.find(pkg => pkg.id === packageId)
      if (selectedPackage) {
        this.selectedPackageForPayment = selectedPackage
        this.paymentMode = mode
        this.paymentModalOpen = true
      }
    })
  }

  closePaymentModal() {
    this.paymentModalOpen = false
    this.selectedPackageForPayment = null
    this.paymentLoading = false
    // Don't reset giftType or recipient data - user might return to previous steps
  }

  // Complete payment modal closure (when payment succeeds or user completely exits)
  completePaymentFlow() {
    this.paymentModalOpen = false
    this.selectedPackageForPayment = null
    this.paymentLoading = false
    this.resetGiftWizard() // Full reset after successful payment or complete exit
  }

  async processPayment() {
    if (!this.selectedPackageForPayment) {
      console.error('No package selected for payment')
      return
    }

    this.paymentLoading = true

    try {
      // Determine gift recipient and message for API call
      const giftRecipient = this.paymentMode === 'gift' && this.giftRecipientUser?.family_user_id
        ? this.giftRecipientUser.family_user_id
        : undefined

      const giftMessage = this.paymentMode === 'gift' && this.giftMessageControl.value?.trim()
        ? this.giftMessageControl.value.trim()
        : undefined

      // Call appropriate API method based on payment mode and gift type
      let apiCall: Observable<any>

      if (this.paymentMode === 'gift') {
        if (!giftRecipient) {
          console.error('No gift recipient selected')
          this.paymentLoading = false
          return
        }

        // Use giftType to determine API call
        if (this.giftType === 'upgrade') {
          apiCall = this.apiService.getPackageUpgradeSessionsId(this.selectedPackageForPayment.id, giftRecipient, giftMessage)
        } else /*if (this.giftType === 'new_membership')*/ {
          apiCall = this.apiService.getPackagePaySessionsId(this.selectedPackageForPayment.id, giftRecipient, giftMessage)
        } /*else {
          console.error('Invalid gift type:', this.giftType)
          this.paymentLoading = false
          return
        }*/
      } else {
        // Regular payment (non-gift)
        if (this.paymentMode === 'upgrade') {
          apiCall = this.apiService.getPackageUpgradeSessionsId(this.selectedPackageForPayment.id)
        } else if (this.paymentMode === 'subscribe') {
          apiCall = this.apiService.getPackagePaySessionsId(this.selectedPackageForPayment.id)
        } else {
          console.error('Invalid payment mode:', this.paymentMode)
          this.paymentLoading = false
          return
        }
      }

      // Execute API call and handle response (matching existing implementation)
      apiCall.pipe(
        catchError((err) => {
          this.paymentLoading = false
          console.error('Payment API error:', err)
          return throwError(err)
        })
      ).subscribe(async (res: any) => {
        if (this.isCapacitorApp) {
          // Capacitor app flow - open browser with payment URL
          const uuid = res['uuid']
          if (uuid) {
            // Import Browser dynamically to avoid SSR issues
            const {Browser} = await import('@capacitor/browser')
            await Browser.open({
              url: `${environment.appURL}/pay?uuid=${uuid}`,
              presentationStyle: 'popover',
            });
            this.paymentLoading = false
            this.closePaymentModal()
            this.channelName = `ProductTransaction.${uuid}`
            if (this.notificationService.Echo)
              this.notificationService.Echo.channel(this.channelName).listen('PaymentTransactionStatusUpdated', (data: any) => {
                let navigationExtras: NavigationExtras = {
                  queryParams: {
                    //packageId: this.packageId,
                    transactionUUID: this.transactionUUID,
                    //...(this.isGiftMode ? { gift: 'true', recipient: this.giftRecipientId, recipientName: this.giftRecipientName } : {})
                  }
                }
                try {
                  if (data['status'] === 'SUCCESS')
                    this.navCtrl.navigateForward('/members/checkout/success', navigationExtras).then()
                  else
                    this.navCtrl.navigateForward('/members/checkout/failure', navigationExtras).then()
                } catch {
                  this.navCtrl.navigateForward('/members/checkout/failure', navigationExtras).then()
                }
                try {
                  Browser.close()
                } catch {
                }
                if (this.channelName)
                  this.notificationService.Echo.leave(this.channelName)
              });
          } else {
            console.error('No UUID received from payment API')
            this.paymentLoading = false
          }
        } else {
          // Web browser flow - store session data for MyFatoorah integration
          this.transactionUUID = res['uuid']
          this.myFatoorahAppleSessionId = res['apple_session_id']
          this.myFatoorahCreditCardSessionId = res['session_id']
          const amount: number = res['amount']

          this.modalCtrl.dismiss()
          this.modal.present().then(() => {
            this.initiateApplePayPayment(amount);
            this.initiateCreditCardPayment(amount);
            this.showPaymentsSpinner = false;
            this.paymentLoading = false
          })
        }
      })

    } catch (error) {
      console.error('Payment processing error:', error)
      this.paymentLoading = false
    }
  }

  onGiftSubscribeClick(packageId: number) {
    // For specific package gifts, we can skip package selection step
    this.giftType = 'new_membership'
    this.resetGiftWizard() // Full reset when starting new specific package gift
    this.packages$?.pipe(take(1)).subscribe(packages => {
      this.selectedPackageForGift = packages?.find(p => p.id === packageId) || null
    })
    this.recipientModalOpen = true
  }

  onGiftUpgradeClick(packageId: number) {
    // For specific package gifts, we can skip package selection step
    this.giftType = 'upgrade'
    this.resetGiftWizard() // Full reset when starting new specific package gift
    this.packages$?.pipe(take(1)).subscribe(packages => {
      this.selectedPackageForGift = packages?.find(p => p.id === packageId) || null
    })
    this.recipientModalOpen = true
  }

  // Methods for general gift flows (package selection required)
  openGiftModalForNewMembershipSimple() {
    this.startGiftFlow('new_membership')
  }

  openGiftModalForUpgradeSimple() {
    this.startGiftFlow('upgrade')
  }

  // Debug method to test package selection modal directly
  testPackageSelectionModal() {
    console.log('Testing package selection modal...')
    this.giftType = 'new_membership'
    this.giftRecipientUser = {
      family_user_id: '12345',
      full_name: 'Test User',
      profile_photo_url: null,
      gender: 'male'
    } as any
    this.loadAvailablePackages()
    setTimeout(() => {
      this.packageSelectionModalOpen = true
      console.log('Package selection modal should be open now')
    }, 200)
  }

  private initiateApplePayPayment(amount: number) {
    this.showPaymentsSpinner = true;

    const config = {
      sessionId: this.myFatoorahAppleSessionId, // Here you add the "SessionId" you receive from InitiateSession Endpoint.
      countryCode: environment.myfatoorah.countryCode,
      currencyCode: environment.myfatoorah.currencyCode,
      amount: amount,
      cardViewId: "card-applepay",
      Language: "ar",
      callback: ((response: any) => {
        this.showPaymentsSpinner = true;
        this.apiService.getInvoiceFromPaymentSessionId(this.transactionUUID, response.sessionId).subscribe((res: any) => {
          this.modal.dismiss().then();
          let navigationExtras: NavigationExtras = {
            queryParams: {
              //packageId: this.packageId,
              transactionUUID: this.transactionUUID,
              //...(this.isGiftMode ? { gift: 'true', recipient: this.giftRecipientId, recipientName: this.giftRecipientName } : {})
            }
          };
          if (res['success'] === true)
            this.navCtrl.navigateForward('/members/checkout/success', navigationExtras).then()
          else
            this.navCtrl.navigateForward('/members/checkout/failure', navigationExtras).then()
          this.showPaymentsSpinner = false;
        });
      }),
      sessionStarted: ((sessionStarted: any) => {
        this.showPaymentsSpinner = true;

      }),
      sessionCanceled: ((sessionCanceled: any) => {
        // this.showPaymentsSpinner = false;
      }),
    };
    myFatoorahAP.init(config);
  }

  private initiateCreditCardPayment(amount: number) {
    this.showPaymentsSpinner = true;
    const config = {
      sessionId: this.myFatoorahCreditCardSessionId, // Here you add the "SessionId" you receive from InitiateSession Endpoint.
      countryCode: environment.myfatoorah.countryCode,
      currencyCode: environment.myfatoorah.currencyCode,
      amount: amount,
      cardViewId: "credit-card",
      Language: "ar",
      callback: ((response: any) => {
      }),
      sessionStarted: ((sessionStarted: any) => {
        this.showPaymentsSpinner = true;
      }),
      sessionCanceled: ((sessionCanceled: any) => {
        // this.showPaymentsSpinner = false;
      }),
    };

    myFatoorah.init(config);
  }

  onWillDismiss(event: Event) {
    const ev = event as CustomEvent<OverlayEventDetail<string>>;
    if (ev.detail.role === 'confirm') {

    }
  }

  cancel() {
    this.transactionUUID = '';
    this.myFatoorahAppleSessionId = '';
    this.myFatoorahCreditCardSessionId = '';
    this.showPaymentsSpinner = false;
    this.modal.dismiss(null, 'cancel');
  }

  payWithCreditCard() {
    this.creditCardProcessing = true;

    (this.window as any).myFatoorah.submit().then(
      (response: { sessionId: string, cardBrand: string }) => {
        // In case of success
        // Here you need to pass session id to you backend here
        const sessionId = response.sessionId;
        const cardBrand = response.cardBrand; //cardBrand will be one of the following values: Master, Visa, Mada, Amex
        this.modal.dismiss().then();
        let navigationExtras: NavigationExtras = {
          queryParams: {
            //productId: this.packageId,
            transactionUUID: this.transactionUUID,
            //...(this.isGiftMode ? { gift: 'true', recipient: this.giftRecipientId, recipientName: this.giftRecipientName } : {})
          }
        };
        this.apiService.getInvoiceFromPaymentSessionId(this.transactionUUID, sessionId).subscribe((res: any) => {
          if (res['success'] === true)
            window.location = res['invoice_url'];
          else
            this.navCtrl.navigateForward('/members/checkout/failure', navigationExtras).then()
        });
        this.creditCardProcessing = false;
      }
    ).catch(
      (error: any) => {
        // In case of errors
        console.error(error);
        this.creditCardProcessing = false;
      }
    );
  }
}
