#container {
  text-align: center;

  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}

#container strong {
  font-size: 20px;
  line-height: 26px;
}

#container p {
  font-size: 16px;
  line-height: 22px;

  color: #8c8c8c;

  margin: 0;
}

#container a {
  text-decoration: none;
}

.swiper {
  width: 100%;
  max-width: 600px;
  height: 100%;
  min-height: 480px;
  margin-block-start: 16px;
  margin-block-end: 16px;
}

ion-icon {
  font-size: 40px;
}

.buttons-wrapper {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-content: center;
  justify-content: center;
  align-items: center;
  gap: 15px;
  max-width: 550px;
  margin: auto;
  margin-block-end: 32px;

  .page-button {
    cursor: pointer;
    background: var(--ion-color-light, #fff);
    width: 175px;
    height: 113px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
}

::ng-deep {
  .swiper-pagination {
    bottom: 0 !important;
  }

  h1, h2, h3, h4, h5, h6 {
    line-height: 1.35 !important;
  }
}

cdk-virtual-scroll-viewport {
  height: 100%;
  width: 100%;
}

.buy-button {
  margin-top: 0;
  margin-bottom: 0;
  position: fixed;
  /*bottom: 25px;*/
  left: 0;
  right: 0;
  top: 80%;
}

swiper-container {
  --swiper-pagination-bullet-inactive-color: #DBA852;
}

swiper-slide {
  border-radius: 18px;
  font-size: 22px;
  font-weight: bold;
  //width: calc(100% - 50px) !important;
  //margin-left: 5px;
  height: 480px;
  border: 1px solid rgba(219, 168, 82, 0.6);
}

/*swiper-slide:nth-child(1n) {
  border: 1px solid rgb(206, 17, 17);
}

swiper-slide:nth-child(2n) {
  border: 1px solid rgb(0, 140, 255);
}

swiper-slide:nth-child(3n) {
  border: 1px solid rgb(10, 184, 111);
}

swiper-slide:nth-child(4n) {
  border: 1px solid rgb(211, 122, 7);
}*/

// Gift mode styles
.recipient-profile {
  --background: var(--ion-color-light);
  --border-radius: 12px;
  border: 1px solid var(--ion-color-success);
  border-radius: 12px;
  margin: 8px 0;

  ion-avatar {
    --size: 40px;
  }

  ion-label h3 {
    color: var(--ion-color-success);
    font-weight: 600;
  }

  ion-label p {
    color: var(--ion-color-medium);
    font-size: 0.9em;
  }
}

// Separator styling (matching friends page)
.separator {
  text-align: center;
  margin: 20px 0;
  position: relative;
  color: var(--ion-color-medium);
  font-weight: 500;

  &:before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--ion-color-light-shade);
    z-index: 1;
  }

  span {
    position: relative;
    background: var(--ion-background-color);
    padding: 0 15px;
    z-index: 2;
  }
}

// Loading and animation styles
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.packages-content {
  opacity: 1;
  transform: translateY(0);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Payment modal styles
.payment-details {
  .gift-info {
    background: var(--ion-color-light);
    border-radius: 8px;
    padding: 8px;

    // Gift message textarea styling
    .gift-message-item {
      --background: var(--ion-color-light);
      --border-color: var(--ion-color-medium);
      --border-style: solid;
      --border-width: 1px;
      --border-radius: 8px;
      --padding-start: 16px;
      --padding-end: 16px;
      --padding-top: 12px;
      --padding-bottom: 12px;
      margin-top: 12px;
      border: 1px solid var(--ion-color-medium);
      border-radius: 8px;

      ion-textarea {
        --background: var(--ion-color-light);
        --color: var(--ion-text-color);
        --placeholder-color: var(--ion-color-medium);
        --placeholder-opacity: 0.7;
        font-size: 14px;
        line-height: 1.4;
        width: 100%;

        // Ensure native textarea is properly styled
        .native-textarea {
          background: var(--ion-color-light) !important;
          color: var(--ion-text-color) !important;
          border: none !important;
          outline: none !important;
          resize: vertical;
          min-height: 80px;

          &:focus {
            box-shadow: none !important;
          }
        }
      }

      // Focus state for the entire item
      &.item-has-focus {
        --border-color: var(--ion-color-primary);
        border-color: var(--ion-color-primary);
        box-shadow: 0 0 0 2px rgba(var(--ion-color-primary-rgb), 0.2);
      }
    }

    // Character counter styling
    .ion-padding-start {
      margin-top: 4px;

      ion-text {
        font-size: 12px;
        color: var(--ion-color-medium);
      }
    }
  }

  .payment-actions {
    ion-button {
      --border-radius: 12px;
      height: 48px;
      font-weight: 600;
    }
  }
}

// Enhanced button styles for gift functionality
ion-button {
  &[fill="outline"] {
    --border-width: 2px;
    --border-style: solid;
  }
}

// Gift wizard styles
.recipient-summary {
  ion-card {
    --background: var(--ion-color-light);
    border: 1px solid var(--ion-color-primary);
  }
}

.package-selection {
  .package-option {
    .clickable-card {
      transition: all 0.3s ease;
      cursor: pointer;
      position: relative;

      &.selected {
        --background: var(--ion-color-primary-tint);
        border: 2px solid var(--ion-color-primary);
        box-shadow: 0 4px 12px rgba(var(--ion-color-primary-rgb), 0.3);
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(0,0,0,0.15);
      }

      &:active {
        transform: translateY(0);
        transition: transform 0.1s ease;
      }
    }

    .package-item {
      pointer-events: none; // Prevent ion-item from interfering with card clicks

      .hidden-radio {
        opacity: 0;
        pointer-events: none;
        position: absolute;
        left: -9999px;
      }
    }
  }

  .features-preview {
    margin-top: 8px;

    .feature-item {
      margin: 4px 0;
      font-size: 14px;

      ion-icon {
        margin-right: 8px;
        font-size: 16px;
      }
    }

    .more-features {
      color: var(--ion-color-medium);
      font-style: italic;
      font-size: 12px;
    }
  }
}

// Auto-Gift Loading Overlay
.auto-gift-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;

  .overlay-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
  }

  .loading-content {
    position: relative;
    z-index: 1;
    max-width: 90%;
    width: 400px;
    animation: slideInUp 0.4s ease-out;

    .loading-card {
      background: var(--ion-color-light);
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      overflow: hidden;

      .loading-header {
        background: var(--ion-color-primary);
        color: white;
        padding: 20px;
        text-align: center;

        .gift-icon {
          font-size: 32px;
          margin-bottom: 8px;
          display: block;
        }

        h2 {
          margin: 0;
          font-size: 1.2rem;
          font-weight: 600;
        }
      }

      .loading-body {
        padding: 24px;
        text-align: center;

        .main-spinner {
          width: 48px;
          height: 48px;
          margin-bottom: 20px;
          --color: var(--ion-color-primary);
        }

        .recipient-info {
          margin-bottom: 20px;

          .recipient-label {
            font-size: 0.9rem;
            color: var(--ion-color-medium);
            margin-bottom: 4px;
          }

          .recipient-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--ion-color-dark);
            margin: 0;

            .recipient-id {
              font-weight: 400;
              color: var(--ion-color-medium);
              font-size: 0.9rem;
            }
          }
        }

        .loading-message {
          .status-message {
            font-size: 1rem;
            color: var(--ion-color-dark);
            margin-bottom: 16px;
            font-weight: 500;
          }

          .progress-dots {
            display: flex;
            justify-content: center;
            gap: 8px;

            .dot {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              background: var(--ion-color-light-shade);
              transition: all 0.3s ease;

              &.active {
                background: var(--ion-color-primary);
                transform: scale(1.2);
              }
            }
          }
        }
      }

      .loading-footer {
        padding: 16px 24px;
        border-top: 1px solid var(--ion-color-light-shade);
        text-align: center;

        .cancel-button {
          --color: var(--ion-color-medium);
          font-size: 0.9rem;
        }
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// Responsive design for mobile
@media (max-width: 768px) {
  .auto-gift-loading-overlay {
    .loading-content {
      width: 95%;
      max-width: 350px;

      .loading-card {
        .loading-header {
          padding: 16px;

          .gift-icon {
            font-size: 28px;
          }

          h2 {
            font-size: 1.1rem;
          }
        }

        .loading-body {
          padding: 20px;

          .main-spinner {
            width: 40px;
            height: 40px;
          }
        }
      }
    }
  }
}

// Dark mode specific styles
.ion-palette-dark {
  .payment-details {
    .gift-info {
      background: var(--ion-color-step-100);
      border: 1px solid var(--ion-color-step-200);

      .gift-message-item {
        --background: var(--ion-color-step-50);
        --border-color: var(--ion-color-step-300);
        --color: var(--ion-text-color);
        background: var(--ion-color-step-50) !important;
        border: 1px solid var(--ion-color-step-300) !important;

        ion-textarea {
          --background: var(--ion-color-step-50);
          --color: var(--ion-text-color);
          --placeholder-color: var(--ion-color-step-600);
          --placeholder-opacity: 1;

          // Ensure the textarea is focusable and visible
          textarea {
            background: var(--ion-color-step-50) !important;
            color: var(--ion-text-color) !important;
            border: 1px solid var(--ion-color-step-300) !important;
            border-radius: 6px !important;
            padding: 8px !important;

            &:focus {
              border-color: var(--ion-color-primary) !important;
              outline: none !important;
              box-shadow: 0 0 0 2px rgba(var(--ion-color-primary-rgb), 0.2) !important;
            }

            &::placeholder {
              color: var(--ion-color-step-600) !important;
              opacity: 1 !important;
            }
          }
        }
      }

      // Character counter in dark mode
      .ion-padding-start {
        ion-text {
          color: var(--ion-color-step-600) !important;
        }
      }
    }
  }

  // Auto-gift loading overlay dark mode
  .auto-gift-loading-overlay {
    .loading-content {
      .loading-card {
        background: var(--ion-color-step-100);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);

        .loading-body {
          .recipient-name {
            color: var(--ion-text-color);

            .recipient-id {
              color: var(--ion-color-step-600);
            }
          }

          .loading-message {
            .status-message {
              color: var(--ion-text-color);
            }
          }
        }

        .loading-footer {
          border-top: 1px solid var(--ion-color-step-200);
        }
      }
    }
  }
}

// Additional global styles for gift message textarea
:host {
  .gift-message-item {
    ion-textarea {
      // Force visibility and interactivity
      opacity: 1 !important;
      visibility: visible !important;
      pointer-events: auto !important;

      .textarea-wrapper {
        .native-textarea {
          opacity: 1 !important;
          visibility: visible !important;
          pointer-events: auto !important;
          touch-action: manipulation !important;
          user-select: text !important;
          -webkit-user-select: text !important;
          -moz-user-select: text !important;
          -ms-user-select: text !important;
        }
      }
    }
  }
}

// Ensure dark mode compatibility across all platforms
@media (prefers-color-scheme: dark) {
  :host {
    .gift-message-item {
      background: var(--ion-color-step-50) !important;
      border: 1px solid var(--ion-color-step-300) !important;

      ion-textarea {
        --background: var(--ion-color-step-50) !important;
        --color: var(--ion-text-color) !important;

        .textarea-wrapper {
          background: var(--ion-color-step-50) !important;

          .native-textarea {
            background: var(--ion-color-step-50) !important;
            color: var(--ion-text-color) !important;
            border: none !important;

            &::placeholder {
              color: var(--ion-color-step-600) !important;
              opacity: 1 !important;
            }
          }
        }

        .label-text-wrapper {
          .label-text {
            color: var(--ion-text-color) !important;
          }
        }
      }
    }
  }
}
