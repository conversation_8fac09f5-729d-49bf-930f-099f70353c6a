<app-header [hideProfileIcon]="false" [showBackButton]="true"></app-header>
<ion-content>
  <ion-grid [fixed]="true" *ngIf="(package$ | async)  as _package$; else spinner">
    <app-card [title]="_package$.title"
              [showExpandButton]="false"
              [scrollable]="false"
              style="height: 100%;min-height: 300px;">

      <!-- Gift Mode Indicator -->
      <ion-item *ngIf="isGiftMode" lines="none" class="ion-margin-bottom">
        <ion-icon name="gift" slot="start" color="primary"></ion-icon>
        <ion-label>
          <h3>وضع الإهداء</h3>
          <p>ستُهدى هذه العضوية إلى: {{ giftRecipientName || giftRecipientId }}</p>
        </ion-label>
      </ion-item>

      <ion-label>
        <h2 class="">
          السعر السنوي:
          {{ _package$?.product?.price | number }}
          ر.س
        </h2>
      </ion-label>
      <ul style="list-style-type: circle;">
        <li *ngFor="let feature$ of _package$.features; let i = index" style="margin-top: 0;">
          <h5 style="margin: 0;">{{ feature$ }}</h5>
        </li>
      </ul>
      <ion-button color="primary" expand="block" mode="ios"
                  style="color: white" class="ion-margin-top"
                  (click)="subscribe()"
                  [disabled]="formLoading">
        <ion-spinner *ngIf="formLoading"></ion-spinner>
        <span *ngIf="!formLoading">
            {{ isGiftMode ? 'ادفع كهدية' : 'ادفع' }}
          </span>
      </ion-button>
    </app-card>
  </ion-grid>
  <ion-modal *ifIsBrowser
             #payModal
             (willDismiss)="onWillDismiss($event)"
             mode="ios"
             [initialBreakpoint]="0.75"
             [breakpoints]="[0.75]"
             (ionModalDidDismiss)="cancel()">
    <ng-template>
      <ion-header>
        <ion-toolbar>
          <ion-buttons slot="start">
            <ion-button (click)="cancel()">ألغ</ion-button>
          </ion-buttons>
          <ion-title color="primary">الدفع</ion-title>
        </ion-toolbar>
      </ion-header>
      <ion-content class="ion-padding">
        <ion-spinner *ngIf="showPaymentsSpinner"
                     style="margin: auto; width: 100%;height: 40px;"></ion-spinner>
        <div id="card-applepay" class="ion-margin-top" [hidden]="showPaymentsSpinner"></div>
        <div class="altwijry-delimiter" *ngIf="!creditCardProcessing"></div>
        <div id="credit-card" [hidden]="showPaymentsSpinner"></div>

        <ion-button *ngIf="myFatoorahCreditCardSessionId"
                    color="secondary"
                    expand="block"
                    class="ion-margin-top"
                    [hidden]="showPaymentsSpinner"
                    (click)="payWithCreditCard()">
          <ion-spinner *ngIf="creditCardProcessing"></ion-spinner>
          <span *ngIf="!creditCardProcessing" [hidden]="showPaymentsSpinner">ادفع</span>
        </ion-button>
      </ion-content>
    </ng-template>
  </ion-modal>
</ion-content>

<ng-template #spinner>
  <div class="ion-flex ion-justify-content-center">
    <ion-spinner size="large" name="crescent" style="width: 70px; height: 70px"></ion-spinner>
  </div>
</ng-template>
