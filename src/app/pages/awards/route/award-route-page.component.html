<app-header [hideProfileIcon]="false" [showBackButton]="true"
            [backTo]="categoryId ? '/user-awards/' + categoryId : '/user-awards'"></app-header>
<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large" class="" style="">
        {{ (route$ | async)!.title }}
      </ion-title>
    </ion-toolbar>
  </ion-header>
  <ng-container *ngIf="dataLoaded; else loading">
    <ng-container *ngIf="route$ | async as route;">
      <ng-container *ngIf="!termsAccepted && (route.terms?.blocks || []).length > 0;">
        <ion-grid [fixed]="true" class="ion-align-items-center">
          <ion-card mode="ios" style="box-shadow: unset; border:solid 1px var(--ion-color-secondary, #113665); max-width: 370px; margin: auto">
            <ion-card-content>
              <ion-row class="ion-justify-content-center ion-align-items-center"
                       *ngFor="let oneBlock of route?.terms?.blocks; let i = index">
                <ion-col class="ion-justify-content-center ion-align-items-center">
                  <pre *ngIf="oneBlock.type === 'code'"> {{ oneBlock.data.code }}</pre>
                  <ion-item-divider *ngIf="oneBlock.type === 'delimiter'"></ion-item-divider>
                  <div *ngIf="oneBlock.type !== 'code'" style="display: inline-block"
                       [innerHTML]="oneBlock | renderBlock"></div>
                </ion-col>
              </ion-row>

              <ion-col>
                <ion-button color="secondary" expand="block" mode="ios" style="color: white;min-width: 100%;"
                            (click)="confirm()">
                  الموافقة على الشروط والأحكام
                </ion-button>
              </ion-col>
            </ion-card-content>
          </ion-card>
        </ion-grid>
      </ng-container>
      <!--<ion-header collapse="condense">
        <ion-toolbar>
          <ion-title size="large" class="ion-margin-vertical" style="">
            جائزة التميز لعام
            <ng-container *ngIf="(awardCategoriesService.$year | async)! > 0;">
              {{ (awardCategoriesService.$year | async) }}
            </ng-container>
            <ng-container *ngIf="category$ | async as category;">
              <br>
              <h4>
                المسار:
                {{ category.title }}
              </h4>
            </ng-container>
          </ion-title>
        </ion-toolbar>
      </ion-header>-->
      <ng-container *ngIf="termsAccepted || (route.terms?.blocks || []).length === 0;">
        <ion-list class="ion-margin-bottom" [inset]="true" [formGroup]="awardForm">
          <ng-container *ngFor="let field of fields">
            @switch (field.type) {
              @case ('RICHTEXT') {
                <ion-item>
                  <ion-textarea type="text" formControlName="item_{{ field.id }}"
                                [errorText]="getErrorMessage('item_' + field.id)"
                                label="{{ field.title }}"></ion-textarea>
                </ion-item>
              }
              @case ('TEXT') {
                <ion-item>
                  <ion-input type="text" formControlName="item_{{ field.id }}"
                             [errorText]="getErrorMessage('item_' + field.id)"
                             placeholder="{{ field.title }}"></ion-input>
                  <br>
                </ion-item>
              }
              @case ('DATE') {
                <ion-item>
                  <ion-input [label]="field.title" labelPlacement="floating" formControlName="item_{{ field.id }}"
                             [errorText]="getErrorMessage('item_' + field.id)"
                             value="{{ awardForm.get('item_' + field.id) ? (awardForm.get('item_' + field.id)!.value | date: 'dd/MM/yyyy') : null }}"
                             id="datePicker_{{ field.id }}" readonly="true"></ion-input>
                  <ion-popover trigger="datePicker_{{ field.id }}" size="cover">
                    <ng-template>
                      <ion-datetime presentation="date" [max]="nowTime" [preferWheel]="true"
                                    formControlName="item_{{ field.id }}"></ion-datetime>
                    </ng-template>
                  </ion-popover>
                </ion-item>
              }
              @case ('SELECT') {
                <ion-item>
                  <ion-select placeholder="{{ field.title }}"
                              formControlName="item_{{ field.id }}">
                    @if (field.data.options && field.data.options.length > 0 && isObject(field.data.options[0])) {
                      <ion-select-option *ngFor="let option of field.data.options" [value]="option.value">
                        {{ option.text }}
                      </ion-select-option>
                    } @else {
                      <ion-select-option *ngFor="let option of field.data.options" [value]="option">
                        {{ option }}
                      </ion-select-option>
                    }
                  </ion-select>
                  <ion-note *ngIf="getErrorMessage('item_' + field.id).length > 0" color="danger">
                    {{ getErrorMessage('item_' + field.id) }}
                  </ion-note>
                </ion-item>
              }
              @case ('FILE') {
                <ion-item>
                  <ion-label>{{ field.title }}</ion-label>
                  <ion-button color="primary" fill="outline" slot="end" (click)="fileInput.click()">
                    <ion-icon name="attach"></ion-icon>
                  </ion-button>
                  <input type="file" hidden #fileInput (change)="fileChange($event, field.id)">
                </ion-item>
                <ion-item style="width: 100%;display: block;"
                          *ngIf="getErrorMessage('item_' + field.id).length > 0 || awardForm.get('item_' + field.id)?.value">
                  <ion-note *ngIf="getErrorMessage('item_' + field.id).length > 0" color="danger">
                    {{ getErrorMessage('item_' + field.id) }}
                  </ion-note>
                  <ion-label *ngIf="awardForm.get('item_' + field.id)?.value">
                    {{ awardForm.get('item_' + field.id)?.value.name }}
                  </ion-label>
                </ion-item>
              }
              @case ('PERCENTAGE') {
                <ion-item>
                  <ion-input [max]="100" [min]="field.data.min || 1" [step]="0.5" type="number"
                             [errorText]="getErrorMessage('item_' + field.id)"
                             type="number" formControlName="item_{{ field.id }}" label="{{ field.title }}"></ion-input>
                </ion-item>
              }
              @case ('GPA') {
                <ng-container formGroupName="item_{{ field.id }}">
                  <ion-item>
                    <ion-select placeholder="نوع المعدل" formControlName="type">
                      <ion-select-option value="gpa-4">المعدل من 4 (GPA)</ion-select-option>
                      <ion-select-option value="gpa-5">المعدل من 5 (GPA)</ion-select-option>
                      <ion-select-option value="percentage">نسبة مئوية</ion-select-option>
                    </ion-select>
                    <ion-note *ngIf="getErrorMessage('item_' + field.id + '.type').length > 0" color="danger">
                      {{ getErrorMessage('item_' + field.id + '.type') }}
                    </ion-note>
                  </ion-item>
                  <ng-container *ngIf="awardForm.get('item_' + field.id)?.value.type as type">
                    <ion-item *ngIf="type">
                      <ion-input *ngIf="type === 'gpa-4'"
                                 [max]="4" [min]="field.data.min!['gpa-4'] || 1" [step]="0.01"
                                 [helperText]="(field.data.min!['gpa-4'] || '1') + ' - 4'"
                                 [errorText]="getErrorMessage('item_' + field.id + '.value')"
                                 type="number" formControlName="value" label="{{ field.title }}"></ion-input>

                      <ion-input *ngIf="type === 'gpa-5'"
                                 [max]="5" [min]="field.data.min!['gpa-5'] || 1" [step]="0.01"
                                 [helperText]="(field.data.min!['gpa-5'] || '1') + ' - 5'"
                                 [errorText]="getErrorMessage('item_' + field.id + '.value')"
                                 type="number" formControlName="value" label="{{ field.title }}"></ion-input>

                      <ion-input *ngIf="type === 'percentage'"
                                 [max]="100" [min]="field.data.min!['percentage'] || 1" [step]="0.1"
                                 [helperText]="(field.data.min!['percentage'] || '1') + ' - 100'"
                                 [errorText]="getErrorMessage('item_' + field.id + '.value')"
                                 type="number" formControlName="value" label="{{ field.title }}"></ion-input>
                    </ion-item>
                  </ng-container>
                </ng-container>
              }
              @default {
                <ion-item>
                  <ion-input type="text" formControlName="item_{{ field.id }}"
                             placeholder="{{ field.title }}"></ion-input>
                </ion-item>
              }
            }
          </ng-container>
          <ion-button color="primary" expand="block" mode="ios" style="color: white;"
                      (click)="store()" [disabled]="formLoading">
            <ion-spinner *ngIf="formLoading"></ion-spinner>
            <span *ngIf="!formLoading">
              إضافة الطلب
            </span>
          </ion-button>
        </ion-list>
      </ng-container>
    </ng-container>
  </ng-container>
</ion-content>
<ng-template #loading>
  <ion-item *ngFor="let one of [].constructor(3)">
    <ion-thumbnail slot="start">
      <ion-skeleton-text [animated]="true"></ion-skeleton-text>
    </ion-thumbnail>
    <ion-label>
      <h3>
        <ion-skeleton-text [animated]="true" style="width: 80%;"></ion-skeleton-text>
      </h3>
    </ion-label>
  </ion-item>
</ng-template>
