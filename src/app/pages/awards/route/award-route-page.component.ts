import {ChangeDetector<PERSON>ef, Component, Inject, Input, OnInit} from '@angular/core';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DatePipe,
  DOCUMENT,
  JsonPipe,
  <PERSON>For<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Ng<PERSON>ptimizedImage,
  NgTemplateOutlet,
  SlicePipe
} from "@angular/common";
import {IonicModule, ToastController} from "@ionic/angular";
import {HeaderComponent} from "../../../layout/header/header.component";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {IS_SERVER_PLATFORM} from "../../../shared/IS_SERVER_PLATFORM.token";
import {CardComponent} from "../../../shared/components/card/card.component";
import {Router, RouterModule} from "@angular/router";
import {NgxSplideModule} from "ngx-splide";
import {<PERSON><PERSON><PERSON>er, FormControl, FormsModule, ReactiveFormsModule, Validators} from "@angular/forms";
import {AwardCategoriesService} from "../../../shared/services/award-categories.service";
import {ImgFallbackDirective} from "../../../img-fallback.directive";
import {AwardRoute} from "../../../shared/models";
import {BehaviorSubject, catchError, finalize} from "rxjs";
import {ApiService} from "../../../shared/services/api.service";
import {TranslateIonicTextsDirective} from "../../../shared/directives/translate-ionic-texts.directive";
import {isObject} from "../../../shared/helpers/helper.functions";
import {RenderBlockPipe} from "../../../shared/pipes/render-block.pipe";

@Component({
  selector: 'app-award-route',
  templateUrl: 'award-route-page.component.html',
  styleUrls: ['award-route-page.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    IfIsBrowserDirective,
    NgIf,
    CardComponent,
    NgTemplateOutlet,
    NgForOf,
    AsyncPipe,
    JsonPipe,
    RouterModule,
    DatePipe,
    NgxSplideModule,
    NgOptimizedImage,
    SlicePipe,
    FormsModule,
    ImgFallbackDirective,
    ReactiveFormsModule,
    TranslateIonicTextsDirective,
    RenderBlockPipe
  ],
  //encapsulation: ViewEncapsulation.None,
})
export class AwardRoutePageComponent implements OnInit {
  dataLoaded = false;
  formLoading = false;
  termsAccepted = false;
  @Input('id') routeId?: number;
  categoryId?: number;
  fields: any[] = [];
  nowTime = new Date().toISOString();
  awardForm = this.formBuilder.group({});
  protected readonly isObject = isObject;
  private _route$ = new BehaviorSubject<AwardRoute>({} as AwardRoute);
  route$ = this._route$.asObservable()

  constructor(
    @Inject(DOCUMENT) private document: Document,
    @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
    protected awardCategoriesService: AwardCategoriesService,
    private toastController: ToastController,
    protected apiService: ApiService,
    private cdr: ChangeDetectorRef,
    private formBuilder: FormBuilder,
    private router: Router,
  ) {

  }

  ngOnInit(): void {
    this.getRoutes();
    if (!this.isServer) {
      this.browserSetup();
    }
  }

  getRoutes() {
    if (this.routeId)
      this.apiService.awardRoute(this.routeId).subscribe((res) => {
        if (typeof(res.terms) === 'string')
          // @ts-ignore
          res.terms = JSON.parse(res.terms);
        this._route$.next(res);
        this.categoryId = res.award_category_id;
        this.fields = res.fields.sort((a: any, b: any) => a.index - b.index)
        this.fields.forEach((field) => {
          if (field.type === 'GPA')
            this.awardForm.addControl('item_' + field.id, this.formBuilder.group({
              type: new FormControl('', Validators.required),
              value: new FormControl('', Validators.required),
            }));
          else
            this.awardForm.addControl('item_' + field.id, new FormControl('', Validators.required));
        });
        this.dataLoaded = true;
        this.cdr.detectChanges();
      });
    else
      this.router.navigate(['/user-awards'])
  }

  browserSetup() {
  }

  store() {
    if (this.awardForm.valid) {
      this.formLoading = true;
      let data: any = {
        award_route_id: this.routeId,
        fields: [],
      };
      this.fields.forEach((field) => {
        if (field.type === 'GPA')
          data.fields.push({
            id: field.id,
            value: {
              type: this.awardForm.get('item_' + field.id)?.value.type,
              value: this.awardForm.get('item_' + field.id)?.value.value,
            },
          });
        else
          data.fields.push({
            id: field.id,
            value: this.awardForm.get('item_' + field.id)?.value,
          });
      });
      this.apiService.awardUserStore(data)
        .pipe(
          finalize(() => this.formLoading = false),
          catchError(({error}) => {
            let errorMessage = error.message || 'خطأ !';
            this.toastController.create({
              header: 'خطأ',
              message: errorMessage,
              position: 'top',
              color: 'danger',
              buttons: [{
                side: 'end',
                text: 'x',
                role: 'cancel',
              }]
            }).then((toast) => {
              toast.present();
              setTimeout(() => toast.dismiss(), 5000);
            });
            throw new Error(JSON.stringify(error))
          })
        )
        .subscribe((res) => {
          this.awardCategoriesService.loadCategories();
          this.router.navigate(['/user-awards'])
        });
    } else {
      this.awardForm.markAllAsTouched();
    }
  }

  fileChange($event: Event, id: any) {
    const file = ($event.target as HTMLInputElement).files?.item(0);
    if (file) {
      this.awardForm.get('item_' + id)?.setValue(file);
    }
  }

  confirm() {
    this.termsAccepted = true;
  }

  getErrorMessage(controlName: string): string {
    let control: FormControl = this.awardForm.get(controlName) as FormControl;
    if (!control || !control!.invalid || !control!.touched)
      return '';
    if (control.hasError('required'))
      return 'الحقل مطلوب';
    if (control.hasError('minlength'))
      return 'الحد الأدنى للحروف';
    if (control.hasError('maxlength'))
      return 'الحد الأقصى للحروف';
    return 'الحقل غير صحيح';
  }
}
