import {ChangeDetectorRef, Component, Input, Inject, OnInit} from '@angular/core';
import {
  As<PERSON><PERSON><PERSON><PERSON>,
  DatePipe,
  DOCUMENT,
  JsonPipe,
  <PERSON>For<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Ng<PERSON>ptimizedImage,
  NgTemplateOutlet, SlicePipe
} from "@angular/common";
import {IonicModule} from "@ionic/angular";
import {HeaderComponent} from "../../../layout/header/header.component";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {IS_SERVER_PLATFORM} from "../../../shared/IS_SERVER_PLATFORM.token";
import {CardComponent} from "../../../shared/components/card/card.component";
import {RouterModule} from "@angular/router";
import {NgxSplideModule} from "ngx-splide";
import {FormsModule} from "@angular/forms";
import {AwardCategoriesService} from "../../../shared/services/award-categories.service";
import {ImgFallbackDirective} from "../../../img-fallback.directive";
import {Activity, AwardCategory, AwardRoute} from "../../../shared/models";
import {BehaviorSubject, take, tap} from "rxjs";
import {ApiService} from "../../../shared/services/api.service";
import {Router} from "@angular/router";

@Component({
  selector: 'app-award-routes',
  templateUrl: 'award-routes-page.component.html',
  styleUrls: ['award-routes-page.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    IfIsBrowserDirective,
    NgIf,
    CardComponent,
    NgTemplateOutlet,
    NgForOf,
    AsyncPipe,
    JsonPipe,
    RouterModule,
    DatePipe,
    NgxSplideModule,
    NgOptimizedImage,
    SlicePipe,
    FormsModule,
    ImgFallbackDirective
  ],
})
export class AwardRoutesPageComponent implements OnInit {
  dataLoaded = false;
  @Input('id') categoryId?: number;
  private _category$ = new BehaviorSubject<AwardCategory>({} as AwardCategory);
  private _routes$ = new BehaviorSubject<AwardRoute[]>([]);
  category$ = this._category$.asObservable()
  routes$ = this._routes$.asObservable()

  constructor(
    @Inject(DOCUMENT) private document: Document,
    @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
    protected awardCategoriesService: AwardCategoriesService,
    protected apiService: ApiService,
    private cdr: ChangeDetectorRef,
    private router: Router,
  ) {

  }

  ngOnInit(): void {
    this.getRoutes();
    if (!this.isServer) {
      this.browserSetup();
    }
  }

  getRoutes() {
    if (this.categoryId)
      this.apiService.awardCategory(this.categoryId).subscribe((res) => {
        this._category$.next(res)
        this._routes$.next(res.routes || [])
        this.dataLoaded = true;
        this.cdr.detectChanges();
      });
    else
      this.router.navigate(['/user-awards'])
  }

  browserSetup() {
  }
}
