<app-header [hideProfileIcon]="false" [showBackButton]="true" backTo="/user-awards"></app-header>
<ion-content [fullscreen]="true">
  <ng-container *ngIf="dataLoaded; else loading">
    <ng-container *ngIf="routes$ | async as routes;">
      <ion-header collapse="condense">
        <ion-toolbar>
          <ion-title size="large" class="ion-margin-vertical" style="">
            جائزة التميز لعام
            <ng-container *ngIf="(awardCategoriesService.$year | async)! > 0;">
              {{ (awardCategoriesService.$year | async) }}
            </ng-container>
            <ng-container *ngIf="category$ | async as category;">
              <br>
              <h4>
                المسار:
                {{ category.title }}
              </h4>
            </ng-container>
          </ion-title>
        </ion-toolbar>
      </ion-header>
      <ion-list class="ion-margin-bottom">
        <ion-item *ngFor="let route of routes" [disabled]="!route.can_submit"
                  [button]="true" [detail]="false" routerLink="/user-awards/route/{{ route.id }}">
          <ion-label>{{ route.title }}</ion-label>
        </ion-item>
      </ion-list>
    </ng-container>
  </ng-container>
</ion-content>
<ng-template #loading>
  <ion-item *ngFor="let one of [].constructor(2)">
    <ion-thumbnail slot="start">
      <ion-skeleton-text [animated]="true"></ion-skeleton-text>
    </ion-thumbnail>
    <ion-label>
      <h3>
        <ion-skeleton-text [animated]="true" style="width: 80%;"></ion-skeleton-text>
      </h3>
    </ion-label>
  </ion-item>
</ng-template>
