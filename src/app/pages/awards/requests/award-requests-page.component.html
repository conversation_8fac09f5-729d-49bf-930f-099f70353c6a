<app-header [hideProfileIcon]="false" [showBackButton]="true" backTo="/user-awards"></app-header>
<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large" class="" style="">
        طلبات جائزة التميز
      </ion-title>
    </ion-toolbar>
  </ion-header>
  <ng-container *ngIf="dataLoaded; else loading">
    <ng-container *ngIf="requests$ | async as requests;">
      @if (requests.length === 0) {
        <ion-item>
          <ion-text color="danger">لا يوجد طلبات في الوقت الحالي</ion-text>
        </ion-item>
      } @else {
        <ion-list class="ion-margin-bottom">
          <ion-item *ngFor="let request of requests" [button]="false" [detail]="false">
            <ion-col>
              <ion-label>
                <h4 class="ion-text-wrap" style="line-height: 1.7 !important;">
                  {{ request.award_route }}
                  <ion-chip [color]="{
                              NEW: 'tertiary',
                              IN_REVIEW: 'primary',
                              APPROVED: 'success',
                              REJECTED: 'danger'
                            }[request.status] ?? 'secondary'"
                            style="float:left;">{{ request.status_locale }}
                  </ion-chip>
                </h4>
                <p class="">{{ request.created_at | date:'longDate' }}</p>
              </ion-label>
            </ion-col>
            <ion-col size="auto" *ngIf="request.status === 'NEW'">
              <ion-spinner color="danger" *ngIf="formLoading" size="small"></ion-spinner>
              <ion-icon color="danger" *ngIf="!formLoading" name="trash-outline" size="small" (click)="confirmDelete(request.id)"
                        style="cursor: pointer;"></ion-icon>
            </ion-col>
          </ion-item>
        </ion-list>
      }
    </ng-container>
  </ng-container>
</ion-content>
<ng-template #loading>
  <ion-item *ngFor="let one of [].constructor(2)">
    <ion-thumbnail slot="start">
      <ion-skeleton-text [animated]="true"></ion-skeleton-text>
    </ion-thumbnail>
    <ion-label>
      <h3>
        <ion-skeleton-text [animated]="true" style="width: 80%;"></ion-skeleton-text>
      </h3>
    </ion-label>
  </ion-item>
</ng-template>
