import {ChangeDetectorRef, Component, Input, Inject, OnInit} from '@angular/core';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DatePipe,
  DOCUMENT,
  JsonPipe,
  NgFor<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ptimizedImage,
  NgTemplateOutlet, SlicePipe
} from "@angular/common";
import {IonicModule, ToastController, AlertController} from "@ionic/angular";
import {HeaderComponent} from "../../../layout/header/header.component";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {IS_SERVER_PLATFORM} from "../../../shared/IS_SERVER_PLATFORM.token";
import {CardComponent} from "../../../shared/components/card/card.component";
import {RouterModule} from "@angular/router";
import {NgxSplideModule} from "ngx-splide";
import {FormsModule} from "@angular/forms";
import {AwardCategoriesService} from "../../../shared/services/award-categories.service";
import {ImgFallbackDirective} from "../../../img-fallback.directive";
import {Activity, AwardCategory, AwardRoute, AwardUser} from "../../../shared/models";
import {BehaviorSubject, finalize, catchError, take, tap} from "rxjs";
import {ApiService} from "../../../shared/services/api.service";
import {Router} from "@angular/router";

@Component({
  selector: 'app-award-requests',
  templateUrl: 'award-requests-page.component.html',
  styleUrls: ['award-requests-page.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    IfIsBrowserDirective,
    NgIf,
    CardComponent,
    NgTemplateOutlet,
    NgForOf,
    AsyncPipe,
    JsonPipe,
    RouterModule,
    DatePipe,
    NgxSplideModule,
    NgOptimizedImage,
    SlicePipe,
    FormsModule,
    ImgFallbackDirective
  ],
})
export class AwardRequestsPageComponent implements OnInit {
  dataLoaded = false;
  formLoading = false;
  private _requests$ = new BehaviorSubject<AwardUser[]>([]);
  requests$ = this._requests$.asObservable()

  constructor(
    @Inject(DOCUMENT) private document: Document,
    @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
    protected awardCategoriesService: AwardCategoriesService,
    private alertController: AlertController,
    protected apiService: ApiService,
    private cdr: ChangeDetectorRef,
    private toastController: ToastController,
    private router: Router,
  ) {

  }

  ngOnInit(): void {
    this.getRequests();
    if (!this.isServer) {
      this.browserSetup();
    }
  }

  getRequests() {
    this.apiService.awardUserList().subscribe((res) => {
      this._requests$.next(res || [])
      this.dataLoaded = true;
      this.cdr.detectChanges();
    });
  }

  browserSetup() {
  }

  async confirmDelete(id: any) {
    const alert = await this.alertController.create({
      header: 'متأكد؟',
      message: 'تأكيد حذف الطلب، لا يمكن التراجع عن هذا الإجراء',
      buttons: [
        {
          text: 'إلغاء',
          role: 'cancel',
          cssClass: 'secondary'
        }, {
          text: 'حذف',
          handler: () => {
            this.delete(id);
          }
        }
      ]
    });

    await alert.present();
  }

  async delete(id: any) {
    this.formLoading = true;
    this.apiService.awardUserDelete(id)
      .pipe(
        finalize(() => this.formLoading = false),
        catchError(({error}) => {
          let errorMessage = error.message || 'خطأ !';
          this.toastController.create({
            header: 'خطأ',
            message: errorMessage,
            position: 'top',
            color: 'danger',
            buttons: [{
              side: 'end',
              text: 'x',
              role: 'cancel',
            }]
          }).then((toast) => {
            toast.present();
            setTimeout(() => toast.dismiss(), 5000);
          });
          throw new Error(JSON.stringify(error))
        })
      )
      .subscribe((res) => {
        this.getRequests();
        this.awardCategoriesService.loadCategories();
      });
  }
}
