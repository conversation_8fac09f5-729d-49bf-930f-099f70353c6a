<app-header [hideProfileIcon]="false" [showBackButton]="true" backTo="/home"></app-header>
<ion-content [fullscreen]="true">
  <ng-container *ngIf="dataLoaded; else loading">
    <ng-container>
      <ion-header collapse="condense">
        <ion-toolbar>
          <ion-title size="large" class="ion-margin-vertical" style="">
            جائزة التميز لعام
            {{ (awardCategoriesService.$year | async) }}
            هـ
          </ion-title>
        </ion-toolbar>
      </ion-header>
      <ion-list class="ion-margin-bottom">
        <ion-item *ngFor="let category of (awardCategoriesService.$categories | async)"
                  [disabled]="!(awardCategoriesService.$active | async) || !(awardCategoriesService.$canSubmit | async)"
                  [button]="true" [detail]="false" routerLink="/user-awards/{{ category.id }}">
          <ion-label>{{ category.title }}</ion-label>
        </ion-item>
      </ion-list>
      <ion-list class="ion-margin-bottom">
        <ion-item [button]="true" [detail]="false" routerLink="/user-awards/requests">
          <ion-label>الطلبات القديمة</ion-label>
        </ion-item>
      </ion-list>
    </ng-container>
  </ng-container>
</ion-content>

<ng-template #loading>
  <ion-item *ngFor="let one of [].constructor(2)">
    <ion-thumbnail slot="start">
      <ion-skeleton-text [animated]="true"></ion-skeleton-text>
    </ion-thumbnail>
    <ion-label>
      <h3>
        <ion-skeleton-text [animated]="true" style="width: 80%;"></ion-skeleton-text>
      </h3>
    </ion-label>
  </ion-item>
</ng-template>
