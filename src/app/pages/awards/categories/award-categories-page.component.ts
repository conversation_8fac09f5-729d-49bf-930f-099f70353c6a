import {ChangeDetectorRef, Component, Inject, OnInit} from '@angular/core';
import {
  AsyncPipe,
  DatePipe,
  DOCUMENT,
  JsonPipe,
  <PERSON>ForOf,
  <PERSON><PERSON>f,
  NgOptimizedImage,
  NgTemplateOutlet, SlicePipe
} from "@angular/common";
import {IonicModule} from "@ionic/angular";
import {HeaderComponent} from "../../../layout/header/header.component";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {IS_SERVER_PLATFORM} from "../../../shared/IS_SERVER_PLATFORM.token";
import {CardComponent} from "../../../shared/components/card/card.component";
import {RouterModule} from "@angular/router";
import {NgxSplideModule} from "ngx-splide";
import {FormsModule} from "@angular/forms";
import {AwardCategoriesService} from "../../../shared/services/award-categories.service";
import {ImgFallbackDirective} from "../../../img-fallback.directive";

@Component({
  selector: 'app-award-categories',
  templateUrl: 'award-categories-page.component.html',
  styleUrls: ['award-categories-page.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    IfIsBrowserDirective,
    NgIf,
    CardComponent,
    NgTemplateOutlet,
    NgForOf,
    AsyncPipe,
    JsonPipe,
    RouterModule,
    DatePipe,
    NgxSplideModule,
    NgOptimizedImage,
    SlicePipe,
    FormsModule,
    ImgFallbackDirective
  ],
})
export class AwardCategoriesPageComponent implements OnInit {
  dataLoaded = false;

  constructor(@Inject(DOCUMENT) private document: Document,
              @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
              protected awardCategoriesService: AwardCategoriesService,
  ) {

  }

  ngOnInit(): void {
    this.awardCategoriesService.$dataLoaded.subscribe((data) => {
      this.dataLoaded = data;
    });
    if (!this.isServer) {
      this.browserSetup();
    }
  }

  browserSetup() {
  }
}
