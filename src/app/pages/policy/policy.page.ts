import {Component} from '@angular/core';
import {AsyncPipe} from '@angular/common';
import {IonicModule} from "@ionic/angular";
import {HeaderComponent} from "../../layout/header/header.component";
import {IfIsBrowserDirective} from "../../shared/IfIsBrowser.directive";
import {AppDataService} from "../../shared/services/app-data.service";

@Component({
  selector: 'app-privacy-policy',
  templateUrl: 'policy.page.html',
  styleUrls: ['policy.page.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    IfIsBrowserDirective,
    AsyncPipe,
  ],
})
export class PolicyPage {
  constructor(
    protected appDataService: AppDataService,
  ) {
  }
}
