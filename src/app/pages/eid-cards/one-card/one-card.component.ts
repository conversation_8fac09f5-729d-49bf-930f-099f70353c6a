import {
  Component,
  OnInit,
  ViewChild,
  ElementRef,
  Input,
  AfterViewInit,
  OnChanges,
  SimpleChanges
} from '@angular/core';
import {IonicModule} from "@ionic/angular";
import {HeaderComponent} from "../../../layout/header/header.component";
import {HttpClientModule} from "@angular/common/http";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {AsyncPipe, DatePipe, JsonPipe, NgForOf, NgIf, NgTemplateOutlet} from "@angular/common";
import {CardComponent} from "../../../shared/components/card/card.component";
import {RouterModule} from "@angular/router";
import {FormsModule} from "@angular/forms";
import {NgxGoogleAnalyticsModule} from "@dustfoundation/ngx-google-analytics";
import {GenericService} from "../../../shared/services/generic.service";
import {Filesystem, Directory} from "@capacitor/filesystem";
import {Share} from '@capacitor/share';

@Component({
  selector: 'one-card',
  templateUrl: './one-card.component.html',
  styleUrls: ['./one-card.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    IfIsBrowserDirective,
    NgIf,
    CardComponent,
    NgTemplateOutlet,
    NgForOf,
    AsyncPipe,
    JsonPipe,
    RouterModule,
    DatePipe,
    FormsModule,
    NgxGoogleAnalyticsModule
  ]
})
export class OneCardComponent implements OnChanges {
  @ViewChild('myCanvas', {static: false}) canvasEl!: ElementRef;
  @ViewChild('pic', {static: false}) pic!: ElementRef;

  private theCanvas: any;
  private theContext: any;
  public finalImage: any;

  private canvasColor = 'rgba(255, 255, 255, 1)';
  private pictureWidth = 0;
  private pictureHeight = 0;
  // Text

  private textWrapWidth = 0;
  private textWrapHeight = 120;
  private fontStyle = '100px Arial';
  private fontColor = '#000'; // For fillText
  private borderWidth = 2.2;
  // private borderColor = 'rgba(0, 0, 0, 1)'; // For strokeText border
  @Input() name = '';
  @Input() minFontSize = 20;
  showCanvas = false;
  @Input() card!: {
    photo_url: string;
    text_font: string;
    text_size: number;
    text_x: number;
    text_y: number;
    text_color: string;
    text_align: string;
    text_width?: number;
    text_bold: boolean;
    line_height: number;
  };
  @Input() userAvatarUrl: string | null = null;

  constructor(
    private genericService: GenericService
  ) {
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['name']) {
      if (this.pictureHeight !== 0 && this.pictureWidth !== 0) {
        this.saveCanvas();
      }
    }

    if (changes['minFontSize'] && changes['minFontSize'].currentValue > 0) {
      this.card.text_size = changes['minFontSize'].currentValue;
    }
  }

  initialiseCanvas() {


    if (!this.canvasEl) {
      return;
    }
    this.theCanvas = this.canvasEl.nativeElement;
    this.theCanvas.width = this.pictureWidth;
    this.theCanvas.height = this.pictureHeight;
    if (this.theCanvas.getContext) {
      this.theContext = this.theCanvas.getContext('2d');
      this.drawTheImage();
    }
  }

  drawTheImage() {
    // Clear Current Image
    this.theContext.clearRect(
      0,
      0,
      this.theCanvas.width,
      this.theCanvas.height
    );

    // Fill background
    this.theContext.fillStyle = this.canvasColor;
    this.theContext.fillRect(0, 0, this.theCanvas.width, this.theCanvas.height);

    // Display the Image
    const img = new Image();
    img.setAttribute('crossOrigin', 'anonymous');
    img.onload = event => {
      this.theContext.drawImage(
        img,
        this.theCanvas.width / 2 - img.width / 2,
        this.theCanvas.height / 2 - img.height / 2
      );
      if (this.name)
        this.drawText();
    };
    img.src = this.card.photo_url;
  }

  drawText() {
    this.theContext.textAlign = this.card.text_align || 'right';
    this.fontStyle = this.card.text_bold ? ' bold' : ''
    this.fontStyle += ' ' + this.card.text_size + 'px';
    this.fontStyle += this.card.text_font ? ' ' + this.card.text_font : '';
    this.fontColor = this.card.text_color;
    this.theContext.font = this.fontStyle;


    this.theContext.fillStyle = this.fontColor; // fill text
    // add line height to text in context
    this.theContext.textBaseline = 'top';
    this.theContext.lineHeight = this.card.line_height;
    this.theContext.lineWidth = this.borderWidth;
    this.theContext.backgroundColor = '#fff';

    // this.theContext.strokeStyle = this.borderColor; // stroke border
    // this.wrapText();
    this.fillTextCenter(this.theContext, this.name, 0, 0, this.theContext.width, this.theContext.height)

    // this.theContext.fillText(this.name, (this.pictureWidth / 2), this.card.text_y); // fill text

    this.finalImage = this.theCanvas.toDataURL('image/jpg');
  }

  fillTextCenter(ctx: any, text: string, x: number, y: number, width: number, height: number) {

    const lines = text.match(/[^\r\n]+/g);
    if (!lines) return;
    for (let i = 0; i < lines.length; i++) {
      let xL = (width - x) / 2
      let yL = +this.card.text_y + ((this.card.line_height ?? 100) * i);
      this.theContext.fillText(lines[i], (this.pictureWidth / 2), yL)
    }
  }

  wrapText() {
    const words = this.name.split(' ');
    let line = '';
    for (let n = 0; n < words.length; n++) {
      const testLine = line + words[n] + ' ';
      const metrics = this.theContext.measureText(testLine);
      const testWidth = metrics.width;
      if (testWidth > this.textWrapWidth && n > 0) {
        this.theContext.fillText(line, this.card.text_x, this.card.text_y); // fill text
        this.theContext.strokeText(line, this.card.text_x, this.card.text_y); // stroke border
        line = words[n] + ' ';
        this.card.text_y += this.textWrapHeight;
      } else {
        line = testLine;
      }
    }
    this.theContext.fillText(line, this.card.text_x, this.card.text_y); // fill text
    // this.theContext.strokeText(line, this.card.text_x, this.card.text_y); // stroke border
  }

  saveCanvas() {
    this.showCanvas = true;
    setTimeout(() => {
      this.initialiseCanvas();
    }, 500);
  }

  onLoad() {
    this.pictureWidth = (this.pic
      .nativeElement as HTMLImageElement).naturalWidth;
    this.pictureHeight = (this.pic
      .nativeElement as HTMLImageElement).naturalHeight;
    this.textWrapWidth = this.card.text_width || this.pictureWidth - 50;

    this.saveCanvas();
  }

  download() {
    if (this.finalImage && this.name) {
      this.theCanvas.toBlob(async (blob: Blob) => {
        const _name = `${this.name}.jpg`
        if (this.genericService.isCapacitorApp()) {
          let base64 = await this.genericService.convertBlobToBase64(blob) as string;
          Filesystem.writeFile({
            path: _name,
            data: base64,
            directory: Directory.Cache,
          }).then(async (res) => {
            await Share.share({
              files: [res.uri],
            })
          })
        } else {
          let ImageUrlLink = document.createElement('a')
          ImageUrlLink.href = window.URL.createObjectURL(blob)
          ImageUrlLink.download = _name
          ImageUrlLink.target = '_blank'
          ImageUrlLink.click()
        }
      }, 'image/jpg')
    }
  }
}
