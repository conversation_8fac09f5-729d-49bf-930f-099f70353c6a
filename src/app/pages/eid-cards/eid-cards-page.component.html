<app-header [hideProfileIcon]="false" [showBackButton]="true" backTo="/home"></app-header>
<ion-content>
  <ion-grid [fixed]="true" class="ion-justify-content-center">
    <ion-row class="ion-justify-content-center">
      <ion-item style="width: 370px; margin: auto;border-radius: 8px;" lines="none">
        <ion-label position="fixed">الاسم</ion-label>
        <ion-textarea [(ngModel)]="username" placeholder="أدخل الاسم رجاء"></ion-textarea>
        <ion-button size="small" *ngIf="false">تعديل</ion-button>
      </ion-item>
    </ion-row>
    <ng-container *ngFor="let oneCard of cards; let i= index">
      <ion-row class="ion-justify-content-center">
        <app-card class="ion-margin-top" title="بطاقة المعايدة" [showExpandButton]="false" [scrollable]="false"
                  style="height: 100%">
          <one-card [card]="oneCard" [name]="username" [minFontSize]="0"></one-card>
        </app-card>
      </ion-row>
    </ng-container>
  </ion-grid>
</ion-content>

