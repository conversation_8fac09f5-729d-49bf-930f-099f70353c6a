import { ChangeDetector<PERSON><PERSON>, Component, Inject, OnInit, TransferState } from '@angular/core';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, DatePipe, DOCUMENT, JsonPipe, <PERSON><PERSON>or<PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgTemplateOutlet} from "@angular/common";
import {IonicModule} from "@ionic/angular";

import {HttpClient, HttpClientModule} from "@angular/common/http";

import {ActivatedRoute, RouterModule} from "@angular/router";
import {Observable, take} from "rxjs";
import {HeaderComponent} from "../../layout/header/header.component";
import {IfIsBrowserDirective} from "../../shared/IfIsBrowser.directive";
import {CardComponent} from "../../shared/components/card/card.component";
import {IS_SERVER_PLATFORM} from "../../shared/IS_SERVER_PLATFORM.token";
import {ApiService} from "../../shared/services/api.service";
import {environment} from "../../../environments/environment";
import {Transaction} from "../../shared/models";
import {OneCardComponent} from "./one-card/one-card.component";
import {FormsModule} from "@angular/forms";


@Component({
  selector: 'app-eid-card',
  templateUrl: 'eid-cards-page.component.html',
  styleUrls: ['eid-cards-page.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    IfIsBrowserDirective,
    NgIf,
    CardComponent,
    NgTemplateOutlet,
    NgForOf,
    AsyncPipe,
    JsonPipe,
    RouterModule,
    DatePipe,
    OneCardComponent,
    FormsModule
  ],
})
export class EidCardsPage implements OnInit {
  protected readonly environment = environment;
  username = '';
  cards = [
    {
      photo_url: '/assets/images/eid-cards/1445/eid1.jpg?v=1.1',
      text_font: 'helvetica',
      text_size: 50,
      text_x: 30,
      text_y: 800,
      text_color: 'var(--ion-color-primary, #113665)',
      text_align: 'center',
      text_bold: true,
      line_height: 50,
    },
    // {
    //   photo_url: '/assets/images/eid-cards/1444/aid1.png?v=2.0',
    //   text_font: 'helvetica',
    //   text_size: 100,
    //   text_x: 599,
    //   text_y: 2050,
    //   text_color: '#1C6A4D',
    //   text_align: 'center',
    //   text_bold: true,
    //   line_height: 200,
    // },

    /*{
      photo_url: '/assets/images/eid-cards/aid1.png',
      text_font: 'helvetica',
      text_size: 150,
      text_x: 599,
      text_y: 2000,
      text_color: '#1C6A4D',
      text_align: 'center',
      text_bold: true,
      line_height: 250,

    },{
      photo_url: '/assets/images/eid-cards/aid2.png',
      text_font: 'helvetica',
      text_size: 130,
      text_x: 599,
      text_y: 2000,
      text_color: '#dfb163',
      text_align: 'center',
      text_bold: false,
      line_height: 200,
    }*/
  ]

  constructor(
    @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
    private apiService: ApiService,
    private transferState: TransferState,
    private activatedRoute: ActivatedRoute,
  ) {
  }

  ngOnInit(): void {
    if (this.isServer) {
      console.log('is server')
    }

    if (!this.isServer) {
      this.browserSetup();
    }
  }

  browserSetup() {
    // get query params using activated route
    this.activatedRoute.params.pipe(take(1)).subscribe(params => {
    });
  }
}
