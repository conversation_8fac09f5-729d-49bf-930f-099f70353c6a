<app-header [hideProfileIcon]="false" [showBackButton]="true"></app-header>
<ion-content>
  <ion-grid [fixed]="true">
    <ion-row>
      <ion-col size="12">
        <ng-container *ngTemplateOutlet="documents"></ng-container>
      </ion-col>
    </ion-row>
    <ion-infinite-scroll (ionInfinite)="onIonInfinite($event)" *ngIf="documentsNextPageUrl">
      <ion-infinite-scroll-content></ion-infinite-scroll-content>
    </ion-infinite-scroll>
  </ion-grid>
</ion-content>

<ng-template #documents>
  <div style=" overflow-y: scroll; overflow-x: hidden;   max-width: 370px;    margin: auto;">
    <ng-container *ngIf="documents$ | async as documents;">
      <ion-spinner *ngIf="loading" style="margin: auto; width: 100%;height: 40px;"></ion-spinner>
      <ion-item *ngIf="!loading && documents.length === 0">
        <ion-text color="danger">لا يوجد وثائق في الوقت الحالي</ion-text>
      </ion-item>
      <ion-item *ngFor="let oneDocument of documents; let i = index" [title]="oneDocument.title"
                [routerLink]="'/documents/' + oneDocument.id"
                lines="full"
                button>
        <ion-avatar slot="start">
          <ion-img [src]="((appDataService.data$ | async)!['iconPath']) || '/assets/icon/logo.svg'"></ion-img>
        </ion-avatar>
        <ion-label>
          <h4 style="line-height: 1.7 !important;">{{oneDocument.title || oneDocument.details}}
            <!--<h6>{{oneDocument.user}}</h6>-->
          </h4>
          <p class=""
             *ngIf="oneDocument.date">{{oneDocument.date.indexOf('ه') >= 0 ? oneDocument.date : (oneDocument.date | date: 'longDate')}}</p>
          <p class="" *ngIf="!oneDocument.date">{{oneDocument.created_at | date: 'longDate'}}</p>
        </ion-label>
      </ion-item>
    </ng-container>
  </div>
</ng-template>
