import {ChangeDetector<PERSON>ef, Component, Inject, OnInit} from '@angular/core';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, DatePipe, DOCUMEN<PERSON>, JsonPipe, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>plateOutlet} from "@angular/common";
import {InfiniteScrollCustomEvent, IonicModule} from "@ionic/angular";
import {HeaderComponent} from "../../../layout/header/header.component";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {IS_SERVER_PLATFORM} from "../../../shared/IS_SERVER_PLATFORM.token";
import {CardComponent} from "../../../shared/components/card/card.component";
import {ApiService} from "../../../shared/services/api.service";
import {HttpClientModule} from "@angular/common/http";
import {BehaviorSubject, finalize, of, take, tap} from "rxjs";
import {RouterModule} from "@angular/router";
import {DocumentModel} from "../../../shared/models";
import {FormatDistancePipe} from "../../../shared/pipes/format-distance.pipe";
import {AppDataService} from "../../../shared/services/app-data.service";


@Component({
  selector: 'app-document-index',
  templateUrl: 'document-index.component.html',
  styleUrls: ['document-index.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    IfIsBrowserDirective,
    NgIf,
    CardComponent,
    NgTemplateOutlet,
    NgForOf,
    AsyncPipe,
    JsonPipe,
    RouterModule,
    DatePipe,
    FormatDistancePipe
  ],
})
export class DocumentIndexComponent implements OnInit {
  isLoading = false;
  documentsPage = 0;
  loading = true;
  documentsNextPageUrl = '';
  private _documents$ = new BehaviorSubject<DocumentModel[]>([]);
  documents$ = this._documents$.asObservable()


  constructor(@Inject(DOCUMENT) private document: Document,
              @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
              private apiService: ApiService,
              public appDataService: AppDataService,
              private cdr: ChangeDetectorRef
  ) {

  }

  ngOnInit(): void {
    if (this.isServer) {
      console.log('is server')
    }
    this.getLiteratures().pipe(take(1)).subscribe();
    if (!this.isServer) {
      this.browserSetup();
    }
  }

  browserSetup() {
  }

  getLiteratures() {
    if (this.isLoading)
      return of();
    this.isLoading = true;
    this.documentsPage++;
    return this.apiService.checkAndGetData(
      this.apiService.getDynamicStateKey(`documents-${this.documentsPage}`),
      this.apiService.getDocuments(this.documentsNextPageUrl),
      []
    ).pipe(take(1), finalize(() => this.isLoading = false), tap((data) => {
      this.loading= false;
      this.documentsNextPageUrl = data.pagination?.next_url;
      this._documents$.next([...this._documents$.value, ...data.data].sort(
        (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      ) ?? []);
      this.cdr.detectChanges();
    }));
  }

  onIonInfinite(ev: any) {
    this.getLiteratures().pipe(take(1)).subscribe();
    setTimeout(() => {
      (ev as InfiniteScrollCustomEvent).target.complete().then();
    }, 500);
  }

  failCallbackImage(event: any) {
    this.appDataService.data$.subscribe(data => {
      event.target.src = (data!['iconPath']) ||  '/assets/icon/logo.svg'
    })
  }
}
