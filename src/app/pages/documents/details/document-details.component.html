<app-header [hideProfileIcon]="false" [showBackButton]="true" backTo="/documents"></app-header>
<ion-content>
  <ion-spinner *ngIf="!document" style="margin: auto; width: 100%;height: 40px;"></ion-spinner>
  <ion-grid [fixed]="true" class="ion-align-items-center" *ngIf="document">
    <ion-card mode="ios" style="box-shadow: unset; border:solid 1px var(--ion-color-secondary, #113665); max-width: 370px; margin: auto">
      <ion-card-content>
        <ion-row class="ion-justify-content-center ion-align-items-center">
          <ion-col class="ion-justify-content-center ion-align-items-center">
            <ion-item>
              <ion-label>العنوان</ion-label>
              <ion-text color="primary">{{document.title || 'بدون'}}</ion-text>
            </ion-item>
            <ion-item>
              <ion-label>النوع</ion-label>
              <ion-text color="primary" [innerHTML]="document.type"></ion-text>
            </ion-item>
            <ion-item *ngIf="document.details">
              <!--<ion-label>التفاصيل</ion-label>-->
              <ion-text class="ion-margin" color="primary" style="text-align: justify;text-justify: inter-word;"
                        [innerHTML]="(document.details || '') | nl2br"></ion-text>
            </ion-item>
            <ion-item>
              <ion-label>التاريخ</ion-label>
              <ion-text
                color="primary">{{ document.date ? (document.date.indexOf('ه') >= 0 ? document.date : (document.date | date: 'longDate')) : (document.created_at | date: 'longDate') }}</ion-text>
            </ion-item>
            <ng-container *ngFor="let attachment of document?.attachments; let i= index">
              <ion-button class="buy-button ion-no-margin ion-margin-top" style="width: 100%"
                          *ngIf="attachment?.url" [href]="attachment?.url" target="_blank">
                <ion-icon name="document-outline"></ion-icon>
                &nbsp;
                {{ attachment.title | limitToPipe: 30}}
              </ion-button>
            </ng-container>
          </ion-col>
        </ion-row>
      </ion-card-content>
    </ion-card>
  </ion-grid>
</ion-content>

