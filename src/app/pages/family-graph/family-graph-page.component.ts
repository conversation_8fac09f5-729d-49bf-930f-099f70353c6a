import {Component, CUSTOM_ELEMENTS_SCHEMA, ElementRef, Inject, OnDestroy, OnInit, ViewChild, ViewContainerRef} from '@angular/core';
import {NgIf} from "@angular/common";
import {IonicModule, ModalController, NavController, Platform, ToastController} from "@ionic/angular";
import {ActivatedRoute, RouterModule} from "@angular/router";
import {Observable, take} from "rxjs";
import {HeaderComponent} from "../../layout/header/header.component";
import {CardComponent} from "../../shared/components/card/card.component";
import {IS_SERVER_PLATFORM} from "../../shared/IS_SERVER_PLATFORM.token";
import {ApiService} from "../../shared/services/api.service";
import {environment} from "../../../environments/environment";
import {FamilyGraph, User} from "../../shared/models";

import * as d3 from 'd3';
import {<PERSON>zy<PERSON><PERSON><PERSON><PERSON>} from './lazy-org-chart';
import {FormBuilder, FormsModule, ReactiveFormsModule, Validators} from "@angular/forms";
import {StorageService} from "../../shared/services/storage.service";
import {ProfileService} from "../../shared/services/profile.service";
import {UserProfilePage} from "./user-profile/user-profile-page.component";
import {FamilyTreeTutorial} from "./family-tree-tutorial/family-tree-tutorial.component";
import {GenericService} from "../../shared/services/generic.service";
import {AppDataService} from "../../shared/services/app-data.service";
import {FileReaderService} from "../../shared/services/file-reader.service";

// noinspection JSVoidFunctionReturnValueUsed
@Component({
  selector: 'app-family-graph',
  templateUrl: 'family-graph-page.component.html',
  styleUrls: ['family-graph-page.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    NgIf,
    CardComponent,
    RouterModule,
    FormsModule,
    ReactiveFormsModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class FamilyGraphPage implements OnInit, OnDestroy {
  slug = '';
  password = '';
  formLoading = false;
  graphLoaded = false;
  isGraphLoading = true;
  chart!: LazyOrgChart
  familyGraph!: FamilyGraph
  graph$!: Observable<FamilyGraph>
  @ViewChild('PointerArrow', {static: false}) PointerArrow!: ElementRef;
  protected readonly environment = environment;
  verifyForm = this.fb.group({
    password: ['', [Validators.required, Validators.minLength(1)]],
  });
  tutorialTimeoutID: any = null;
  appLottiePlayer: any = null;
  maleIcon: any = null;
  femaleIcon: any = null;
  constructor(
    @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
    public apiService: ApiService,
    private activatedRoute: ActivatedRoute,
    private fb: FormBuilder,
    public profileService: ProfileService,
    public storageService: StorageService,
    public toastController: ToastController,
    private navCtrl: NavController,
    private modalCtrl: ModalController,
    private viewContainerRef: ViewContainerRef,
    public platform: Platform,
    public genericService: GenericService,
    protected appDataService: AppDataService,
    protected fileReaderService: FileReaderService,
  ) {
    this.startListening()
  }

  ngOnInit(): void {
    if (this.isServer) {
      console.log('is server')
    } else {
      this.browserSetup();
    }

    this.appDataService.data$.subscribe((data) => {
      if (data!['appLottiePlayer']) {
        this.appLottiePlayer = data!['appLottiePlayer']
      }
    });
  }

  browserSetup() {
    // get query params using activated route
    this.activatedRoute.params.pipe(take(1)).subscribe(params => {
      this.slug = params['slug'];
      if (this.slug === 'me') {
        this.graph$ = this.apiService.getUserFamilyGraph();
        this.graph$.subscribe(async (data: any) => {
          this.familyGraph = data
          this.apiResponse(data, this)
        }, async (error) => {
          if (error.status === 401)
            this.apiService.logoutAction(window.location.pathname)
          else if (error.message !== 'token not found') {
            this.isGraphLoading = false
            await (await this.toastController.create({
              message: 'خطأ أثناء تحميل بيانات المشجرة يمكنك المحاولة لاحقاً!',
              duration: 5000,
              position: 'bottom',
              icon: 'alert-circle-outline',
              color: 'warning',
            })).present()
          }
        })
      } else if (this.slug === 'root') {
        this.graph$ = this.apiService.getRootFamilyGraph();
        this.graph$.subscribe(async (data: any) => {
          this.familyGraph = data
          this.apiResponse(data, this)
        }, async (error) => {
          if (error.status === 401)
            this.apiService.logoutAction(window.location.pathname)
          else {
            this.isGraphLoading = false
            await (await this.toastController.create({
              message: 'خطأ أثناء تحميل بيانات المشجرة يمكنك المحاولة لاحقاً!',
              duration: 5000,
              position: 'bottom',
              icon: 'alert-circle-outline',
              color: 'warning',
            })).present()
          }
        })
      } else if (this.slug === 'management-crew') {
        this.graph$ = this.apiService.getManagementCrewGraph();
        this.graph$.subscribe(async (data: any) => {
          this.familyGraph = data
          this.apiResponse(data, this)
        }, async (error) => {
          if (error.status === 401)
            this.apiService.logoutAction(window.location.pathname)
          else {
            this.isGraphLoading = false
            await (await this.toastController.create({
              message: 'خطأ أثناء تحميل بيانات المشجرة يمكنك المحاولة لاحقاً!',
              duration: 5000,
              position: 'bottom',
              icon: 'alert-circle-outline',
              color: 'warning',
            })).present()
          }
        })
      } else {
        this.graph$ = this.apiService.getFamilyGraph(this.slug);
        this.graph$.subscribe(async (data: any) => {
          this.familyGraph = data
          this.apiResponse(data, this)
        }, (error) => {
          this.navCtrl.navigateRoot('/home').then(async () => {
            const toast = await this.toastController.create({
              message: 'رابط المشجرة غير صحيح !',
              duration: 5000,
              position: 'bottom',
              icon: 'alert-circle-outline',
              color: 'warning',
            });
            await toast.present();
          })
        })
      }
    });
  }

  verifyPassword(): void {
    if (this.verifyForm.valid && this.verifyForm.controls.password.value) {
      this.password = this.verifyForm.controls.password.value
      this.formLoading = true;
      this.graph$ = this.apiService.getFamilyGraph(this.slug, this.verifyForm.controls.password.value)
      this.graph$.subscribe(async (data: any) => {
        this.familyGraph = data
        this.apiResponse(data, this)
      })
    }
  }

  private async apiResponse(data: any, $_this: any) {
    this.formLoading = false;
    this.graphLoaded = true;
    this.isGraphLoading = false;
    if (
      (await this.storageService.getItem('FamilyGraphTutorialPlayed')) !== 'true' &&
      this.slug !== 'management-crew'
    ) {
      this.tutorialTimeoutID = setTimeout(async () => {
        if (this.viewContainerRef.element.nativeElement.classList.contains('ion-page-hidden'))
          return
        const toast = await this.toastController.create({
          message: 'يمكنك استعراض التلمحيات بالضغط على أيقونة التعجب أعلى الشاشة!',
          duration: 20000,
          position: 'bottom',
          icon: 'alert-circle-outline',
          color: 'warning',
        });
        // await toast.present();
        // this.PointerArrow.nativeElement.style.display = 'block'
        toast.onWillDismiss().then(() => {
          this.PointerArrow.nativeElement.style.display = 'none'
        })
      }, 5000)
    }
    if (data.data && data.data.length > 0) {
      let draw = ($this: any) => {
        if (document.querySelector('#chartContainer') === null)
          setTimeout(draw, 50, $this)
        else
          $this.drawChart.bind($this)(data.data)
      }
      setTimeout(draw, 50, $_this)
    } else {

    }
  }

  private drawChart(data: any): void {
    const player = document.querySelector("lottie-player");

    this.chart = (new LazyOrgChart())
      // @ts-ignore
      .container('#chartContainer')
      .data(data)
      .rootMargin(100)
      // @ts-ignore
      .hasChildren((item: any) => item.children_count > 0)
      .loadChildren((item: any) => {
        if (this.appLottiePlayer) {
          // @ts-ignore
          document.getElementById('LoaderOverlay').style.visibility = 'visible';
          // @ts-ignore
          player.play()
        }
        return this.apiService.getFamilyGraphChildren(this.slug, item.id, this.password);
      })
      .afterUpdate(() => {
        if (this.appLottiePlayer) {
          // @ts-ignore
          document.getElementById('LoaderOverlay').style.visibility = 'hidden';
          // @ts-ignore
          player.stop()
        }
      })
      .nodeWidth((d: any) => 210)
      .nodeHeight((d: any) => 120)
      .childrenMargin((d: any) => 300)
      .compactMarginBetween((d: any) => 75)
      .compactMarginPair((d: any) => 80)
      .buttonContent((d: any) => {
        // get direct children count
        let childrenCount = 0
        if (d.node.data.children_count > 0)
          childrenCount = d.node.data.children_count
        else if (d.state.allNodes.filter((l: any) => l.parent?.id === d.node.id).length > 0)
          childrenCount = d.state.allNodes.filter((l: any) => l.parent?.id === d.node.id).length
        return `<div class="more"
                     style="width: 100%;height: 27px;text-align: center;border-radius: 0.0625rem 0 0.1875rem 0.1875rem;border-right: 1px solid var(--ion-color-secondary, #113665);border-bottom: 1px solid var(--ion-color-secondary, #113665);border-left: 1px solid var(--ion-color-secondary, #113665);background: #fff;box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25) inset;display: flex;justify-content: space-evenly;align-content: center;flex-wrap: nowrap;flex-direction: row;align-items: center;margin-top: -1px;"
                     data-has-children="${childrenCount > 0 ? 'true' : 'false'}">
                        <img style="width: 14px; top: 10px;" src="${environment.baseUrl}/v1/icons/people.svg"/>
                        <span style="color: var(--ion-color-secondary, #113665); text-align: center;margin-right: -5px;">${childrenCount}</span>
                    </div>`;
      })
      .linkUpdate(function (d: any, i: any, arr: any) {
        // @ts-ignore
        d3.select(this)
          .attr('stroke', (d: any) =>
            d.data._upToTheRootHighlighted ? '#152785' : 'lightgray'
          )
          .attr('stroke-width', (d: any) =>
            d.data._upToTheRootHighlighted ? 5 : 1.5
          )
          .attr('stroke-dasharray', '4,4');

        if (d.data._upToTheRootHighlighted) {
          // @ts-ignore
          d3.select(this).raise();
        }
      })
      .nodeContent((d: any, i: any, arr: any, state: any) => {
        const imageDim = 80;
        const membershipIcons = {
          'premium': `${environment.baseUrl}/v1/icons/membership-premium.svg`,
          'moshark': `${environment.baseUrl}/v1/icons/membership-moshark.svg`,
          'montseb': `${environment.baseUrl}/v1/icons/membership-montseb.svg`,
          '3amel': `${environment.baseUrl}/v1/icons/membership-3amel.svg`,
        }
        // @ts-ignore
        const badgeUrl = membershipIcons[d?.data?.membership] ?? null;
        const isDead = d.data.isDead ? `<p style="margin: 0;color: #DB5252; text-align: center; font-style: normal; font-weight: 700; line-height:1;">-${d.data.gender === 'MALE' ? 'رحمه الله' : 'رحمها الله'}-</p>` : '';
        const _title = d.data._title ? `<p style="margin: 0;color: #DBA852; text-align: center; font-style: normal; font-weight: 700; line-height:1;">${d.data._title}</p>` : '';

        let gender = (d.data.gender ?? 'male').toLowerCase();

        let fallbackURL = `/assets/d3-images/${gender}-icon.svg`;
        let fallbackIcon = gender === 'male' ? this.maleIcon : this.femaleIcon;
        let avatarImage = '';
        if (d.data.avatar)
          avatarImage = `<img src="${d.data.avatar}" onerror="this.src='${fallbackURL}'" style="margin-top:-${(imageDim / 2)}px;width:100%;border-radius:100px;max-width:${imageDim}px;height:${imageDim}px;" alt="user gender icon"/>`;
        else
          avatarImage = `<div class="avatar-img" style="margin-top:-${(imageDim / 2)}px;width:100%;border-radius:100px;max-width:${imageDim}px;height:${imageDim}px;">${fallbackIcon}</div>`;
        // noinspection CssInvalidPropertyValue
        return `<div class="card"
                style="width:${d.width}px;height:${d.height}px;display: flex;flex-direction: column;font-family: 'helvetica', Arial, sans-serif;border-radius: 0.5rem;border: 1px solid var(--ion-color-secondary, #113665);background: #fff;">
                  <div style="display: flex; justify-content: space-between; width: 100%; flex-direction: row; align-items: flex-start;">
                    ${badgeUrl ? `<img src="${badgeUrl}" style="margin-top:5px;margin-right:5px;width:30px;height:30px;" />` : '<span style="margin-top:5px;margin-right:5px;width:30px;height:30px;"></span>'}
                    ${avatarImage}
                    ${d.data.is_group_member ? `<img src="${environment.baseUrl}/v1/icons/group-membership.svg" style="width: 100%; float: left;margin-top:5px;margin-left:5px;max-width:35px;height:35px;" />` : '<span style="width: 100%; float: left;margin-top:5px;margin-left:5px;max-width:35px;height:35px;"></span>'}
                  </div>
                  <div style="top:${(imageDim / 2) + 'px'};height:auto;width:${d.width + 'px'};">
                    <div style="display: flex;flex-direction: column;color: var(--ion-color-secondary, #113665); text-align: center; font-size: 1rem; font-family: helvetica, Arial, sans-serif; font-style: normal; font-weight: 700;">
                       <span id="card-${d.data.id}">${d.data.name}</span>
                       ${_title}
                       ${isDead}
                       <p style="margin: 0; letter-spacing: 0.125rem;"> ${((d.data.parentId || d.data.parentNode) && d.data.family_user_id) ? d.data.family_user_id : ''}</p>
                    </div>
                    <div style="float:left;margin-left: 2px;margin-top: -${(d.data.isDead || d.data._title) ? 20 : 10}px;width: 54px;direction: ltr;">
                      ${d.data.hasPhone ? `<img src="${environment.baseUrl}/v1/icons/verified-phone.svg" style="width: 100%;max-width:25px;height:25px;" />` : ''}
                      ${d.data.is_researcher ? `<img src="${environment.baseUrl}/v1/icons/researcher.svg" style="width: 100%;max-width:25px;height:25px;" />` : ''}
                    </div>
                    ${d.data.voluntaries > 0 ? (`<div style="float:right;margin-right: 2px;margin-top: -${(d.data.isDead || d.data._title) ? 20 : 10}px;direction: ltr;">
                      <img src="${environment.baseUrl}/v1/icons/voluntary.svg" style="max-width:25px;height:25px;"/>
                       <div style="display: inline-block">
                        <span style="${
                          `font-size: ${d.data.voluntaries > 999 ? 7 : 8}px;width: ${d.data.voluntaries > 999 ? 16 : 12}px;color: #DBA852; font-family: Tajawal; font-weight: 700; line-height: 0; height: 12px; margin-left: -${d.data.voluntaries > 999 ? 19 : 16}px; margin-top: 24px;background: #ffff;border-radius: 10px;text-align: center;padding: 2px;`
                        }">${d.data.voluntaries}</span>
                       </div>
                    </div>`) : ''}
                  </div>
               </div>`;
      })
      .onNodeClick(async (userId: any) => {
        // @ts-ignore
        const user = this.chart.data().find((user: any) => user.id === userId)
        await this.openModal(user)
      })
      .initialZoom(0.5)
      .render()

    let baseSvg = d3.select("#chartContainer svg")
    let svgGroup = baseSvg.selectChild("g");
    setTimeout(function () {
      svgGroup.selectAll("g.node").each(function (d: any) {
        if (d.data.parentNode === true)
          centerNode(d);
      })
    }, 100)

    function zoom(event: any) {
      if (event.transform != null)
        svgGroup.attr("transform", event.transform);
    }

    function centerNode(source: any) {
      // @ts-ignore
      let t = d3.zoomTransform(baseSvg.node()),
        y = ((-source.y0 + window.innerHeight - 500) * t.k) / 2,
        x = ((-source.x0) * t.k) / 2,
        zoomListener = d3.zoom().scaleExtent([0.1, 3]).on("zoom", zoom);
      d3.select('svg').transition().duration(750)
        // @ts-ignore
        .call(zoomListener.transform, d3.zoomIdentity.translate(x, y).scale(t.k));
    }
  }

  async openModal(userInfo: any) {
    const modal = await this.modalCtrl.create({
      component: UserProfilePage,
      initialBreakpoint: 0.90,
      breakpoints: [0, 0.90, 1],
      componentProps: {
        'user': userInfo,
        'currentUser': this.profileService.$user,
      }
    });
    await modal.present();
    const {data, role} = await modal.onWillDismiss();
    if (role === 'confirm') {
      this.graph$.subscribe(async (data: any) => this.apiResponse(data, this))
    }
  }

  openFamilyTreeTutorial() {
    try {
      this.toastController.dismiss()
    } catch {
    }
    if (this.tutorialTimeoutID)
      clearTimeout(this.tutorialTimeoutID)
    this.modalCtrl.create({
      component: FamilyTreeTutorial,
    }).then(async (modal) => {
      modal.present().then();
      const {data, role} = await modal.onWillDismiss();
      if (role !== 'backdrop')
        this.storageService.setItem('FamilyGraphTutorialPlayed', 'true')
    })
  }

  openFamilyGraphTutorialVideo() {
    this.modalCtrl.create({
      component: FamilyTreeTutorial,
    }).then(async (modal) => {
      modal.present().then();
      await modal.onWillDismiss();
      this.storageService.setItem('FamilyGraphTutorialPlayed', 'true')
    })
  }

  protected readonly window = window;

  private async startListening(): Promise<void> {
    try {
      this.maleIcon = await this.fileReaderService.readFile('/assets/d3-images/male-icon.svg');
      this.femaleIcon = await this.fileReaderService.readFile('/assets/d3-images/female-icon.svg');
    } catch (error) {
    }
    window.addEventListener("beforeunload", this.ngOnDestroy.bind(this))
    const _this = this
    let observer = new MutationObserver(function (event) {
      // @ts-ignore
      if (event[0].target.classList.contains('ion-page-hidden')) {
        _this.PointerArrow.nativeElement.style.display = 'none'
        _this.toastController.dismiss()
      }
    })
    observer.observe(this.viewContainerRef.element.nativeElement, {
      attributes: true,
      attributeFilter: ['class'],
      childList: false,
      characterData: false
    })
  }

  private stopListening(): void {
    window.removeEventListener("beforeunload", this.ngOnDestroy.bind(this))
  }

  ngOnDestroy(): void {
    this.stopListening()
    this.disableTutorial()
    this.viewContainerRef
      .element
      .nativeElement
      .parentElement
      .removeChild(this.viewContainerRef.element.nativeElement)
  }

  disableTutorial(): void {
    this.stopListening()
    try {
      if (this.tutorialTimeoutID)
        clearTimeout(this.tutorialTimeoutID)
    } catch {
    }
    try {
      this.toastController.dismiss()
    } catch {
    }
  }
}
