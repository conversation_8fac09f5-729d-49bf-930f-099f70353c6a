// @ts-nocheck

import * as d3 from 'd3';
import {OrgChart} from './d3-org-chart';

export class <PERSON><PERSON><PERSON>rg<PERSON>hart extends OrgChart<any> {
  private getLazyLoadingAttrs: () => {
    hasChildren: (d: any) => boolean;
    loadChildren: (d: any) => Promise<Awaited<any[]>>;
    afterUpdate: () => void
  };

  constructor() {
    super();
    const l = {
      loadChildren: () => Promise.resolve([]),
      hasChildren: (): boolean => false,
      afterUpdate: () => {
      },
    };
    this.getLazyLoadingAttrs = () => l;

    Object.keys(l).forEach((k) => {
      // @ts-ignore
      this[k] = function (t: any) {
        // @ts-ignore
        return t ? (l[k] = t, this) : l[k];
      };
    });
  }


  addNodes(nodes: any) {
    for (const obj of nodes) {

      const attrs = this.getChartState();

      if (attrs.allNodes.some(({data}) => attrs.nodeId(data) === attrs.nodeId(obj))) {
        console.log(`ORG CHART - ADD - Node with id "${attrs.nodeId(obj)}" already exists in tree`);
        return this;
      }

      if (!attrs.allNodes.some(({data}) => attrs.nodeId(data) === attrs.parentNodeId(obj))) {
        console.log(`ORG CHART - ADD - Parent node with id "${attrs.parentNodeId(obj)}" not found in the tree`);
        return this;
      }
      if (obj._centered && !obj._expanded)
        obj._expanded = true;
      if (!attrs.data)
        attrs.data = []
      attrs.data.push(obj);
    }

    this.updateNodesState();
    return this;
  }


  override onButtonClick(e: any, d: any) {
    const attrs = this.getLazyLoadingAttrs();
    if (!d.children && !d._children && d.data && attrs.hasChildren(d.data)) {
      attrs.loadChildren(d.data)
        .then((nodes: any) => {
          this.addNodes(nodes.map((node: any) => {
            node._expanded = true;
            return node;
          }));
          super.onButtonClick(e, d);
        });
    } else {
      super.onButtonClick(e, d);
    }
  }


  override update(params: any) {
    let _a, _b, _c;
    super.update(params);
    (_b = (_a = this.getLazyLoadingAttrs()).afterUpdate) === null || _b === void 0 ? void 0 : _b.call(_a);
    const container = d3.select(this.getChartState().container).node();
    if (container) { // @ts-ignore
      for (const node of container.querySelectorAll(".node-button-g")) {
        node.setAttribute('transform', 'translate(105,140)')
        if ((_c = node.querySelector(".node-button-div")) === null || _c === void 0 ? void 0 : _c.childElementCount) {
          if (node.querySelectorAll('.more[data-has-children=true]').length > 0) {
            node.removeAttribute("display");
            node.removeAttribute("opacity");
          }
        }
      }
    }
  }
  ;
}
