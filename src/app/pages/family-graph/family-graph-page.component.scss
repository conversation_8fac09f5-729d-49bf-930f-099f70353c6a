#chartContainer {
  font-family: 'helvetica', <PERSON><PERSON>, sans-serif;
  width: 100%;
  height: 100%;
}

:host ::ng-deep #chartContainer svg {
  width: 100%;
  height: 100%;
  background: var(--ion-background-color);
  -webkit-transform: translate3d(0, 0, 0) !important;
  -ms-transform: translate3d(0, 0, 0) !important;
  transform: translate3d(0, 0, 0) !important;
  user-select: none;
  -ms-user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
}

#loader {
  position: absolute;
  left: calc(50% - 150px);
  top: calc(50% - 150px);
  z-index: 2;
  width: 300px;
  height: 300px;
}

.overlay {
  z-index: 1;
  position: fixed;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  visibility: hidden;
}

:host ::ng-deep .avatar-img svg,
:host ::ng-deep .card svg,
:host ::ng-deep .more svg {
  background: unset !important;
}

/*:host ::ng-deep {
  .node-button-div {
    height: 26px !important;
  }

  .node-button-foreign-object,
  .node-button-rect,
  .node-button-g {
    height: 0.005px !important;
  }
}*/

.slide-top {
  position: absolute;
  display: none;
  left: calc(50% - (min(370px, 100vw) / 2) + 0.9rem); //2rem;//31px
  z-index: 99;
  -webkit-animation: slide-top 1s cubic-bezier(0.250, 0.460, 0.450, 0.940) infinite alternate;
  animation: slide-top 1s cubic-bezier(0.250, 0.460, 0.450, 0.940) infinite alternate;
}

/**
 * ----------------------------------------
 * animation slide-top
 * ----------------------------------------
 */
@-webkit-keyframes slide-top {
  0% {
    -webkit-transform: translateY(25px);
    transform: translateY(25px);
  }
  100% {
    -webkit-transform: translateY(-25px);
    transform: translateY(-25px);
  }
}

@keyframes slide-top {
  0% {
    -webkit-transform: translateY(25px);
    transform: translateY(25px);
  }
  100% {
    -webkit-transform: translateY(-25px);
    transform: translateY(-25px);
  }
}

