  <ng-container *ngIf="!isGraphLoading; else spinner">
    <app-header [hideProfileIcon]="true" [hideLogo]="false" [showBackButton]="true"
                logoSize="40px"
                backTo="/home">
      <ion-button left fill="clear" class="ion-justify-content-start"
                  (click)="openFamilyTreeTutorial()"
                  color="secondary"
                  style="position: absolute; left: -10px;">
        <ion-icon name="alert-circle-outline" slot="icon-only"></ion-icon>
      </ion-button>
    </app-header>
    <ion-content>
      <div class="slide-top" #PointerArrow>
        <ion-icon name="arrow-up-outline" style="font-size: 32px;"></ion-icon>
      </div>
      <ng-container *ngIf="graphLoaded">
        <div class="overlay" id="LoaderOverlay">
          <lottie-player [src]="appLottiePlayer" id="loader"
                         background="transparent" speed="1" style="width: 300px; height: 300px;" loop></lottie-player>
        </div>
        <div *ngIf="(familyGraph.data ?? []).length > 0; else errorsTemplate"
             id="chartContainer" style="width: 100%;height: 100%">
        </div>
      </ng-container>
      <ion-chip color="warning" *ngIf="!genericService.isCapacitorApp()"
                class="ion-margin-bottom" style="position: absolute; bottom: 10px;right: 10px;">
        <ion-label>إطلاق تجريبي</ion-label>
      </ion-chip>
      <ng-template #errorsTemplate>
        <ion-grid [fixed]="true">
          <app-card [showExpandButton]="false" [scrollable]="false">
            <div *ngIf="familyGraph.action === 'expired'">
              <ion-text color="danger">
                <h5 style="font-size: 0.7rem">
                  رابط المشاركة منتهي !
                </h5>
              </ion-text>
              <!--<ion-item-divider></ion-item-divider>-->
              <a [routerLink]="'/home'">الصفحة الرئيسية</a>
            </div>
            <div *ngIf="familyGraph.action === 'profile-update'" [formGroup]="verifyForm">
              <div style="text-align:center">
                <img src="/assets/images/icons/digital-tree.svg" style="height: 310px" class="ion-margin-bottom"/>
                <ion-text color="primary">
                  <h2 class="ion-no-margin">
                    <b>

                      أوشكنا على الإنتهاء من مشجرتك!
                    </b>

                  </h2>
                </ion-text>
                <ion-text color="secondary">
                  <strong>
                    <h2 class="">
                      كرماً أكمل معلوماتك الشخصية
                    </h2>
                  </strong>
                </ion-text>
                <ion-button color="secondary" class="ion-margin"
                            [routerLink]="'/me/edit'" [queryParams]="{returnURL: window.location.pathname}">
                  التالي
                </ion-button>
              </div>
            </div>
            <div *ngIf="familyGraph.action === 'password'" [formGroup]="verifyForm">
              <ion-text color="primary">
                <h2 style="font-size: 1.3rem">أدخل رمز التحقق</h2>
              </ion-text>
              <ion-input formControlName="password" [disabled]="formLoading"
                         style="margin: 0 7px;max-width: calc(100% - 20px);text-align: center;direction: ltr; border-bottom: #7d7d7d solid 1px;"
                         placeholder="1234"></ion-input>
              <ion-button (click)="verifyPassword()"
                          style="margin: 10px 7px;max-width: calc(100% - 20px);min-width: 7rem;"
                          mode="ios" color="secondary" expand="block"
                          [disabled]="formLoading">
                <ion-spinner *ngIf="formLoading"></ion-spinner>
                <span *ngIf="!formLoading">
            تحقق
            </span>
              </ion-button>
            </div>
          </app-card>
        </ion-grid>
      </ng-template>
    </ion-content>
  </ng-container>
<ng-template #spinner>
  <div class="ion-flex ion-justify-content-center">
    <ion-spinner size="large" name="crescent" style="width: 70px; height: 70px"></ion-spinner>
  </div>
</ng-template>

