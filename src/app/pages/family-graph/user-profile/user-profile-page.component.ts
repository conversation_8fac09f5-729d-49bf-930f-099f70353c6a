import {ActionSheetController, IonicModule, ModalController} from "@ionic/angular";
import {Component, ElementRef, OnInit, ViewChild} from "@angular/core";
import {FormsModule} from "@angular/forms";
import {Async<PERSON>ipe, NgIf} from "@angular/common";
import {Router, RouterLink} from "@angular/router";
import {ProfileService} from "../../../shared/services/profile.service";
import {ImgFallbackDirective} from "../../../img-fallback.directive";
import {User} from "../../../shared/models";
import {ProfileImageUpload} from "../../profile/profile-image-upload";
import {ApiService} from "../../../shared/services/api.service";
import {StorageService} from "../../../shared/services/storage.service";
import {Observable} from "rxjs";
import {AppDataService} from "../../../shared/services/app-data.service";

@Component({
  selector: 'app-user-profile-page',
  templateUrl: 'user-profile-page.component.html',
  styleUrls: ['user-profile-page.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    FormsModule,
    AsyncPipe,
    NgIf,
    RouterLink,
    ImgFallbackDirective,
  ],
})
export class UserProfilePage extends ProfileImageUpload implements OnInit {
  public user = {
    id: 0,
    name: '',
    avatar: '',
    gender: '',
    family_user_id: '',
    full_name: '',
    membership_package_id: null,
    bio: '',
  };
  public currentUser!: Observable<User | undefined>;
  avatarChanged = false;
  isSameUser = false;
  collectingFormUrl: string | null = null;
  @ViewChild('fileInput', {static: false}) fileInput!: ElementRef;

  constructor(
    _apiService: ApiService,
    _storageService: StorageService,
    public _profileService: ProfileService,
    _modalCtrl: ModalController,
    _actionSheetCtrl: ActionSheetController,
    public appDataService: AppDataService,
    private router: Router,
  ) {
    super(_apiService, _storageService, _profileService, _modalCtrl, _actionSheetCtrl)
    this.appDataService.data$.subscribe(data => {
      this.collectingFormUrl = (data['collectingFormUrl'] as string) || null
    })
  }

  ngOnInit(): void {
    console.log(this.user);
    this.currentUser.subscribe(usr => {
      this.isSameUser = usr?.id === this.user.id
    })
  }

  cancel() {
    return this.modalCtrl.dismiss(null, this.avatarChanged ? 'confirm' : 'cancel');
  }

  override getFileInput(): ElementRef<any> {
    return this.fileInput;
  }

  override afterSuccess(usr: User, action: string) {
    this.user.avatar = usr.profile_photo_url;
    this.avatarChanged = true;
  }

  public getUserAvatar() {
    return this.user.gender ? ('assets/d3-images/' + this.user.gender.toLowerCase() + '-icon.svg') : '/assets/icon/logo.svg'
  }

  protected readonly window = window;

  segmentChanged($event: MouseEvent) {

  }

  // Navigate to gift membership flow
  giftMembership() {
    if (!this.user.family_user_id || !this.user.full_name) {
      console.error('Cannot gift membership: missing user data', {
        familyUserId: this.user.family_user_id,
        fullName: this.user.full_name
      });
      return;
    }

    const recipientId = this.user.family_user_id;
    const recipientName = encodeURIComponent(this.user.full_name);

    console.log('Navigating to gift membership for:', {
      recipientId,
      recipientName: this.user.full_name,
      encodedName: recipientName
    });

    // Close the modal first
    this.cancel().then(() => {
      // Navigate to members page with auto-gift parameters
      this.router.navigate(['/members'], {
        queryParams: {
          recipientId: recipientId,
          recipientName: recipientName
        }
      }).then(() => {
        console.log('Successfully navigated to gift membership page');
      }).catch(error => {
        console.error('Error navigating to gift membership page:', error);
      });
    });
  }

  // Navigate to self-subscription
  subscribeSelf() {
    console.log('Navigating to self-subscription for current user');

    // Close the modal first
    this.cancel();

    // Navigate to members page for self-subscription
    this.router.navigate(['/members']).then(() => {
      console.log('Successfully navigated to members page for self-subscription');
    }).catch(error => {
      console.error('Error navigating to members page:', error);
    });
  }
}
