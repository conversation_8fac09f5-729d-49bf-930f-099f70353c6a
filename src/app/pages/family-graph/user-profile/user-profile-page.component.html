<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button color="medium" fill="clear" (click)="cancel()">
        <ion-icon slot="icon-only" name="close-circle-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title color="light">
      <ion-text color="primary">
        {{user.name}}
      </ion-text>
    </ion-title>
    <ion-buttons slot="end" *ngIf="isSameUser">
      <ion-button color="medium"
                  [routerLink]="'/me/edit'"
                  [queryParams]="{returnURL: window.location.pathname}"
                  (click)="cancel()">تعديل
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>
<ion-content class="ion-padding">
  <ion-grid [fixed]="true" class="ion-flex-column">
    <div class="profile-card ion-padding-horizontal ion-margin-bottom">
      <input #fileInput type="file" (change)="openCropperDialog($event)" accept="image/*" hidden>
      <ion-grid>
        <ion-row>
          <ion-col>
            <ion-avatar style="margin: auto;width: 90px; height: 90px;">
              <div class="AvatarContainer">
                @let fallbackURL = ('/assets/d3-images/' + (user.gender ?? 'male').toLowerCase() + '-icon.svg') ;
                @let fallbackIcon = ((user.gender ?? 'male').toLowerCase() + '-icon') ;
                @if (user.avatar) {
                  <img
                    [src]="user.avatar ?? fallbackURL"
                    [appImgFallback]="fallbackURL"
                    disabled="avatarUpdating"
                    width="120" height="120">
                } @else {
                  <ion-icon slot="start" [name]="fallbackIcon" style="font-size: 114px;"></ion-icon>
                }
                <div style="position: absolute;"
                     *ngIf="(_profileService.$user|async)?.can_update_picture && isSameUser && (_profileService.$user|async)?.gender === 'MALE' && !avatarUpdating"
                     id="avatarMenu" (click)="openAvatarActionsSheet()">
                  <ion-icon name="camera-outline" color="primary"></ion-icon>
                </div>
                <ion-spinner *ngIf="avatarUpdating"></ion-spinner>
              </div>
              <!--<div class="AvatarContainer">
                <img [ngSrc]="user.avatar || getUserAvatar()"
                     [appImgFallback]="getUserAvatar()"
                     width="160" height="160" style="max-width: unset">
                <div style="position: absolute;" id="avatarMenu"
                     *ngIf="(_profileService.$user|async)?.can_update_picture && isSameUser && user.gender === 'MALE' && !avatarUpdating"
                     (click)="openAvatarActionsSheet()">
                  <ion-icon name="camera-outline" color="primary"></ion-icon>
                </div>
                <ion-spinner *ngIf="avatarUpdating"></ion-spinner>
              </div>-->
            </ion-avatar>
          </ion-col>
        </ion-row>

        <ion-row>
          <ion-col class="ion-no-padding">
            <ion-label class="ion-text-wrap ion-text-center">
              <ion-text color="secondary">
                <h5 style="line-height: 1.7; font-weight: bolder; letter-spacing: 4px">{{user.family_user_id}}</h5>
              </ion-text>
              <h4 style="line-height: 1.7; font-weight: bolder">{{user.full_name}}</h4>
            </ion-label>
          </ion-col>
        </ion-row>

        <ion-row class="ion-margin-bottom ion-margin-top">
          <ion-col class="ion-no-padding">
            <ion-label class="ion-text-wrap ion-text-center">
              <h5 style="line-height: 1.7;">{{user.bio}}</h5>
            </ion-label>
          </ion-col>
        </ion-row>

        <!-- Membership Status and Action Buttons -->
        <ion-row class="ion-margin-top">
          <ion-col class="ion-no-padding">
            <!-- User does not have membership -->
            <div *ngIf="!(user.membership_package_id !== null && user.membership_package_id !== undefined)" class="membership-actions-section">

              <!-- Gift Membership Button (for other users) -->
              <ion-button *ngIf="!isSameUser"
                          expand="block"
                          color="primary"
                          size="large"
                          fill="clear"
                          (click)="giftMembership()"
                          class="membership-action-button">
                <ion-icon name="gift" slot="start"></ion-icon>
                إهداء عضوية
              </ion-button>

              <!-- Subscribe Button (for own profile) -->
              <ion-button *ngIf="isSameUser"
                         expand="block"
                         color="secondary"
                         size="large"
                         (click)="subscribeSelf()"
                         class="membership-action-button">
                <ion-icon name="card" slot="start"></ion-icon>
                اخْتَرْ عُضْوِيَّتَكَ الآنَ
              </ion-button>
            </div>

          </ion-col>
        </ion-row>
      </ion-grid>
    </div>

    <ion-segment #tabs color="medium" value="articles" style="max-width: 600px;">
      <ion-segment-button value="articles" (click)="segmentChanged($event)">
        <h5 class="ion-text-wrap w-100 ion-no-margin" style="font-size: 0.76rem !important;">الوثائق والمقالات</h5>
      </ion-segment-button>
      <ion-segment-button value="activities" (click)="segmentChanged($event)">
        <h5 style="font-size: 0.76rem !important;" class="ion-text-wrap w-100 ion-no-margin">البرامج والأنشطة</h5>
      </ion-segment-button>
      <ion-segment-button value="literature" (click)="segmentChanged($event)">
        <h5 style="font-size: 0.76rem !important;" class="ion-text-wrap w-100 ion-no-margin">المؤلفات</h5>
      </ion-segment-button>
    </ion-segment>
    <ion-list style="width: 100%; max-width: 600px; margin-top: 6px;" class="">
      <ion-item lines="none">
      </ion-item>
      <ion-item lines="none">
        <ion-label *ngIf="tabs.value === 'articles' && collectingFormUrl !== null">
          <h6 class="ion-text-wrap ion-text-center">
            ماذا تعرف عن {{user.full_name}}؟
            <br>
            تعال حدّثنا {{user.gender === 'MALE' ? 'عنه' : 'عنها'}} هنا:
            <br>
            <a [href]="collectingFormUrl" target="_blank">{{ collectingFormUrl }}</a>
          </h6>
        </ion-label>
        <ion-label *ngIf="tabs.value !== 'articles' || !collectingFormUrl">
          <h6 class="ion-text-wrap ion-text-center">
            كن بالقرب..
          </h6>
        </ion-label>
      </ion-item>
      <ion-item lines="none">
      </ion-item>
    </ion-list>
  </ion-grid>
</ion-content>
