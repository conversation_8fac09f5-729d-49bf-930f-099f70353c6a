
.profile-card {
  border-radius: 8px;
  background: var(--ion-color-light);
  min-height: 200px;
  width: 100%;
  max-width: 600px;
}

.AvatarContainer {
  height: 100%;
  width: 100%;
  border-radius: 50%;
  border-style: solid;
  position: relative;

  img {
    height: 100%;
    width: 100%;
    max-width: unset;
    border-radius: 50%;
  }

  ion-spinner {
    position: absolute;
    top: calc(50% - 15px);
    right: calc(50% - 15px);
    height: 30px;
    width: 30px;
  }

  div {
    position: absolute;
    top: 10px;
    right: -8px;
    border-radius: 50%;
    height: 30px;
    width: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--ion-color-light);
    box-shadow: 0 0 8px 1px #B8B8B8;
    cursor: pointer;
    font-size: 20px !important;
  ;
  }
}

// Membership Status and Actions Styles
.membership-status-section {
  .membership-card {
    margin: 0;
    border-radius: 12px;
    border: 2px solid var(--ion-color-success);
    background: var(--ion-color-success-tint);

    ion-card-content {
      padding: 12px;
    }

    ion-item {
      --padding-start: 0;
      --inner-padding-end: 0;
      --background: transparent;

      ion-label {
        h3 {
          color: var(--ion-color-success-shade);
          font-weight: 600;
          margin-bottom: 4px;
        }

        p {
          color: var(--ion-color-success-shade);
          margin: 0;
          font-size: 0.9rem;
        }
      }

      ion-badge {
        font-weight: 600;
        font-size: 0.8rem;
      }
    }
  }
}

.membership-actions-section {
  .membership-action-button {
    --border-radius: 12px;
    height: 48px;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 8px;

    &[color="primary"] {
      --background: var(--ion-color-primary);
      --background-hover: var(--ion-color-primary-shade);
      --background-activated: var(--ion-color-primary-shade);
    }

    &[color="secondary"] {
      --background: var(--ion-color-secondary);
      --background-hover: var(--ion-color-secondary-shade);
      --background-activated: var(--ion-color-secondary-shade);
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .membership-actions-section {
    .membership-action-button {
      height: 44px;
      font-size: 0.95rem;
    }
  }

  .membership-status-section {
    .membership-card {
      ion-item {
        ion-label {
          h3 {
            font-size: 0.95rem;
          }

          p {
            font-size: 0.85rem;
          }
        }
      }
    }
  }
}
