import {IonicModule, IonicSlides, ModalController} from "@ionic/angular";
import {Component, CUSTOM_ELEMENTS_SCHEMA, ElementRef, OnInit, ViewChild} from "@angular/core";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, DatePipe, NgIf, NgOptimizedImage, NgTemplateOutlet} from "@angular/common";
@Component({
  selector: 'app-family-tree-tutorial',
  templateUrl: 'family-tree-tutorial.component.html',
  styleUrls: ['family-tree-tutorial.component.scss'],
  standalone: true,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [
    IonicModule,
    NgIf,
    NgTemplateOutlet,
    ReactiveFormsModule,
  ],
})
export class FamilyTreeTutorial implements OnInit {
  @ViewChild('swiper')
  swiperModules = [IonicSlides];

  constructor(
    public modalCtrl: ModalController,
  ) {
  }

  ngOnInit(): void {
  }
}
