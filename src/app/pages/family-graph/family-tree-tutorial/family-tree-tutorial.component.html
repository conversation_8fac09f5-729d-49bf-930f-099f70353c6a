<ion-header>
<!--  <ion-toolbar>-->
<!--    <ion-buttons slot="start">-->
<!--      <ion-button expand="block" color="secondary"-->
<!--                  [disabled]="(swiperRef?.nativeElement?.swiper?.activeIndex ?? 0) === 0"-->
<!--                  (click)="swiperRef?.nativeElement?.swiper?.slidePrev()">-->
<!--        الخلف-->
<!--      </ion-button>-->
<!--    </ion-buttons>-->
<!--    <ion-title>تلميحات</ion-title>-->
<!--    <ion-buttons slot="end">-->
<!--      <ion-button expand="block" color="secondary"-->
<!--                  *ngIf="(swiperRef?.nativeElement?.swiper?.activeIndex ?? 0) < 2"-->
<!--                  (click)="swiperRef?.nativeElement?.swiper?.slideNext()">-->
<!--        التالي-->
<!--      </ion-button>-->
<!--      <ion-button expand="block"-->
<!--                  *ngIf="(swiperRef?.nativeElement?.swiper?.activeIndex ?? 0) === 2"-->
<!--                  (click)="modalCtrl.dismiss()" color="medium">-->
<!--        إنهاء-->
<!--      </ion-button>-->
<!--    </ion-buttons>-->
<!--  </ion-toolbar>-->
</ion-header>
<ion-content>
<!--  <swiper-container-->
<!--    allow-slide-next="false"-->
<!--    allow-slide-prev="false"-->
<!--    #swiper-->
<!--    [modules]="swiperModules"-->
<!--    dir="rtl"-->
<!--    class="ion-margin swiper">-->
<!--    <swiper-slide>-->
<!--      <div class="ion-align-items-center ion-align-self-center ion-justify-content-center" style="display: flex;">-->
<!--        <div class="wistia_responsive_padding" style="padding:177.78% 0 0 0;position:relative;/*width: 100%;*/width: calc(100% - 4rem);"><div class="wistia_responsive_wrapper" style="height:100%;left:0;position:absolute;top:0;width:100%;">-->
<!--          <div class="wistia_embed wistia_async_n34fh27t0r seo=false videoFoam=true autoPlay=true muted=true fakeFullScreen=true fitStrategy=contain" style="height:100%;position:relative;width:100%">&nbsp;</div>-->
<!--        </div></div>-->
<!--        &lt;!&ndash;<div class="wistia_embed wistia_async_3e5oem00lz seo=true videoFoam=false" style="height:533px;position:relative;width:300px"><div class="wistia_swatch" style="height:100%;left:0;opacity:0;overflow:hidden;position:absolute;top:0;transition:opacity 200ms;width:100%;"><img src="https://fast.wistia.com/embed/medias/3e5oem00lz/swatch" style="filter:blur(5px);height:100%;object-fit:contain;width:100%;" alt="" aria-hidden="true" onload="this.parentNode.style.opacity=1;" /></div></div>&ndash;&gt;-->
<!--      </div>-->
<!--    </swiper-slide>-->
<!--    <swiper-slide>-->
<!--      <div class="ion-align-items-center ion-align-self-center ion-justify-content-center" style="display: flex;">-->
<!--        <ng-container *ngTemplateOutlet="slide1">-->
<!--        </ng-container>-->
<!--      </div>-->
<!--    </swiper-slide>-->
<!--    <swiper-slide>-->
<!--        <div class="ion-align-items-center ion-align-self-center ion-justify-content-center" style="display: flex;">-->
<!--          <ng-container *ngTemplateOutlet="slide2">-->
<!--          </ng-container>-->
<!--        </div>-->
<!--    </swiper-slide>-->
<!--  </swiper-container>-->
</ion-content>
<ng-template #slide1>
  <ion-list style="width: 100%;" class="ion-margin-horizontal ">
    <ion-item lines="none" class="">
      <img style="max-width: 250px; margin: auto"
           src="assets/images/family-tree/family-tree-tutorial-1.png"
           alt="تعريف بالبطاقة الشخصية">
    </ion-item>
  </ion-list>
</ng-template>
<ng-template #slide2>
  <ion-list style="width: 100%;" class="ion-margin-horizontal icons-list">
    <ion-item lines="none" class="">
      <img style="max-width: 250px;padding: 5px 0; margin: auto" src="assets/images/family-tree/node-example.svg"
           alt="تعريف بالبطاقة الشخصية">

    </ion-item>
    <ion-item lines="none" style="width: 100%;">
      <img src="/assets/d3-images/membership.svg" class="ion-margin-end" style="width: 32px;"/>
      أيقونة مشترك في عضوية الصندوق
    </ion-item>

    <ion-item lines="none" style="width: 100%;">
      <img src="/assets/d3-images/researcher.svg" class="ion-margin-end" style="width: 32px;"/>
      أيقونة باحث إجتماعي
    </ion-item>
    <ion-item lines="none" style="width: 100%;">
      <!--<img src="/assets/d3-images/voluntary.svg" class="ion-margin-end" style="width: 32px;"/>-->
      <div style="direction: ltr;" class="ion-margin-start">
        <img src="/assets/d3-images/voluntary.svg" style="max-width:32px;height:32px;"/>
        <div style="display: inline-block">
           <span
             style="font-size: 12px;color: #DBA852;font-family: Tajawal;font-weight: 700;margin-left: -15px;background: #ffff;border-radius: 25px;text-align: center;padding: 4px;">
             12
           </span>
        </div>
      </div>
      أيقونة ساعات التطوع
    </ion-item>
    <ion-item lines="none" style="width: 100%;">
      <img src="/assets/d3-images/group-membership.svg" class="ion-margin-end" style="width: 32px;"/>
      أيقونة عضو لجنة
    </ion-item>
    <ion-item lines="none" style="width: 100%;">
      <img src="/assets/d3-images/verified-phone.svg" class="ion-margin-end" style="width: 32px;"/>
      أيقونة توثيق رقم الجوال
    </ion-item>
  </ion-list>
</ng-template>
