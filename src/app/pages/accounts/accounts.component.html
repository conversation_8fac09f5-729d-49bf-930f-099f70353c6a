<app-header [hideProfileIcon]="true" [showBackButton]="true"></app-header>
<ion-content [fullscreen]="false">
  <ion-header>
    <ion-toolbar>
      <ion-segment [(ngModel)]="accountDisplayMode" (ionChange)="updateAccountDisplayMode($event)">
        <ion-segment-button value="accountNumber">رقم الحساب</ion-segment-button>
        <ion-segment-button value="ibanNumber">رقم الآيبان</ion-segment-button>
      </ion-segment>
    </ion-toolbar>
  </ion-header>
  <ion-list class="ion-margin-top">
    <ion-spinner *ngIf="loading" style="margin: auto; width: 100%;height: 40px;"></ion-spinner>
    <ion-item *ngIf="!loading && accounts.length === 0">
      <ion-text color="danger">لا يوجد حسابات بنكية لعرضها في الوقت الحالي</ion-text>
    </ion-item>
    <ion-item [detail]="false" lines="full" *ngFor="let oneAccount of accounts;">
      <ion-img slot="start"
               style="width:40px"
               [src]="oneAccount.logo"></ion-img>
      <ion-label>
        <h2 style="line-height: 1.7 !important; text-wrap: pretty; color: var(--ion-text-color)" >{{ oneAccount.title }}</h2>
        <h4 class="" style="line-height: 1.7 !important; color: var(--ion-text-color)">
         {{ accountDisplayMode === 'ibanNumber' ? oneAccount.iban : oneAccount.account }}
        </h4>
      </ion-label>
      <ion-button fill="clear" size="small" slot="end"
                  (click)="copyThis(accountDisplayMode === 'ibanNumber' ? oneAccount.iban : oneAccount.account)">
        <ion-icon name="copy-outline" slot="icon-only" style="font-size: 20px; fill: var(--ion-text-color); stroke: var(--ion-text-color);color: var(--ion-text-color)"></ion-icon>
      </ion-button>
    </ion-item>
  </ion-list>
</ion-content>
