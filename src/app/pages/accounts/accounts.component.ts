import {ChangeDetectorRef, Component, OnInit} from '@angular/core';
import {NgForOf, NgIf} from "@angular/common";
import {AlertController, IonicModule, Platform, ToastController} from "@ionic/angular";
import {HeaderComponent} from 'src/app/layout/header/header.component';
import {ApiService} from "../../shared/services/api.service";
import {take, tap} from "rxjs";
import {FormsModule} from "@angular/forms";
import {BankAccount} from "../../shared/models";
import {Clipboard} from '@angular/cdk/clipboard';

@Component({
  selector: 'app-accounts',
  templateUrl: 'accounts.component.html',
  styleUrls: ['accounts.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    NgIf,
    NgForOf,
    FormsModule,
  ],
})
export class AccountsComponent implements OnInit {

  loading = true;
  accounts: BankAccount[] = [];
  accountDisplayMode: 'ibanNumber' | 'accountNumber' = 'ibanNumber';

  constructor(
    public platform: Platform,
    public apiService: ApiService,
    private cdr: ChangeDetectorRef,
    private clipboard: Clipboard,
    private toastController: ToastController,
    private alertController: AlertController
  ) {}

  ngOnInit(): void {
    this.getBankAccount().pipe(take(1)).subscribe();
  }

  getBankAccount() {
    return this.apiService.getBankAccounts()
      .pipe(take(1), tap((data) => {
        this.loading = false;
        this.accounts = (data.data as any[])
          .sort((a, b) => a.index - b.index);
        this.cdr.detectChanges();
      }));
  }

  async copyThis(text: string) {
    if (!text) return;

    text = text.replace(/\s/g, "");

    try {
      // Use the Clipboard API if available (modern browsers)
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(text);
        await this.showSuccessAlert('تم نسخ ' +
          (this.accountDisplayMode === 'ibanNumber' ? 'رقم الآيبان' : 'رقم الحساب') +
          ' بنجاح');
      }
      // Fallback to Angular CDK clipboard
      else {
        const success = this.clipboard.copy(text);
        if (success) {
          await this.showSuccessAlert('تم نسخ ' +
            (this.accountDisplayMode === 'ibanNumber' ? 'رقم الآيبان' : 'رقم الحساب') +
            ' بنجاح');
        }
      }
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  }

  async showSuccessAlert(message: string) {
    const toast = await this.toastController.create({
      message: message,
      duration: 2000,
      position: 'bottom',
      color: 'success'
    });
    await toast.present();
  }

  updateAccountDisplayMode(event: any) {
    this.accountDisplayMode = event.detail.value;
    this.cdr.detectChanges();
  }
}
