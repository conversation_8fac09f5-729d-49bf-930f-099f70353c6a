<app-header [hideProfileIcon]="true" [showBackButton]="true" backTo="/home"></app-header>
<ion-content color="light" [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large" class="ion-margin-vertical" style="">الدخول</ion-title>
    </ion-toolbar>
  </ion-header>
  <ion-grid [fixed]="true" [formGroup]="loginForm">
    <ion-row class="ion-margin-bottom">
      <ion-col class="altwijry-card ion-padding-bottom">
        @if (loginFormReady) {
          <splide #mainSplide
                  [options]="{ drag: false, pagination: false, arrows: false, direction: 'rtl', width: '100%' }"
                  style="width:100%">
            <splide-slide>
              <ion-row class="ion-justify-content-center ion-flex-column ion-align-items-center">
                <ion-icon name="altwijry-login" class="mobile-icon ion-margin-top"></ion-icon>
                <ion-text color="primary">
                  <h2 style="font-size: 1.3rem">اكتب رقم الجوال أو الرقم التعريفي</h2>
                </ion-text>
                <ion-input formControlName="mobileOrFamilyNumber"
                           style="max-width: 200px; text-align: center;direction: ltr; border-bottom: #7d7d7d solid 1px;"
                           label="" (keydown.enter)="login()"
                           type="tel" placeholder="05xxxxxxxx | 22528"></ion-input>
                <ion-text>
                  <h5 style="font-size: 0.7rem">سنقوم بإرسال <strong>رمز تحقق</strong> إذا كان رقم جوالك مضافا في
                    المشجرة
                  </h5>
                </ion-text>
                <ion-button (click)="login()" style="min-width: 7rem;" mode="ios" color="secondary" expand="block"
                            [disabled]="loginMethodLoading || formLoading">
                  @if (loginMethodLoading || formLoading) {
                    <ion-spinner></ion-spinner>
                  } @else {
                    أرسل الرمز
                  }
                </ion-button>
              </ion-row>
            </splide-slide>
            <splide-slide>
              <ng-container *ngTemplateOutlet="verifyOtp"></ng-container>
            </splide-slide>
            <splide-slide>
              <ng-container *ngTemplateOutlet="mobileNotRegistered"></ng-container>
            </splide-slide>
            <splide-slide>
              <ng-container *ngTemplateOutlet="otpMethodSelector"></ng-container>
            </splide-slide>
            <splide-slide>
              <ng-container *ngTemplateOutlet="noMethodAvailable"></ng-container>
            </splide-slide>
            <splide-slide>
              <ng-container *ngTemplateOutlet="blockedContent"></ng-container>
            </splide-slide>
          </splide>
        } @else {
          <div style="width: 100%;">
            <ion-row class="ion-justify-content-center ion-flex-column ion-align-items-center">
              <ion-icon name="altwijry-login" class="mobile-icon ion-margin-top"></ion-icon>
              <ion-item *ngFor="let one of [].constructor(2)" style="width: 100%">
                <ion-thumbnail slot="start">
                  <ion-skeleton-text [animated]="true"></ion-skeleton-text>
                </ion-thumbnail>
                <ion-label>
                  <h3>
                    <ion-skeleton-text [animated]="true" style="width: 80%;"></ion-skeleton-text>
                  </h3>
                </ion-label>
              </ion-item>
            </ion-row>
          </div>
        }
      </ion-col>
    </ion-row>

    <app-faqs/>
  </ion-grid>
</ion-content>

<ng-template #verifyOtp [formGroup]="loginForm">
  <ion-row class="ion-justify-content-center ion-flex-column ion-align-items-center">
    <ion-button fill="clear" (click)="goto(0)" class="ion-justify-content-end"
                style="position: absolute; right: -10px; top: -10px; rotate: 180deg; padding: 0; margin: 0;">
      <ion-icon name="altwijry-back-button" class="altwijry-back-button" slot="icon-only"></ion-icon>
    </ion-button>
    <ion-icon name="altwijry-key" class="mobile-icon ion-margin-top"></ion-icon>
    <ion-text color="primary">
      <h2 style="font-size: 1.3rem">أدخل رمز التحقق</h2>
    </ion-text>
    <ion-input formControlName="otp"
               style="max-width: 200px; text-align: center;direction: ltr; border-bottom: #7d7d7d solid 1px;"
               (keydown.enter)="verifyOTP()"
               type="tel" placeholder="1234"
               autocomplete="one-time-code"></ion-input>
    <ion-text *ngIf="method === 'sms'">
      <h5 style="font-size: 0.7rem !important;">
        سوف يصلك
        <strong>رمز التحقق</strong>
        عبر رسالة نصية اذا كان رقم جوالك مضافا بالمشجرة
      </h5>
    </ion-text>
    <ion-text *ngIf="method === 'email'">
      <h5 style="font-size: 0.7rem">
        أدخل
        <strong>رمز التحقق</strong>
        المرسل لبريدك الإلكتروني المضاف بالمشجرة
      </h5>
    </ion-text>
    <ion-text *ngIf="method === '2fa'">
      <h5 style="font-size: 0.7rem">
        أدخل
        <strong>رمز التحقق</strong>
        الموجود ببرنامج توليد الرموز الخاص بكم
      </h5>
    </ion-text>
    <ion-button (click)="verifyOTP()" style="min-width: 7rem;" mode="ios" color="secondary" expand="block"
                [disabled]="otpVerificationLoading || formLoading">
      @if (otpVerificationLoading || formLoading) {
        <ion-spinner></ion-spinner>
      } @else {
        تسجيل الدخول
      }
    </ion-button>
  </ion-row>
</ng-template>

<ng-template #mobileNotRegistered [formGroup]="loginForm">
  <ion-row class="ion-justify-content-center ion-flex-column ion-align-items-center">
    <ion-button fill="clear" (click)="goto(0)" class="ion-justify-content-end"
                style="position: absolute; right: -10px; top: -10px; rotate: 180deg; padding: 0; margin: 0;">
      <ion-icon name="altwijry-back-button" class="altwijry-back-button" slot="icon-only"></ion-icon>
    </ion-button>
    <ion-icon name="altwijry-username-error" class="mobile-icon ion-margin-top"></ion-icon>
    <ion-text color="danger">
      <h2 style="font-size: 1.3rem">عذرا جوالك غير مسجل لدينا!</h2>
    </ion-text>
    <ion-text>
      <p>
        <a style="text-decoration: unset; display: block;" class="ion-text-center"
           href="https://forms.gle/ApBB6PLZJL4GMMF27" target="_blank">
          اضغط هنا وقم بتعبئة النموذج، وسيتم التواصل معك وإضافتك للمشجرة الإلكترونية
        </a>
      </p>
    </ion-text>
  </ion-row>
</ng-template>

<ng-template #noMethodAvailable [formGroup]="loginForm">
  <ion-row class="ion-justify-content-center ion-flex-column ion-align-items-center">

    <ion-button fill="clear" (click)="goto(0)" class="ion-justify-content-end"
                style="position: absolute; right: -10px; top: -45px; rotate: 180deg; padding: 0; margin: 0;">
      <ion-icon name="altwijry-back-button" class="altwijry-back-button" slot="icon-only"></ion-icon>
    </ion-button>
    <ion-icon name="altwijry-username-error" class="mobile-icon ion-margin-top"></ion-icon>
    <ion-text color="danger">
      <h2 style="font-size: 1.3rem">عذرا لم نتكمن من إرسال رمز التحقق !</h2>
    </ion-text>
    <ion-text>
      <p>
        <a style="text-decoration: unset; display: block;" class="ion-text-center"
           href="https://forms.gle/ApBB6PLZJL4GMMF27" target="_blank">
          اضغط هنا وقم بتعبئة النموذج، وسيتم التواصل معك وإضافتك للمشجرة الإلكترونية
        </a>
      </p>
    </ion-text>
  </ion-row>
</ng-template>

<ng-template #otpMethodSelector>
  <ion-row class="ion-justify-content-center ion-flex-column ion-align-items-center">

    <ion-button fill="clear" (click)="goto(0)" class="ion-justify-content-end"
                style="position: absolute; right: -10px; top: -45px; rotate: 180deg; padding: 0; margin: 0;">
      <ion-icon name="altwijry-back-button" class="altwijry-back-button" slot="icon-only"></ion-icon>
    </ion-button>

    <ion-button (click)="selectMethod('whatsapp')" style="width: 100%;" mode="ios" color="secondary" expand="block"
                [disabled]="methodSelectionLoading || formLoading" *ngIf="(methods?.indexOf('whatsapp') ?? -1) >= 0">
      @if (methodSelectionLoading || formLoading) {
        <ion-spinner></ion-spinner>
      } @else {
        <i class="fa fab fa-whatsapp"></i>
        عن طريق الواتساب
      }
    </ion-button>

    <ion-button (click)="selectMethod('sms')" style="width: 100%;" mode="ios" color="secondary" expand="block"
                [disabled]="methodSelectionLoading || formLoading" *ngIf="(methods?.indexOf('sms') ?? -1) >= 0">
      @if (methodSelectionLoading || formLoading) {
        <ion-spinner></ion-spinner>
      } @else {
        <i class="fa fas fa-sms"></i>
        رسالة جوال
      }
    </ion-button>

    <ion-button (click)="selectMethod('father_sms')" style="width: 100%;" mode="ios" color="secondary" expand="block"
                [disabled]="methodSelectionLoading || formLoading" *ngIf="(methods?.indexOf('father_sms') ?? -1) >= 0">
      @if (methodSelectionLoading || formLoading) {
        <ion-spinner></ion-spinner>
      } @else {
        <i class="fa fas fa-sms"></i>
        رسالة جوال "الأب"
      }
    </ion-button>

    <ion-button (click)="selectMethod('email')" style="width: 100%;" mode="ios" color="secondary" expand="block"
                [disabled]="methodSelectionLoading || formLoading" *ngIf="(methods?.indexOf('email') ?? -1) >= 0">
      @if (methodSelectionLoading || formLoading) {
        <ion-spinner></ion-spinner>
      } @else {
        <i class="fa fas fa-envelope"></i>
        البريد الإلكتروني
      }
    </ion-button>

    <ion-button (click)="selectMethod('2fa')" style="width: 100%;" mode="ios" color="secondary" expand="block"
                [disabled]="methodSelectionLoading || formLoading" *ngIf="(methods?.indexOf('2fa') ?? -1) >= 0">
      @if (methodSelectionLoading || formLoading) {
        <ion-spinner></ion-spinner>
      } @else {
        <i class="fa fas fa-key"></i>
        مولد الرموز
      }
    </ion-button>
  </ion-row>
</ng-template>

<ng-template #blockedContent>
  <ion-row class="ion-justify-content-center ion-flex-column ion-align-items-center">
    <ion-icon name="altwijry-username-error" class="mobile-icon ion-margin-top"></ion-icon>
    <ion-text color="danger">
      <h2 style="font-size: 1rem !important;">عذرا تم حظرك من تسجيل الدخول، يرجى العودة لاحقاً!</h2>
    </ion-text>
  </ion-row>
</ng-template>
