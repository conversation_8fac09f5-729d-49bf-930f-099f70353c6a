import {
  After<PERSON>iewInit,
  ChangeDetector<PERSON><PERSON>,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  Inject,
  Input,
  OnInit,
  ViewChild
} from '@angular/core';
import {DOCUMENT, <PERSON><PERSON>orOf, <PERSON><PERSON>f, NgTemplateOutlet} from "@angular/common";
import {Alert<PERSON>ontroller, IonicModule, NavController} from "@ionic/angular";
import {HeaderComponent} from "../../layout/header/header.component";
import {IS_SERVER_PLATFORM} from "../../shared/IS_SERVER_PLATFORM.token";
import {ApiService} from "../../shared/services/api.service";
import {HttpClientModule} from "@angular/common/http";
import {RouterModule} from "@angular/router";
import {FormBuilder, FormsModule, ReactiveFormsModule, Validators} from "@angular/forms";
import {catchError, finalize} from "rxjs";
import {StorageService} from "../../shared/services/storage.service";
import {ProfileService} from "../../shared/services/profile.service";
import {FeaturesService} from "../../shared/services/features.service";
import {NgxSplideComponent, NgxSplideModule} from "ngx-splide";
import {AppDataService} from "../../shared/services/app-data.service";
import {FaqsComponent} from "../../shared/components/faqs/faqs.component";
import {AuthService} from "../../shared/services/auth.service";

@Component({
  selector: 'app-login',
  templateUrl: 'login-page.component.html',
  styleUrls: ['login-page.component.scss', "login-page.component.less"],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    NgIf,
    NgForOf,
    HttpClientModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    NgTemplateOutlet,
    NgxSplideModule,
    FaqsComponent,
  ],
})
export class LoginPage implements OnInit, AfterViewInit {
  @Input() returnURL?: string;
  @ViewChild('mainSplide')
  mainSplide!: NgxSplideComponent;

  userToken?: string;
  uuid?: string;
  method?: string;
  methods?: string[];
  loginForm = this.fb.group({
    mobileOrFamilyNumber: ['', [Validators.required, Validators.minLength(5)]],
    otp: ['', [Validators.minLength(6)]],
  });

  // Loading states
  formLoading = false;
  loginMethodLoading = false;
  otpVerificationLoading = false;
  methodSelectionLoading = false;
  // methodSelectionLoading: { [key: string]: boolean } = {};
  public loginFormReady = false;

  constructor(
    @Inject(DOCUMENT) private document: Document,
    @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
    private apiService: ApiService,
    private featuresService: FeaturesService,
    private cdr: ChangeDetectorRef,
    private fb: FormBuilder,
    private alertCtrl: AlertController,
    private navCtrl: NavController,
    private storageService: StorageService,
    private profileService: ProfileService,
    public appDataService: AppDataService,
    private authService: AuthService
  ) {
  }

  ngAfterViewInit(): void {
    let block = () => {
      console.log('check');
      if (this.mainSplide)
        this.goto(5)
      else
        setTimeout(block, 100);
    };
    // this.storageService.isReady().subscribe(ready => {
    //   if (ready) {
    this.isDeviceBlocked().then((isBlocked) => {
      if (isBlocked)
        setTimeout(block, 100);
      this.loginFormReady = true;
      this.cdr.detectChanges();
    });
    // }
    // });
  }

  ngOnInit(): void {
    if (this.isServer)
      console.log('is server')
    if (!this.isServer)
      this.browserSetup();
  }

  browserSetup() {

  }

  async login() {
    if (this.loginForm.invalid) {
      const alert = await this.alertCtrl.create({
        header: 'خطأ',
        message: 'البيانات المدخلة غير صحيحة/موجودة .',
        buttons: ['موافق']
      });
      await alert.present();
      return;
    }

    this.loginMethodLoading = true;
    let mobileOrFamilyNumber = this.loginForm.value.mobileOrFamilyNumber ?? '';

    try {
      if (mobileOrFamilyNumber?.length === 5) {
        await this.loginBy(mobileOrFamilyNumber, 'family_user_id')
        return
      }
      if (mobileOrFamilyNumber?.indexOf('@') >= 0) {
        await this.loginBy(mobileOrFamilyNumber, 'email')
        return
      }
      if (mobileOrFamilyNumber.length >= 10) {
        let formattedMobile = mobileOrFamilyNumber?.startsWith('0') ? '966' + mobileOrFamilyNumber.substring(1) : mobileOrFamilyNumber;
        await this.loginBy(formattedMobile, 'phone');
        return
      }
    } finally {
      this.loginMethodLoading = false;
      this.cdr.detectChanges();
    }
  }

  loginBy(val: string, col: string) {
    this.formLoading = true;
    return this.apiService.getLoginMethods(val, col).pipe(
      catchError((exception) => {
        const {error} = exception;
        if (exception.status == 429) {
          this.blockDevice()
        } else if (error.error === 'لا يوجد مستخدم مسجل بهذا الرقم !')
          this.goto(2)
        else if (error.errorType === 'none_method')
          this.goto(4)
        throw new Error(JSON.stringify(error))
      }),
      finalize(() => {
        this.formLoading = false;
        this.cdr.detectChanges();
      })
    ).subscribe((res) => {
      if (res.uuid) {
        this.uuid = res.uuid;
        this.goto(1)
        this.method = res.method
      } else if (res.methods.length === 1 && res.methods[0] === '2fa') {
        this.userToken = res.token;
        this.method = '2fa';
        this.goto(1)
      } else if (res.methods.length >= 1) {
        this.userToken = res.token;
        this.methods = res.methods;
        this.goto(3)
      } else
        this.goto(2)
    })
  }

  selectMethod(method: string) {
    // this.methodSelectionLoading[method] = true;
    this.methodSelectionLoading = true;
    this.method = method;

    if (this.userToken === undefined) {
      // this.methodSelectionLoading[method] = false;
      this.methodSelectionLoading = false;
      return this.goto(0);
    }

    if (method === '2fa') {
      // this.methodSelectionLoading[method] = false;
      this.methodSelectionLoading = false;
      this.goto(1)
    } else {
      this.apiService.getOTP(this.userToken, method).pipe(
        catchError((exception) => {
          const {error} = exception;
          if (exception.status == 429) {
            this.blockDevice()
          } else if (error.error === 'خطأ حاول مرة أخرى !')
            this.goto(2)
          throw new Error(JSON.stringify(error))
        }),
        finalize(() => {
          // this.methodSelectionLoading[method] = false;
          this.methodSelectionLoading = false;
          this.cdr.detectChanges();
        })
      ).subscribe((res) => {
        this.formLoading = false;
        this.uuid = res.uuid;
        this.goto(1)
      })
    }
  }

  goto(slideNumber: number) {
    if (slideNumber === 0) {
      this.loginForm.controls.mobileOrFamilyNumber.setValue('');
      this.loginForm.controls.otp.setValue('');
      this.userToken = this.uuid = this.method = this.methods = undefined;
    }
    this.mainSplide.getSplideInstance().go(slideNumber)
  }

  blockDevice() {
    this.goto(5)
    const blockUntil = new Date(Date.now() + 30 * 60 * 1000); // for 30Minute
    this.storageService.setItem('loginBlockedUntil', blockUntil).then();
  }

  async isDeviceBlocked() {
    return new Date(await this.storageService.getItem('loginBlockedUntil') ?? 0) > new Date();
  }

  async verifyOTP() {
    this.formLoading = true;
    this.otpVerificationLoading = true;

    try {
      if (this.method === '2fa') {
        await this.verify2FAOTP();
      } else {
        await this.verifyRegularOTP();
      }
    } finally {
      this.otpVerificationLoading = false;
      this.cdr.detectChanges();
    }
  }

  private async verify2FAOTP() {
    this.apiService.loginWith2FA(this.userToken ?? '', this.loginForm.value.otp ?? '').pipe(
      catchError(async ({error}) => {
        this.formLoading = false;
        const incorrectOTP = await this.alertCtrl.create({
          header: 'خطأ',
          message: 'الرمز المدخل غير صحيح !',
          buttons: ['موافق']
        })
        incorrectOTP.present().then();
        throw new Error(JSON.stringify(error))
      })
    ).subscribe(async (res) => {
      await this.handleSuccessfulLogin(res);
    })
  }

  private async verifyRegularOTP() {
    this.apiService.verifyOTP(this.uuid ?? '', this.loginForm.value.otp ?? '').pipe(
      catchError(async ({error}) => {
        this.formLoading = false;
        const incorrectOTP = await this.alertCtrl.create({
          header: 'خطأ',
          message: 'الرمز المدخل غير صحيح',
          buttons: ['موافق']
        })
        incorrectOTP.present().then();
        throw new Error(JSON.stringify(error))
      })
    ).subscribe(async (res) => {
      await this.handleSuccessfulLogin(res);
    })
  }

// Update your handleSuccessfulLogin method

  private async handleSuccessfulLogin(res: any) {
    await this.storageService.setItem('access_token', res.access_token);
    await this.storageService.setItem('expires_at', res.expires_at);

    this.apiService.me().subscribe({
      next: async (res) => {
        this.formLoading = false;
        this.featuresService.loadFeatures();
        await this.storageService.setItem('user', JSON.stringify(res));
        await this.profileService.setUser(res);
        this.navCtrl.navigateRoot(this.returnURL || '/home').then();
      },
      error: async (error) => {
        this.formLoading = false;
        const alert = await this.alertCtrl.create({
          header: 'خطأ',
          message: 'حدث خطأ أثناء تسجيل الدخول',
          buttons: ['موافق']
        });
        await alert.present();
      }
    });
  }

  protected readonly Array = Array;
}
