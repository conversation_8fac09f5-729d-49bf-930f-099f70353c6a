<app-header [hideProfileIcon]="false" [showBackButton]="true" backTo="/home"></app-header>
<ion-content>
  <ion-grid [fixed]="true" style="max-width: 370px;height: 100%;"
            class="ion-align-items-center"
            *ngIf="loaded; else spinner">
    <app-card [showExpandButton]="false" [scrollable]="false" style="height: 100%;">
      <ion-card-content style="height: 100%;">
        <iframe [attr.data-tally-src]="formUrl" *ngIf="formUrl; else errorText"
                width="100%" height="100%" frameborder="0" marginheight="0" marginwidth="0" loading="lazy"></iframe>
        <ng-template #errorText>
          <ion-text color="danger">
            <h4>{{ error }}</h4>
          </ion-text>
        </ng-template>
      </ion-card-content>
    </app-card>
  </ion-grid>
  <ng-template #spinner>
    <div class="ion-flex ion-justify-content-center">
      <ion-spinner size="large" name="crescent" style="width: 70px; height: 70px"></ion-spinner>
    </div>
  </ng-template>
</ion-content>

