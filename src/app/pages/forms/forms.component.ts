import { Component, Input, OnInit, AfterViewInit, CUSTOM_ELEMENTS_SCHEMA, TransferState } from '@angular/core';
import {HeaderComponent} from "../../layout/header/header.component";
import {IonicModule} from "@ionic/angular";
import {IfIsBrowserDirective} from "../../shared/IfIsBrowser.directive";
import {HttpClient, HttpClientModule} from "@angular/common/http";
import {ApiService} from "../../shared/services/api.service";

import {Router} from '@angular/router';
import {DatePipe, NgForOf, NgIf} from "@angular/common";
import {environment} from "../../../environments/environment";
import {RenderBlockPipe} from "../../shared/pipes/render-block.pipe";
import {CardComponent} from "../../shared/components/card/card.component";

@Component({
  selector: 'app-forms',
  templateUrl: './forms.component.html',
  styleUrls: ['./forms.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    IfIsBrowserDirective,
    NgForOf,
    NgIf,
    RenderBlockPipe,
    DatePipe,
    CardComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class FormsComponent implements OnInit, AfterViewInit {
  @Input() slug?: string;
  formUrl?: string;
  loaded = false;
  error = '';
  env = environment;

  constructor(private apiService: ApiService,
              private http: HttpClient,
              private transferState: TransferState,
              private router: Router,) {
    //get id params from url parameters
  }

  ngAfterViewInit(): void {
    if (this.slug) {
      this.apiService.getTallyForm(this.slug).subscribe(res => {
        this.loaded = true;
        if (res.success) {
          this.formUrl = res.url;
          setTimeout(function () {
            if ('Tally' in window) {
              window.addEventListener('Tally.FormPageView', (payload: any) => {
                console.log(payload);
              });
              window.addEventListener('Tally.FormSubmitted', (payload: any) => {
                console.log(payload);
              });
              // @ts-ignore
              window.Tally.loadEmbeds()
            }
          }, 500);
        } else {
          if (res.exists)
            this.error  = 'شكرا لك تم تسجيل مشاركتك .';
        }
      })
    }
  }

  ngOnInit() {

  }
}
