<app-header [hideProfileIcon]="true" [showBackButton]="true"></app-header>
<ion-content>
  <ion-grid [fixed]="true">
    <app-card title="إضافة حجز جديد" [showExpandButton]="false" [scrollable]="false">
      <ion-spinner *ngIf="!isReady" style="margin: auto; width: 100%;height: 40px;"></ion-spinner>

      <div class="" *ngIf="isReady" [formGroup]="createForm">

        <ion-item class="ion-margin-horizontal">
          <ion-input label="تاريخ الحجز" labelPlacement="floating"
                     value="{{ (createForm.controls.eventDate.value) | date: 'dd/MM/yyyy' }}"
                     id="eventDatePicker" class="ion-text-end" readonly="true"></ion-input>
          <ion-popover trigger="eventDatePicker" size="cover">
            <ng-template>
              <ion-datetime presentation="date"
                            [min]="nowTime"
                            formControlName="eventDate"></ion-datetime>
            </ng-template>
          </ion-popover>
        </ion-item>

        <ion-item class="ion-margin-horizontal ion-margin-top">
          <ion-select placeholder="الغرض من الحجز" formControlName="eventType" TranslateIonicTexts>
            <ion-select-option value="MARRIAGE">زواج</ion-select-option>
            <ion-select-option value="MEETING">اجتماع عمل</ion-select-option>
          </ion-select>
        </ion-item>

        <ion-item class="ion-margin-horizontal" *ngIf="createForm.controls.eventType.value !== 'MARRIAGE'">
          <ion-input label="من" labelPlacement="floating"
                     value="{{ (createForm.controls.eventStartAt.value) | date: 'hh:mm a' }}"
                     id="eventStartAtPicker" class="ion-text-end" readonly="true"></ion-input>
          <ion-popover trigger="eventStartAtPicker" size="cover" style="width: 100%">
            <ng-template>
              <ion-datetime presentation="time" formControlName="eventStartAt"></ion-datetime>
            </ng-template>
          </ion-popover>
        </ion-item>

        <ion-item class="ion-margin-horizontal" *ngIf="createForm.controls.eventType.value !== 'MARRIAGE'">
          <ion-input label="إلى" labelPlacement="floating"
                     value="{{ (createForm.controls.eventEndAt.value) | date: 'hh:mm a' }}"
                     id="eventEndAt" class="ion-text-end" readonly="true"></ion-input>
          <ion-popover trigger="eventEndAt" size="cover" style="width: 100%">
            <ng-template>
              <ion-datetime presentation="time" formControlName="eventEndAt"></ion-datetime>
            </ng-template>
          </ion-popover>
        </ion-item>

        <ion-list class="ion-margin-horizontal ion-margin-top" *ngIf="services$.length > 0">
          <ion-item *ngFor="let service$ of services$; let i= index">
            <ion-checkbox justify="end" [value]="service$.id" #checkboxes
                          (ionChange)="updateServices(service$.id, $event)">{{ service$.title }}</ion-checkbox>
          </ion-item>
        </ion-list>

        <ion-item class="ion-margin-top ion-margin-bottom ion-margin-horizontal">
          <ion-textarea label="ملاحظات" labelPlacement="floating" formControlName="details"></ion-textarea>
        </ion-item>

        <ion-text color="danger" class="ion-text-center ion-margin-top" *ngIf="errorMessage">
          <h2 style="font-size: 1.3rem">{{ errorMessage }}</h2>
        </ion-text>

        <ion-button color="primary" expand="block" mode="ios" style="color: white;"
                    class="ion-margin-top ion-margin-horizontal"
                    (click)="store()" [disabled]="formLoading">
          <ion-spinner *ngIf="formLoading"></ion-spinner>
          <span *ngIf="!formLoading">
            إضافة جديد
          </span>
        </ion-button>
      </div>
    </app-card>
  </ion-grid>
</ion-content>
