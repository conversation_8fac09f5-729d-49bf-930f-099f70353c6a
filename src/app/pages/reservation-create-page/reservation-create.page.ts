import {ChangeDetector<PERSON><PERSON>, <PERSON>mponent, <PERSON><PERSON><PERSON><PERSON>, make<PERSON>tate<PERSON><PERSON>, OnInit, Query<PERSON>ist, ViewChildren} from '@angular/core';
import {InfiniteScrollCustomEvent, IonicModule, ToastController} from "@ionic/angular";
import {HeaderComponent} from "../../layout/header/header.component";
import {IfIsBrowserDirective} from "../../shared/IfIsBrowser.directive";
import {ReservationService} from "../../shared/services/reservation.service";
import {BehaviorSubject, catchError, Observable, of, take, tap} from "rxjs";
import {Service} from "../../shared/models";
import {StorageService} from "../../shared/services/storage.service";
import {CardComponent} from "../../shared/components/card/card.component";
import {AsyncPipe, CurrencyPipe, DatePipe, NgForOf, NgIf, NgTemplateOutlet} from "@angular/common";
import {RouterLink} from "@angular/router";
import {FormA<PERSON>y, FormB<PERSON>er, FormControl, FormsModule, ReactiveFormsModule, Validators} from "@angular/forms";
import {TranslateIonicTextsDirective} from "../../shared/directives/translate-ionic-texts.directive";

@Component({
  selector: 'reservation-create-page',
  templateUrl: 'reservation-create.page.html',
  styleUrls: ['reservation-create.page.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    IfIsBrowserDirective,
    CardComponent,
    NgIf,
    NgTemplateOutlet,
    AsyncPipe,
    DatePipe,
    NgForOf,
    RouterLink,
    CurrencyPipe,
    ReactiveFormsModule,
    FormsModule,
    TranslateIonicTextsDirective,
  ],
})
export class ReservationCreatePage implements OnInit {
  @ViewChildren("checkboxes") checkboxes: QueryList<ElementRef> = new QueryList<ElementRef>();
  errorMessage: string | null = null;
  services$: Service[] = [];
  public formLoading: boolean = false;
  public isReady: boolean = true;
  nowTime = new Date().toISOString();
  createForm = this.formBuilder.group({
    eventDate: ['', [
      Validators.required,
      Validators.minLength(10)
    ]],
    eventStartAt: ['', [
      Validators.required,
      Validators.minLength(10)
    ]],
    eventEndAt: ['', [
      Validators.required,
      Validators.minLength(10)
    ]],
    eventType: ['', [
      Validators.required,
      Validators.minLength(10)
    ]],
    details: ['', [
      Validators.required,
      Validators.minLength(10)
    ]],
    services: this.formBuilder.array([])
  });

  constructor(private storageService: StorageService,
              private reservationService: ReservationService,
              public toastController: ToastController,
              private datePipe: DatePipe,
              private formBuilder: FormBuilder,
              private cdr: ChangeDetectorRef,) {

  }

  getData() {
    return this.reservationService.services().pipe(take(1), tap((data) => {
      this.services$ = data.data;
      this.cdr.detectChanges();
    }));
  }

  ngOnInit(): void {
    this.getData().pipe(take(1)).subscribe();
  }

  ionViewWillEnter() {
    // load data...
    this.refreshForm();
  }

  refreshForm() {
    this.createForm.patchValue({
      eventDate: (new Date()).toISOString(),
      eventStartAt: '',
      eventEndAt: '',
      eventType: '',
      details: '',
      services: [],
    });
  }

  updateServices(id$: number, event: Event) {
    const _services = this.createForm.get('services') as FormArray;
    if (event.target && 'checked' in event.target && event.target.checked == true) {
      _services.push(new FormControl(id$));
    } else {
      let idx = _services.controls.findIndex(x => x.value == id$);
      if (idx >= 0)
        _services.removeAt(idx);
    }
  }

  store() {
    this.formLoading = true
    this.errorMessage = null
    this.reservationService.store({
      date: this.createForm.controls.eventDate.value?.split('T')[0],
      event_type: this.createForm.controls.eventType.value,
      details: this.createForm.controls.details.value,
      time_start: this.datePipe.transform(this.createForm.controls.eventStartAt.value, 'hh:mm a', '', 'en-US'),
      time_end: this.datePipe.transform(this.createForm.controls.eventEndAt.value, 'hh:mm a', '', 'en-US'),
      services: this.createForm.controls.services.value.filter(_serviceId => _serviceId !== null && _serviceId !== undefined),
    }).pipe(
      catchError(({error}) => {
        this.formLoading = false

        if (Object.keys(error.errors).length === 1)
          this.errorMessage = error.message;
        else
          this.errorMessage = 'خطأ في بيانات الحجز.';
        throw new Error(JSON.stringify(error))
      })
    ).subscribe((res) => {
      this.formLoading = false;
      this.createForm.reset();
      this.checkboxes.forEach((element) => {
        if ('checked' in element)
          element.checked = false;
      });
      this.toastController.create({
        header: 'تم الحجز بنجاح',
        message: 'جاري مراجعة الحجز من قبل المسؤلين !',
        position: 'top',
        buttons: [{
          side: 'end',
          text: 'x',
          role: 'cancel',
        }]
      }).then((toast) => {
        toast.present();
      });
    })
  }

  protected readonly console = console;
}
