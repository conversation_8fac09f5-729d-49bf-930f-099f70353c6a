import {Component, Inject, OnInit, TransferState} from '@angular/core';
import {DOCUMENT} from "@angular/common";
import {IonicModule, ModalController, NavController} from "@ionic/angular";

import {HttpClientModule} from "@angular/common/http";

import {ActivatedRoute, RouterModule} from "@angular/router";
import {HeaderComponent} from "../../../layout/header/header.component";
import {CardComponent} from "../../../shared/components/card/card.component";
import {ApiService} from "../../../shared/services/api.service";
import {IS_SERVER_PLATFORM} from "../../../shared/IS_SERVER_PLATFORM.token";
import {take} from "rxjs";


@Component({
  selector: 'app-checkout-failure',
  templateUrl: 'failure-page.component.html',
  styleUrls: ['failure-page.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    CardComponent,
    RouterModule
  ],
})
export class CheckoutFailurePage implements OnInit {
  productId = -1;
  transactionUUID = '';

  constructor(@Inject(DOCUMENT) private document: Document,
              @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
              private apiService: ApiService,
              private transferState: TransferState,
              private activatedRoute: ActivatedRoute,
              private modalCtrl: ModalController,
              private navCtrl: NavController) {

  }

  ngOnInit(): void {
    this.modalCtrl.getTop().then(d => d?.dismiss)
    this.browserSetup();
  }

  browserSetup() {
    // get query params using activated route
    this.activatedRoute.queryParams.pipe(take(1)).subscribe(params => {
      this.productId = params['productId'];
      this.transactionUUID = params['transactionUUID'];
    });

  }

  goBack() {
    if (this.productId)
      this.navCtrl.navigateBack(`/store?productId=${this.productId}`).then();
    else
      this.navCtrl.navigateBack(`/store`).then();
  }
}
