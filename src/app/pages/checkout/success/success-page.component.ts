import {ChangeDetectorRef, Component, Inject, OnInit, TransferState} from '@angular/core';
import {AsyncPipe, DatePipe, DOCUMENT, <PERSON>sonPipe, <PERSON>ForOf, Ng<PERSON>f, NgTemplateOutlet} from "@angular/common";
import {IonicModule, ModalController} from "@ionic/angular";

import {HttpClient, HttpClientModule} from "@angular/common/http";

import {ActivatedRoute, RouterModule} from "@angular/router";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {HeaderComponent} from "../../../layout/header/header.component";
import {CardComponent} from "../../../shared/components/card/card.component";
import {ApiService} from "../../../shared/services/api.service";
import {IS_SERVER_PLATFORM} from "../../../shared/IS_SERVER_PLATFORM.token";
import {take} from "rxjs";


@Component({
  selector: 'app-checkout-success',
  templateUrl: 'success-page.component.html',
  styleUrls: ['success-page.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    IfIsBrowserDirective,
    NgIf,
    CardComponent,
    NgTemplateOutlet,
    NgForOf,
    AsyncPipe,
    JsonPipe,
    RouterModule,
    DatePipe
  ],
})
export class CheckoutSuccessPage implements OnInit {
  productId = -1;
  transactionUUID = '';

  constructor(@Inject(DOCUMENT) private document: Document,
              @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
              private apiService: ApiService,
              private transferState: TransferState,
              private modalCtrl: ModalController,
              private activatedRoute: ActivatedRoute,
  ) {

  }

  ngOnInit(): void {
    this.modalCtrl.getTop().then(d=> d?.dismiss)
    this.browserSetup();
  }

  browserSetup() {
    // get query params using activated route
    this.activatedRoute.queryParams.pipe(take(1)).subscribe(params => {
      this.productId = params['productId'];
      this.transactionUUID = params['transactionUUID'];
    });
  }

}
