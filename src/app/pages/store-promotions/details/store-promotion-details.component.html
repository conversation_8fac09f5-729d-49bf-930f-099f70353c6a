<app-header [showBackButton]="true" backTo="/store-promotions">
  <span slot="title">{{ storePromotion?.title || 'تفاصيل العرض' }}</span>
</app-header>

<ion-content>
  <!-- Loading State -->
  <div *ngIf="!storePromotion" class="loading-container">
    <ion-spinner name="circular"></ion-spinner>
  </div>

  <!-- Promotion Details -->
  <div *ngIf="storePromotion">
    <!-- Banner Slider -->
    <splide [options]="splideOptions" *ngIf="storePromotion.banners && storePromotion.banners.length > 0">
      <splide-slide *ngFor="let banner of storePromotion.banners">
        <ion-img [src]="banner"></ion-img>
      </splide-slide>
    </splide>

    <!-- Store Header -->
    <div class="ion-text-center ion-padding">
      <ion-avatar style="width: 80px; height: 80px; margin: 0 auto 16px;">
        <ion-img
          [src]="storePromotion.logo_url || '/assets/icon/logo.svg'"
          (ionError)="failCallbackImage($event)">
        </ion-img>
      </ion-avatar>

      <h2>{{ storePromotion.title }}</h2>

      <ion-chip [color]="storePromotion.discount_type === 'PERCENTAGE' ? 'danger' : 'success'">
        <ion-icon name="pricetag"></ion-icon>
        <ion-label>
          {{ storePromotion.discount_type === 'PERCENTAGE' ? storePromotion.discount + '%' : storePromotion.discount + ' ر.س' }} خصم
        </ion-label>
      </ion-chip>
    </div>

    <!-- Description Section -->
    <ion-list *ngIf="storePromotion.description" class="ion-margin-top">
      <ion-item lines="none">
        <ion-label class="ion-text-wrap">
          <p [innerHTML]="storePromotion.description | nl2br" style="font-size: 16px !important; line-height: 1.6;"></p>
        </ion-label>
      </ion-item>
    </ion-list>

    <!-- Coupon Section -->
    <ion-list *ngIf="storePromotion.coupon" class="ion-margin-top">
      <ion-item lines="none">
        <ion-icon name="ticket-outline" slot="start" color="primary"></ion-icon>
        <ion-label class="ion-text-wrap">
          <h2>كود الخصم</h2>
          <p style="font-family: monospace; font-size: 18px; color: var(--ion-color-primary); margin-top: 8px;">{{ storePromotion.coupon }}</p>
        </ion-label>
        <ion-button
          fill="clear"
          slot="end"
          (click)="copyCoupon()">
          <ion-icon slot="icon-only" name="copy-outline"></ion-icon>
        </ion-button>
      </ion-item>
    </ion-list>

    <!-- How to Use Section -->
    <ion-list *ngIf="storePromotion.how_to_use" class="ion-margin-top">
      <ion-item lines="none">
        <ion-icon name="information-circle-outline" slot="start" color="primary"></ion-icon>
        <ion-label class="ion-text-wrap">
          <h2>وصف العرض</h2>
          <p [innerHTML]="storePromotion.how_to_use | nl2br"></p>
        </ion-label>
      </ion-item>
    </ion-list>

    <!-- Action Buttons -->
    <ion-list class="ion-margin-top">
      <ion-list-header>
        <ion-label>روابط</ion-label>
      </ion-list-header>
      <ion-item *ngIf="storePromotion.website_url" button (click)="openLink(storePromotion.website_url)" detail="false">
        <ion-icon name="globe-outline" slot="start" style="opacity: 0.8;"></ion-icon>
        <ion-label>زيارة الموقع</ion-label>
        <ion-icon name="open-outline" slot="end" style="opacity: 0.6;"></ion-icon>
      </ion-item>

      <ion-item *ngIf="storePromotion.apple_store_url" button (click)="openLink(storePromotion.apple_store_url)" detail="false">
        <ion-icon name="logo-apple" slot="start" style="opacity: 0.8;"></ion-icon>
        <ion-label>App Store</ion-label>
        <ion-icon name="open-outline" slot="end" style="opacity: 0.6;"></ion-icon>
      </ion-item>

      <ion-item *ngIf="storePromotion.google_play_url" button (click)="openLink(storePromotion.google_play_url)" detail="false">
        <ion-icon name="logo-google-playstore" slot="start" style="opacity: 0.8;"></ion-icon>
        <ion-label>Google Play</ion-label>
        <ion-icon name="open-outline" slot="end" style="opacity: 0.6;"></ion-icon>
      </ion-item>
    </ion-list>

  </div>
</ion-content>
