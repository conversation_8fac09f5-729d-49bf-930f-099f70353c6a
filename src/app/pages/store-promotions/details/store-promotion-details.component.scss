// Minimal custom styles - leverage Ionic defaults
:host {
  max-width: var(--page-width);
  margin: auto;
}
// Loading state
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
}

// Splide slider adjustments
splide {
  .splide__slide {
    ion-img {
      width: 100%;
      height: 200px;
      object-fit: cover;
    }
  }

  ::ng-deep {
    .splide__arrow {
      background: rgba(0, 0, 0, 0.5);
      opacity: 0.8;

      svg {
        fill: white;
      }
    }

    .splide__pagination__page {
      background: rgba(255, 255, 255, 0.5);

      &.is-active {
        background: white;
      }
    }
  }
}

// Store header
h2 {
  font-size: 24px;
  font-weight: 600;
  margin: 8px 0 16px;
}

// List styling
ion-list {
  ion-list-header {
    ion-label {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;

      ion-icon {
        font-size: 20px;
      }
    }
  }

  ion-item {
    --padding-top: 12px;
    --padding-bottom: 12px;

    ion-label {
      h2 {
        font-weight: 600;
        margin-bottom: 8px;
      }

      p {
        color: var(--ion-color-medium);
        line-height: 1.5;
      }
    }
  }
}


// RTL Support
:host-context([dir="rtl"]) {
  ion-list-header {
    ion-label {
      ion-icon {
        margin-left: 8px;
        margin-right: 0;
      }
    }
  }
}

// Responsive
@media (max-width: 576px) {
  splide {
    .splide__slide {
      ion-img {
        height: 150px;
      }
    }
  }
}
