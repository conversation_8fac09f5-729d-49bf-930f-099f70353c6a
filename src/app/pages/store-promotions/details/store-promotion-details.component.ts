import {Component, Input, OnInit} from '@angular/core';
import {HeaderComponent} from "../../../layout/header/header.component";
import {IonicModule, ToastController} from "@ionic/angular";
import {IfIsBrowserDirective} from "../../../shared/IfIsBrowser.directive";
import {HttpClientModule} from "@angular/common/http";
import {StorePromotion} from "../../../shared/models";
import {ApiService} from "../../../shared/services/api.service";
import {take, tap} from "rxjs";
import {Router} from '@angular/router';
import {AsyncPipe, DatePipe, DecimalPipe, NgForOf, NgIf, NgClass} from "@angular/common";
import {environment} from "../../../../environments/environment";
import {RenderBlockPipe} from "../../../shared/pipes/render-block.pipe";
import {CardComponent} from "../../../shared/components/card/card.component";
import {FormatDistancePipe} from "../../../shared/pipes/format-distance.pipe";
import {OneCardComponent} from "../../eid-cards/one-card/one-card.component";
import {nl2brPipe} from "../../../nl2br.pipe";
import {TruncatePipe} from "../../../truncate.pipe";
import {GenericService} from "../../../shared/services/generic.service";
import {AppDataService} from "../../../shared/services/app-data.service";
import {NgxSplideModule} from 'ngx-splide';
import {Options} from '@splidejs/splide';
import {Browser} from '@capacitor/browser';
import {Clipboard} from '@capacitor/clipboard';

@Component({
  selector: 'app-store-promotion-details',
  templateUrl: './store-promotion-details.component.html',
  styleUrls: ['./store-promotion-details.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    HttpClientModule,
    IfIsBrowserDirective,
    NgForOf,
    NgIf,
    RenderBlockPipe,
    DatePipe,
    CardComponent,
    FormatDistancePipe,
    OneCardComponent,
    DecimalPipe,
    nl2brPipe,
    TruncatePipe,
    AsyncPipe,
    NgClass,
    NgxSplideModule
  ]
})
export class StorePromotionDetailsComponent implements OnInit {
  @Input() storePromotion?: StorePromotion;
  storePromotionId?: number;
  env = environment;
  
  splideOptions: Options = {
    type: 'loop',
    perPage: 1,
    gap: '1rem',
    padding: '1rem',
    arrows: true,
    pagination: true,
    autoplay: true,
    interval: 5000,
    pauseOnHover: true,
    pauseOnFocus: true,
    direction: 'rtl'
  };

  constructor(private apiService: ApiService,
              protected genericService: GenericService,
              public toastController: ToastController,
              public appDataService: AppDataService,
              private router: Router) {
    this.storePromotionId = Number(this.router.url.split('/')[2]);
    if (this.storePromotionId)
      this.getStorePromotionById(this.storePromotionId).subscribe();
    if (!this.storePromotionId) {

    }
  }

  getStorePromotionById(id: number) {
    return this.apiService.checkAndGetData(
      this.apiService.getDynamicStateKey(`store-promotion-details-${this.storePromotionId}`),
      this.apiService.getStorePromotionById(this.storePromotionId ?? 0),
      []
    ).pipe(take(1), tap((data: StorePromotion) => {
      this.storePromotion = data;
    }))
  }

  ngOnInit() {
  }

  failCallbackImage(event: any) {
    this.appDataService.data$.subscribe(data => {
      event.target.src = (data!['iconPath']) ||  '/assets/icon/logo.svg'
    })
  }

  async copyCoupon() {
    if (this.storePromotion?.coupon) {
      try {
        await Clipboard.write({
          string: this.storePromotion.coupon
        });
        
        const toast = await this.toastController.create({
          message: 'تم نسخ كود الخصم',
          duration: 2000,
          position: 'bottom',
          color: 'success'
        });
        await toast.present();
      } catch (error) {
        console.error('Failed to copy coupon:', error);
      }
    }
  }
  
  
  openLink(url: string) {
    Browser.open({ url });
  }
}
