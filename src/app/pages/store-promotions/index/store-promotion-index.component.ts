import { ChangeDetectorRef, Component, Inject, OnInit, TransferState } from '@angular/core';
import {AsyncPipe, DatePipe, DOCUMENT, JsonPipe, <PERSON>ForO<PERSON>, <PERSON><PERSON><PERSON>, NgTemplateOutlet, Ng<PERSON>lass} from "@angular/common";
import {InfiniteScrollCustomEvent, IonicModule} from "@ionic/angular";
import {HeaderComponent} from "../../../layout/header/header.component";
import {IS_SERVER_PLATFORM} from "../../../shared/IS_SERVER_PLATFORM.token";
import {ApiService} from "../../../shared/services/api.service";
import {HttpClient} from "@angular/common/http";

import {BehaviorSubject, finalize, of, take, tap} from "rxjs";
import {RouterModule} from "@angular/router";
import {StorePromotion} from "../../../shared/models";
import {AppDataService} from "../../../shared/services/app-data.service";
import {TruncatePipe} from "../../../truncate.pipe";


@Component({
  selector: 'app-store-promotions-index',
  templateUrl: 'store-promotion-index.component.html',
  styleUrls: ['store-promotion-index.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    HeaderComponent,
    NgIf,
    NgForOf,
    AsyncPipe,
    RouterModule,
    TruncatePipe
  ],
})
export class StorePromotionIndexComponent implements OnInit {
  loading = true;
  isLoading = false;
  storePromotionsPage = 0;
  storePromotionsNextPageUrl = '';
  private _storePromotions$ = new BehaviorSubject<StorePromotion[]>([]);
  storePromotions$ = this._storePromotions$.asObservable()


  constructor(@Inject(DOCUMENT) private document: Document,
              @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
              private apiService: ApiService,
              private transferState: TransferState,
              private http: HttpClient,
              public appDataService: AppDataService,
              private cdr: ChangeDetectorRef
  ) {

  }

  ngOnInit(): void {
    if (this.isServer) {
      console.log('is server')
    }
    this.getStorePromotions().pipe(take(1)).subscribe();

    if (!this.isServer) {
      this.browserSetup();
    }
  }

  browserSetup() {
  }

  getStorePromotions() {
    if (this.isLoading)
      return of();
    this.isLoading = true;
    this.storePromotionsPage++;
    return this.apiService.checkAndGetData(
      this.apiService.getDynamicStateKey(`storePromotions-${this.storePromotionsPage}`),
      this.apiService.getStorePromotions(this.storePromotionsNextPageUrl),
      []
    ).pipe(take(1), finalize(() => this.isLoading = false), tap((data) => {
      this.loading = false;
      this.storePromotionsNextPageUrl = data.pagination?.next_url;
      // Combine existing and new promotions
      const existingPromotions = this._storePromotions$.value;
      const newPromotions = data.data || [];

      // Create a map to remove duplicates by ID
      const promotionsMap = new Map<number, StorePromotion>();

      // Add existing promotions to map
      existingPromotions.forEach(promo => promotionsMap.set(promo.id, promo));

      // Add new promotions to map (will overwrite if duplicate)
      newPromotions.forEach((promo: any) => promotionsMap.set(promo.id, promo));

      // Convert back to array and sort
      const uniquePromotions = Array.from(promotionsMap.values()).sort(
        (a, b) => {
          // First sort by date
          const dateCompare = new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
          // If dates are equal, sort by ID for consistent ordering
          return dateCompare !== 0 ? dateCompare : b.id - a.id;
        }
      );

      this._storePromotions$.next(uniquePromotions);
      this.cdr.detectChanges();
    }));
  }

  onIonInfinite(ev: any) {
    this.getStorePromotions().pipe(take(1)).subscribe();
    setTimeout(() => {
      (ev as InfiniteScrollCustomEvent).target.complete().then();
    }, 500);
  }

  failCallbackImage(event: any) {
    this.appDataService.data$.subscribe(data => {
      event.target.src = (data!['iconPath']) ||  '/assets/icon/logo.svg'
    })
  }

  handleRefresh(event: any) {
    // Reset pagination
    this._storePromotions$.next([]);
    this.storePromotionsPage = 0;
    this.storePromotionsNextPageUrl = '';

    this.getStorePromotions().pipe(take(1)).subscribe(() => {
      event.target.complete();
    });
  }
}
