<app-header [hideProfileIcon]="false" [showBackButton]="true" backTo="/home">
  <span slot="title">عروض وكوبونات</span>
</app-header>

<ion-content>
  <ion-refresher slot="fixed" (ionRefresh)="handleRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <!-- Loading State -->
  <div *ngIf="loading" class="ion-text-center ion-padding">
    <ion-spinner name="circular"></ion-spinner>
  </div>

  <!-- Empty State -->
  <div *ngIf="!loading && (storePromotions$ | async)?.length === 0" class="ion-text-center ion-padding">
    <ion-icon name="pricetags-outline" color="medium" style="font-size: 64px;"></ion-icon>
    <h3>لا توجد عروض متاحة حالياً</h3>
    <p>تابعنا للحصول على أحدث العروض والخصومات</p>
  </div>

  <!-- Promotions List -->
  <ion-list *ngIf="!loading && (storePromotions$ | async) as storePromotions" lines="none">
    <ion-item *ngFor="let promotion of storePromotions" [routerLink]="['/store-promotions', promotion.id]" detail button class="promotion-item">
      <ion-thumbnail slot="start">
        <ion-img
          [src]="promotion.logo_url || '/assets/icon/logo.svg'"
          (ionError)="failCallbackImage($event)">
        </ion-img>
      </ion-thumbnail>

      <ion-badge [color]="promotion.discount_type === 'PERCENTAGE' ? 'danger' : 'success'" class="discount-badge">
        {{ promotion.discount_type === 'PERCENTAGE' ? promotion.discount + '%' : promotion.discount + ' ر.س' }}
      </ion-badge>

      <ion-label class="ion-text-wrap">
        <h2>{{ promotion.title }}</h2>
        <p *ngIf="promotion.description">{{ promotion.description | limitToPipe: 50 }}</p>
      </ion-label>
    </ion-item>
  </ion-list>

  <!-- Infinite Scroll -->
  <ion-infinite-scroll (ionInfinite)="onIonInfinite($event)" *ngIf="storePromotionsNextPageUrl">
    <ion-infinite-scroll-content></ion-infinite-scroll-content>
  </ion-infinite-scroll>
</ion-content>
