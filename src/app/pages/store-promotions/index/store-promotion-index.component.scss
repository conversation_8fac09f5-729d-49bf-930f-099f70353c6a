:host {
  max-width: var(--page-width);
  margin: auto;
}
// Use default Ionic styles - minimal custom CSS
ion-list {
  background: transparent;
}

ion-item.promotion-item {
  --padding-top: 12px;
  --padding-bottom: 12px;
  --background: var(--ion-color-light);
  margin-bottom: 8px;
  position: relative;
  align-items: center;

  ion-thumbnail {
    --size: 56px;
    --border-radius: 8px;
  }

  ion-label {
    position: relative;

    .discount-badge {
      position: absolute;
      top: 0;
      right: 0;
      font-size: 12px;
      padding: 4px 10px;
      font-weight: 600;
      border-radius: 12px;
    }

    h2 {
      font-weight: 600;
      margin-bottom: 4px;
      margin-top: 0;
      font-size: 16px;
      line-height: 1.3;
    }

    p {
      color: var(--ion-color-medium);
      margin: 4px 0;
      font-size: 14px;
      line-height: 1.4;
    }

    ion-note {
      display: inline-flex;
      align-items: center;
      gap: 4px;
      margin-top: 6px;
      font-size: 13px;

      ion-icon {
        font-size: 14px;
      }
    }
  }
}

// RTL Support
:host-context([dir="rtl"]) {
  ion-item.promotion-item {
    ion-label {
      .discount-badge {
        right: auto;
        left: 0;
      }

      h2 {
        padding-right: 0;
      }

      ion-note {
        ion-icon {
          margin-right: 0;
          margin-left: 4px;
        }
      }
    }
  }
}

ion-badge{
  position: absolute;
  top: 0;
  left: 16px;
}
