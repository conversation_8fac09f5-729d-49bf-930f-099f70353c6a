import {HttpParams} from '@angular/common/http';
import {
  Component,
  Inject,
  Input,
  OnInit
} from '@angular/core';
import {ActivatedRoute} from '@angular/router';
import {NavController} from '@ionic/angular';
import {IS_SERVER_PLATFORM} from "../shared/IS_SERVER_PLATFORM.token";

@Component({
  selector: 'app-redirect',
  template: '',
  standalone: true,
})
export class RedirectPage implements OnInit {
  @Input('redirect_to') redirectTo: string | null = null
  @Input('code') code: string | null = null
  @Input('scope') scope: string | null = null

  constructor(
    @Inject(IS_SERVER_PLATFORM) private isServer: boolean,
    private activatedRoute: ActivatedRoute,
    private navCtrl: NavController,
  ) {
  }

  ngOnInit(): void {
    if (this.isServer) {
    } else {
      try {
        if (this.redirectTo === undefined || this.redirectTo === null || this.redirectTo.length === 0) {
          if (this.code && this.scope && this.scope.indexOf('activity') >= 0)
            this.navCtrl.navigateRoot(`me/strava/callback?${(new HttpParams({
              fromObject: {
                ...Object.keys(this.activatedRoute.snapshot.queryParams)
                  .filter(key => key !== 'redirect_to')
                  .reduce((obj, key) => {
                    // @ts-ignore
                    obj[key] = this.activatedRoute.snapshot.queryParams[key];
                    return obj;
                  }, {})
                //...Object.fromEntries<any[]>(Object.entries(this.activatedRoute.snapshot.queryParams).filter((v:any) => v[0] !== 'redirect_to'))
              }
            })).toString()}`)
          else
            this.navCtrl.navigateRoot('home')
        } else
          window.location.href = `${decodeURIComponent(this.redirectTo)}?${(new HttpParams({
            fromObject: {
              ...Object.keys(this.activatedRoute.snapshot.queryParams)
                .filter(key => key !== 'redirect_to')
                .reduce((obj, key) => {
                  // @ts-ignore
                  obj[key] = this.activatedRoute.snapshot.queryParams[key];
                  return obj;
                }, {})
              //...Object.fromEntries<any[]>(Object.entries(this.activatedRoute.snapshot.queryParams).filter((v:any) => v[0] !== 'redirect_to'))
            }
          })).toString()}`;
      } catch {
        this.navCtrl.navigateRoot('home')
      }
    }
  }
}
