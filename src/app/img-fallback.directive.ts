import { Directive, Input, ElementRef, HostListener } from '@angular/core';

@Directive({
  selector: 'img[appImgFallback]',
  standalone: true,
})
export class ImgFallbackDirective {

  @Input() appImgFallback: string | undefined;

  constructor(private eRef: ElementRef) { }

  @HostListener('error')
  loadFallbackOnError() {
    if(this.appImgFallback !== undefined)
    {
      const element: HTMLImageElement = <HTMLImageElement>this.eRef.nativeElement;
      element.src = this.appImgFallback || '/assets/icon/logo.svg';
    }
  }

}
