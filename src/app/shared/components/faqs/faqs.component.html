<ng-container *ngIf="((appDataService.data$ | async)!['faqs'] ?? []) as faqsData">
  <app-card title="الأسئلة الشائعة" [showExpandButton]="false" [scrollable]="false"
            *ngIf="Array.isArray(faqsData) && faqsData.length > 0">
    <ion-accordion-group class="faq-accordion-group">
      <ion-accordion [value]="i" *ngFor="let faq of faqsData; let i = index" class="faq-accordion">
        <ion-item slot="header" color="light" class="faq-header">
          <ion-label class="ion-text-wrap" [innerHTML]="faq.title | sanitizeHtml" [attr.title]="faq.title">
          </ion-label>
        </ion-item>

        <div class="ion-padding faq-content" slot="content">
          <div class="content-wrapper">
            <ng-container *ngFor="let block of faq.content?.blocks; let blockIndex = index">
              <!-- Code Block -->
              <div *ngIf="block.type === 'code'" class="code-block">
                <pre><code>{{ block.data.code }}</code></pre>
              </div>

              <!-- Delimiter -->
              <ion-item-divider *ngIf="block.type === 'delimiter'" class="delimiter">
              </ion-item-divider>

              <!-- Other Blocks -->
              <div *ngIf="block.type !== 'code' && block.type !== 'delimiter'"
                   class="content-block"
                   [innerHTML]="block | renderBlock">
              </div>
            </ng-container>
          </div>
        </div>
      </ion-accordion>
    </ion-accordion-group>
  </app-card>
</ng-container>
