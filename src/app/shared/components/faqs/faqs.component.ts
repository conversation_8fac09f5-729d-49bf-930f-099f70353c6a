import {Component} from '@angular/core';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ng<PERSON>ptimizedImage} from "@angular/common";
import {NgxSplideModule} from "ngx-splide";
import {AppDataService} from "../../services/app-data.service";
import {SanitizeHtmlPipe} from "../../pipes/sanitize-html.pipe";
import {RenderBlockPipe} from "../../pipes/render-block.pipe";
import { IonicModule } from '@ionic/angular';
import {HeaderComponent} from "../../../layout/header/header.component";
import {CardComponent} from "../card/card.component";
import {IfIsBrowserDirective} from "../../IfIsBrowser.directive";

@Component({
  selector: 'app-faqs',
  templateUrl: './faqs.component.html',
  styleUrls: ['./faqs.component.scss'],
  standalone: true,
  imports: [
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    IonicModule,
    AsyncPipe,
    NgxSplideModule,
    NgOptimizedImage,
    SanitizeHtmlPipe,
    RenderBlockPipe,
    HeaderComponent,
    CardComponent,
    IfIsBrowserDirective,
  ]
})
export class FaqsComponent {
  constructor(
    public appDataService: AppDataService
  ) {

  }

  protected readonly Array = Array;
}
