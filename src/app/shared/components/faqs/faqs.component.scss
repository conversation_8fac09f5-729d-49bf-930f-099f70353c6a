:host {
  display: block;
  --content-padding: 16px;
}

// Accordion group container
.faq-accordion-group {
  display: flex;
  flex-direction: column;
  gap: 1px;
  padding-bottom: var(--content-padding);

  // Ensure content is scrollable within card
  max-height: 80vh;
  overflow-y: auto;

  // Better scrollbar styling
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--ion-color-light);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--ion-color-medium);
    border-radius: 4px;
  }
}

.faq-accordion {
  /*margin-bottom: 1px;*/
  width: 100%;

  &:last-child {
    margin-bottom: var(--content-padding); // Ensure last item has bottom spacing
  }
}

.faq-header {
  --padding-start: var(--content-padding);
  --padding-end: var(--content-padding);
  --min-height: 48px;

  ion-label {
    white-space: normal;
    overflow-wrap: break-word;
    word-wrap: break-word;
    direction: rtl; /* For Arabic text */
    padding: 8px 0;
  }
}

.faq-content {
  direction: rtl; /* For Arabic text */
  padding: var(--content-padding) !important;
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.code-block {
  background: var(--ion-color-light);
  padding: var(--content-padding);
  border-radius: 8px;
  overflow-x: auto;

  pre {
    margin: 0;
    direction: ltr; /* Code should always be LTR */
    code {
      white-space: pre-wrap;
      word-wrap: break-word;
    }
  }
}

.delimiter {
  --background: transparent;
  --padding-start: 0;
  --padding-end: 0;
  --inner-padding-end: 0;
  border-bottom: 1px solid var(--ion-color-light);
  margin: var(--content-padding) 0;
}

.content-block {
  line-height: 1.6;
  text-align: justify;

  &:not(:last-child) {
    margin-bottom: 16px;
  }

  ::ng-deep * {
    max-width: 100%;
    overflow-wrap: break-word;
    word-wrap: break-word;
  }
}

// Ensure proper padding on mobile
@media (max-width: 576px) {
  .faq-accordion-group {
    max-height: 70vh;
  }

  :host {
    --content-padding: 12px;
  }
}


:host ::ng-deep {
  app-card .card-content {
    overflow-y: auto !important;
  }
}
