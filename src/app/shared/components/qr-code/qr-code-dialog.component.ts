import {ChangeDetectionStrategy, Component} from '@angular/core';
import {<PERSON><PERSON><PERSON><PERSON>} from '@alyle/ui';
import {IonicModule, ModalController} from "@ionic/angular";
import { QRCodeModule } from 'angularx-qrcode';

@Component({
  templateUrl: './qr-code-dialog.component.html',
  styleUrls: ['qr-code-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  //standalone: true,
  providers: [
    StyleRenderer
  ],
  standalone: true,
  imports: [
    IonicModule,
    QRCodeModule,
  ]
})

export class QrCodeDialogComponent {
  // @ts-ignore
  protected data: string;

  constructor(
    private modalCtrl: ModalController
  ) {
  }

  cancel() {
    return this.modalCtrl.dismiss(null, 'cancel');
  }
}
