<div *ngIf="appBannerService.$data | async as ads"
     class="ion-margin-top">
  <!--<splide [options]="{direction: 'rtl', autoplay:true, arrows:false}"
          *ngIf="ads.length > 0 || (activeWorkoutProgram && bannerFeatures['activeWorkoutProgram'] && (featuresService.features$ | async)!['Strava'])">
    <splide-slide *ngIf="activeWorkoutProgram && (featuresService.features$ | async)!['Strava']">
      <app-active-strava-card [activeWorkoutProgram]="activeWorkoutProgram"/>
    </splide-slide>
    <splide-slide *ngFor="let slide of ads">
      <img class="ads" (click)="handelAdClick(slide)" priority
           [ngSrc]="slide.image_url" alt="slide.title" width="500" height="150"/>
    </splide-slide>
  </splide>-->
  <splide [options]="{direction: 'rtl', autoplay:true, arrows:false}" *ngIf="ads.length > 0">
    <splide-slide *ngFor="let slide of ads">
      <img class="ads" (click)="handelAdClick(slide)" priority
           [ngSrc]="slide.image_url" alt="slide.title" width="500" height="150"/>
    </splide-slide>
  </splide>
</div>
