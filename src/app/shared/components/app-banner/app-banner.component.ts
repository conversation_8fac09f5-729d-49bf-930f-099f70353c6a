import {Component, Input, OnInit} from '@angular/core';
import {Async<PERSON>ip<PERSON>, DecimalPipe, <PERSON>F<PERSON>, NgIf, NgOptimizedImage} from "@angular/common";
import {AppBannersService} from "../../services/app-banners.service";
import {ApiService} from "../../services/api.service";
import {NgxSplideModule} from "ngx-splide";
import {Router, RouterModule} from '@angular/router';
import {AppLauncher} from '@capacitor/app-launcher';
import {IonicModule, Platform} from '@ionic/angular';
import {AppDataService} from "../../services/app-data.service";
import {take} from 'rxjs';
import {FeaturesService} from "../../services/features.service";
import {ActiveStravaCardComponent} from "../active-strava-card/active-strava-card.component";

@Component({
  selector: 'app-app-banner',
  templateUrl: './app-banner.component.html',
  styleUrls: ['./app-banner.component.scss'],
  standalone: true,
  imports: [
    NgIf,
    NgFor,
    AsyncPipe,
    DecimalPipe,
    NgxSplideModule,
    NgOptimizedImage,
    IonicModule,
    RouterModule,
    ActiveStravaCardComponent,
  ]
})
export class AppBannerComponent implements OnInit {
  activeWorkoutProgram: any = undefined;
  @Input() bannerFeatures: any = {};

  constructor(
    public appBannerService: AppBannersService,
    public featuresService: FeaturesService,
    public appDataService: AppDataService,
    public apiService: ApiService,
    public platform: Platform,
    public router: Router,
  ) {
  }

  ngOnInit() {
    this.appDataService.data$
      .pipe(take(1))
      .subscribe(data => {
        if ('activeWorkoutProgram' in data)
          this.activeWorkoutProgram = data['activeWorkoutProgram'];
      });
  }

  handelAdClick(slide: any) {
    /*if (slide.type === 'PRODUCT' && slide.data['product_id']) {
      this.router.navigate(['/store/products/' + slide.data['product_id']])
    } else if (slide.type === 'CATEGORY' && slide.data['category_id']) {
      this.selectedCategory = parseInt(slide.data['category_id']);
      this._products$.next([]);
      this.productsLoaded = false;
      this.productsNextPageUrl = undefined;
      this.getProducts()
    }*/
    if (slide.url) {
      if (slide.in_app)
        this.router.navigateByUrl(slide.url).then();
      else
        AppLauncher.openUrl({url: slide.url})
    }
    this.apiService.appBannerClick(slide.id).subscribe();
  }
}
