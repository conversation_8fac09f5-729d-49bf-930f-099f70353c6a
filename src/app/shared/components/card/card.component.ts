import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {IonicModule} from "@ionic/angular";
import {NgIf} from "@angular/common";

@Component({
  selector: 'app-card',
  templateUrl: './card.component.html',
  styleUrls: ['./card.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    NgIf
  ]
})
export class CardComponent implements OnInit {
  @Input() title: string = '';
  @Input() showExpandButton = true;
  @Input() scrollable = true;
  @Output() expandButtonClicked = new EventEmitter<boolean>();

  constructor() {

  }

  ngOnInit() {
  }

}
