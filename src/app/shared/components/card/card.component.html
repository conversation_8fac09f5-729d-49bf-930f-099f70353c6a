<ion-grid class="grid" style="height:{{scrollable ? '250px' : '100%'}};">
  <ion-row class="ion-justify-content-between" style="width: 100%">
    <ion-col [size]="showExpandButton ? 10 : 12" *ngIf="title">
      <ion-text color="primary">
        <h1 class="text-secondary"
            style="text-align: start;display: inline-block;padding: 0 8px;margin: 0;">{{ title }}</h1>
      </ion-text>
    </ion-col>
    <ion-col size="2" *ngIf="showExpandButton" class="ion-no-padding ion-align-self-end ion-text-left"
             style="padding-inline-end: 17px;">
      <ion-icon (click)="expandButtonClicked.emit(true)" name="arrow-expand"
                class="ion-align-self-center expand-icon"></ion-icon>
    </ion-col>
  </ion-row>
  <div style="height:{{scrollable ? '250px' : '100%'}}; overflow-y: scroll" class="card-content">
    <ng-content></ng-content>
  </div>
</ion-grid>
