<ion-content>
  <ngx-scanner-qrcode #action="scanner" [config]="config"
                      (event)="onEvent($event, action)"></ngx-scanner-qrcode>
  <!-- Loading -->
  <p *ngIf="action.isLoading">⌛ Loading...</p>
  <!-- start -->
  <ion-button [color]="action.isStart ? 'danger' : 'primary'" expand="block"
              (click)="handle(action, action.isStart ? 'stop' : 'start')">{{ action.isStart ? 'التوقف' : 'البدء' }}
  </ion-button>
</ion-content>
