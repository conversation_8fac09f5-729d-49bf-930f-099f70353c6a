import {<PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hild} from "@angular/core";
import {
  LOAD_WASM,
  NgxScannerQrcodeComponent,
  NgxScannerQrcodeModule,
  ScannerQRCodeConfig,
  ScannerQRCodeResult,
} from 'ngx-scanner-qrcode';
import {GenericService} from "../../services/generic.service";
import {Haptics, NotificationType} from '@capacitor/haptics';
import {IonicModule, ModalController} from "@ionic/angular";
import {NgIf} from "@angular/common";
import {Inject} from '@angular/core';

LOAD_WASM().subscribe((res: any) => true);

@Component({
  selector: 'app-qr-code-scanner',
  templateUrl: 'qr-code-scanner.component.html',
  styleUrls: ['qr-code-scanner.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    NgxScannerQrcodeModule,
    NgIf,
  ],
})
export class QrCodeScannerComponent implements <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> {
  @ViewChild('action') scanner!: NgxScannerQrcodeComponent;
  BEEP = `data:audio/mpeg;base64,SUQzBAAAAAAAI1RTU0UAAAAPAAADTGF2ZjU4LjI5LjEwMAAAAAAAAAAAAAAA/+M4wAAAAAAAAAAAAEluZm8AAAAPAAAABQAAAkAAgICAgICAgICAgICAgICAgICAgKCgoKCgoKCgoKCgoKCgoKCgoKCgwMDAwMDAwMDAwMDAwMDAwMDAwMDg4ODg4ODg4ODg4ODg4ODg4ODg4P//////////////////////////AAAAAExhdmM1OC41NAAAAAAAAAAAAAAAACQEUQAAAAAAAAJAk0uXRQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/+MYxAANQAbGeUEQAAHZYZ3fASqD4P5TKBgocg+Bw/8+CAYBA4XB9/4EBAEP4nB9+UOf/6gfUCAIKyjgQ/Kf//wfswAAAwQA/+MYxAYOqrbdkZGQAMA7DJLCsQxNOij///////////+tv///3RWiZGBEhsf/FO/+LoCSFs1dFVS/g8f/4Mhv0nhqAieHleLy/+MYxAYOOrbMAY2gABf/////////////////usPJ66R0wI4boY9/8jQYg//g2SPx1M0N3Z0kVJLIs///Uw4aMyvHJJYmPBYG/+MYxAgPMALBucAQAoGgaBoFQVBUFQWDv6gZBUFQVBUGgaBr5YSgqCoKhIGg7+IQVBUFQVBoGga//SsFSoKnf/iVTEFNRTMu/+MYxAYAAANIAAAAADEwMFVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV`;

  public config: ScannerQRCodeConfig = {
    vibrate: 0,
    isBeep: false,
    constraints: {
      video: {
        // @ts-ignore
        width: this.window.innerWidth
      },
    },
  };

  constructor(
    protected modalCtrl: ModalController,
    protected genericService: GenericService,
    @Inject('Window') private window: Window,
  ) {
  }

  ngOnDestroy(): void {
    try {
      if (this.scanner && (this.scanner.isStart || this.scanner.isLoading))
        this.handle(this.scanner, 'stop')
    } catch (e) {
    }
  }

  ngOnInit() {
  }

  private VIBRATE(TIME: number) {
    try {
      if ('vibrate' in window?.navigator) {
        window?.navigator?.vibrate(TIME);
      } else if (this.genericService.isCapacitorApp()) {
        Haptics.vibrate({duration: TIME})
      }
    } catch (e) {
    }
  }

  private PLAY_AUDIO(BEEP: any) {
    const audio = new Audio(BEEP);
    // when the sound has been loaded, execute your code
    audio.oncanplaythrough = () => {
      const promise = audio.play();
      if (promise) {
        promise.catch((e) => {
          if (e.name === "NotAllowedError" || e.name === "NotSupportedError") {
            // console.log(e.name);
          }
        });
      }
    };
  };

  public onEvent(e: ScannerQRCodeResult[], action?: any): void {
    if (e.length) {
      this.handle(this.scanner, 'stop')
      this.VIBRATE(500);
      if (this.genericService.isCapacitorApp())
        Haptics.notification({type: NotificationType.Success})
      else
        this.PLAY_AUDIO(this.BEEP);
      this.modalCtrl.dismiss({
        qrCode: e[0].value
      });
    }
  }

  public handle(action: any, fn: string): void {
    if (action === undefined)
      return;
    const playDeviceFacingBack = (devices: any[]) => {
      // front camera or back camera check here!
      const device = devices.find(f => (/back|الخلفية|خلفية|rear|environment/gi.test(f.label))); // Default Back Facing Camera
      action.playDevice(device ? device.deviceId : devices[0].deviceId);
    }

    if (fn === 'start') {
      action[fn](playDeviceFacingBack).subscribe(/*(r: any) => console.log(fn, r), alert*/);
    } else {
      action[fn]().subscribe(/*(r: any) => console.log(fn, r), alert*/);
    }
  }
}
