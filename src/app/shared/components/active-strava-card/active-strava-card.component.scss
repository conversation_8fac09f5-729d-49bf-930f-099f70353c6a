ion-grid {
  padding: unset;
}

.stats-card {
  background: var(--ion-color-light, #fff);
  direction: rtl;
  margin: 16px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  height: 150px;
}

.logo-section {
  background: var(--ion-color-light, #fff);
  color: white;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  position: relative;
  overflow: hidden;
}

.logo-container {
  text-align: center;
  /*position: relative;*/
  background: #007955;
  z-index: 2;
  height: 100%;
  width: 100%;
  /*border-radius: 5px;*/
}

.background-svg {
  position: absolute;
  top: 33%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: auto;
  /*opacity: 0.2;*/
  /*background: white;*/
  z-index: 1;
}

.title {
  font-size: 14px !important;
  font-weight: bold;
  position: relative;
  margin-top: 2px;
  z-index: 1;
}

.stats-container {
  padding: 2px;
}

.stats-header {
  color: #007955;
  font-size: 20px !important;
  margin-bottom: 4px;
  margin-top: unset;
  font-weight: bold;
}

.remaining-days {
  color: #DBA852;
  font-size: 12px !important;
  margin-bottom: 8px;
}

.stats-values {
  margin-top: 5px;
  justify-content: space-between;
}

.stat-box {
  /*background-color: #F5F5F5;*/
  background: var(--ion-color-light, #fff);
  border-radius: 12px;
  padding: 4px;
  text-align: center;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.stat-box h2 {
  color: #007955;
  font-size: 20px !important;
  margin: 0;
  font-weight: bold;
}

.stat-box p {
  color: #666;
  margin: 8px 0 0 0;
}
