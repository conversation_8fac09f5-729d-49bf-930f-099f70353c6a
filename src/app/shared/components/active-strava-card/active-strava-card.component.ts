import {Component, Input, OnInit} from '@angular/core';
import {DecimalPipe, NgIf} from "@angular/common";
import {RouterModule} from '@angular/router';
import {IonicModule} from '@ionic/angular';
import {AppDataService} from "../../services/app-data.service";
import {take} from 'rxjs';
import {TafkeetPipe} from "../../pipes/tafkeet.pipe";

@Component({
  selector: 'app-active-strava-card',
  templateUrl: './active-strava-card.component.html',
  styleUrls: ['./active-strava-card.component.scss'],
  standalone: true,
  imports: [
    NgIf,
    DecimalPipe,
    IonicModule,
    RouterModule,
    TafkeetPipe,
    TafkeetPipe,
  ]
})
export class ActiveStravaCardComponent implements OnInit {
  @Input() activeWorkoutProgram: any = undefined;

  constructor(
    public appDataService: AppDataService,
  ) {
  }

  ngOnInit() {
    this.appDataService.data$
      .pipe(take(1))
      .subscribe(data => {
        if ('activeWorkoutProgram' in data)
          this.activeWorkoutProgram = data['activeWorkoutProgram'];
      });
  }
}
