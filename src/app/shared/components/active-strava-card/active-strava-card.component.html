<ion-card class="stats-card" [style]="{'margin': 'unset'}"
          *ngIf="activeWorkoutProgram" routerLink="/workout-programs/{{ activeWorkoutProgram.id }}">
  <ion-card-content [style]="{padding: 'unset'}">
    <ion-grid>
      <ion-row>
        <!-- Logo Section -->
        <ion-col size="4" class="logo-section" [style]="{padding: 'unset'}">
          <div class="logo-container">
            <!--<svg class="background-svg" width="99" height="145" viewBox="0 0 99 145" fill="none"
                 xmlns="http://www.w3.org/2000/svg">
              <g clip-path="url(#clip0_2027_1884)">
                <path
                  d="M0.014069 37.455C0.014069 0.910133 0.0158276 -35.6347 0 -72.1796C0 -72.8006 0.098483 -73 0.779071 -73C33.2521 -72.9788 65.7251 -72.9788 98.1981 -72.9965C98.8364 -72.9965 99 -72.8624 99 -72.1973C98.9824 0.9366 98.9824 74.0687 99 147.203C99 147.869 98.8347 148.004 98.1981 148.002C89.8428 147.979 81.4876 147.977 73.1324 148.002C72.5221 148.004 72.4905 147.827 72.6171 147.33C72.9565 145.983 73.2485 144.625 73.5932 143.279C73.7004 142.861 73.6019 142.667 73.215 142.483C65.7796 138.958 58.3512 135.423 50.9245 131.878C50.6413 131.742 50.4215 131.749 50.1401 131.878C42.8418 135.232 35.54 138.581 28.2312 141.91C27.7863 142.113 27.7564 142.367 27.8373 142.771C28.1468 144.325 28.4106 145.89 28.7517 147.435C28.8661 147.956 28.7113 147.993 28.2752 147.991C23.5163 147.979 18.7575 147.982 13.9986 147.982C9.5634 147.982 5.12639 147.968 0.691139 147.998C0.103759 148.002 0.00351725 147.834 0.00351725 147.284C0.0175862 110.674 0.0158276 74.0634 0.0158276 37.4532L0.014069 37.455ZM50.5481 76.6569C50.7451 76.7468 50.921 76.8227 51.0951 76.9074C61.6152 81.9638 72.137 87.0184 82.6536 92.0853C83.0809 92.29 83.2392 92.3164 83.3676 91.7625C84.9292 85.0212 86.5155 78.2835 88.1211 71.5529C88.2583 70.9812 88.1493 70.7448 87.6164 70.4837C75.4151 64.4958 63.2243 58.4868 51.0388 52.4671C50.6695 52.2836 50.4004 52.2871 50.0311 52.4671C43.8478 55.514 37.6574 58.545 31.4671 61.576C25.6249 64.4358 19.781 67.2922 13.9389 70.152C13.6733 70.2808 13.4183 70.3585 13.5045 70.8013C14.8674 77.853 16.2057 84.9101 17.5546 91.9653C17.69 92.6711 17.6953 92.6658 18.3829 92.3306C28.9505 87.1718 39.5198 82.0149 50.0891 76.858C50.2421 76.7839 50.4004 76.7204 50.5517 76.6533L50.5481 76.6569ZM21.3321 106.662C21.3585 106.792 21.3831 106.898 21.4025 107.004C21.7366 108.773 22.0707 110.543 22.4049 112.311C23.3932 117.535 24.3851 122.757 25.3611 127.983C25.4631 128.53 25.5598 128.717 26.1754 128.427C34.0681 124.721 41.9766 121.051 49.8728 117.353C50.3477 117.131 50.7134 117.125 51.19 117.348C59.2199 121.065 67.2586 124.758 75.2902 128.47C75.7281 128.673 75.8741 128.674 75.9989 128.134C77.5747 121.309 79.1733 114.488 80.7912 107.673C80.8932 107.24 80.7701 107.106 80.4307 106.944C70.6176 102.26 60.808 97.5704 51.0036 92.8704C50.6589 92.7046 50.3899 92.7134 50.0469 92.8757C45.4569 95.0546 40.8599 97.2175 36.2646 99.3858C31.4354 101.663 26.608 103.943 21.7806 106.224C21.5836 106.318 21.2917 106.337 21.3321 106.658V106.662Z"
                  fill="#007955"/>
              </g>
            </svg>-->
            <h2 class="title">{{ activeWorkoutProgram.title }}</h2>
            <img class="background-svg" src="assets/images/icons/strava-arrows.svg" alt="strava">
          </div>
        </ion-col>

        <!-- Statistics Section -->
        <ion-col size="8">
          <div class="stats-container">
            <h3 class="stats-header">إحصائيات المبادرة</h3>
            <p class="remaining-days">{{ activeWorkoutProgram.daysRemaining  | tafkeet:'day':'متبقي' }}
              على
              نهاية المبادرة</p>

            <ion-row class="stats-values">
              <ion-col size="5.8">
                <div class="stat-box">
                  <h2>{{ activeWorkoutProgram.participants | number: "1.0-0" }}</h2>
                  <p *ngIf="activeWorkoutProgram.participants === 0"></p>
                  <p *ngIf="activeWorkoutProgram.participants === 1">مشارك واحد</p>
                  <p *ngIf="activeWorkoutProgram.participants >= 2 && activeWorkoutProgram.participants <= 10">مشاركين</p>
                  <p *ngIf="activeWorkoutProgram.participants >10">مشارك</p>
                </div>
              </ion-col>
              <ion-col size="5.8">
                <div class="stat-box">
                  <h2>{{ activeWorkoutProgram.kilometers | number: "1.0-1" }}</h2>
                  <p>كيلومتر</p>
                </div>
              </ion-col>
            </ion-row>
          </div>
        </ion-col>
      </ion-row>
    </ion-grid>
  </ion-card-content>
</ion-card>
