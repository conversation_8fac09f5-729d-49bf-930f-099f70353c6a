import {Directive, Inject, TemplateRef, ViewContainerRef} from "@angular/core";
import {IS_SERVER_PLATFORM} from "./IS_SERVER_PLATFORM.token";

@Directive({
  selector: '[ifIsBrowser]',
  standalone: true,
})
export class IfIsBrowserDirective {
  constructor(
    @Inject(IS_SERVER_PLATFORM) isServer: boolean,
    templateRef: TemplateRef<any>,
    viewContainer: ViewContainerRef
  ) {
    if (!isServer) {
      viewContainer.createEmbeddedView(templateRef);
    }
  }
}
