export function objectToFormData(obj: any, form?: FormData, namespace?: string): FormData {
  const formData = form || new FormData();
  for (let property in obj) {
    if (obj.hasOwnProperty(property)) {
      const formKey = namespace ? `${namespace}[${property}]` : property;
      const val = obj[property];
      if (typeof val === 'object' && !(val instanceof File))
        objectToFormData(val, formData, formKey);
      else
        formData.append(formKey, val === undefined ? null : val);
    }
  }
  return formData;
}

export function isObject(obj: any): boolean {
  return typeof obj === 'object' && !Array.isArray(obj) && obj !== null;
}

export function convertArabicToEnglishNumbers(arabicNumber: string | number): string {
  const arabicToEnglishMap: { [key: string]: string } = {
    '٠': '0',
    '١': '1',
    '٢': '2',
    '٣': '3',
    '٤': '4',
    '٥': '5',
    '٦': '6',
    '٧': '7',
    '٨': '8',
    '٩': '9'
  };

  return (typeof (arabicNumber) === 'string' ? arabicNumber : arabicNumber.toString()).replace(/[٠-٩]/g, (match) => arabicToEnglishMap[match]);
}


export function isDigit(char: number | string) {
  return char >= '0' && char <= '9';
}

export function containsOnlyDigits(str: string) {
  return str.split('').every(isDigit);
}
