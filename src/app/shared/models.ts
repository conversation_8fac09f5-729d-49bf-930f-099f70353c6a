export interface NEWS {
  id: number;
  title: string;
  content: BlockContent;
  image: string;
  created_at: Date;
}

export interface CASE {
  id: number;
  title: string;
  content: string;
  image: string;
  remaining_amount: number;
  remaining_amount_percentage: number;
  created_at: Date;
}

export interface Pagination {
  total: number;
  next_url?: any;
}

export interface APIResponse<T> {
  data: T[];
  pagination: Pagination;
}

export interface APIDataResponse<T> {
  data: T;
}

export interface File {
  url: string;
}

export interface Data {
  title?: string;
  html?: string;
  file?: File;
  caption?: string;
  withBorder?: boolean;
  stretched?: boolean;
  withBackground?: boolean;
  text?: string;
  level?: number;
  items?: string[] | Item[];
  style?: "ordered" | "unordered";
  code?: string;
  content?: string[][];
  link?: string;
  meta?: Meta;
  embed?: string;
  message?: string;
}

export interface Item {
  text: string;
  checked: boolean;
}

export interface Meta {
  title?: string;
  description?: string;
  image?: {
    url: string;
  };
  site_name?: string;
}

export interface Block {
  id: string;
  type: "image" | "header" | "heading" | "paragraph" | "list" | "delimiter" | "quote" | "code" | "raw" | "table" | "linkTool" | "embed" | "warning" | "marker" | "checklist" | "rawTool" | "inlineCode" | "inlineCodeT";
  data: Data;
}

export interface BlockContent {
  time: number;
  blocks: Block[];
  version: string;
}

export interface BlockContent {
  time: number;
  blocks: Block[];
  version: string;
}

export interface Faq {
  title: string;
  content: string;
}

export interface ActivityUser {
  role: string
  user: string
}

export interface ActivitySponsor {
  title: string
  photo_url: string | null
}

export interface DocumentAttachment {
  title: string
  url: string
}

export interface Activity {
  id: number
  title: string
  description: string
  terms_and_conditions_required: boolean
  terms_and_conditions: BlockContent
  cover_url: string
  cover_thumb_url: string
  start_at: Date
  end_at: Date
  age_from: number
  age_to: number
  hide_members_list: boolean
  accepting_requests: boolean
  can_apply: boolean
  request_paid: boolean
  request_price: number
  current_user_request: any
  children: any
  support_amount: number
  sponsors_support_amount: number
  remaining: number
  current_member_roles: string[]
  members: ActivityUser[]
  sponsors: ActivitySponsor[]
  created_at: string
}

export interface Event {
  id: number
  title: string
  description: string
  cover_url: string
  cover_thumb_url: string
  due_at: string
  start_at: string
  end_at: string
  in_guests_list: boolean
  is_running: boolean
  is_past: boolean
  can_checkin: boolean
  is_responsible_user: boolean
  is_attended: boolean
  attendances_count: number | undefined
  created_at: string
  guests_count: {
    allowded: number,
    current: number,
  }
}

export interface Literature {
  id: number
  category: string
  title: string
  brief: string
  user: string
  file_url: string
  cover_image_url: string
  publish_date: string
  created_at: string
}

export interface DocumentModel {
  id: number
  title: string
  details: string
  type: string
  date: string
  users: string[]
  attachments: DocumentAttachment[]
  created_at: string
}

export interface CompanyModel {
  id: number
  title: string
  description: string
  thumb_url: string
  logo_url: string
  website_url: string
  snapchat_url: string
  linkedin_url: string
  facebook_url: string
  instagram_url: string
  twitter_url: string
  google_maps_url: string
  created_at: string
}

export interface Product {
  id: number
  type: string
  title: string
  content: string
  image_url: any
  thumb_image_url: any
  amount: number
  stock: number
  remaining: number
  gathered: number
  percentage: number
  limit: boolean;
  created_at: string;
  primary_category_id: number;
  categories: number[]
  statistics: {
    last_payment: string;
    payments: number;
    views: number;
  }
}

export interface WorkoutProgram {
  id: number
  title: string
  start_at: string
  end_at: string
  created_at: string;
  statistics: {
    users_count: string;
    total_distance: string;
    total_moving_time: string;
  }
  user_data: {
    summary: any;
    week_chart: any[];
    activities: any[];
    current_challenge: any;
    authed: boolean;
    strava_linked: boolean;
  }
}

export interface UserRegion {
  id: number;
  title_en: string,
  title_ar: string,
  created_at: string;
}

export interface Category {
  id: number;
  index: number,
  title: string,
  type: string,
  data: any,
}

export interface FeaturesData {
  [key: string]: boolean
}

export interface AppData {
  [key: string]: string | number | boolean | any[] | any
}

export interface Athlete {
  profile: string;
  firstname: string;
  lastname: string;
  activities_count: number;
  moving_time: number;
  distance: number;
}

export interface User {
  id: number;
  father_id: number;
  family_user_id: string;
  name: string;
  full_name: string;
  marital_status: string;
  health_status: string;
  educational_status: string;
  phone: string;
  email?: any;
  gender: string;
  bio: string;
  dob: string;
  linkedin_url: string;
  is_dead: boolean;
  is_system_admin: boolean;
  dod?: any;
  profile_photo_url?: any;
  user_region_id: number | null;
  user_region: UserRegion | null;
  can_update_picture: boolean;
  unfilled_children: boolean;
  membership?: {
    start_at: Date,
    expired_at: Date,
    actual_balance: number,
    package: {
      id: number,
      title: string,
      slug: string,
      upgradable_packages_id: number[],
    }
  },
  roles: UserRoles;
  parents: UserParent[];
  siblings: UserSiblings[];
  children: any[];
}

export interface UserRoles {
  admin: string;
}

export interface UserParent {
  id: number;
  father_id: number;
  name: string;
  gender: string;
  profile_photo_url?: any;
}

export interface UserSiblings {
  id: number;
  father_id: number;
  name: string;
  gender: string;
  profile_photo_url?: any;
}

export interface Transaction {
  uuid: string
  slug: string
  amount: number
  pdf_url: string
  confirmed_at: string
  created_at: string
  product: Product
}

// Generic transaction details interface for success page
export interface TransactionDetails {
  transaction_uuid: string;
  transaction_type: 'regular' | 'gift' | 'subscription' | 'donation' | 'refund' | 'upgrade';
  transaction_status: string;
  payment_status: string;
  transaction_amount: number;
  transaction_date: string;
  package_id?: number;
  product_id?: number;
  package_details?: {
    id: number;
    title: string;
    slug: string;
    price: number;
    features?: string[];
  };
  user_details?: {
    id: number;
    name: string;
    family_user_id: string;
  };
  transaction_metadata?: TransactionMetadata;
}

// Actual API response structure for transaction details
export interface ActualTransactionResponse {
  uuid: string;
  productId: number;
  packageId: number;
  amount: number;
  status: string;
  confirmed_at: string | null;
  is_gift: boolean;
  gift: boolean;
  recipient: string;
  recipientName: string;
  giftMessage: string | null;
  giftSubType: string;
}

// Flexible metadata structure for different transaction types
export interface TransactionMetadata {
  // Gift transaction metadata
  gift_sub_type?: 'new_membership' | 'upgrade';
  giver_user_id?: number;
  recipient_user_id?: number;
  recipient_family_user_id?: string;
  recipient_name?: string;
  gift_message?: string;
  from_package_id?: number;
  upgrade_cost?: number;
  original_price?: number;

  // Subscription metadata
  billing_cycle?: 'monthly' | 'yearly';
  auto_renew?: boolean;
  trial_period_days?: number;
  subscription_start_date?: string;
  subscription_end_date?: string;

  // Donation metadata
  cause_id?: number;
  cause_name?: string;
  is_anonymous?: boolean;
  dedication_message?: string;

  // Refund metadata
  original_transaction_id?: string;
  refund_reason?: string;
  refund_amount?: number;

  // Upgrade metadata
  from_package_title?: string;
  to_package_title?: string;
  upgrade_effective_date?: string;

  // Generic metadata for future transaction types
  [key: string]: any;
}

export interface FamilyGraph {
  success: boolean,
  action: string,
  data: object[],
}

export interface Reservation {
  id: number;
  event_type: string;
  status: string;
  event_type_locale: string;
  status_locale: string;
  start_at: Date;
  end_at: Date;
  created_at: Date;
  services: Service[];
}

export interface Consultation {
  id: number;
  type: string;
  major: string;
  details: string;
  created_at: Date;
  status: {
    title: string,
    color: string,
  };
}

export interface Service {
  id: number;
  title: string;
  pivot?: { notes: string | null } | null;
}

export interface Package {
  id: number
  title: string
  icon: string
  slug: string
  cost: number
  members_count: number
  created_at: string;
  features: string[];
  product: {
    id: number
    title: string
    content: string
    price: number
    image_url: any
    thumb_image_url: any
  }
}

export interface QuranCompetition {
  id: number
  title: string
  year: number
  accept_users: boolean
  closed: boolean
  user_reach_max: boolean
  user_has_request: boolean
  user_request_status: string | null
  created_at: string
  challenges?: QuranChallenge[]
}

export interface QuranChallenge {
  id: number
  chapters: number
  points: number
}

export interface StorePromotion {
  id: number
  title: string
  description: string
  logo_url: string
  coupon: string
  how_to_use: string
  discount: string
  discount_type: 'PERCENTAGE' | 'FIXED'
  valid_until?: string | null
  is_expired: boolean
  days_remaining?: number | null
  location?: string | null
  website_url?: string | null
  apple_store_url?: string | null
  google_play_url?: string | null
  banners?: string[]
  interests_count: number
  created_at: string
}

export interface AwardCategoriesData {
  year: number;
  active: boolean,
  can_submit: boolean,
  data: AwardCategory[],
}

export interface AwardCategory {
  id: number;
  title: string,
  routes: AwardRoute[] | undefined,
}

export interface AwardUser {
  id: number;
  award_route: string,
  award_category: string,
  reject_notes: string,
  status: string,
  status_locale: string,
  year: string,
  created_at: string
}

export interface AwardRoute {
  id: number;
  award_category_id: number;
  title: string,
  description: string,
  terms: BlockContent,
  limit: string,
  limit_data: object,
  fields: any[],
  can_submit: boolean,
  //getJsonTerms(): JSON.parse(this.terms),
}


export interface AdBanner {
  id: string | number;
  image_url: string;
  type: string;
  data: any;
}

export interface BankAccount {
  id: number
  type: string
  bank: string
  title: string
  account: string
  created_at: string
  updated_at: string
  iban: string
  is_enabled: boolean
  index?: number
  logo?: string
}

export interface Card {
  id: number;
  title: string;
  image_url: string;
  is_active?: boolean;
  category?: string;
  order: number;
  text_fields: CardTextField[];
  avatar: {
    enabled: boolean;
    x: number;
    y: number;
    width: number;
    height: number;
  };
  fabric_json?: any;
  canvas?: {
    width: number;
    height: number;
    grid_size: number;
  };
}

export interface CardTextField {
  id: number;
  card_id: number;
  label: string;
  placeholder?: string;
  text_font: string;
  text_size: number;
  text_x: number;
  text_y: number;
  text_color: string;
  text_align: string;
  text_width?: number;
  text_bold: boolean;
  line_height: number;
  order: number;
  created_at?: string;
  updated_at?: string;
  variable_name?: string;
}
