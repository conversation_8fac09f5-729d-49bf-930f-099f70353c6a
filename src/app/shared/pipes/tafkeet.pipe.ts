import {Pipe, PipeTransform} from "@angular/core";

type LiteralVariants = {
    one: string;
    two: string;
    other: string;
}

interface LiteralDefinitions {
    minute: LiteralVariants;
    day: LiteralVariants;
    // Can easily extend with more time units
}

@Pipe({
    name: 'tafkeet',
    standalone: true
})
export class TafkeetPipe implements PipeTransform {
    literals: LiteralDefinitions = {
        minute: {
            one: 'دقيقة',
            two: 'دقيقتان',
            other: 'دقائق'
        },
        day: {
            one: 'يوم',
            two: 'يومان',
            other: 'أيام'
        }
    }

    transform(count: number, key: keyof LiteralDefinitions, prefix: string): string {
        const timeUnit = this.literals[key];
        if (count === 0) return 'آخر' + ' ' + timeUnit.one;
        if (count === 1) return prefix + ' ' + timeUnit.one;
        if (count === 2) return prefix + ' ' + timeUnit.two;
        return prefix + ' ' + count + ' ' + timeUnit.other;
    }
}
