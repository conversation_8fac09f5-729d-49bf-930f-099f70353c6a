import {Pipe, PipeTransform} from '@angular/core';

@Pipe({
  name: 'secondsFormatter',
  standalone: true
})
export class SecondsFormatterPipe implements PipeTransform {
  transform(value: any): string {
    if (value === 0)
      return '0';
    let hours = Math.floor(value / 3600);
    let minutes = Math.floor((value % 3600) / 60);
    let text = [];
    if (hours > 0)
      text.push(`${hours}س`)
    if (minutes > 0 || hours === 0)
      text.push(`${minutes}د`)
    return text.join(' ');
  }
}
