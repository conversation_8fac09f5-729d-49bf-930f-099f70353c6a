import {Pipe, PipeTransform} from "@angular/core";
import {formatDistanceToNowStrict} from "date-fns";
import {ar as arLocale} from "date-fns/locale";

@Pipe({
  name: 'formatDistance',
  standalone: true
})
export class FormatDistancePipe implements PipeTransform {
  constructor() {
  }

  transform(date?: string, addSuffix = true) {
    if (!date) return date;
    return formatDistanceToNowStrict(new Date(date), {addSuffix, locale: arLocale});
  }
}
