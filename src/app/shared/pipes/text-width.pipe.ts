// write sanitizer pipe to sanitize html


import {Pipe, PipeTransform} from "@angular/core";

@Pipe({
  name: 'textWidth',
  standalone: true
})
export class TextWidthPipe implements PipeTransform {
  constructor() {
  }

  transform(text?: string) {
    if (!text) return text;
    const fontSize = 1.7; // Replace with the desired font size in pixels
    const fontFamily = "helvetica, Arial, sans-serif"; // Replace with the desired font family
    const fontWeight = "normal"; // Replace with the desired font weight

    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    if (!ctx) return 60;
    ctx.font = `${fontWeight} ${fontSize}rem ${fontFamily}`;
    const width = ctx.measureText(text).width;

    return Math.max(Number(width.toFixed(0)), 60);
  }
}
