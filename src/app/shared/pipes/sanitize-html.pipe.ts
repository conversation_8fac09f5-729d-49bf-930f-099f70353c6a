// write sanitizer pipe to sanitize html


import {Pipe, PipeTransform} from "@angular/core";
import {DomSanitizer} from "@angular/platform-browser";

@Pipe({
  name: 'sanitizeHtml',
  standalone: true
})
export class SanitizeHtmlPipe implements PipeTransform {
  constructor(
    private sanitizer: DomSanitizer
  ) {
  }

  transform(html?: string) {
    if (!html) return html;
    return this.sanitizer.bypassSecurityTrustHtml(html);
  }
}
