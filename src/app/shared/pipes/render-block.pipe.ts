import {Pipe, PipeTransform} from "@angular/core";
import {Block, Item} from "../models";
import {environment} from "../../../environments/environment";

@Pipe({
  name: 'renderBlock',
  standalone: true
})
export class RenderBlockPipe implements PipeTransform {

  transform(value: Block): any {
    switch (value.type) {
      case 'paragraph':
        return '<p>' + value.data.text + '</p>';
      case 'header':
      case 'heading':
        return '<h' + value.data.level + '>' + value.data.text + '</h' + value.data.level + '>';
      case 'image':
        let _url = value.data.file?.url;
        if (_url && !(_url.startsWith('https://') || _url.startsWith('http://')))
          _url = `${environment.storageUrl}${_url}`
        return _url ? `<img src="${_url}">` : '';
      case 'list':
        if (value.data.style === 'ordered')
          return '<ol>' + value.data.items?.map(item => '<li>' + item + '</li>').join('') + '</ol>';
        return '<ul>' + value.data.items?.map(item => '<li>' + item + '</li>').join('') + '</ul>';
      case 'delimiter':
      case 'code':
        return '';
      case 'quote':
        return '<blockquote>' + value.data.text + '</blockquote>';
      case 'raw':
        return value.data.html;
      case 'table':
        return '<table>' + value.data.content?.map(row => '<tr>' + row.map(cell => '<td>' + cell + '</td>').join('') + '</tr>').join('') + '</table>';
      case 'linkTool':
        return '<a href="' + value.data.link + '">' + (value.data.meta?.title ?? value.data.link) + '</a>';
      case 'embed':
        return '<iframe src="' + value.data.embed + '"></iframe>';
      case 'warning':
        return '<div class="cdx-block cdx-warning">' + value.data.title + '</div>' +
          '<div class="cdx-block cdx-warning">' + value.data.message + '</div>';
      case 'checklist':
        return '<ul class="cdx-block cdx-checklist">' + value.data.items?.map((item) => '<li class="cdx-checklist__item">' + (item as Item).text + '</li>').join('') + '</ul>';
      default:
        return value;
    }
  }
}
