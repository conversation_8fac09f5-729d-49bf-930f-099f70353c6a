import { Injectable } from "@angular/core";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>p<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest, HttpErrorResponse } from "@angular/common/http";
import { Observable, from, throwError, catchError, switchMap } from "rxjs";
import { environment } from "../../../environments/environment";
import { StorageService } from "./storage.service";
import { NavController, ToastController } from "@ionic/angular";
import { Router } from "@angular/router";
import { App } from '@capacitor/app';

@Injectable()
export class AltwijryHttpInterceptor implements HttpInterceptor {
  constructor(
    private storageService: StorageService,
    private navController: NavController,
    private router: Router,
    private toastController: ToastController
  ) {}

  /**
   * Show toast message to the user in Arabic
   */
  private async showToast(message: string, duration: number = 3000, color: string = 'warning'): Promise<void> {
    const toast = await this.toastController.create({
      message: message,
      duration: duration,
      position: 'bottom',
      color: color,
      buttons: [
        {
          text: 'موافق',
          role: 'cancel'
        }
      ],
      cssClass: 'rtl-toast' // Add a CSS class for RTL support
    });
    await toast.present();
  }

  /**
   * Logout user by clearing storage and redirecting to login
   */
  private logoutUser(returnUrl: string): void {
    // Clear storage to reset app state
    this.storageService.clear().then(() => {
      // Show toast message in Arabic
      this.showToast('انتهت جلستك. يرجى تسجيل الدخول مرة أخرى.', 4000, 'danger');

      // Navigate to login with return URL
      this.navController.navigateRoot('login', {
        queryParams: { returnURL: returnUrl }
      });
    });
  }

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // Only intercept requests to our API
    if (!req.url.includes(environment.baseUrl)) {
      return next.handle(req);
    }

    // Step 1: Add app info headers
    const appInfoPromise = App.getInfo()
      .then(appInfo => {
        return req.clone({
          headers: req.headers
            .set('X-APP-ID', appInfo.id)
            .set('X-APP-BUILD', appInfo.build)
            .set('X-APP-VERSION', appInfo.version)
        });
      })
      .catch(() => {
        // If app info fails, continue with original request
        // console.warn('Failed to get app info');
        return req;
      });

    // Step 2: Process authentication headers
    return from(appInfoPromise).pipe(
      switchMap(reqWithAppInfo => {
        // Check if this is a skip-auth request
        if (reqWithAppInfo.headers.has('skip-auth')) {
          const cleanedReq = reqWithAppInfo.clone({
            headers: reqWithAppInfo.headers.delete('skip-auth')
          });
          return next.handle(cleanedReq);
        }

        // Get token from storage
        return from(this.storageService.getItem('access_token')).pipe(
          switchMap(token => {
            // Handle authentication
            if (token === null && !reqWithAppInfo.headers.has('guests-allowed')) {
              this.showToast('يرجى تسجيل الدخول للمتابعة.', 3000, 'warning');
              this.logoutUser(this.router.url);
              return throwError(() => new Error('Authentication required'));
            }

            // Add token to request
            const authorizedReq = reqWithAppInfo.clone({
              headers: reqWithAppInfo.headers
                .delete('guests-allowed')
                .set('Authorization', `Bearer ${token || ''}`)
            });

            // Process the request and handle errors
            return next.handle(authorizedReq).pipe(
              catchError((error: HttpErrorResponse) => {
                if (error.status === 401) {
                  // On unauthorized, clear storage and redirect to login
                  console.log('Token expired or invalid, logging out');
                  this.logoutUser(this.router.url);
                  return throwError(() => new Error('Session expired'));
                }
                else if (error.status === 403) {
                  // On forbidden, redirect to home
                  console.log('Access forbidden, redirecting to home');
                  this.showToast('ليس لديك صلاحية للوصول إلى هذا المحتوى.', 3000, 'danger');
                  this.navController.navigateRoot('/home');
                  return throwError(() => new Error('Access forbidden'));
                }
                else if (error.status === 0 || error.status === 504) {
                  // Network error
                  this.showToast('خطأ في الاتصال. يرجى التحقق من اتصالك بالإنترنت.', 3000, 'warning');
                }
                else if (error.status >= 500) {
                  // Server error
                  this.showToast('خطأ في الخادم. يرجى المحاولة مرة أخرى لاحقًا.', 3000, 'danger');
                }

                // Pass through other errors
                return throwError(() => error);
              })
            );
          })
        );
      })
    );
  }
}
