import { ModalController } from '@ionic/angular';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ModalService {

  constructor(private modalController: ModalController) { }

  async closeAllModals(data?: any, role?: string) {
    try {
      let currentModal = await this.modalController.getTop();
      while (currentModal) {
        await this.modalController.dismiss(data, role);
        currentModal = await this.modalController.getTop();
      }
    } catch (error) {
      console.error('Error closing modals:', error);
    }
  }
}
