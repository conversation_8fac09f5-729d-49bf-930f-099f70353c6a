// storageService
// This service is used to store data in local storage
//
// Path: src/app/shared/services/storage.service.ts

import {Injectable} from '@angular/core';
import {Preferences} from '@capacitor/preferences';
import {Storage} from '@ionic/storage-angular';

@Injectable({
  providedIn: 'root'
})
export class StorageService {
  private _storage: Storage | null = null;

  constructor(private storage: Storage) {
    this.init();
  }

  async init() {
    // If using, define drivers here: await this.storage.defineDriver(/*...*/);
    this._storage = await this.storage.create();
    this.migratePreferencesToStorage();
  }

  async setItem(key: string, value: any) {
    return await this.storage.set(key, value);
  }

  async getItem(key: string) {
    return (await this._storage?.get(key));
  }

  async removeItem(key: string) {
    return (await this._storage?.remove(key));
  }
  /**
   * Clears all stored data from storage
   * @returns Promise that resolves when storage is cleared
   */
  async clear(): Promise<void> {
    if (!this._storage) {
      console.warn('Storage not initialized');
      return;
    }

    try {
      // Clear all data from Ionic Storage
      await this._storage.clear();
      console.log('Storage cleared successfully');
      return;
    } catch (error) {
      console.error('Error clearing storage:', error);
      throw error;
    }
  }


  migratePreferencesToStorage() {
    // Migrate all preferences to storage
    Preferences.keys().then(({keys}) => {
      keys.forEach(async key => {
        const value = (await Preferences.get({key})).value;
        await this.setItem(key, value);
        await Preferences.remove({key});
      });
    });
  }

}
