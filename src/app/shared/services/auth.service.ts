// src/app/shared/services/auth.service.ts

import { Injectable } from '@angular/core';
import { StorageService } from './storage.service';
import { NavController, ToastController } from '@ionic/angular';
import { Router, NavigationStart } from '@angular/router';
import { filter } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private tokenExpiryTimeout: any = null;

  constructor(
    private storageService: StorageService,
    private navController: NavController,
    private toastController: ToastController,
    private router: Router
  ) {
    // Check token on navigation events
    this.router.events.pipe(
      filter(event => event instanceof NavigationStart)
    ).subscribe(() => {
      this.checkTokenExpiration();
    });
  }


  /**
   * Check if token has expired
   */
  async checkTokenExpiration() {
    const expiresAt = await this.storageService.getItem('expires_at');
    const token = await this.storageService.getItem('access_token');

    // If we have a token and it's expired, logout
    if (token && expiresAt && expiresAt < Date.now()) {
      this.logoutUser();
      return true;
    }

    return false;
  }

  /**
   * Logout user automatically
   */
  async logoutUser() {
    // Clear timeout
    if (this.tokenExpiryTimeout) {
      clearTimeout(this.tokenExpiryTimeout);
      this.tokenExpiryTimeout = null;
    }
    // Show logout notification
    const toast = await this.toastController.create({
      message: 'انتهت جلستك. يرجى تسجيل الدخول مرة أخرى.',
      duration: 4000,
      position: 'bottom',
      color: 'danger',
      buttons: [
        {
          text: 'موافق',
          role: 'cancel'
        }
      ],
      cssClass: 'rtl-toast'
    });

    await toast.present();

    // Clear storage
    await this.storageService.clear();

    // Navigate to login
    const currentUrl = window.location.pathname;
    this.navController.navigateRoot('login', {
      queryParams: { returnURL: currentUrl }
    });
  }
}
