import {Injectable} from "@angular/core";
import {BehaviorSubject, shareReplay, map} from "rxjs";
import {ApiService} from "./api.service";
import {AwardCategoriesData, AwardCategory} from "../models";

@Injectable({
  providedIn: 'root'
})
export class AwardCategoriesService {
  private _dataLoaded$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  private _categories$: BehaviorSubject<AwardCategory[]> = new BehaviorSubject<AwardCategory[]>([]);
  private _data$: BehaviorSubject<AwardCategoriesData> = new BehaviorSubject<AwardCategoriesData>({} as AwardCategoriesData);
  private _year$: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  private _active$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  private _can_submit$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  get $dataLoaded() {
    return this._dataLoaded$.asObservable();
  }

  get $categories() {
    return this._categories$.asObservable();
  }

  get $data() {
    return this._data$.asObservable();
  }

  get $year() {
    return this._year$.asObservable();
  }

  get $active() {
    return this._active$.asObservable();
  }

  get $canSubmit() {
    return this._can_submit$.asObservable();
  }

  constructor(private apiService: ApiService) {
    this.initialize().then(r => {
    })
  }

  async initialize() {
    this.loadCategories();
  }

  loadCategories() {
    this.apiService.awardCategories().subscribe((res) => {
      this._categories$ = new BehaviorSubject<AwardCategory[]>(res.data);
      this._year$ = new BehaviorSubject<number>(res.year);
      this._active$ = new BehaviorSubject<boolean>(res.active);
      this._can_submit$ = new BehaviorSubject<boolean>(res.can_submit);
      this._data$ = new BehaviorSubject<AwardCategoriesData>(res);
      this._dataLoaded$.next(true);
    })
  }
}
