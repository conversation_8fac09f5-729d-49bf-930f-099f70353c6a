import {Injectable} from "@angular/core";
import {BehaviorSubject, shareReplay, map} from "rxjs";
import {ApiService} from "./api.service";
import {Category} from "../models";

@Injectable({
  providedIn: 'root'
})
export class CategoriesService {
  private _dataLoaded$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  private _categories$: BehaviorSubject<Category[]> = new BehaviorSubject<Category[]>([]);

  get $dataLoaded() {
    return this._dataLoaded$.asObservable();
  }

  get $categories() {
    return this._categories$.asObservable();
  }

  get $literatureCategories() {
    return this._categories$.asObservable().pipe(
      map((categories) => categories.filter((c) => c.type === 'LITERATURE'))
    );
  }

  get $companyCategories() {
    return this._categories$.asObservable().pipe(
      map((categories) => categories.filter((c) => c.type === 'COMPANY'))
    );
  }

  get $consultationCategories() {
    return this._categories$.asObservable().pipe(
      map((categories) => categories.filter((c) => c.type === 'CONSULTATION'))
    );
  }

  get $productCategories() {
    return this._categories$.asObservable().pipe(
      map((categories) =>
        categories.filter((c) => c.type === 'PRODUCT')
          .sort((a, b) => a.index - b.index)
      )
    );
  }

  constructor(private apiService: ApiService) {
    this.initialize().then(r => {
    })
  }

  async initialize() {
    this.loadCategories().subscribe((res) => {
      this._categories$ = new BehaviorSubject<Category[]>(res.data);
      this._dataLoaded$.next(true);
    })
  }

  loadCategories() {
    return this.apiService.categories();
  }
}
