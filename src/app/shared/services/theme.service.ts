import {Injectable, OnD<PERSON>roy} from '@angular/core';
import {BehaviorSubject, Observable, takeUntil, Subject} from 'rxjs';
import {StorageService} from './storage.service';

@Injectable({
  providedIn: 'root',
})
export class ThemeService implements OnDestroy {
  private _userPreferences: 'system' | 'light' | 'dark' | null = null;
  private _themeColors: { light: { [key: string]: string }; dark: { [key: string]: string } } | null = null;
  private _customColors: { [key: string]: string } | null = null;

  get userPreferences(): 'system' | 'light' | 'dark' {
    return this._userPreferences || 'system';
  }

  get isDark(): Observable<boolean> {
    return this._isDark.asObservable();
  }

  private _isDark = new BehaviorSubject(
    window.matchMedia('(prefers-color-scheme: dark)').matches
  );

  private destroy$ = new Subject<void>();

  constructor(private storageService: StorageService) {
    this.loadUserPreferences();
    window
      .matchMedia('(prefers-color-scheme: dark)')
      .addEventListener('change', (mediaQuery) => {
        if (this.userPreferences === 'system') {
          this._isDark.next(mediaQuery.matches);
          const htmlElement = document.documentElement;
          if (mediaQuery.matches) {
            htmlElement.classList.add('ion-palette-dark');
          } else {
            htmlElement.classList.remove('ion-palette-dark');
          }
        }
      });
  }

  private async loadUserPreferences(): Promise<void> {
    try {
      const savedTheme = await this.storageService.getItem('theme-preference');
      this._userPreferences = savedTheme || 'system';
    } catch (error) {
      console.warn('Failed to load theme preferences:', error);
      this._userPreferences = 'system';
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  async initializeTheme(theme: {
    light: { [key: string]: string };
    dark: { [key: string]: string };
  }): Promise<void> {
    this._themeColors = theme;
    await this.loadUserPreferences();
    await this.loadCustomColors();
    this.applyThemeMode();
    this.updateColors(theme);
    this._isDark.pipe(takeUntil(this.destroy$)).subscribe(() => {
      if (this._themeColors) {
        this.updateColors(this._themeColors);
      }
    });
  }

  private applyThemeMode(): void {
    const preference = this.userPreferences;
    const htmlElement = document.documentElement;
    
    if (preference === 'dark') {
      this._isDark.next(true);
      htmlElement.classList.add('ion-palette-dark');
    } else if (preference === 'light') {
      this._isDark.next(false);
      htmlElement.classList.remove('ion-palette-dark');
    } else {
      // system preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      this._isDark.next(prefersDark);
      if (prefersDark) {
        htmlElement.classList.add('ion-palette-dark');
      } else {
        htmlElement.classList.remove('ion-palette-dark');
      }
    }
  }

  updateColors(colors: { light: { [key: string]: string }; dark: { [key: string]: string } }) {
    // If custom colors are set, use them instead of theme colors
    if (this._customColors) {
      this.applyColorSet(this._customColors);
      return;
    }

    const isDarkMode = this._isDark.value;
    const colorSet = isDarkMode ? colors.dark : colors.light;
    this.applyColorSet(colorSet);
  }

  private applyColorSet(colorSet: { [key: string]: string }) {
    for (const key in colorSet) {
      if (colorSet.hasOwnProperty(key)) {
        const colorValue = colorSet[key];

        // Handle custom CSS variables (those that start with --)
        if (key.startsWith('--')) {
          document.documentElement.style.setProperty(key, colorValue);
          continue;
        }

        // Handle Ionic color variables
        const cssVariable = `--ion-color-${key}`;
        document.documentElement.style.setProperty(cssVariable, colorValue);
        this.updateShadeTint(colorValue, key);
        this.updateContrast(colorValue, key);
      }
    }
  }

  private updateShadeTint(color: string, colorName: string) {
    const rgb = this.hexToRgb(color);
    if (rgb) {
      const shade = this.shadeColor(rgb, -0.15);
      const tint = this.tintColor(rgb, 0.15);

      const rgbVar = `--ion-color-${colorName}-rgb`;
      const shadeVar = `--ion-color-${colorName}-shade`;
      const tintVar = `--ion-color-${colorName}-tint`;

      document.documentElement.style.setProperty(rgbVar, `${rgb.r}, ${rgb.g}, ${rgb.b}`);
      document.documentElement.style.setProperty(shadeVar, this.rgbToHex(shade.r, shade.g, shade.b));
      document.documentElement.style.setProperty(tintVar, this.rgbToHex(tint.r, tint.g, tint.b));
    }
  }

  private updateContrast(color: string, colorName: string) {
    const rgb = this.hexToRgb(color);
    if (rgb) {
      // Calculate relative luminance
      const luminance = 0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b;

      // Choose white or black based on luminance
      const contrastColor = luminance > 128 ? '#000000' : '#ffffff';
      const contrastRgb = luminance > 128 ? '0,0,0' : '255,255,255';

      const contrastVar = `--ion-color-${colorName}-contrast`;
      const contrastRgbVar = `--ion-color-${colorName}-contrast-rgb`;

      document.documentElement.style.setProperty(contrastVar, contrastColor);
      document.documentElement.style.setProperty(contrastRgbVar, contrastRgb);
    }
  }

  private hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
      }
      : null;
  }

  private rgbToHex(r: number, g: number, b: number): string {
    return '#' + [r, g, b].map((x) => x.toString(16).padStart(2, '0')).join('');
  }

  private shadeColor(rgb: { r: number; g: number; b: number }, percent: number): { r: number; g: number; b: number } {
    const f = parseInt('ffffff', 16);
    const t = percent < 0 ? 0 : f;
    const p = percent < 0 ? percent * -1 : percent;
    const R = rgb.r;
    const G = rgb.g;
    const B = rgb.b;
    const newR = Math.round((t - R) * p) + R;
    const newG = Math.round((t - G) * p) + G;
    const newB = Math.round((t - B) * p) + B;
    return {r: newR, g: newG, b: newB};
  }

  private tintColor(rgb: { r: number; g: number; b: number }, percent: number): { r: number; g: number; b: number } {
    return this.shadeColor(rgb, percent);
  }

  async setTheme(mode: 'system' | 'light' | 'dark'): Promise<void> {
    try {
      // Save preference to storage
      await this.storageService.setItem('theme-preference', mode);
      this._userPreferences = mode;
      console.log('theme set to:', mode);

      // Apply the theme immediately
      this.applyThemeMode();
      
      // Update colors if theme colors are available
      if (this._themeColors) {
        this.updateColors(this._themeColors);
      }
    } catch (error) {
      console.error('Failed to save theme preference:', error);
    }
  }

  /**
   * Sets custom colors that override theme colors
   * @param colors Object with color key-value pairs (e.g., { primary: '#ff0000', secondary: '#00ff00' })
   */
  async setCustomColors(colors: { [key: string]: string } | null): Promise<void> {
    try {
      if (colors) {
        await this.storageService.setItem('custom-theme-colors', colors);
        this._customColors = colors;
        this.applyColorSet(colors);
      } else {
        // Clear custom colors and revert to theme colors
        await this.storageService.removeItem('custom-theme-colors');
        this._customColors = null;
        if (this._themeColors) {
          this.updateColors(this._themeColors);
        }
      }
    } catch (error) {
      console.error('Failed to save custom colors:', error);
    }
  }

  /**
   * Loads custom colors from storage
   */
  private async loadCustomColors(): Promise<void> {
    try {
      const savedColors = await this.storageService.getItem('custom-theme-colors');
      if (savedColors) {
        this._customColors = savedColors;
      }
    } catch (error) {
      console.warn('Failed to load custom colors:', error);
    }
  }
}
