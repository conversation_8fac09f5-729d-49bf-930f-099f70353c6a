import {Injectable} from "@angular/core";
import {BehaviorSubject} from "rxjs";
import {ApiService} from "./api.service";

@Injectable({
  providedIn: 'root'
})
export class StoreBannersService {
  private _dataLoaded$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  private _data$: BehaviorSubject<any[]> = new BehaviorSubject<any[]>([]);

  get $dataLoaded() {
    return this._dataLoaded$.asObservable();
  }

  get $data() {
    return this._data$.asObservable();
  }

  constructor(private apiService: ApiService) {
    this.initialize().then(r => {
    })
  }

  async initialize() {
  }

  loadStoreBanners() {
    return this.apiService.storeBanners()
  }
}
