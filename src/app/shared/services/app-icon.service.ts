import {Injectable} from '@angular/core';
import {AppIcon} from "@capacitor-community/app-icon";
import {Platform} from "@ionic/angular";
import {BehaviorSubject} from 'rxjs';
import {GenericService} from "./generic.service";

@Injectable({
  providedIn: 'root'
})
export class AppIconService {
  public defaultIcon = 'goldGold';
  public appIcon = 'goldGold';
  public appIconChanged$ = new BehaviorSubject<string>(this.defaultIcon);
  appIcons = [
    {
      slug: 'whiteBlack',
      text: 'أبيض',
      selected: false
    },
    {
      slug: 'goldGold',
      text: 'ذهبي 1',
      selected: false
    },
    {
      slug: 'goldWhite',
      text: 'ذهبي 2',
      selected: false
    },
    {
      slug: 'goldBlack',
      text: 'ذهبي 3',
      selected: false
    },
    {
      slug: 'greenWhite',
      text: 'أخضر',
      selected: false
    },
    {
      slug: 'blackWhite',
      text: 'أسود',
      selected: false
    },
    {
      slug: 'grayWhite',
      text: 'رصاصي',
      selected: false
    },
    {
      slug: 'orangeOrange',
      text: 'برتقالي',
      selected: false
    }
  ]


  constructor(private platform: Platform,
              private genericService: GenericService) {
    if (!this.genericService.isCapacitorApp())
      return;

    // set default icon selected on init
    AppIcon.getName().then((icon) => {
      this.setIconSelected(icon.value ?? this.defaultIcon);
      this.appIconChanged$.next(icon.value ?? this.defaultIcon);
    })
  }

  /***
   * set icon selected by slug name and update the list
   * @param iconSlug
   */
  setIconSelected(iconSlug: string) {
    if (!this.genericService.isCapacitorApp()) {
      return;
    }

    this.appIcons = this.appIcons.map(oneIcon => {
      oneIcon.selected = oneIcon.slug === iconSlug;
      return oneIcon;
    });
    this.appIcon = iconSlug;
    this.appIconChanged$.next(iconSlug);

    if (iconSlug === this.defaultIcon) {
      this.resetIcon();
      console.log('(appicon service) reset to: ', iconSlug)

      return;
    }
    console.log('(appicon service) appIcon changed to: ', iconSlug)


    this.changeIcon(iconSlug);
  }


  /**
   * change app icon by name and disable other icons
   * @param iconName
   */
  async changeIcon(iconName: string) {
    if (iconName === this.defaultIcon) {
      // reset to default
      this.resetIcon();
      console.log('reset icon');
      return;
    }
    console.log('iconName', iconName)

    const disableIcons = this.appIcons
      .filter(icon => icon.slug !== iconName)
      .map(icon => icon.slug);

    console.log('disableIcons', disableIcons)


    try {
      const isSupported = await AppIcon.isSupported();
      console.debug(`Alternate Icons Supported: `, isSupported.value);

      AppIcon.change({name: iconName, suppressNotification: true, disable: disableIcons}).then((res) => {
        console.log('AppIcon.change: ', iconName)
      });


    } catch (error) {
      console.debug(error);
    }


  }

  resetIcon() {
    try {
      AppIcon.reset({suppressNotification: true}).then((res) => {
        console.log('AppIcon.reset: ', res)
      });
    } catch (error) {
      console.debug(error);
    }
  }

}
