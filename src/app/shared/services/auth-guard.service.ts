import {inject, Injectable} from "@angular/core";
import {ActivatedRouteSnapshot, RouterStateSnapshot} from "@angular/router";
import {StorageService} from "./storage.service";
import {NavController} from "@ionic/angular";

@Injectable({providedIn: 'root'})
export class AuthGuard {
  constructor(
    private storageService: StorageService,
  ) {
  }

  async canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    const navCtrl = inject(NavController);
    const accessToken = await this.storageService.getItem('access_token');
    return accessToken !== null ? true : navCtrl.navigateRoot('login', {
      queryParams: {returnURL: `${state.url}`}
    });
  }
}
