import {inject, Inject, Injectable, TransferState} from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {environment} from "../../../environments/environment";
import {APIResponse, Consultation} from "../models";
import {IS_SERVER_PLATFORM} from "../IS_SERVER_PLATFORM.token";
import {Observable, of, tap} from "rxjs";

@Injectable({
  providedIn: 'root'
})
export class ConsultationService {
  env = environment
  http = inject(HttpClient);
  tstate = inject(TransferState);

  constructor(@Inject(IS_SERVER_PLATFORM) private isServer: boolean
  ) {

  }

  checkAndGetData(key: string & {
    __not_a_string: never;
    __value_type?: void
  }, getDataObservable: Observable<any>, defaultValue: any = []) {
    if (this.tstate.hasKey(key)) {
      return of(this.tstate.get(key, defaultValue));
    } else {
      return getDataObservable.pipe(
        tap((data) => {
          if (this.isServer) {
            this.tstate.set(key, data);
          }
        })
      );
    }
  }

  index(nextPageUrl?: string) {
    return this.http.get<APIResponse<Consultation>>(nextPageUrl ? nextPageUrl : `${this.env.baseUrl}/consultations`);
  }

  store(data: object) {
    return this.http.post(`${this.env.baseUrl}/consultations`, data);
  }

  show(id: number) {
    return this.http.get<APIResponse<Consultation>>(`${this.env.baseUrl}/consultations/${id}`);
  }
}
