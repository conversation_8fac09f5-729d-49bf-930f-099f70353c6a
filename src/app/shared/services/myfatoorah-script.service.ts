import { Injectable, Inject } from '@angular/core';
import { environment } from '../../../environments/environment';
import { IS_SERVER_PLATFORM } from '../IS_SERVER_PLATFORM.token';

@Injectable({
  providedIn: 'root'
})
export class MyfatoorahScriptService {
  private scriptsLoaded = {
    session: false,
    applepay: false
  };

  constructor(
    @Inject(IS_SERVER_PLATFORM) private isServer: boolean
  ) {}

  /**
   * Load MyFatoorah scripts dynamically based on environment configuration
   */
  async loadScripts(): Promise<void> {
    if (this.isServer) {
      return Promise.resolve();
    }

    const promises: Promise<void>[] = [];

    // Load session script
    if (!this.scriptsLoaded.session) {
      promises.push(this.loadScript(environment.myfatoorah.session_js_url, 'myfatoorah-session'));
    }

    // Load Apple Pay script
    if (!this.scriptsLoaded.applepay) {
      promises.push(this.loadScript(environment.myfatoorah.applepay_js_url, 'myfatoorah-applepay'));
    }

    await Promise.all(promises);
  }

  /**
   * Load a single script dynamically
   */
  private loadScript(src: string, id: string): Promise<void> {
    return new Promise((resolve, reject) => {
      // Check if script already exists
      if (document.getElementById(id)) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.type = 'text/javascript';
      script.src = src;
      script.id = id;
      
      script.onload = () => {
        if (id === 'myfatoorah-session') {
          this.scriptsLoaded.session = true;
        } else if (id === 'myfatoorah-applepay') {
          this.scriptsLoaded.applepay = true;
        }
        resolve();
      };
      
      script.onerror = (error) => {
        console.error(`Failed to load MyFatoorah script: ${src}`, error);
        reject(error);
      };

      document.body.appendChild(script);
    });
  }

  /**
   * Check if scripts are loaded
   */
  areScriptsLoaded(): boolean {
    return this.scriptsLoaded.session && this.scriptsLoaded.applepay;
  }

  /**
   * Wait for scripts to be loaded
   */
  async waitForScripts(): Promise<void> {
    if (this.areScriptsLoaded()) {
      return Promise.resolve();
    }

    return this.loadScripts();
  }
}
