import {inject, Injectable} from "@angular/core";
import {HttpClient} from "@angular/common/http";
import {StorageService} from "./storage.service";
import {BehaviorSubject, shareReplay} from "rxjs";
import {User, UserRegion} from "../models";
import {ApiService} from "./api.service";
import {environment} from "../../../environments/environment";

@Injectable({
  providedIn: 'root'
})
export class ProfileService {
  env = environment
  http = inject(HttpClient);
  private _user$ = new BehaviorSubject<User | null>(null);
  private _regions$: BehaviorSubject<UserRegion[] | null> = new BehaviorSubject<UserRegion[] | null>(null);

  constructor(
    private storageService: StorageService,
    private apiService: ApiService,
  ) {
    this.initialize().then(r => {
    })
  }

  get $user() {
    return this._user$.asObservable().pipe(shareReplay(1));
  }

  get $regions() {
    return this._regions$.asObservable();
  }

  async initialize() {
    const token = await this.storageService.getItem('access_token');
    const JsonUser = (await this.storageService.getItem('user'));
    const user = JSON.parse(JsonUser || '{}') as User;
    if (token) {
      if (user.id)
        this._user$.next(user);
      this.loadUser();
    }
    this.loadRegions();
  }

  async setUser(user: User) {
    await this.storageService.setItem('user', JSON.stringify(user));
    this._user$.next(user);
  }

  removeUser() {
    this.storageService.removeItem('user');
    this._user$.next(null);
  }

  loadUser() {
    this.apiService.me().subscribe((res) => {
      /*Sentry.setUser({
        id: res.id,
        name: res.name,
        family_user_id: res.family_user_id,
      })*/
      this._user$.next(res);
      this.storageService.setItem('user', JSON.stringify(res));
    })
  }

  getChildren() {
    return this.http.get(`${this.env.baseUrl}/auth/me/children`);
  }

  loadRegions() {
    this.apiService.userRegions().subscribe((res) => {
      this._regions$ = new BehaviorSubject<UserRegion[] | null>(res.data);
    })
  }

  getUnfilledChildren() {
    return this.http.get(`${this.env.baseUrl}/auth/unfilled-children`);
  }

  updateUnfilledChild(data: object) {
    return this.http.put(`${this.env.baseUrl}/auth/unfilled-children/`, data);
  }
}
