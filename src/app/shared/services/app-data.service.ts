import {Injectable} from "@angular/core";
import {BehaviorSubject, shareReplay} from "rxjs";
import {ApiService} from "./api.service";
import {StorageService} from "./storage.service";
import {AppData} from "../models";

@Injectable({
  providedIn: 'root'
})
export class AppDataService {
  private _data$: BehaviorSubject<AppData> = new BehaviorSubject<AppData>({});

  get data$() {
    return this._data$.asObservable().pipe(shareReplay(1));
  }

  constructor(private apiService: ApiService, private storageService: StorageService) {
  }

  async initialize() {
    const appDateString = await this.storageService.getItem('app_data');
    try {
      if (appDateString) {
        this._data$.next(JSON.parse(appDateString));
      }
    } catch {
    }
    this.loadAppData();
  }

  loadAppData() {
    this.apiService.appData().subscribe((res) => {
      this.storageService.setItem('app_data', JSON.stringify(res));
      this._data$.next(res)
      //this._data$ = new BehaviorSubject<AppData>(res);
    })
  }
}
