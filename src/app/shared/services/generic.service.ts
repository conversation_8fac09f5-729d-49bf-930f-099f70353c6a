import {Injectable} from '@angular/core';
import {ToastController, Platform} from "@ionic/angular";
import {Capacitor} from "@capacitor/core";

@Injectable({
  providedIn: 'root'
})
export class GenericService {
  constructor(
    private platform: Platform,
    private toastController: ToastController,
  ) {
  }

  public convertBlobToBase64 = (blob: Blob) => new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onerror = reject;
    reader.onload = () => {
      resolve(reader.result);
    };
    reader.readAsDataURL(blob);
  });

  public isCapacitorApp = () => {
    return this.platform.is('capacitor') || this.platform.is('cordova');
  }

  public isIosApp = () => {
    return this.isCapacitorApp() && (Capacitor.getPlatform() === 'ios')
  }

  public isAndroidApp = () => {
    return this.isCapacitorApp() && Capacitor.getPlatform() === 'android'
  }

  public getPlatform = () => {
    return Capacitor.getPlatform();
  }

  public webPlatform = () => {
    if (/iPad|iPhone|iPod/.test(navigator.userAgent))
      return 'ios';

    if (/android/i.test(navigator.userAgent))
      return 'android';

    return undefined;
  }

  public isMobileWeb = () => {
    return this.platform.is('mobileweb');
  }

  public noPaymentToast = () => {
    this.toastController.create({
      message: 'عفواً عمليات الدفع غير متاحة بالوقت الحالي!',
      position: 'top',
      color: 'danger',
      cssClass: 'custom-toast',
      buttons: [{
        side: 'end',
        text: 'إلغاء',
        role: 'cancel',
      }],
    }).then((toast) => {
      toast.present();
    });
  }
}
