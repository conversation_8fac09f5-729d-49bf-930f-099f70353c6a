import {Injectable} from "@angular/core";
import {StorageService} from "./storage.service";
import {BehaviorSubject, firstValueFrom, lastValueFrom, shareReplay} from "rxjs";
import {ApiService} from "./api.service";
import {FeaturesData} from "../models";

@Injectable({
  providedIn: 'root'
})
export class FeaturesService {
  private _features$: BehaviorSubject<FeaturesData> = new BehaviorSubject<FeaturesData>({
    Memberships: false,
    FamilyGraph: false,
    ActiveMembers: false,
    Store: false,
    Activities: false,
    Companies: false,
    Literatures: false,
    Documents: false,
    HallReservation: false,
    Consultations: false,
    ManagementCrew: false,
    News: true,
  });

  get features$() {
    return this._features$.asObservable().pipe(shareReplay(1));
  }

  constructor(private apiService: ApiService) {
  }


  loadFeatures() {
   const res =  this.apiService.features().pipe(shareReplay(1));
     res.subscribe((res) => {
      this._features$ = new BehaviorSubject<FeaturesData>(res);
    })

    return res;
  }
}
