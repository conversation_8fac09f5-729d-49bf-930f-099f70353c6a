import {inject, Inject, Injectable, TransferState} from '@angular/core';
import {HttpClient, HttpHeaders} from "@angular/common/http";
import {environment} from "../../../environments/environment";
import {APIDataResponse, APIResponse, Reservation, Service} from "../models";
import {IS_SERVER_PLATFORM} from "../IS_SERVER_PLATFORM.token";
import {Observable, of, tap} from "rxjs";

@Injectable({
  providedIn: 'root'
})
export class ReservationService {
  env = environment
  http = inject(HttpClient);
  tstate = inject(TransferState);

  constructor(@Inject(IS_SERVER_PLATFORM) private isServer: boolean
  ) {

  }

  checkAndGetData(key: string & {
    __not_a_string: never;
    __value_type?: void
  }, getDataObservable: Observable<any>, defaultValue: any = []) {
    if (this.tstate.hasKey(key)) {
      return of(this.tstate.get(key, defaultValue));
    } else {
      return getDataObservable.pipe(
        tap((data) => {
          if (this.isServer) {
            this.tstate.set(key, data);
          }
        })
      );
    }
  }

  index(nextPageUrl?: string) {
    return this.http.get<APIResponse<Reservation>>(nextPageUrl ? nextPageUrl : `${this.env.baseUrl}/hall-reservations`);
  }

  services() {
    return this.http.get<APIDataResponse<Service[]>>(`${this.env.baseUrl}/services`);
  }

  store(data: object) {
    return this.http.post(`${this.env.baseUrl}/hall-reservations`,  data);
  }

  delete(id: number | string) {
    return this.http.delete(`${this.env.baseUrl}/hall-reservations/${id}`);
  }
}
