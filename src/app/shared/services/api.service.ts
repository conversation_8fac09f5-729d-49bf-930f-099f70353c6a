import {inject, Inject, Injectable, makeState<PERSON><PERSON>, <PERSON>Key, TransferState} from '@angular/core';
import {HttpClient, HttpHeaders} from "@angular/common/http";
import {environment} from "../../../environments/environment";
import {
  Activity, AdBanner,
  ActualTransactionResponse,
  APIDataResponse,
  APIResponse, AppData, AwardCategoriesData, AwardCategory, AwardRoute, AwardUser, BankAccount, Card, Category,
  DocumentModel, FamilyGraph, FeaturesData,
  Literature,
  NEWS,
  Package,
  Product, QuranCompetition,
  Transaction,
  TransactionDetails,
  User, UserRegion, WorkoutProgram
} from "../models";
import {catchError, Observable, of, tap, map, lastValueFrom} from "rxjs";
import {IS_SERVER_PLATFORM} from "../IS_SERVER_PLATFORM.token";
import {StorageService} from "./storage.service";
import {NavController} from "@ionic/angular";
import {FeaturesService} from "./features.service";
import {objectToFormData} from "../helpers/helper.functions";
import {filter} from "rxjs/operators";

@Injectable({
  providedIn: 'root'
})
export class ApiService {
  env = environment
  http = inject(HttpClient);
  tstate = inject(TransferState);

  constructor(
    public storageService: StorageService,
    private navCtrl: NavController,
    @Inject(IS_SERVER_PLATFORM) private isServer: boolean
  ) {

  }

  /**
   * Get all cards with optional filters
   */
  getCards(params: {
    category?: string;
    show_inactive?: boolean;
  } = {}): Observable<APIResponse<Card>> {
    return this.http.get<APIResponse<Card>>(`${this.env.baseUrl}/cards`, {
      params: params
    });
  }

  /**
   * Get a specific card by ID
   */
  getCard(id: number): Observable<APIDataResponse<Card>> {
    return this.http.get<APIDataResponse<Card>>(`${this.env.baseUrl}/cards/${id}`);
  }

  getMembersList(params: any, nextPageUrl?: string) {
    return this.http.get<APIResponse<any>>(nextPageUrl ? `${this.env.baseUrl}/${nextPageUrl}` : `${this.env.baseUrl}/members`, {
      params: params,
    });
  }

  getNews(nextPageUrl?: string) {
    return this.http.get<APIResponse<NEWS>>(nextPageUrl ? nextPageUrl : `${this.env.baseUrl}/news`, {
      headers: new HttpHeaders({
        'guests-allowed': 'true'
      })
    });
  }

  getNewsById(id: number) {
    return this.http.get<APIResponse<NEWS>>(`${this.env.baseUrl}/news/${id}`, {
      headers: new HttpHeaders({
        'guests-allowed': 'true'
      })
    });
  }

  checkAndGetData(key: string & {
    __not_a_string: never;
    __value_type?: void
  }, getDataObservable: Observable<any>, defaultValue: any = []) {
    if (this.tstate.hasKey(key)) {
      return of(this.tstate.get(key, defaultValue));
    } else {

      return getDataObservable.pipe(
        tap((data) => {
          if (this.isServer) {
            this.tstate.set(key, data);
          }
        })
      );
    }
  }

  getDynamicStateKey(key: string) {
    return makeStateKey(key);
  }

  getOTP(token: string, method: string) {
    return this.http.post<{
      success: boolean,
      uuid: string,
    }>(`${this.env.baseUrl}/auth/otp`, {token, method}, {
      headers: new HttpHeaders({'skip-auth': 'true'})
    });
  }

  getLoginMethods(val: string, col: string) {
    let _data = {};
    // @ts-ignore
    _data[col] = val
    return this.http.post<{
      success: boolean,
      uuid: string,
      token: string,
      methods: string[],
      method?: string,
    }>(`${this.env.baseUrl}/auth/otp-methods`, _data, {
      headers: new HttpHeaders({'skip-auth': 'true'})
    });
  }

  verifyOTP(uuid: string, otp: string) {
    return this.http.post<{
      access_token: string,
      token_type: string,
      expires_in: number,
      expired_at: string
    }>(`${this.env.baseUrl}/auth/login`, {otp, uuid}, {
      headers: new HttpHeaders({
        'skip-auth': 'true'
      })
    });
  }

  loginWith2FA(token: string, code: string) {
    return this.http.post<{
      access_token: string,
      token_type: string,
      expires_in: number,
      expired_at: string,
    }>(`${this.env.baseUrl}/auth/login-2fa`, {token, code}, {
      headers: new HttpHeaders({'skip-auth': 'true'})
    });
  }

  qrcodeLogin(uuid: string) {
    return this.http.post(`${this.env.baseUrl}/auth/qr-code-login`, {uuid});
  }

  me() {
    return this.http.get<User>(`${this.env.baseUrl}/auth/me`);
  }

  getUserAppleWalletPass() {
    return this.http.get(`${this.env.baseUrl}/auth/me/apple-wallet`, {
      responseType: 'blob',
      observe: 'response',
    });
  }

  getUserGoogleWalletPass() {
    return this.http.get(`${this.env.baseUrl}/auth/me/google-wallet`);
  }

  userRegions() {
    return this.http.get<APIDataResponse<UserRegion[]>>(`${this.env.baseUrl}/user-regions`, {
      headers: new HttpHeaders({'guests-allowed': 'true'})
    });
  }

  categories() {
    return this.http.get<APIDataResponse<Category[]>>(`${this.env.baseUrl}/categories`, {
      headers: new HttpHeaders({'guests-allowed': 'true'})
    });
  }

  storeBanners() {
    return this.http.get<APIDataResponse<AdBanner[]>>(`${this.env.baseUrl}/store-banners`, {
      headers: new HttpHeaders({'guests-allowed': 'true'})
    });
  }

  storeBannerClick(id: number | string) {
    return this.http.put<APIDataResponse<Category[]>>(`${this.env.baseUrl}/store-banners/${id}/click`, {
      headers: new HttpHeaders({'guests-allowed': 'true'})
    });
  }

  appBanners(data: any = {}) {
    return this.http.get<APIDataResponse<Category[]>>(`${this.env.baseUrl}/app-banners`, {
      headers: new HttpHeaders({'guests-allowed': 'true'}),
      params: data,
    });
  }

  appBannerClick(id: number | string) {
    return this.http.put<APIDataResponse<Category[]>>(`${this.env.baseUrl}/app-banners/${id}/click`, {
      headers: new HttpHeaders({'guests-allowed': 'true'})
    });
  }

  appLinkClick(id: number | string) {
    return this.http.put<APIDataResponse<Category[]>>(`${this.env.baseUrl}/app-links/${id}/click`, {
      headers: new HttpHeaders({'guests-allowed': 'true'})
    });
  }

  features() {
    return this.http.get<FeaturesData>(`${this.env.baseUrl}/features`, {
      headers: new HttpHeaders({'guests-allowed': 'true'})
    });
  }

  appData() {
    return this.http.get<AppData>(`${this.env.baseUrl}/app-data`, {
      headers: new HttpHeaders({'guests-allowed': 'true'})
    });
  }

  statistics() {
    return this.http.get<{ data: any[] }>(`${this.env.baseUrl}/statistics`, {
      headers: new HttpHeaders({'guests-allowed': 'true'})
    });
  }

  updateUserProfile(userData: any) {
    return this.http.put(`${this.env.baseUrl}/auth/me`, userData)
  }

  accountDelete(deleteFormData: any) {
    return this.http.post(`${this.env.baseUrl}/auth/delete-request`, deleteFormData)
  }

  accountHasDelete() {
    return this.http.get<{ hasRequest: boolean }>(`${this.env.baseUrl}/auth/delete-request-status`)
  }

  base64ToBlob(dataURI: string) {
    const splitDataURI = dataURI.split(',')
    const byteString = splitDataURI[0].indexOf('base64') >= 0 ? atob(splitDataURI[1]) : decodeURI(splitDataURI[1])
    const mimeString = splitDataURI[0].split(':')[1].split(';')[0]

    const ia = new Uint8Array(byteString.length)
    for (let i = 0; i < byteString.length; i++)
      ia[i] = byteString.charCodeAt(i)

    return new Blob([ia], {type: mimeString})
  }

  updateUserAvatar(base64Image: string) {
    const formData = new FormData();
    formData.append('method', 'PUT');
    formData.append('image', this.base64ToBlob(base64Image), 'avatar.png');
    return this.http.post<{
      data: User
    }>(`${this.env.baseUrl}/auth/me/photo`, formData)
  }

  deleteUserAvatar() {
    return this.http.delete<{ data: User }>(`${this.env.baseUrl}/auth/me/photo`)
  }

  logout() {
    return this.http.get<{
      success: boolean
    }>(`${this.env.baseUrl}/auth/logout`);
  }

  getPackages() {
    return this.http.get<APIDataResponse<Package[]>>(`${this.env.baseUrl}/packages`,
      {
        headers: new HttpHeaders({
          'guests-allowed': 'true'
        })
      });
  }

  getPackage(productId: number) {
    return this.http.get<Package>(`${this.env.baseUrl}/packages/${productId}`);
  }

  getUpgradePackage(productId: number) {
    return this.http.get<Package>(`${this.env.baseUrl}/packages/${productId}/upgrade`);
  }

  getPackagePaySessionsId(productId: number, giftRecipientId?: string, giftMessage?: string) {
    const params: any = {};
    if (giftRecipientId) {
      params.gift_recipient = giftRecipientId;
      if (giftMessage) {
        params.gift_message = giftMessage;
      }
    }
    return this.http.get<Package>(`${this.env.baseUrl}/packages/${productId}/pay`, { params });
  }

  getPackageUpgradeSessionsId(productId: number, giftRecipientId?: string, giftMessage?: string) {
    const params: any = {};
    if (giftRecipientId) {
      params.gift_recipient = giftRecipientId;
      if (giftMessage) {
        params.gift_message = giftMessage;
      }
    }
    return this.http.get<Package>(`${this.env.baseUrl}/packages/${productId}/upgrade/pay`, { params });
  }

  getProducts(category_id: any, nextPageUrl?: string) {
    return this.http.get<APIResponse<Product>>(nextPageUrl ? `${this.env.baseUrl}/${nextPageUrl}` : `${this.env.baseUrl}/products`,
      {
        headers: new HttpHeaders({
          'guests-allowed': 'true'
        }),
        params: {
          ...(category_id ? {category_id: category_id} : {})
        }
      });
  }

  getProductById(id: string | number) {
    return this.http.get<Product>(`${this.env.baseUrl}/products/${id}`, {
      headers: new HttpHeaders({
        'guests-allowed': 'true'
      })
    });
  }

  getTransaction(transactionUUID: string) {
    return this.http.get<any>(`${this.env.baseUrl}/transactions/${transactionUUID}`, {
      headers: new HttpHeaders({
        'guests-allowed': 'true'
      })
    });
  }

  // Get complete transaction details for any transaction type
  getTransactionDetails(transactionUUID: string) {
    return this.http.get<{
      success: boolean,
      data: ActualTransactionResponse
    }>(`${this.env.baseUrl}/transactions/${transactionUUID}/details`, {
      headers: new HttpHeaders({
        'guests-allowed': 'true'
      })
    });
  }

  getWorkoutPrograms(nextPageUrl?: string) {
    return this.http.get<APIResponse<WorkoutProgram[]>>(nextPageUrl ? nextPageUrl : `${this.env.baseUrl}/workout-programs`,
      {
        headers: new HttpHeaders({
          'guests-allowed': 'true'
        })
      });
  }

  getWorkoutProgramById(id: string | number) {
    return this.http.get<WorkoutProgram>(`${this.env.baseUrl}/workout-programs/${id}`,
      {
        headers: new HttpHeaders({
          'guests-allowed': 'true'
        })
      });
  }

  getWorkoutProgramTimeline(id: string | number, params: any, nextPageUrl?: string) {
    return this.http.get<APIResponse<any>>(nextPageUrl ? `${this.env.baseUrl}/${nextPageUrl}` : `${this.env.baseUrl}/workout-programs/${id}/timeline`, {
      params: params,
    });
  }

  getFriendships(params: any, nextPageUrl?: string) {
    return this.http.get<APIResponse<any>>(nextPageUrl ? `${this.env.baseUrl}/${nextPageUrl}` : `${this.env.baseUrl}/friendships`, {
      params: params,
    });
  }

  getFriendRequests(params: any, nextPageUrl?: string) {
    return this.http.get<APIResponse<any>>(nextPageUrl ? `${this.env.baseUrl}/${nextPageUrl}` : `${this.env.baseUrl}/friend-requests`, {
      params: params,
    });
  }

  deleteFriendship(friendUserId: number) {
    return this.http.delete<APIResponse<any>>(`${this.env.baseUrl}/friendships/${friendUserId}`);
  }

  deleteFriendRequest(friendRequestId: number) {
    return this.http.delete<APIResponse<any>>(`${this.env.baseUrl}/friend-requests/${friendRequestId}`);
  }

  updateFriendRequest(friendRequestId: number, data: any) {
    return this.http.put<APIResponse<any>>(`${this.env.baseUrl}/friend-requests/${friendRequestId}`, data);
  }

  sendFriendRequest(data: any) {
    return this.http.post<APIResponse<any>>(`${this.env.baseUrl}/friend-requests`, data);
  }

  getFriendStatistics() {
    return this.http.get<APIResponse<any>>(`${this.env.baseUrl}/friend-requests/statistics`);
  }

  validateFamilyUserId(familyUserId: string) {
    return this.http.get<any>(`${this.env.baseUrl}/users/validate/${familyUserId}`);
  }

  // Secure gift eligibility check (replaces getUserByFamilyId + checkUserMembershipStatus)
  checkGiftEligibility(familyUserId: string) {
    return this.http.get<{
      success: boolean,
      data: {
        user_exists: boolean,
        has_active_membership: boolean,
        can_receive_gift: boolean,
        profile_photo_url: string,
        gender: string,
      }
    }>(`${this.env.baseUrl}/users/${familyUserId}/gift-eligibility`);
  }

  // Secure upgrade eligibility check
  checkUpgradeEligibility(familyUserId: string, packageId: number) {
    return this.http.get<{
      success: boolean,
      data: {
        user_exists: boolean,
        has_active_membership: boolean,
        can_receive_upgrade: boolean
      }
    }>(`${this.env.baseUrl}/users/${familyUserId}/upgrade-eligibility/${packageId}`);
  }

  // Legacy methods (deprecated - kept for backward compatibility)
  /** @deprecated Use checkGiftEligibility instead for security */
  getUserByFamilyId(familyUserId: string) {
    return this.http.get<{data: User}>(`${this.env.baseUrl}/users/family-id/${familyUserId}`);
  }

  /** @deprecated Use checkGiftEligibility instead for security */
  checkUserMembershipStatus(familyUserId: string) {
    return this.http.get<{data: {has_active_membership: boolean, membership?: any}}>(`${this.env.baseUrl}/users/${familyUserId}/membership-status`);
  }

  getStorePromotions(nextPageUrl?: string) {
    return this.http.get<APIResponse<WorkoutProgram[]>>(nextPageUrl ? nextPageUrl : `${this.env.baseUrl}/store-promotions`,
      {
        headers: new HttpHeaders({
          'guests-allowed': 'true'
        })
      });
  }

  getStorePromotionById(id: string | number) {
    return this.http.get<APIResponse<WorkoutProgram>>(`${this.env.baseUrl}/store-promotions/${id}`,
      {
        headers: new HttpHeaders({
          'guests-allowed': 'true'
        })
      });
  }

  getActivities(nextPageUrl?: string) {
    return this.http.get<APIResponse<Activity[]>>(nextPageUrl ? nextPageUrl : `${this.env.baseUrl}/activities`,
      {
        headers: new HttpHeaders({
          'guests-allowed': 'true'
        })
      });
  }

  getActivityById(id: string | number) {
    return this.http.get<APIResponse<Activity>>(`${this.env.baseUrl}/activities/${id}`,
      {
        headers: new HttpHeaders({
          'guests-allowed': 'true',
        })
      });
  }

  applyActivityRequestById(id: string | number, data: any = []) {
    return this.http.post<any>(`${this.env.baseUrl}/activities/${id}/requests`, {children: data});
  }

  getEvents(nextPageUrl?: string) {
    return this.http.get<APIResponse<Activity[]>>(nextPageUrl ? nextPageUrl : `${this.env.baseUrl}/events`,
      {
        headers: new HttpHeaders({
          'guests-allowed': 'true',
        })
      });
  }

  getEventById(id: string | number) {
    return this.http.get<APIResponse<Activity>>(`${this.env.baseUrl}/events/${id}`,
      {
        headers: new HttpHeaders({
          'guests-allowed': 'true',
        })
      });
  }

  getEventAppleWalletPass(id: string | number) {
    return this.http.get(`${this.env.baseUrl}/events/${id}/apple-wallet-invitation`, {
      responseType: 'blob',
      observe: 'response',
    });
  }

  getEventGoogleWalletPass(id: string | number) {
    return this.http.get(`${this.env.baseUrl}/events/${id}/google-wallet-invitation`, {
      responseType: 'blob',
      observe: 'response',
    });
  }

  getEventInvitation(id: string | number) {
    return this.http.get<any>(`${this.env.baseUrl}/events/${id}/invitation`);
  }

  applyForEventById(id: string | number, data: any) {
    return this.http.post<any>(`${this.env.baseUrl}/events/${id}/attendance`, data);
  }

  getQuranCompetitions(nextPageUrl?: string) {
    return this.http.get<APIResponse<QuranCompetition[]>>(nextPageUrl ? nextPageUrl : `${this.env.baseUrl}/quran-competitions`);
  }

  getQuranCompetitionById(id: string | number, params: any = {}) {
    return this.http.get<APIDataResponse<QuranCompetition>>(`${this.env.baseUrl}/quran-competitions/${id}`, {params});
  }

  applyForQuranCompetitionById(id: string | number, data: any) {
    return this.http.post<any>(`${this.env.baseUrl}/quran-competitions/${id}/requests`, data);
  }

  getBankAccounts() {
    return this.http.get<APIResponse<BankAccount[]>>(`${this.env.baseUrl}/bank-accounts`,
      {
        headers: new HttpHeaders({
          'guests-allowed': 'true'
        })
      });
  }


  getLiteratures(nextPageUrl?: string) {
    return this.http.get<APIResponse<Literature[]>>(nextPageUrl ? nextPageUrl : `${this.env.baseUrl}/literatures`,
      {
        headers: new HttpHeaders({
          'guests-allowed': 'true'
        })
      });
  }

  getLiteratureById(id: string | number) {
    return this.http.get<APIDataResponse<Literature>>(`${this.env.baseUrl}/literatures/${id}`,
      {
        headers: new HttpHeaders({
          'guests-allowed': 'true'
        })
      });
  }

  getDocuments(nextPageUrl?: string) {
    return this.http.get<APIResponse<DocumentModel[]>>(nextPageUrl ? nextPageUrl : `${this.env.baseUrl}/documents`,
      {
        headers: new HttpHeaders({
          'guests-allowed': 'true'
        })
      });
  }

  getDocumentById(id: string | number) {
    return this.http.get<APIDataResponse<DocumentModel>>(`${this.env.baseUrl}/documents/${id}`,
      {
        headers: new HttpHeaders({
          'guests-allowed': 'true'
        })
      });
  }

  getCompanies(nextPageUrl?: string) {
    return this.http.get<APIResponse<DocumentModel[]>>(nextPageUrl ? nextPageUrl : `${this.env.baseUrl}/companies`,
      {
        headers: new HttpHeaders({
          'guests-allowed': 'true'
        })
      });
  }

  getCompanyById(id: string | number) {
    return this.http.get<APIDataResponse<DocumentModel>>(`${this.env.baseUrl}/companies/${id}`,
      {
        headers: new HttpHeaders({
          'guests-allowed': 'true'
        })
      });
  }

  getMyfatoorahSessionId(productId: string | number, name: string, phone: string, amount: number) {
    return this.http.post<{ session_id: string }>(`${this.env.baseUrl}/products/${productId}/transactions`, {
        amount: amount,
        user_name: name,
        user_phone: phone
      },
      {
        headers: new HttpHeaders({
          'guests-allowed': 'true'
        })
      })
  }

  getInvoiceFromPaymentSessionId(transactionUUID: string, paymentSessionId: string, extraData: object = {}) {
    return this.http.post<{ session_id: string }>(`${this.env.baseUrl}/transactions/${transactionUUID}/execute`, {
        "payment_session_id": paymentSessionId,
        "redirect": window.location.origin,
        ...extraData,
      },
      {
        headers: new HttpHeaders({
          'guests-allowed': 'true'
        })
      })
  }

  getInvoiceDetails(invoiceSlug: string) {
    return this.http.get<Transaction>(`${this.env.baseUrl}/transactions/${invoiceSlug}/slug`, {
      headers: new HttpHeaders({
        'guests-allowed': 'true'
      })
    })
  }

  getFamilyGraph(slug: string, password?: string) {
    return this.http.get<FamilyGraph>(`${this.env.baseUrl}/family-graph/${slug}`,
      {
        headers: new HttpHeaders({
          'guests-allowed': 'true',
          ...(password ? {
            'X-PASSWORD': password
          } : {})
        })
      })
  }

  async getFamilyGraphChildren(slug: string, id: number, password?: string | null): Promise<any> {
    if (slug === 'me') {
      return this.getUserFamilyGraphChildren(id);
    }
    if (slug === 'root') {
      return this.getRootFamilyGraphChildren(id);
    }

    let headers = new HttpHeaders();
    if (password) {
      headers = headers.set('X-PASSWORD', password);
    }

    const observable = this.http.get<any>(`${environment.baseUrl}/family-graph/${slug}/user/${id}/children`, {headers});

    try {
      return await lastValueFrom(observable);

    } catch (error) {
      console.error(`Error fetching family graph children for slug '${slug}':`, error);
      return Promise.reject(error);
    }
  }

  getUserFamilyGraph() {
    return this.http.get<FamilyGraph>(`${this.env.baseUrl}/user-family-graph`)
  }


  async getUserFamilyGraphChildren(id: number): Promise<any> {
    const token = await this.storageService.getItem('access_token');
    if (!token) {
      console.error('Access token not found');
      this.logoutAction();
      return Promise.reject('Access token not found');
    }

    const headers = new HttpHeaders({
      Authorization: `Bearer ${token}`,
    });

    const observable = this.http.get<any>(`${environment.baseUrl}/user-family-graph/user/${id}/children`, {headers});

    try {
      return await lastValueFrom(observable); // Use lastValueFrom
    } catch (error) {
      console.error('Error fetching user family graph children:', error);
      this.logoutAction();
      return Promise.reject(error);
    }
  }


  getRootFamilyGraph() {
    return this.http.get<FamilyGraph>(`${this.env.baseUrl}/root-family-graph`)
  }

  async getRootFamilyGraphChildren(id: number): Promise<any> {
    const token = await this.storageService.getItem('access_token');
    if (!token) {
      console.error('Access token not found');
      this.logoutAction();
      return Promise.reject('Access token not found');
    }

    const headers = new HttpHeaders({
      Authorization: `Bearer ${token}`,
    });

    const observable = this.http.get<any>(`${environment.baseUrl}/root-family-graph/user/${id}/children`, {headers});

    try {
      return await lastValueFrom(observable); // Use lastValueFrom
    } catch (error) {
      console.error('Error fetching root family graph children:', error);
      this.logoutAction();
      return Promise.reject(error);
    }
  }

  getManagementCrewGraph() {
    return this.http.get<FamilyGraph>(`${this.env.baseUrl}/management-crew`)
  }

  getTallyForm(formId: string) {
    return this.http.get<any>(`${this.env.baseUrl}/tally-forms`, {
      params: {
        form_id: formId,
      }
    })
  }

  logoutActionHandler(returnURL: string | null) {
    this.storageService.removeItem('access_token');
    this.storageService.removeItem('expires_at');
    this.storageService.removeItem('expired_at');
    this.storageService.removeItem('user');
    (new FeaturesService(this)).loadFeatures()
    this.navCtrl.navigateRoot('login', {
      queryParams: returnURL === null ? null : {
        returnURL: returnURL
      }
    }).then();
  }

  async logoutAction(returnURL: string | null = null) {
    const token = await this.storageService.getItem('access_token');
    if (token === null) {
      this.logoutActionHandler(returnURL)
    } else
      this.logout().pipe(
        catchError((err) => {
          this.logoutActionHandler(returnURL)
          return err;
        })
      ).subscribe((res) => this.logoutActionHandler(returnURL))
  }

  awardCategories() {
    return this.http.get<AwardCategoriesData>(`${this.env.baseUrl}/award-categories`, {
      headers: new HttpHeaders({'guests-allowed': 'true'})
    });
  }

  awardCategory(id: string | number) {
    return this.http.get<AwardCategory>(`${this.env.baseUrl}/award-categories/${id}`, {
      headers: new HttpHeaders({'guests-allowed': 'true'})
    });
  }

  awardRoute(id: string | number) {
    return this.http.get<AwardRoute>(`${this.env.baseUrl}/award-routes/${id}`, {
      headers: new HttpHeaders({'guests-allowed': 'true'})
    });
  }

  awardUserList() {
    return this.http.get<AwardUser[]>(`${this.env.baseUrl}/award-users`);
  }

  awardUserDelete(id: string | number) {
    return this.http.delete<any>(`${this.env.baseUrl}/award-users/${id}`);
  }

  awardUserStore(data: any) {
    let formData = objectToFormData(data);
    return this.http.post<AwardRoute>(`${this.env.baseUrl}/award-users`, formData, {
      headers: new HttpHeaders({'guests-allowed': 'true'})
    });
  }

  getTimelineActivities(params: any, nextPageUrl?: string) {
    return this.http.get<APIResponse<Activity[]>>(nextPageUrl ? nextPageUrl : `${this.env.baseUrl}/timeline-activities`, {
      params: params,
    });
  }

  storeCardDownload(id: number | string) {
    return this.http.post(`${this.env.baseUrl}/cards/${id}/download`, {});
  }

}
