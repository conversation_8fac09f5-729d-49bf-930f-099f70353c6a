import {HttpClient, HttpParams} from "@angular/common/http";
import {inject, Injectable} from "@angular/core";
import {environment} from "../../../environments/environment";
import {Athlete} from "../models";
import {GenericService} from "./generic.service";

@Injectable({
  providedIn: 'root'
})
export class StravaService {
  env = environment
  http = inject(HttpClient);
  genericService = inject(GenericService);

  constructor() {
  }

  getAuthorizationUrl(scopes: string[]): string {
    const STRAVA_CLIENT_ID = this.env.STRAVA_CLIENT_ID
    let STRAVA_REDIRECT_URL: string;

    const STRAVA_CALLBACK_DOMAIN = this.env.STRAVA_CALLBACK_DOMAIN;
    if (this.genericService.isCapacitorApp()) {
      STRAVA_REDIRECT_URL = `altuwaijri://me/strava/callback`;
    } else {
      STRAVA_REDIRECT_URL = `https://${STRAVA_CALLBACK_DOMAIN}/me/strava/callback`;
    }
    STRAVA_REDIRECT_URL = `https://${STRAVA_CALLBACK_DOMAIN}/redirect?${new HttpParams({
      fromObject: {
        'redirect_to': encodeURIComponent(STRAVA_REDIRECT_URL),
      }
    }).toString()}`;

    return `https://www.strava.com/oauth/mobile/authorize?${(new HttpParams({
      fromObject: {
        'client_id': STRAVA_CLIENT_ID,
        'redirect_uri': STRAVA_REDIRECT_URL,
        'response_type': 'code',
        'approval_prompt': 'auto',
        ...(scopes.length > 0 ? {'scope': scopes.join(',')} : {})
      }
    })).toString()}`;
  }

  authorizeCode(code: string, state: string | null) {
    return this.http.post<{
      success: boolean,
      error: string,
      exception: any,
    }>(`${this.env.baseUrl}/strava/auth/callback`, {code, state});
  }

  deauthorize() {
    return this.http.post<{
      success: boolean,
    }>(`${this.env.baseUrl}/strava/deauthorize`, {});
  }

  athlete() {
    return this.http.get<{
      access_token_expired: boolean,
      athlete: Athlete,
    }>(`${this.env.baseUrl}/strava/athlete`);
  }
}
