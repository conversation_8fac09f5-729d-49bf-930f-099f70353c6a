import {Injectable} from "@angular/core";
import {BehaviorSubject, shareReplay} from "rxjs";
import {ApiService} from "./api.service";
import {StorageService} from "./storage.service";

@Injectable({
  providedIn: 'root'
})
export class StatisticsService {
  private _data$: BehaviorSubject<any[]> = new BehaviorSubject<any[]>([]);
  private _dataLoaded$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  get $data() {
    return this._data$.asObservable().pipe(shareReplay(1));
  }

  get $dataLoaded() {
    return this._dataLoaded$.asObservable().pipe(shareReplay(1));
  }

  constructor(private apiService: ApiService, private storageService: StorageService) {
    this.initialize().then(r => {
    });
  }

  async initialize() {
    const statisticsDateString = await this.storageService.getItem('statistics');
    try {
      if (statisticsDateString) {
        this._data$.next(JSON.parse(statisticsDateString));
        this._dataLoaded$.next(true);
      }
    } catch {
    }
    this.loadData();
  }

  loadData() {
    this.apiService.statistics().subscribe(({data}) => {
      this._data$.next(data);
      this._dataLoaded$.next(true);
      this.storageService.setItem('statistics', JSON.stringify(data));
    })
  }
}
