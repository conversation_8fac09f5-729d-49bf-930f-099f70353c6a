import {Injectable} from '@angular/core';
import Echo from 'laravel-echo';
import {environment} from "../../../environments/environment";
import {StorageService} from "./storage.service";

@Injectable({
  providedIn: 'root',
})
export class NotificationService {
  public Echo!: Echo<any>;

  constructor(
    private storageService: StorageService
  ) {
    this.initEcho().then()
  }

  public async initEcho() {
    if (environment.wsHost && environment.wsAppId) {
      if (this.Echo)
        return;
      const accessToken = await this.storageService.getItem('access_token');
      this.Echo = new Echo({
        broadcaster: 'reverb',
        key: environment.wsAppId,
        wsHost: environment.wsHost,
        wsPort: environment.wsPort,
        wssPort: environment.wssPort,
        forceTLS: environment.wsForceTLS,
        enabledTransports: ['ws', 'wss'],
        authEndpoint: `${environment.baseUrl}/broadcasting/auth`,
        auth: {
          headers: {
            ...(accessToken ? {
              Authorization: `Bearer ${accessToken}`,
            } : {}),
            Accept: 'application/json',
          }
        },
        userAuthentication: {
          endpoint: `${environment.baseUrl}/broadcasting/user-auth`,
          headers: {
            ...(accessToken ? {
              Authorization: `Bearer ${accessToken}`,
            } : {}),
            Accept: 'application/json',
          }
        },
      });
      window.Echo = this.Echo;
    }

    /*this.echo.connector.socket.on('connect', () => {
      console.log('%c Connected to WebSocket', 'background: blue; color: white');
    });

    this.echo.connector.socket.on('reconnecting', () => {
      console.log('%c ReConnecting to WebSocket', 'background: yellow; color: green');
    });

    this.echo.connector.socket.on('disconnect', () => {
      console.log('%c Disconnected from WebSocket', 'background: red; color: white');
    });

    this.echo.channel('user-channel').listen('.UserEvent', (data: any) => {
      // Handle the real-time notification data here
      console.log('Notification received:', data);
    });*/
  }
}
