import {Injectable} from "@angular/core";
import {BehaviorSubject} from "rxjs";
import {ApiService} from "./api.service";
import {GenericService} from "./generic.service";

@Injectable({
  providedIn: 'root'
})
export class AppBannersService {
  private _dataLoaded$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  private _data$: BehaviorSubject<any[]> = new BehaviorSubject<any[]>([]);

  get $dataLoaded() {
    return this._dataLoaded$.asObservable();
  }

  get $data() {
    return this._data$.asObservable();
  }

  constructor(private apiService: ApiService, private genericService: GenericService) {
    this.initialize().then(r => {
    })
  }

  async initialize() {
    this.loadStoreBanners();
  }

  loadStoreBanners() {
    this.apiService.appBanners({
      platform: this.genericService.isCapacitorApp() ? 'app' : 'web',
      device: this.genericService.isCapacitorApp() ? this.genericService.getPlatform() : this.genericService.webPlatform() ?? null,
    }).subscribe((res) => {
      this._data$ = new BehaviorSubject<any[]>(res.data);
      this._dataLoaded$.next(true);
    })
  }
}
