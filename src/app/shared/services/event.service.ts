import {inject, Inject, Injectable} from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {environment} from "../../../environments/environment";
import {APIDataResponse} from "../models";
import {IS_SERVER_PLATFORM} from "../IS_SERVER_PLATFORM.token";

@Injectable({
  providedIn: 'root'
})
export class EventService {
  env = environment
  http = inject(HttpClient);

  constructor(@Inject(IS_SERVER_PLATFORM) private isServer: boolean
  ) {

  }

  guestsIndex(eventId: string | number) {
    return this.http.get<APIDataResponse<any>>(`${this.env.baseUrl}/events/${eventId}/guests`);
  }

  guestsStore(eventId: number | string, data: object) {
    return this.http.post(`${this.env.baseUrl}/events/${eventId}/guests`, data);
  }

  downloadEventInvitation(eventId: number | string, eventInvitationUUID: string, data: object) {
    return this.http.post(`${this.env.baseUrl}/events/${eventId}/invitations/${eventInvitationUUID}/download`, data, {
      responseType: 'blob',
      observe: 'response',
    });
  }
}
