import {inject, NgModule} from '@angular/core';
import {
  ActivatedRouteSnapshot, Params,
  PreloadAllModules, provideRouter,
  Route,
  Router,
  RouterModule,
  RouterStateSnapshot,
  Routes,
  UrlSegment, withComponentInputBinding, withRouterConfig
} from '@angular/router';
import {HomePage} from "./pages/home/<USER>";
import {SoonPage} from "./pages/soon/soon.page";
import {NewsDetailsComponent} from "./pages/news/news-details/news-details.component";
import {LoginPage} from "./pages/login/login-page.component";
import {StorePage} from "./pages/store/store-page.component";
import {CheckoutSuccessPage} from "./pages/checkout/success/success-page.component";
import {CheckoutFailurePage} from "./pages/checkout/failure/failure-page.component";
import {ProfilePageComponentPage} from "./pages/profile/profile-page.component";
import {StorageService} from "./shared/services/storage.service";
import {ProductDetailsWrapperComponent} from "./pages/store/product-details/product-details-wrapper.component";
import {InvoicePage} from "./pages/invoice/invoice-page.component";
import {HallReservation} from "./pages/hall-reservation/hall-reservation.component";
import {ReservationCreatePage} from "./pages/reservation-create-page/reservation-create.page";
import {PackagesPage} from "./pages/members/packages/packages-page.component";
import {SubscribePage} from "./pages/members/subscribe/subscribe-page.component";
import {UpgradePage} from "./pages/members/upgrade/upgrade-page.component";
import {MembershipCheckoutSuccessPage} from "./pages/members/checkout/success/success-page.component";
import {ActivityDetailsComponent} from "./pages/activities/activity-details/activity-details.component";
import {ActivityIndexComponent} from "./pages/activities/index/activity-index.component";
import {LiteratureIndexComponent} from "./pages/literatures/index/literature-index.component";
import {LiteratureDetailsComponent} from "./pages/literatures/literature-details/literature-details.component";
import {DocumentIndexComponent} from "./pages/documents/index/document-index.component";
import {DocumentDetailsComponent} from "./pages/documents/details/document-details.component";
import {CompanyIndexComponent} from "./pages/companies/index/company-index.component";
import {CompanyDetailsComponent} from "./pages/companies/details/company-details.component";
import {ProfileEditPageComponentPage} from "./pages/profile/edit/profile-edit-page.component";
import {FamilyGraphPage} from "./pages/family-graph/family-graph-page.component";
import {NavController} from "@ionic/angular";
import {User} from "./shared/models";
import {AuthGuard} from "./shared/services/auth-guard.service";
import {ConsultationCreatePage} from "./pages/consultation/create-page/consultation-create.page";
import {ConsultationHomePage} from "./pages/consultation/home-page/consultation-home.page";
import {AccountsComponent} from "./pages/accounts/accounts.component";
import {NewsIndexComponent} from "./pages/news/news-index/news-index.component";
import {SettingsComponent} from "./pages/settings/settings/settings.component";
import {ChangeAppIconComponent} from "./pages/settings/change-app-icon/change-app-icon.component";
import {ProfileStravaPageComponent} from "./pages/profile/strava/profile-strava-page.component";
import {
  ProfileStravaCallbackPageComponent
} from "./pages/profile/strava-callback/profile-strava-callback-page.component";
import {RedirectPage} from "./pages/redirect.page";
import {WorkoutProgramIndexComponent} from "./pages/workout-programs/index/workout-program-index.component";
import {WorkoutProgramDetailsComponent} from "./pages/workout-programs/details/workout-program-details.component";
import {ActivitiesCheckoutSuccessPage} from "./pages/activities/checkout/success/success-page.component";
import {ActivitiesCheckoutFailurePage} from "./pages/activities/checkout/failure/failure-page.component";
import {ChildrenPageComponent} from "./pages/profile/children/children-page.component";
import {ChildrenCreatePageComponent} from "./pages/profile/children-create/children-create-page.component";
import {EventIndexComponent} from "./pages/events/index/event-index.component";
import {EventDetailsComponent} from "./pages/events/event-details/event-details.component";
import {EventAttendanceComponent} from "./pages/events/event-attendance/event-attendance.component";
import {MembershipCheckoutFailurePage} from "./pages/members/checkout/failure/failure-page.component";
import {PolicyPage} from "./pages/policy/policy.page";
import {ProfileDeletePageComponentPage} from "./pages/profile/delete/profile-delete-page.component";
import {QuranCompetitionIndexComponent} from "./pages/quran-competitions/index/quran-competition-index.component";
import {QuranCompetitionApplyComponent} from "./pages/quran-competitions/apply/quran-competition-apply.component";
import {QuranCompetitionDetailsComponent} from "./pages/quran-competitions/details/quran-competition-details.component";
import {StorePromotionIndexComponent} from "./pages/store-promotions/index/store-promotion-index.component";
import {StorePromotionDetailsComponent} from "./pages/store-promotions/details/store-promotion-details.component";
import {FormsComponent} from "./pages/forms/forms.component";
import {MembersListPage} from "./pages/members/list/members-list-page.component";
import {AppInfoComponent} from "./pages/settings/app-info/app-info.component";
import {PayPage} from "./pages/pay/pay.page";
import {PayCheckoutSuccessPage} from "./pages/pay/checkout/success/success-page.component";
import {PayCheckoutFailurePage} from "./pages/pay/checkout/failure/failure-page.component";
import {EventGuestsComponent} from "./pages/events/event-guests/event-guests.component";
import {AwardCategoriesPageComponent} from "./pages/awards/categories/award-categories-page.component";
import {AwardRoutesPageComponent} from "./pages/awards/routes/award-routes-page.component";
import {AwardRoutePageComponent} from "./pages/awards/route/award-route-page.component";
import {AwardRequestsPageComponent} from "./pages/awards/requests/award-requests-page.component";
import {ProfileService} from "./shared/services/profile.service";
import {FeaturesService} from "./shared/services/features.service";
import {containsOnlyDigits} from "./shared/helpers/helper.functions";
import {FriendsPageComponent} from "./pages/profile/friends/friends-page.component";
import {NotificationsSettingsComponent} from "./pages/settings/notifications-settings/notifications-settings.component";
import {CardIndexComponent} from "./pages/cards/index/card-index.component";
import {CardDetailsComponent} from "./pages/cards/details/card-details.component";
import {AboutPage} from "./pages/about/about.page";
import {TermsPage} from "./pages/terms/terms.page";

const CheckIsLoggedIn = async (route: Route, segments: UrlSegment[]) => {
  const storageService = inject(StorageService);
  const profileService = inject(ProfileService);
  const featuresService = inject(FeaturesService);
  const navCtrl = inject(NavController);
  const accessToken = await storageService.getItem('access_token');
  let expiresAt = String(await storageService.getItem('expires_at'));
  const path = segments.length > 0 ? segments.map(s => s.path).join('/') : route.path;
  if (expiresAt !== null && expiresAt !== undefined && expiresAt !== '' && expiresAt?.indexOf(' ') > 0)
    expiresAt = Math.floor((new Date(expiresAt)).getTime()).toString()
  if (expiresAt === null || !containsOnlyDigits(expiresAt) || new Date(parseInt(expiresAt)) < new Date()) {
    await storageService.removeItem('access_token');
    await storageService.removeItem('expires_at');
    await storageService.removeItem('user');
    profileService.removeUser();
    featuresService.loadFeatures()
  }
  if (accessToken === null || expiresAt === null || !containsOnlyDigits(expiresAt) || new Date(parseInt(expiresAt)) < new Date())
    return navCtrl.navigateForward('login', {
      queryParams: {returnURL: `/${path}`}
    });
  return true;
}

const HasRoles = async (route: Route, segments: UrlSegment[]) => {
  const storageService = inject(StorageService);
  const navCtrl = inject(NavController);
  const accessToken = await storageService.getItem('access_token');
  const isLoggedIn = accessToken !== null;
  if (isLoggedIn) {
    const JsonUser = (await storageService.getItem('user'));
    const user = JSON.parse(JsonUser ?? '{}') as User;
    return Object.keys(user.roles || []).length > 0 ? true : navCtrl.navigateRoot('soon');
  } else
    return navCtrl.navigateRoot('login', {
      queryParams: {returnURL: `/${route.path}`}
    });
}

export const routes: Routes = [
  {
    path: 'redirect',
    component: RedirectPage,
  },
  {
    path: 'home',
    //canMatch: [HasRoles],
    component: HomePage,
  },
  {
    path: 'accounts',
    canMatch: [],
    component: AccountsComponent,
  },
  {
    path: 'me',
    component: ProfilePageComponentPage,
    canMatch: [CheckIsLoggedIn],
  },
  {
    path: 'me/edit',
    component: ProfileEditPageComponentPage,
    canMatch: [CheckIsLoggedIn],
  },
  {
    path: 'me/delete',
    component: ProfileDeletePageComponentPage,
    canMatch: [CheckIsLoggedIn],
  },
  {
    path: 'me/children',
    component: ChildrenPageComponent,
    canMatch: [CheckIsLoggedIn],
  }, {
    path: 'me/friends',
    component: FriendsPageComponent,
    canMatch: [CheckIsLoggedIn],
  },
  {
    path: 'me/children/create',
    component: ChildrenCreatePageComponent,
    canMatch: [CheckIsLoggedIn],
  },
  {
    path: 'me/strava',
    component: ProfileStravaPageComponent,
    canMatch: [CheckIsLoggedIn],
  },
  {
    path: 'me/strava/callback',
    component: ProfileStravaCallbackPageComponent,
    canMatch: [CheckIsLoggedIn],
  },
  {
    path: 'cases',
    children: [
      {
        path: '',
        component: HomePage,
      },
      {
        path: ':id',
        component: ProductDetailsWrapperComponent
      }
    ]
  },
  {
    path: 'activities',
    canMatch: [CheckIsLoggedIn],
    children: [
      {
        path: '',
        component: ActivityIndexComponent,
      },
      {
        path: ':id',
        children: [
          {
            path: '',
            component: ActivityDetailsComponent,
          },
          {
            path: 'checkout',
            children: [
              {
                path: 'success',
                component: ActivitiesCheckoutSuccessPage
              },
              {
                path: 'failure',
                component: ActivitiesCheckoutFailurePage
              }
            ]
          },
        ]
      }
    ]
  },
  {
    path: 'quran-competitions',
    canMatch: [CheckIsLoggedIn],
    children: [
      {
        path: '',
        component: QuranCompetitionIndexComponent,
      },
      {
        path: ':id',
        children: [
          {
            path: '',
            component: QuranCompetitionDetailsComponent,
          },
          {
            path: 'apply',
            component: QuranCompetitionApplyComponent,
          },
        ]
      }
    ]
  },
  {
    path: 'events',
    canMatch: [CheckIsLoggedIn],
    children: [
      {
        path: '',
        component: EventIndexComponent,
      },
      {
        path: ':id',
        children: [
          {
            path: '',
            component: EventDetailsComponent,
          },
          {
            path: 'attendance',
            component: EventAttendanceComponent,
          },
          {
            path: 'guests',
            component: EventGuestsComponent,
          },
        ]
      }
    ]
  },
  {
    path: 'stats',
    canMatch: [CheckIsLoggedIn],
    loadComponent: () => import('./pages/stats/stats.component').then(m => m.StatsComponent),
  },
  {
    path: 'literatures',
    //canMatch: [CheckIsLoggedIn],
    children: [
      {
        path: '',
        component: LiteratureIndexComponent,
      },
      {
        path: ':id',
        component: LiteratureDetailsComponent
      }
    ]
  },
  {
    path: 'documents',
    canMatch: [CheckIsLoggedIn],
    children: [
      {
        path: '',
        component: DocumentIndexComponent,
      },
      {
        path: ':id',
        component: DocumentDetailsComponent
      }
    ]
  },
  {
    path: 'companies',
    //canMatch: [CheckIsLoggedIn],
    children: [
      {
        path: '',
        component: CompanyIndexComponent,
      },
      {
        path: ':id',
        component: CompanyDetailsComponent
      }
    ]
  },
  {
    path: 'hall-reservation',
    canMatch: [CheckIsLoggedIn],
    children: [
      {
        path: '',
        component: HallReservation,
      },
      {
        path: 'create',
        component: ReservationCreatePage,
      },
      /*{
        path: ':id',
        component: ReservationCreatePage
      }*/
    ],
  },
  {
    path: 'consultations',
    canMatch: [CheckIsLoggedIn],
    children: [
      {
        path: '',
        component: ConsultationHomePage,
      },
      {
        path: 'create',
        component: ConsultationCreatePage,
      },
    ],
  },
  {
    path: 'store',
    canMatch: [CheckIsLoggedIn],
    children: [
      {
        path: '',
        component: StorePage,
      },
      {
        // Legacy product detail route that now redirects to the parent store page
        // with a query parameter
        path: 'products/:id',
        redirectTo: ({params}: Params) => {
          return `/store?productId=${+params['id']}`;
        }
      }
    ]
  },
  {
    path: 'workout-programs',
    component: WorkoutProgramIndexComponent,
  },
  {
    canMatch: [CheckIsLoggedIn],
    path: 'workout-programs/:id',
    component: WorkoutProgramDetailsComponent,
  },
  {
    path: 'members',
    canMatch: [CheckIsLoggedIn],
    children: [
      {
        path: '',
        component: PackagesPage
      },
      {
        path: 'list',
        component: MembersListPage,
      },
      {
        path: 'checkout',
        children: [
          {
            path: 'success',
            component: MembershipCheckoutSuccessPage
          },
          {
            path: 'failure',
            component: MembershipCheckoutFailurePage
          }
        ]
      },
      {
        path: 'subscribe/:id',
        component: SubscribePage,
        //canMatch: [CheckIsLoggedIn],
      },
      {
        path: 'upgrade/:id',
        component: UpgradePage,
        //canMatch: [CheckIsLoggedIn],
      }
    ]
  },
  {
    path: 'checkout',
    //canMatch: [CheckIsLoggedIn],
    children: [
      {
        path: '',
        component: StorePage,
      },
      {
        path: 'success',
        component: CheckoutSuccessPage
      },
      {
        path: 'failure',
        component: CheckoutFailurePage
      }
    ]
  },
  {
    path: 'settings',
    component: SettingsComponent,
    canMatch: [CheckIsLoggedIn],
  },
  {
    path: 'settings/notifications',
    component: NotificationsSettingsComponent,
    canMatch: [CheckIsLoggedIn],
  },
  {
    path: 'settings/info',
    component: AppInfoComponent,
    canMatch: [CheckIsLoggedIn],
  },
  {
    path: 'settings/change-app-icon',
    component: ChangeAppIconComponent,
    canMatch: [CheckIsLoggedIn],
  },
  {
    path: 'login',
    component: LoginPage,
    canMatch: [async () => {
      const storageService = inject(StorageService);
      const router = inject(Router);
      const accessToken = await storageService.getItem('access_token');
      return accessToken === null ? true : router.navigateByUrl('me');
    }],
  },
  {
    path: 'Invoice/:invoiceSlug',
    component: InvoicePage
  },
  {
    path: 'family-graph/:slug',
    component: FamilyGraphPage,
    canActivate: [
      function (route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
        return (state.url.indexOf('/me?') >= 0 || state.url.indexOf('/root?') >= 0 ||
          state.url.endsWith('/me') || state.url.endsWith('/root')) ?
          inject(AuthGuard).canActivate(route, state) : true
      }
    ],
  },
  {
    path: 'cards',
    children: [
      {
        path: '',
        component: CardIndexComponent,
        pathMatch: 'full'
      },
      {
        path: ':id',
        component: CardDetailsComponent
      }]
  },
  {
    path: 'store-promotions',
    children: [
      {
        path: '',
        component: StorePromotionIndexComponent,
        pathMatch: 'full'
      },
      {
        path: ':id',
        component: StorePromotionDetailsComponent
      }]
  },
  {
    path: 'news',
    //canMatch: [CheckIsLoggedIn],
    component: NewsIndexComponent
  },
  {
    path: 'news/:id',
    //canMatch: [CheckIsLoggedIn],
    component: NewsDetailsComponent
  },
  {
    path: 'soon',
    component: SoonPage,
  },
  {
    path: 'policy',
    component: PolicyPage,
  },
  {
    path: 'about',
    component: AboutPage,
  },
  {
    path: 'terms',
    component: TermsPage,
  },
  {
    path: 'pay',
    children: [
      {
        path: '',
        component: PayPage,
        pathMatch: 'full'
      },
      {
        path: 'checkout-success',
        component: PayCheckoutSuccessPage
      },
      {
        path: 'checkout-failure',
        component: PayCheckoutFailurePage
      }
    ]
  },
  {
    path: 'user-awards',
    canMatch: [CheckIsLoggedIn],
    children: [
      {
        path: '',
        component: AwardCategoriesPageComponent,
      },
      {
        path: 'route/:id',
        component: AwardRoutePageComponent
      },
      {
        path: 'requests',
        component: AwardRequestsPageComponent
      },
      {
        path: ':id',
        component: AwardRoutesPageComponent
      },
    ]
  },
  {
    path: 'forms/:slug',
    //canMatch: [CheckIsLoggedIn],
    component: FormsComponent
  },
  {
    path: '',
    redirectTo: 'home',
    pathMatch: 'full'
  },
  {
    path: '**',
    redirectTo: 'home',
  }
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      preloadingStrategy: PreloadAllModules,
      initialNavigation: 'enabledBlocking',
      enableTracing: false
    }),

  ],
  exports: [RouterModule],
  providers: [
    provideRouter(routes, withComponentInputBinding(), withRouterConfig({
      paramsInheritanceStrategy: 'always' // This helps with nested routes
    })),
  ]
})
export class AppRoutingModule {
}
