/// <reference types="@capacitor/splash-screen" />
import { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'sa.altwijriy.familyapp',
  appName: 'التويجري',
  webDir: 'dist/app/browser',
  server:{
    androidScheme: 'http',
  },
  plugins: {
    SplashScreen: {
      launchShowDuration: 15000,
      launchAutoHide: false,
      launchFadeOutDuration: 3000,
      backgroundColor: "#ffffffff",
      androidSplashResourceName: "splash",
      androidScaleType: "CENTER_CROP",
      showSpinner: true,
      androidSpinnerStyle: "large",
      iosSpinnerStyle: "large",
      spinnerColor: "#999999",
      splashFullScreen: true,
      splashImmersive: true,
      layoutName: "launch_screen",
      useDialog: true,
    },
    Camera: {
      permissions: {
        camera: "Camera access is required to scan QR CODES."
      }
    }
  }
};

export default config;
